#此环境是本地开发使用，但不会使用本地跨域代理模式

NODE_ENV = development
# just a flag
ENV = 'dev'

# base api 天工
VUE_APP_BASE_API = 'https://tg-api-dev.estar-go.com'
# VUE_APP_BASE_API = 'https://tg-api.estar-go.com'


# 体验版-地推码 渠道ID
VUE_APP_CHANNEL_ID = 'cns032fta6ec738o34n0'
# 体验版-地推码 二级渠道ID
VUE_APP_SUB_CHANNEL_ID = 'con1vvqknrgc73df6ku0'

# base api 天奕
VUE_APP_TY_BASE_API = 'https://higo-api-dev.elf-go.com'

# base  开发环境 天工网站
VUE_APP_TG_HOST = 'https://tg-dev.estar-go.com'
# VUE_APP_TG_HOST = 'https://tg.estar-go.com'

# 天工H5中台
VUE_APP_TG_H5_HOST = 'https://tg-h5-dev.estar-go.com'
# VUE_APP_TG_H5_HOST = 'https://tg-h5.estar-go.com'
