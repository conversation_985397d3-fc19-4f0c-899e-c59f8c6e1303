# 教育小程序

这是一个基于 uniapp 开发的微信小程序,主要用于教育机构的教学管理和学生服务。

## 项目结构

```
├── src
│   ├── pages                    # 页面文件
│   │   ├── student             # 学生端页面
│   │   │   ├── home           # 首页
│   │   │   ├── course         # 课程
│   │   │   ├── classSchedule  # 课表
│   │   │   └── my             # 我的
│   │   └── teacher            # 教师端页面
│   │       ├── home           # 工作台
│   │       ├── sort           # 功能
│   │       ├── report         # 报表
│   │       └── usercenter     # 我的
│   ├── components              # 通用组件
│   │   ├── alert              # 提示弹窗
│   │   ├── confirm            # 确认弹窗
│   │   ├── image-uploader     # 图片上传组件
│   │   └── ...                # 其他组件
│   ├── services               # API 服务
│   ├── uni_modules            # uni扩展组件
│   ├── utils                  # 工具函数
│   ├── static                 # 静态资源
│   ├── App.vue               # 应用入口
│   ├── main.js               # 主入口
│   ├── manifest.json         # 配置文件
│   └── pages.json           # 页面路由配置
```

## 功能模块

### 学生端

1. **首页**

   - 微信登录授权
   - 订阅消息管理
   - 问卷调查入口
   - 学员信息绑定

2. **课程管理**

   - 课表查看
   - 报读课程
   - 学习报告

3. **个人中心**
   - 个人信息管理
   - 优惠券管理
   - 学习报告详情

### 教师端

1. **工作台**

   - 系统登录
   - 功能分类
   - 报表查看

2. **客户管理**

   - 新增意向客户
   - 客户信息管理
   - 沟通记录管理

3. **班级管理**

   - 花名册管理
   - 学员信息查看
   - 沟通记录
   - 班级学员列表

4. **排课管理**

   - 课表管理
   - 考勤管理
   - 实到人数统计

5. **其他功能**
   - 资料库
   - 富文本编辑
   - 地推码管理
   - 隐私政策
   - 用户协议

## 订阅消息管理

### 功能说明

小程序提供了一套完整的订阅消息管理系统，用于向用户推送重要通知。由于微信限制每次最多订阅3个模板，系统采用了批量订阅的方式处理多个模板。

### 主要特性

- 支持多个消息模板的批量订阅
- 自动分批处理（每批3个模板）
- 完整的订阅状态跟踪
- 用户友好的订阅流程
- 详细的日志记录

### 消息类型

1. **课程相关通知**
   - 课程总结通知
   - 课消提醒通知
   - 班级通知

2. **活动相关通知**
   - 家长课堂通知
   - 电子合同签署通知
   - 问卷通知

### 使用说明

1. **初始化订阅**
   - 系统会在适当时机（如用户点击特定功能）自动触发订阅流程
   - 首次订阅会请求用户授权

2. **订阅流程**
   - 系统自动获取可订阅的消息模板
   - 按每批3个模板进行分组
   - 依次请求用户订阅每一批模板
   - 记录每个模板的订阅状态

3. **状态管理**
   - 用户可在"消息通知设置"中查看和管理订阅状态
   - 支持单个开启/关闭特定类型的通知
   - 可随时重新订阅之前拒绝的通知

### 开发者说明

1. **模板订阅实现**
   ```javascript
   // 批量订阅示例
   async subscribeNextBatch() {
     if (this.currentSubscribeIndex >= this.subscribeTemplates.length) {
       console.log("所有模板订阅完成");
       this.resetSubscriptionState();
       return;
     }

     const currentBatch = this.subscribeTemplates.slice(
       this.currentSubscribeIndex,
       this.currentSubscribeIndex + 3
     );

     uni.requestSubscribeMessage({
       tmplIds: currentBatch,
       success: (res) => {
         // 处理订阅结果
         this.currentSubscribeIndex += 3;
       },
       fail: (err) => {
         // 处理订阅失败
       }
     });
   }
   ```

2. **状态追踪**
   - 使用 `hasInitialized` 标志追踪初始化状态
   - 使用 `currentSubscribeIndex` 追踪当前订阅进度
   - 详细的日志记录系统记录每个模板的订阅状态

3. **错误处理**
   - 完整的错误状态处理（accept/reject/ban/fail）
   - 用户拒绝订阅时的优雅降级
   - 订阅失败时的重试机制

## 自定义组件

### ImageUploader 图片上传组件

一个用于选择、预览和上传图片的通用组件，支持多图上传、图片预览和删除功能。

#### 主要特性

- 支持多图片选择和上传
- 支持图片预览和删除
- 支持 OSS 云存储上传
- 可自定义最大上传数量和样式

#### 用法示例

```vue
<template>
  <image-uploader
    v-model="imageList"
    :max-count="6"
    :oss-uploader="Oss"
    @error="handleError"
  />
</template>
```

详细文档请参考 [ImageUploader 组件文档](src/components/image-uploader/README.md)

## 开发环境要求

- 开发工具：HBuilderX
- Node 版本：>= 12.0.0
- 微信开发者工具

## 技术栈

- 框架：uni-app (仅支持微信小程序)
- UI 组件：uview-ui
- 状态管理：Vuex
- HTTP 请求：uni.request

## 项目运行

1. 安装依赖

```bash
npm install
```

2. 开发模式

```bash
npm run dev:mp-weixin
```

3. 生产构建

```bash
npm run build:mp-weixin
```

## 注意事项

1. 小程序相关

   - appid 配置在 manifest.json 中
   - 订阅消息模板需要在微信公众平台配置
   - 包体积控制在 2MB 以内(分包 8MB)

2. 开发相关
   - 仅支持微信小程序端
   - 网络请求域名需要在微信公众平台备案
   - 注意处理用户隐私协议弹窗

## 第三方组件

- uv-ui-tools: UI 组件工具集
- next-paging: 分页组件
- uv-qrcode: 二维码生成
- sp-editor: 富文本编辑器

## 更新日志

### v1.0.0 (2024-01-20)

- 初始版本发布
- 完成基础功能开发
- 支持微信登录和订阅消息

## 维护者

- 开发团队

## 许可证

[MIT](LICENSE)
