import http from "./_utils/request.js";
import qs from "qs";

// 获取阿里云配置
function AliyunWareCategory(params) {
  const newData = qs.stringify(params, { arrayFormat: "repeat" });
  const data = {
    url: `/api/market-service/aliyun/config?${newData}`,
    method: "GET"
  };
  return http.request(data);
}
// 获取阿里云配置
function AliyunWareCategoryOfStudent(params) {
  const newData = qs.stringify(params, { arrayFormat: "repeat" });
  const data = {
    url: `/web/questionnaire-web-service/aliyun/config?${newData}`,
    method: "GET"
  };
  return http.request(data);
}

export { AliyunWareCategory, AliyunWareCategoryOfStudent };
