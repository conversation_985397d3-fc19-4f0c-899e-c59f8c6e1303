/* eslint-disable no-param-reassign */
import HTTP from "./http.js";
import { getOpenId, login } from "@/services/student/home";
import {
  getDataByRole,
  processUserList,
  getCheckedStudentInfo
} from "@/utils/user";
const ACCESS_TOKEN = "token"; // token凭证的key
const TOKEN_KEY = {
  TEACHER: "token",
  STUDENT: "session"
};
const CLIENT = "client";
// 创建配置信息
const Config = {
  baseUrl: process.env.VUE_APP_BASE_API, // 公共请求前缀（域名）
  timeout: 10 * 1000 // 请求超时时间
};
// 初始化请求实例
const HttpClint = new HTTP(Config);
// 请求拦截器
HttpClint.interceptor.request = (config) => {
  // console.log("config :>> ", config);
  // 添加 Token 凭证
  if (typeof config.header !== "object") config.header = {};
  config.header[CLIENT] = "miniprogram";
  const portType = uni.getStorageSync("portType");
  const storeTokenKey = uni.getStorageSync(TOKEN_KEY[portType]);
  const roleKey = {
    default: 1,
    student: 3,
    customer: 2
  };
  // 后端判断是否是意向 还是学员 游客
  const visitor = roleKey[uni.getStorageSync("session").role] ?? 1; // 身份
  const operationId = uni.getStorageSync("session").operation_id; // 操作人;
  const token = portType === "STUDENT" ? storeTokenKey.token : storeTokenKey;
  if (token) {
    config.header[ACCESS_TOKEN] = token;
  }
  config.header.visitor = visitor;
  config.header["Operation-Id"] = operationId || "";
  // 不需要传入token
  console.log("config.data :>> ", config.data, config);
  if (config.data) {
    const { UNAUTHORIZED } = config.data;
    if (UNAUTHORIZED) {
      config.header[ACCESS_TOKEN] = "";
      config.header.visitor = "";
      config.header["Operation-Id"] = "";
    }
  }
  return config;
};
// 响应拦截器
HttpClint.interceptor.response = (response) => {
  /* 以下是一个 响应统一处理 的示例，可以根据自己的场景业务进行修改 */
  if ([200].includes(response.statusCode)) {
    // if (response.header['Transfer-Encoding']) {
    //   return response.data;
    // }
    const portType = uni.getStorageSync("portType");
    if (response.data.code !== 0) {
      const message = response.data.message ?? "";
      if (portType !== "STUDENT" && message && message !== "员工信息不存在") {
        uni.showToast({ title: message, icon: "error" });
      }
      // if (message && message !== "code凭证不能为空") {

      // }
    }
    return response.data;
  }
  if ([401].includes(response.statusCode)) {
    uni.showToast({ title: "登录过期", icon: "none", mask: true });
    uni.clearStorageSync();
    setTimeout(() => {
      uni.navigateTo({ url: "/pages/index/index" });
    }, 1000);
    return false;
  }
  if ([403].includes(response.statusCode)) {
    // uni.showToast({ title: "权限不足，请联系管理员！", icon: "none" });
    uni.$u.toast("权限不足，请联系管理员！");
    return false;
  }
  if ([400].includes(response.statusCode)) {
    const err = response.data.err ?? "";
    if (err) {
      uni.showToast({ title: err, icon: "error" });
    }
    return false;
  }
  // 416 身份发生改变 相当于重新重新登录 无感
  // {"code":   2,
  // "status": -1,
  // "err":    "验证登录者身份失败",}
  // {"code":   3,
  // "status": -1,
  // "err":    "用户不存在",}
  // {"code":    1,
  // "visitor": info.IsVisitor,
  // "status":  -1,
  // "err":     "参数缺失",}
  // {"code":    4,
  // "visitor": info.IsVisitor,
  // "status":  -1,
  // "err":     "身份不合法",}
  if ([416].includes(response.statusCode)) {
    // const err = response.data.err ?? "";
    if (uni.getStorageSync("session")) {
      uni.login({
        provider: "weixin",
        onlyAuthorize: true, // 微信登录仅请求授权认证
        success: async (event) => {
          const { code } = event;
          const res = await getOpenId({ code, UNAUTHORIZED: true });
          if (res.code === 0) {
            const res2 = await login({
              open_id: res.data.openid,
              UNAUTHORIZED: true
            });
            if (res2.code === 0) {
              const role =
                !res2.data.is_student && !res2.data.is_customer
                  ? "default"
                  : res2.data.is_student
                  ? "student"
                  : "customer";
              // this.role = role;
              // const session = { ...res2.data, role };
              uni.setStorageSync("session", { ...res2.data, role });
              uni.setStorageSync("curStudentInfo", "");
              // 获取选中的学生/客户信息
              await getCheckedStudentInfo(
                res2.data.open_id,
                role,
                res2.data.operation_id
              );
              // 根据角色获取用户列表
              await getDataByRole(
                this,
                role,
                res2.data.open_id,
                (code, data, message) => {
                  processUserList(code, data, message, this, res2.data.open_id);
                }
              );
              // this.session = session;
              // this.role = this.session.role;
              // this.rowIdField = this.rowIds[this.role];
            }
          }
        }
      });
    }
    uni.reLaunch({ url: "/pages/redirect/index" });
    getApp().globalData.tabIndex = 0;
    return false;
  }
  uni.showToast({ title: "未知错误，请重试！", icon: "error" });

  return false;
};

export default HttpClint;
