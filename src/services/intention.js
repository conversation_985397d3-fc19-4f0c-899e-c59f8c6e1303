// 意向客户

import http from "./_utils/request.js";
import qs from "qs";
function customerCreate(params) {
  const data = {
    url: "/api/market-service/miniprogram/customer/create",
    method: "POST",
    data: params
  };
  return http.request(data);
}
function customerUpadte(params) {
  const data = {
    url: `/api/market-service/miniprogram/customer/update?customer_id=${params.customer_id}`,
    method: "PATCH",
    data: params
  };
  return http.request(data);
}
function getStatusList(params) {
  const data = {
    url: "/api/market-service/miniprogram/customer/status-list",
    method: "GET",
    data: params
  };
  return http.request(data);
}
function getStudentsCustomerList(params) {
  const newData = qs.stringify(params, { arrayFormat: "repeat" });
  const data = {
    url: `/api/market-service/miniprogram/customer/list?${newData}`,
    method: "GET"
  };
  return http.request(data);
}
function getChannelList(params) {
  const data = {
    url: "/api/market-service/miniprogram/channel/list",
    method: "GET",
    data: params
  };
  return http.request(data);
}
function getSubChannelList(params) {
  const data = {
    url: "/api/market-service/miniprogram/subchannel/list",
    method: "GET",
    data: params
  };
  return http.request(data);
}
function getCustomerInfo(params) {
  const data = {
    url: "/api/market-service/miniprogram/customer/info",
    method: "GET",
    data: params
  };
  return http.request(data);
}
function getIntentionLevelList(params) {
  const data = {
    url: "/api/market-service/public/miniprogram/intention-level/list",
    method: "GET",
    data: params
  };
  return http.request(data);
}
// 正式学员列表
function getRegularStudentList(params) {
  const newData = qs.stringify(params, { arrayFormat: "repeat" });
  const data = {
    url: `/api/student-service/miniprogram/student/short-list?${newData}`,
    method: "GET"
  };
  return http.request(data);
}

// 根据校区返回对应的员工信息
function getAdvisorList(params) {
  const newData = qs.stringify(params, { arrayFormat: "repeat" });
  const data = {
    url: `/api/organization-service/miniprogram/employee/department-list?${newData}`,
    method: "GET"
  };
  return http.request(data);
}

// 根据id获取关联部门列表
function getDepartmentList(params) {
  const newData = qs.stringify(params, { arrayFormat: "repeat" });
  const data = {
    url: `/api/organization-service/public/department/list?${newData}`,
    method: "GET"
  };
  return http.request(data);
}

// 小程序渠道 获取直营销线下
function getOfflineChannelList(params) {
  const newData = qs.stringify(params, { arrayFormat: "repeat" });
  const data = {
    url: `/api/market-service/miniprogram/channel/offline-list?${newData}`,
    method: "GET"
  };
  return http.request(data);
}
export {
  customerCreate,
  customerUpadte,
  getStatusList,
  getStudentsCustomerList,
  getChannelList,
  getSubChannelList,
  getCustomerInfo,
  getIntentionLevelList,
  getRegularStudentList,
  getAdvisorList,
  getDepartmentList,
  getOfflineChannelList
};
