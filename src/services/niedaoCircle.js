import http from "./_utils/request.js";

function momentsList(params) {
  const data = {
    url: "/api/questionnaire-service/admin/moments/teacher/list",
    method: "GET",
    data: params
  };
  return http.request(data);
}
function momentsDate(params) {
  const data = {
    url: "/api/questionnaire-service/admin/moments/teacher/date",
    method: "GET",
    data: params
  };
  return http.request(data);
}

function updateTopStatus(params) {
  const data = {
    url: "/api/questionnaire-service/admin/moments/update/top-status",
    method: "POST",
    data: params
  };
  return http.request(data);
}
function deleteMoment(params) {
  const data = {
    url: "/api/questionnaire-service/admin/moments/teacher/delete",
    method: "POST",
    data: params
  };
  return http.request(data);
}
function momentDetail(params) {
  const data = {
    url: "/api/questionnaire-service/admin/moments/teacher/detail",
    method: "GET",
    data: params
  };
  return http.request(data);
}

function momentDelete(params) {
  const data = {
    url: "/api/questionnaire-service/admin/moments/teacher/delete",
    method: "POST",
    data: params
  };
  return http.request(data);
}
function teacherInfo(params) {
  const data = {
    url: "/api/questionnaire-service/admin/moments/teacher/info",
    method: "GET",
    data: params
  };
  return http.request(data);
}
function momentCreate(params) {
  const data = {
    url: "/api/questionnaire-service/admin/moments/teacher/create",
    method: "POST",
    data: params
  };
  return http.request(data);
}
export {
  momentsList,
  momentsDate,
  updateTopStatus,
  deleteMoment,
  momentDetail,
  momentCreate,
  momentDelete,
  teacherInfo
};
