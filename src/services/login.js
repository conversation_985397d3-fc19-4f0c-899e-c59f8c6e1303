import http from "./_utils/request.js";

function login(params) {
  const data = {
    url: "/api/login/login",
    method: "POST",
    data: params
  };
  return http.request(data);
}
function getPermission(params) {
  const data = {
    url: "/api/permission-service/employee/all-permission",
    method: "GET",
    data: params
  };
  return http.request(data);
}
// 获取校区下的某个接口权限
function checkPower(params) {
  params.department_id = uni.getStorageSync("checkedSchool")[0].id;

  const data = {
    url: "/api/permission-service/role-permission/check-power",
    method: "GET",
    data: params
  };
  return http.request(data);
}
// // 获取校区下的某个接口权限
// function getDepartmentEmployeeInfo(params) {
//   const data = {
//     url: "/api/organization-service/miniprogram/office-post/department-employee/info",
//     method: "GET",
//     data: params
//   };
//   return http.request(data);
// }
function getEmployeeInfo(params) {
  const data = {
    url: "/api/organization-service/employee/info",
    method: "GET",
    data: params
  };
  return http.request(data);
}

function getSchoolData(params) {
  const data = {
    url: "/api/organization-service/overr-view/school",
    method: "GET",
    data: params
  };
  return http.request(data);
}
// 获取openid
function getOpenId(params) {
  const data = {
    url: "/web/questionnaire-web-service/public/getOpenId",
    method: "GET",
    data: params
  };
  return http.request(data);
}
// 教师端通过openid自动登录
function miniprogramLogin(params) {
  const data = {
    url: "/api/login/miniprogramLogin",
    method: "POST",
    data: params
  };
  return http.request(data);
}
// 教师端退出登录
function miniprogramLogout(params) {
  const data = {
    url: "/api/login/miniprogramUnbind",
    method: "POST",
    data: params
  };
  return http.request(data);
}
// 获取上一次的登录客户类型，是学生还是教师
function getLoginLast(params) {
  const data = {
    url: "/web/questionnaire-web-service/public/getLoginLog",
    method: "GET",
    data: params
  };
  return http.request(data);
}
export {
  login,
  getPermission,
  getSchoolData,
  getEmployeeInfo,
  getOpenId,
  miniprogramLogin,
  miniprogramLogout,
  getLoginLast,
  checkPower
};
