import http from "./_utils/request.js";
import qs from "qs";

// 获取班级列表
function getClassroomList(params) {
  const newData = qs.stringify(params, { arrayFormat: "repeat" });
  const data = {
    url: `/api/school-service/miniprogram/classroom/list?${newData}`,
    method: "GET"
  };
  return http.request(data);
}
// 获取班级学员列表
function getClassroomStudentList(params) {
  const newData = qs.stringify(params, { arrayFormat: "repeat" });
  const data = {
    url: `/api/school-service/miniprogram/classroom-student/list?${newData}`,
    method: "GET"
  };
  return http.request(data);
}

// 获取学员列表
function getSearchStudentList(params) {
  const newData = qs.stringify(params, { arrayFormat: "repeat" });
  const data = {
    url: `/api/student-service/miniprogram/student/search-list?${newData}`,
    method: "GET"
  };
  return http.request(data);
}
//
/*
 * 发送学员评价
 */
function feedbackSend(params) {
  const data = {
    url: "/api/school-service/miniprogram/feedback/send",
    method: "POST",
    data: params
  };
  return http.request(data);
}
// 发送家长、班级、课程
function feedbackKindsSend(params) {
  const data = {
    url: "/api/school-service/admin/feedback/send",
    method: "POST",
    data: params
  };
  return http.request(data);
}
// 获取班级学员沟通记录
function getFeedbackStudentList(params) {
  const newData = qs.stringify(params, { arrayFormat: "repeat" });
  const data = {
    url: `/api/school-service/miniprogram/feedback/studentList?${newData}`,
    method: "GET"
  };
  return http.request(data);
}
// 获取沟通记录详情
function getFeedbackDetail(params) {
  const newData = qs.stringify(params, { arrayFormat: "repeat" });
  const data = {
    url: `/api/school-service/miniprogram/feedback/detail?${newData}`,
    method: "GET"
  };
  return http.request(data);
}
/*
 * 撤销学员评价
 */
function feedbackCancel(params) {
  const data = {
    url: "/api/school-service/miniprogram/feedback/cancel",
    method: "POST",
    data: params
  };
  return http.request(data);
}

export {
  getClassroomList,
  getSearchStudentList,
  getClassroomStudentList,
  feedbackSend,
  feedbackKindsSend,
  getFeedbackStudentList,
  getFeedbackDetail,
  feedbackCancel
};
