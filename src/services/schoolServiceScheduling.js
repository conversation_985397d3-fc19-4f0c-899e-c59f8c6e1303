// 排课管理

import http from "./_utils/request.js";
import qs from "qs";

function getSchoolServiceSchedulingList(params) {
  const newData = qs.stringify(params, { arrayFormat: "repeat" });
  const data = {
    url: `/api/school-service/miniprogram/scheduling/list?${newData}`,
    method: "GET"
  };
  return http.request(data);
}
function getAttendanceList(params) {
  const data = {
    url: `/api/school-service/miniprogram/scheduling/student/list`,
    method: "GET",
    data: params
  };
  return http.request(data);
}
function getSchedulingClassroom(params) {
  const data = {
    url: `/api/school-service/miniprogram/schoolroom/list-with-scheduling`,
    method: "GET",
    data: params
  };
  return http.request(data);
}
// 获取课表列表
function getRollCallList(params) {
  const data = {
    url: `/api/school-service/miniprogram/scheduling/roll-call/list`,
    method: "GET",
    data: params
  };
  return http.request(data);
}
// 上课点名学员列表
function getSchedulingStudentList(params) {
  const data = {
    url: `/api/school-service/miniprogram/scheduling/student/list`,
    method: "GET",
    data: params
  };
  return http.request(data);
}
// 上课点名学员列表
function getFeedbackStudentList(params) {
  const data = {
    url: `/api/school-service/miniprogram/feedback/scheduling-students`,
    method: "GET",
    data: params
  };
  return http.request(data);
}

// 撤销
function cancelFeedback(params) {
  const data = {
    url: `/api/school-service/miniprogram/feedback/cancel`,
    method: "POST",
    data: params
  };
  return http.request(data);
}
function lockAccount(params) {
  const data = {
    url: "/api/school-service/miniprogram/check-in/lock-account",
    method: "POST",
    data: params
  };
  return http.request(data);
}
// 开始点名上课
function beginRollCall(params) {
  const data = {
    url: `/api/school-service/miniprogram/scheduling/begin?scheduling_id=${params.scheduling_id}`,
    method: "POST",
    data: params
  };
  return http.request(data);
}

// 点名上课锁
function rollCallLock(params) {
  const data = {
    url: "/api/school-service/miniprogram/check-in/roll-call-lock",
    method: "POST",
    data: params
  };
  return http.request(data);
}

// 点名上课续锁
function sustainLock(params) {
  const data = {
    url: "/api/school-service/miniprogram/check-in/roll-call-sustain-lock",
    method: "POST",
    data: params
  };
  return http.request(data);
}
// 解锁
function unlockAccount(params) {
  const data = {
    url: "/api/school-service/miniprogram/check-in/roll-call-unlock",
    method: "POST",
    data: params
  };
  return http.request(data);
}
// 上课点名
function rollCall(params) {
  const data = {
    url: "/api/school-service/miniprogram/check-in/roll-call",
    method: "POST",
    data: params
  };
  return http.request(data);
}
// 撤销上课
function revocation(params) {
  const data = {
    url: "/api/school-service/miniprogram/scheduling/revocation",
    method: "POST",
    data: params
  };
  return http.request(data);
}
export {
  getSchoolServiceSchedulingList,
  getAttendanceList,
  getSchedulingClassroom,
  getRollCallList,
  lockAccount,
  rollCallLock,
  sustainLock,
  unlockAccount,
  getSchedulingStudentList,
  getFeedbackStudentList,
  cancelFeedback,
  rollCall,
  beginRollCall,
  revocation
};
