import http from "../_utils/request.js";
import qs from "qs";
/*
 * 绑定学员
 */
export const bindUser = (params) => {
  const data = {
    url: "/web/questionnaire-web-service/bindUser",
    method: "POST",
    data: params
  };
  return http.request(data);
};
// 获取验证码
export const sendSms = (params) => {
  const data = {
    url: "/web/questionnaire-web-service/sendSms",
    method: "POST",
    data: params
  };
  return http.request(data);
};

// 获取学员列表
export const getStudentList = (params) => {
  const newData = qs.stringify(params, { arrayFormat: "repeat" });
  const data = {
    url: `/web/questionnaire-web-service/getStudentList?${newData}`,
    method: "GET"
  };
  return http.request(data);
};

// 记录选中的学员
export const checkedStudent = (params) => {
  const data = {
    url: "/web/questionnaire-web-service/checkedStudent",
    method: "POST",
    data: params
  };
  return http.request(data);
};

// 获取选中的学员
export const checkedStudentInfo = (params) => {
  const newData = qs.stringify(params, { arrayFormat: "repeat" });
  const data = {
    url: `/web/questionnaire-web-service/checkedStudentInfo?${newData}`,
    method: "GET"
  };
  return http.request(data);
};

// 获取意向客户列表
export const customerList = (params) => {
  const newData = qs.stringify(params, { arrayFormat: "repeat" });
  const data = {
    url: `/web/questionnaire-web-service/getCustomerList?${newData}`,
    method: "GET"
  };
  return http.request(data);
};

// 记录选中的意向学员
export const checkedCustomer = (params) => {
  const data = {
    url: "/web/questionnaire-web-service/checkedCustomer",
    method: "POST",
    data: params
  };
  return http.request(data);
};
// 清除新推送问卷消息
export const clearRead = (params) => {
  const data = {
    url: "/web/questionnaire-web-service/surveyWeb/clear-read",
    method: "POST",
    data: params
  };
  return http.request(data);
};
// 获取是否有新推送问卷
export const hasNewSurvey = (params) => {
  const newData = qs.stringify(params, { arrayFormat: "repeat" });
  const data = {
    url: `/web/questionnaire-web-service/surveyWeb/has-new-survey?${newData}`,
    method: "GET"
  };
  return http.request(data);
};

// 获取意向客户问卷列表
export const checkedCustomerInfo = (params) => {
  const newData = qs.stringify(params, { arrayFormat: "repeat" });
  const data = {
    url: `/web/questionnaire-web-service/checkedCustomerInfo?${newData}`,
    method: "GET"
  };
  return http.request(data);
};

// 获取tabbar显示权限
export const menuShow = (params) => {
  const newData = qs.stringify(params, { arrayFormat: "repeat" });
  const data = {
    url: `/web/questionnaire-web-service/menuShow?${newData}`,
    method: "GET"
  };
  return http.request(data);
};

// 解绑学员
export const unbindStudent = (params) => {
  const data = {
    url: "/web/questionnaire-web-service/unBindUser",
    method: "POST",
    data: params
  };
  return http.request(data);
};

// 获取意向详情
export const getIntentDetail = (params) => {
  const newData = qs.stringify(params, { arrayFormat: "repeat" });
  const data = {
    url: `/web/questionnaire-web-service/getCustomerInfo?${newData}`,
    method: "GET"
  };
  return http.request(data);
};

// 获取学员详情
export const getStudentDetail = (params) => {
  const newData = qs.stringify(params, { arrayFormat: "repeat" });
  const data = {
    url: `/web/questionnaire-web-service/getStudentInfo?${newData}`,
    method: "GET"
  };
  return http.request(data);
};

// 获取游客详情
export const getCustomerDetail = (params) => {
  const newData = qs.stringify(params, { arrayFormat: "repeat" });
  const data = {
    url: `/web/questionnaire-web-service/getVisitorInfo?${newData}`,
    method: "GET"
  };
  return http.request(data);
};
// 设置意向头像
export const setIntentAvatar = (params) => {
  const data = {
    url: "/web/questionnaire-web-service/setCustomerHead",
    method: "POST",
    data: params
  };
  return http.request(data);
};

// 设置学员头像
export const setStudentHead = (params) => {
  const data = {
    url: "/web/questionnaire-web-service/setStudentHead",
    method: "POST",
    data: params
  };
  return http.request(data);
};

// 设置游客头像
export const setCustomerHead = (params) => {
  const data = {
    url: "/web/questionnaire-web-service/setVisitorHead",
    method: "POST",
    data: params
  };
  return http.request(data);
};

// 获取用户绑定的关系
export const getUserRelation = (params) => {
  const data = {
    url: "/web/questionnaire-web-service/getBindRoleList",
    method: "GET",
    data: params
  };
  return http.request(data);
};

// 获取电子合同消息推送
export const getElectronContractMessage = (params) => {
  const data = {
    url: "/web/order-web-service/public/contract/has-new-survey",
    method: "GET",
    data: params
  };
  return http.request(data);
};

//  取消小红点提醒 电子合同
export const cancelElectronContractMessage = (params) => {
  const data = {
    url: "/web/order-web-service/public/contract/clear-read",
    method: "POST",
    data: params
  };
  return http.request(data);
};

// 获取消息模板
export const getMessageTemplate = (params) => {
  const data = {
    url: "/api/questionnaire-service/public/wechat/getMiniTemplateId",
    method: "GET",
    data: params
  };
  return http.request(data);
};
