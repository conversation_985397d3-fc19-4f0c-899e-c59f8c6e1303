import http from "../_utils/request.js";
import qs from "qs";

// 获取openid
export const getSchoolData = (params) => {
  const newData = qs.stringify(params, { arrayFormat: "repeat" });
  const data = {
    url: `/api/organization-service/miniprogram/overr-view/school?${newData}`,
    method: "GET",
    data: params
  };
  return http.request(data);
};

// 完善信息
export const customerCreate = (params) => {
  const data = {
    url: "/api/market-service/public/miniprogram/customer/create",
    method: "POST",
    data: params
  };
  return http.request(data);
};

// 调研表详情
export const surveyInfo = (params) => {
  const newData = qs.stringify(params, { arrayFormat: "repeat" });
  const data = {
    url: `/web/questionnaire-web-service/public/getSurveyInfo?${newData}`,
    method: "GET",
    data: params
  };
  return http.request(data);
};
// 满意度调查提交
export const createSurveyInfo = (params) => {
  const data = {
    url: "/web/questionnaire-web-service/public/createSurveyInfo",
    method: "POST",
    data: params
  };
  return http.request(data);
};

// 获取用户手机号
export const getUserPhoneNumber = (params) => {
  const data = {
    url: "/api/questionnaire-service/public/wechat/getUserPhoneNumber",
    method: "POST",
    data: params
  };
  return http.request(data);
};
// 获取用户手机号
export const sendToCustomerSurvey = (params) => {
  const data = {
    url: "/web/questionnaire-web-service/public/sendToCustomerSurvey",
    method: "POST",
    data: params
  };
  return http.request(data);
};

// 意向客户-提交问卷
export const customerCreateSurvey = (params) => {
  const data = {
    url: "/web/questionnaire-web-service/surveyWeb/customerCreateSurvey",
    method: "POST",
    data: params
  };
  return http.request(data);
};

// 意向客户-问卷详情
export const customerSurveyInfo = (params) => {
  const newData = qs.stringify(params, { arrayFormat: "repeat" });
  const data = {
    url: `/web/questionnaire-web-service/surveyWeb/customerSurveyInfo?${newData}`,
    method: "GET",
    data: params
  };
  return http.request(data);
};
