import http from "../_utils/request.js";
import qs from "qs";

// 获取调查问卷列表
export const surveyList = (params) => {
  const newData = qs.stringify(params, { arrayFormat: "repeat" });
  const data = {
    url: `/web/questionnaire-web-service/surveyWeb/list?${newData}`,
    method: "GET"
  };
  return http.request(data);
};

// 获取意向客户问卷列表
export const customerList = (params) => {
  const newData = qs.stringify(params, { arrayFormat: "repeat" });
  const data = {
    url: `/web/questionnaire-web-service/surveyWeb/customerList?${newData}`,
    method: "GET"
  };
  return http.request(data);
};
