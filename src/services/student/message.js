import http from "../_utils/request.js";
import qs from "qs";

// 获取消息信息
export const getMessageInfo = (params) => {
  const newData = qs.stringify(params, { arrayFormat: "repeat" });
  const data = {
    url: `/web/questionnaire-web-service/wechat-message-info?${newData}`,
    method: "GET"
  };
  return http.request(data);
};

// 设置消息通知
export const setMessageNotice = (params) => {
  const data = {
    url: `/web/questionnaire-web-service/set-wechat-message`,
    method: "POST",
    data: params
  };
  return http.request(data);
};
