import http from "../_utils/request.js";
import qs from "qs";

export const videoList = (params) => {
  const newData = qs.stringify(params, { arrayFormat: "repeat" });
  const data = {
    url: `/web/questionnaire-web-service/featuredVideos/list?${newData}`,
    method: "GET"
  };
  return http.request(data);
};
export const parentList = (params) => {
  const data = {
    url: "/web/questionnaire-web-service/featuredVideos/parent-list",
    method: "GET"
  };
  return http.request(data);
};

export const departmentCity = (params) => {
  const newData = qs.stringify(params, { arrayFormat: "repeat" });
  const data = {
    url: `/web/questionnaire-web-service/featuredVideos/department-city?${newData}`,
    method: "GET"
  };
  return http.request(data);
};
