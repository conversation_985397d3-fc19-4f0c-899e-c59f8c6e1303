import http from "../_utils/request.js";
import qs from "qs";
/*
 * 获取opid
 */
export const login = (params) => {
  const data = {
    url: "/web/questionnaire-web-service/public/login",
    method: "POST",
    data: params
  };
  return http.request(data);
};

// 获取openid
export const getOpenId = (params) => {
  const newData = qs.stringify(params, { arrayFormat: "repeat" });
  const data = {
    url: `/web/questionnaire-web-service/public/getOpenId?${newData}`,
    method: "GET"
  };
  return http.request(data);
};
// 获取banner列表
export const getBannerList = (params) => {
  const data = {
    url: "/web/questionnaire-web-service/banner/list",
    method: "GET"
  };
  return http.request(data);
};
// 点击量
export const clickBanner = (params) => {
  const data = {
    url: "/web/questionnaire-web-service/banner/click",
    method: "POST",
    data: params
  };
  return http.request(data);
};
// 小程序验证码登录
export const loginByCode = (params) => {
  const data = {
    url: "/web/questionnaire-web-service/public/codeLogin",
    method: "POST",
    data: params
  };
  return http.request(data);
};
// 根据凭证获取手机号
export const getPhoneByCode = (params) => {
  const data = {
    url: "/web/questionnaire-web-service/wechat/getUserPhoneNumber",
    method: "POST",
    data: params
  };
  return http.request(data);
};

// 手机号登录
export const loginByPhone = (params) => {
  const data = {
    url: "/web/questionnaire-web-service/public/mobileLogin",
    method: "POST",
    data: params
  };
  return http.request(data);
};

// 获取验证码
export const getCode = (params) => {
  const data = {
    url: "/web/questionnaire-web-service/public/sendMobileCode",
    method: "POST",
    data: params
  };
  return http.request(data);
};

// 绑定游客后
export const bindVisitor = (params) => {
  const newData = qs.stringify(params, { arrayFormat: "repeat" });
  const data = {
    url: `/web/questionnaire-web-service/getVisitorNoBuild?${newData}`,
    method: "GET"
  };
  return http.request(data);
};

// 角色绑定
export const bindRole = (params) => {
  const data = {
    url: "/web/questionnaire-web-service/public/getRoleList",
    method: "GET"
  };
  return http.request(data);
};

// 查询是否有可绑定学员
export const getBindStudent = (params) => {
  const newData = qs.stringify(params, { arrayFormat: "repeat" });
  const data = {
    url: `/web/questionnaire-web-service/getNoBuild?${newData}`,
    method: "GET"
  };
  return http.request(data);
};

// 记录用户地理位置
export const recordLocation = (params) => {
  const data = {
    url: "/web/questionnaire-web-service/weChatLocation",
    method: "POST",
    data: params
  };
  return http.request(data);
};

// 获取客服信息
export const getqRCodeService = (params) => {
  const data = {
    url: "/web/questionnaire-web-service/qRCode/detail",
    method: "GET"
  };
  return http.request(data);
};
