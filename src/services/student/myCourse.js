import http from "../_utils/request.js";
import qs from "qs";

// 获取我的课程
export const courseList = (params) => {
  const newData = qs.stringify(params, { arrayFormat: "repeat" });
  const data = {
    url: `/web/course-web-service/public/course-minpro/list?${newData}`,
    method: "GET"
  };
  return http.request(data);
};

// 获取课程分类
export const courseCategory = (params) => {
  const newData = qs.stringify(params, { arrayFormat: "repeat" });
  const data = {
    url: `/web/course-web-service/public/course-minpro/category?${newData}`,
    method: "GET"
  };
  return http.request(data);
};

// 获取课程详情
export const getCourseDetail = (params) => {
  const newData = qs.stringify(params, { arrayFormat: "repeat" });
  const data = {
    url: `/web/course-web-service/public/course-minpro/info?${newData}`,
    method: "GET"
  };
  return http.request(data);
};
