import http from "../_utils/request.js";
import qs from "qs";

// 创建订单
export const createOrder = (params) => {
  const data = {
    url: `/web/order-web-service/public/wxorder/create`,
    method: "POST",
    data: params
  };
  return http.request(data);
};

// 取消订单
export const cancelOrder = (params) => {
  const data = {
    url: `/web/order-web-service/public/wxorder/cancel`,
    method: "POST",
    data: params
  };
  return http.request(data);
};

// 订单金额计算
export const calculateOrder = (params) => {
  const data = {
    url: `/web/order-web-service/public/wxorder/calculate`,
    method: "POST",
    data: params
  };
  return http.request(data);
};

// 订单详情
export const detailOrder = (params) => {
  const newData = qs.stringify(params, { arrayFormat: "repeat" });
  const data = {
    url: `/web/order-web-service/public/wxorder/detail?${newData}`,
    method: "GET"
  };
  return http.request(data);
};

// 订单列表
export const orderList = (params) => {
  const newData = qs.stringify(params, { arrayFormat: "repeat" });
  const data = {
    url: `/web/order-web-service/public/wxorder/list?${newData}`,
    method: "GET"
  };
  return http.request(data);
};

// 获取可用优惠卷列表
export const couponList = (params) => {
  const data = {
    url: `/web/coupon-web-service/coupon-use/coupon-list`,
    method: "POST",
    data: params
  };
  return http.request(data);
};

// 小程序可叠加优惠卷列表
export const overlayList = (params) => {
  const data = {
    url: `/web/coupon-web-service/coupon-use/overlay-list`,
    method: "POST",
    data: params
  };
  return http.request(data);
};

// 获取优惠卷折扣价格
export const getCouponPrice = (params) => {
  const data = {
    url: `/web/coupon-web-service/coupon-use/discount`,
    method: "POST",
    data: params
  };
  return http.request(data);
};

// 获取校区
export const getSchool = (params) => {
  const newData = qs.stringify(params, { arrayFormat: "repeat" });
  const data = {
    url: `/web/questionnaire-web-service/department/all?${newData}`,
    method: "GET"
  };
  return http.request(data);
};

// 支付
export const payMin = (params) => {
  const data = {
    url: `/web/order-web-service/yop/minipro-pay`,
    method: "POST",
    data: params
  };
  return http.request(data);
};

// 轮询获取支付结果
export const getPayResult = (params) => {
  const newData = qs.stringify(params, { arrayFormat: "repeat" });
  const data = {
    url: `/web/order-web-service/yop/pay-result?${newData}`,
    method: "GET"
  };
  return http.request(data);
};

// 记录观看点击次数
export const watchCollect = (params) => {
  const data = {
    url: `/web/course-web-service/public/course-minpro/watch-collect`,
    method: "POST",
    data: params
  };
  return http.request(data);
};
