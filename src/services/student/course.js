import http from "../_utils/request.js";
import qs from "qs";

// 我的课程列表
export const getCourseList = (params) => {
  const newData = qs.stringify(params, { arrayFormat: "repeat" });
  const data = {
    url: `/web/order-web-service/public/wallet/good-list?${newData}`,
    method: "GET",
    data: params
  };
  return http.request(data);
};
// 我的课消记录
export const getCourseRecord = (params) => {
  const newData = qs.stringify(params, { arrayFormat: "repeat" });
  const data = {
    url: `/web/order-web-service/public/deduct/deduct-list?${newData}`,
    method: "GET",
    data: params
  };
  return http.request(data);
};
export const getClassroomList = (params) => {
  const newData = qs.stringify(params, { arrayFormat: "repeat" });
  const data = {
    url: `/web/questionnaire-web-service/getClassroomList?${newData}`,
    method: "GET",
    data: params
  };
  return http.request(data);
};
