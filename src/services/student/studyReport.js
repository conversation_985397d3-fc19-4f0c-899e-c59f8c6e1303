import http from "../_utils/request.js";
import qs from "qs";
// 学习报告列表
export const studentFeedbackList = (params) => {
  const newData = qs.stringify(params, { arrayFormat: "repeat" });
  const data = {
    url: `/web/questionnaire-web-service/studentFeedbackList?${newData}`,
    method: "GET"
  };
  return http.request(data);
};

// 学习报告详情
export const studentFeedbackInfo = (params) => {
  const newData = qs.stringify(params, { arrayFormat: "repeat" });
  const data = {
    url: `/web/questionnaire-web-service/studentFeedbackInfo?${newData}`,
    method: "GET"
  };
  return http.request(data);
};
