import http from "../_utils/request.js";
import qs from "qs";

// 获取校区介绍列表
export const getDepartmentList = (params) => {
  const newData = qs.stringify(params, { arrayFormat: "repeat" });
  const data = {
    url: `/web/questionnaire-web-service/departmentIntroduce/list?${newData}`,
    method: "GET",
    data: params
  };
  return http.request(data);
};

// 获取校区介绍详情
export const getDepartmentDetail = (params) => {
  const newData = qs.stringify(params, { arrayFormat: "repeat" });
  const data = {
    url: `/web/questionnaire-web-service/departmentIntroduce/detail?${newData}`,
    method: "GET",
    data: params
  };
  return http.request(data);
};

// 获取模块列表
export const getModuleList = (params) => {
  const newData = qs.stringify(params, { arrayFormat: "repeat" });
  const data = {
    url: `/web/questionnaire-web-service/departmentIntroduce/module-list?${newData}`,
    method: "GET",
    data: params
  };
  return http.request(data);
};

// 获取模块详情
export const getModuleDetail = (params) => {
  const newData = qs.stringify(params, { arrayFormat: "repeat" });
  const data = {
    url: `/web/questionnaire-web-service/departmentIntroduce/module-detail?${newData}`,
    method: "GET",
    data: params
  };
  return http.request(data);
};
