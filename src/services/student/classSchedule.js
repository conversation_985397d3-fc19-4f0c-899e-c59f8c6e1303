import http from "../_utils/request.js";
import qs from "qs";
// export const getSchedulingList = (params) => {
//   const data = {
//     url: "",
//     method: "POST",
//     data: params
//   };
//   return http.request(data);
// };

// 意向客户-问卷详情
export const getSchedulingList = (params) => {
  const newData = qs.stringify(params, { arrayFormat: "repeat" });
  const data = {
    url: `/web/questionnaire-web-service/getSchedulingList?${newData}`,
    method: "GET",
    data: params
  };
  return http.request(data);
};
