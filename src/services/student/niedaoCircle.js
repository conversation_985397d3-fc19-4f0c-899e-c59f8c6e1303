import http from "../_utils/request.js";
import qs from "qs";

// 获取我的聂道圈
export const momentsList = (params) => {
  const newData = qs.stringify(params, { arrayFormat: "repeat" });
  const data = {
    url: `/web/questionnaire-web-service/moments/student/list?${newData}`,
    method: "GET"
  };
  return http.request(data);
};
// 获取我的聂道圈详情
export const momentsDetail = (params) => {
  const newData = qs.stringify(params, { arrayFormat: "repeat" });
  const data = {
    url: `/web/questionnaire-web-service/moments/student/detail?${newData}`,
    method: "GET"
  };
  return http.request(data);
};
// 获取聂道圈日期
export const momentsDate = (params) => {
  const newData = qs.stringify(params, { arrayFormat: "repeat" });
  const data = {
    url: `/web/questionnaire-web-service/moments/teacher/date?${newData}`,
    method: "GET"
  };
  return http.request(data);
};
// 点赞聂道圈
export const likeMoment = (params) => {
  const data = {
    url: `/web/questionnaire-web-service/moments/like-moment`,
    method: "POST",
    data: params
  };
  return http.request(data);
};
// 浏览朋友圈
export const viewMoment = (params) => {
  const data = {
    url: `/web/questionnaire-web-service/moments/view-moment`,
    method: "POST",
    data: params
  };
  return http.request(data);
};
// 分享聂道圈详情
export const shareMomentsDetail = (params) => {
  const newData = qs.stringify(params, { arrayFormat: "repeat" });
  const data = {
    url: `/web/questionnaire-web-service/moments/student/share-detail?${newData}`,
    method: "GET"
  };
  return http.request(data);
};
