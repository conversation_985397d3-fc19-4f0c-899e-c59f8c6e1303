import http from "../_utils/request.js";
import qs from "qs";

// 获取反馈类型列表
export const getFeedbackCategories = (params) => {
  const newData = qs.stringify(params, { arrayFormat: "repeat" });
  const data = {
    url: `/web/questionnaire-web-service/complaint/categories?${newData}`,
    method: "GET"
  };
  return http.request(data);
};

// 提交反馈
export const createFeedback = (params) => {
  const data = {
    url: `/web/questionnaire-web-service/complaint/create`,
    method: "POST",
    data: params
  };
  return http.request(data);
};

// 获取反馈详情
export const getFeedbackDetail = (params) => {
  const newData = qs.stringify(params, { arrayFormat: "repeat" });
  const data = {
    url: `/web/questionnaire-web-service/complaint/info?${newData}`,
    method: "GET"
  };
  return http.request(data);
};

// 获取用户反馈列表
export const getFeedbackList = (params) => {
  const newData = qs.stringify(params, { arrayFormat: "repeat" });
  const data = {
    url: `/web/questionnaire-web-service/complaint/list?${newData}`,
    method: "GET"
  };
  return http.request(data);
};
