import http from "../_utils/request.js";
import qs from "qs";

// 获取家庭组列表
export const getFamilyList = (params) => {
  const newData = qs.stringify(params, { arrayFormat: "repeat" });
  const data = {
    url: `/web/questionnaire-web-service/getBindFamily?${newData}`,
    method: "GET",
    data: params
  };
  return http.request(data);
};

// 修改绑定角色
export const updateBindRole = (params) => {
  const data = {
    url: `/web/questionnaire-web-service/mainSaveBindRole`,
    method: "POST",
    data: params
  };
  return http.request(data);
};

// 修改绑定角色
export const mainSaveBindRole = (params) => {
  const data = {
    url: `/web/questionnaire-web-service/mainSaveBindRole`,
    method: "POST",
    data: params
  };
  return http.request(data);
};

// 主账号解绑学员
export const malnUumunBinduser = (params) => {
  const data = {
    url: `/web/questionnaire-web-service/mainNumUnBindUser`,
    method: "POST",
    data: params
  };
  return http.request(data);
};

// 分享绑定学员
export const shareBindUser = (params) => {
  const data = {
    url: `/web/questionnaire-web-service/shareBindUser`,
    method: "POST",
    data: params
  };
  return http.request(data);
};
