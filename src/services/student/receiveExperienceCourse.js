import http from "../_utils/request.js";
// import qs from "qs";

// 获取预约校区列表
export const getCampusList = (params) => {
  const data = {
    url: `/web/questionnaire-web-service/appointment/department`,
    method: "GET",
    data: params
  };
  return http.request(data);
};

// 预约新增意向客户
export const addCustomer = (params) => {
  const data = {
    url: `/web/questionnaire-web-service/appointment/receive`,
    method: "POST",
    data: params
  };
  return http.request(data);
};

// 获取当前学员预约状态
export const getStudentAppointmentStatus = (params) => {
  const data = {
    url: `/web/questionnaire-web-service/appointment/info`,
    method: "GET",
    data: params
  };
  return http.request(data);
};
