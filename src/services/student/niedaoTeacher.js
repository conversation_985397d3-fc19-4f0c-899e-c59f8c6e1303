import http from "../_utils/request.js";
import qs from "qs";

// 获取行政区划
export const departmentCity = (params) => {
  const newData = qs.stringify(params, { arrayFormat: "repeat" });
  const data = {
    url: `/web/questionnaire-web-service/nieDaoTeacher/department-city?${newData}`,
    method: "GET"
  };
  return http.request(data);
};

// 获取聂道名师列表
export const niedaoTeacherList = (params) => {
  const newData = qs.stringify(params, { arrayFormat: "repeat" });
  const data = {
    url: `/web/questionnaire-web-service/nieDaoTeacher/list?${newData}`,
    method: "GET"
  };
  return http.request(data);
};

// 获取聂道名师详情
export const niedaoTeacherDetail = (params) => {
  const newData = qs.stringify(params, { arrayFormat: "repeat" });
  const data = {
    url: `/web/questionnaire-web-service/nieDaoTeacher/detail?${newData}`,
    method: "GET"
  };
  return http.request(data);
};
