import http from "../_utils/request.js";

// 获取时间相册标签类型列表
export const getAlbumTypeList = (params) => {
  const data = {
    url: `/api/student-service/public/time-album/get-album-type-list`,
    method: "GET",
    data: params
  };
  return http.request(data);
};
// 创建时间相册
export const createTimeAlbum = (params) => {
  const data = {
    url: `/api/student-service/miniprogram/time-album/create`,
    method: "POST",
    data: params
  };
  return http.request(data);
};

export const batchAddTimeAlbum = (params) => {
  const data = {
    url: `/api/student-service/miniprogram/time-album/batch-add`,
    method: "POST",
    data: params
  };
  return http.request(data);
};
// 删除时间相册
export const deleteTimeAlbum = (params) => {
  const data = {
    url: `/api/student-service/miniprogram/time-album/delete`,
    method: "POST",
    data: params
  };
  return http.request(data);
};
// 获取学员时间相册列表
export const getTimeAlbumList = (params) => {
  const data = {
    url: `/api/student-service/miniprogram/time-album/infoList`,
    method: "GET",
    data: params
  };
  return http.request(data);
};
// 修改时间相册
export const updateTimeAlbum = (params) => {
  const data = {
    url: `/api/student-service/miniprogram/time-album/save`,
    method: "POST",
    data: params
  };
  return http.request(data);
};

// 教师获取时间相册详情
export const getTimeAlbumDetail = (params) => {
  const api = "/api/student-service/public/time-album/info";
  const data = {
    url: api,
    method: "GET",
    data: params
  };
  return http.request(data);
};

// 获取资料库列表
export const getFileList = (params) => {
  const data = {
    url: `/api/organization-service/databank/file-list`,
    method: "GET",
    data: params
  };
  return http.request(data);
};
