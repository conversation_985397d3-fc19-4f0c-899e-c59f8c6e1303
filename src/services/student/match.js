import http from "../_utils/request.js";
import qs from "qs";
/*
 * 获取赛事列表
 */
export const getMatchList = (params) => {
  const newData = qs.stringify(params, { arrayFormat: "repeat" });
  const data = {
    url: `/web/questionnaire-web-service/match/list?${newData}`,
    method: "GET"
  };
  return http.request(data);
};

// 获取赛事报名详情
export const getMatchDetail = (params) => {
  const newData = qs.stringify(params, { arrayFormat: "repeat" });
  const data = {
    url: `/web/questionnaire-web-service/match/detail?${newData}`,
    method: "GET"
  };
  return http.request(data);
};

// 获取我的赛事报名列表
export const getMyMatchList = (params) => {
  const newData = qs.stringify(params, { arrayFormat: "repeat" });
  const data = {
    url: `/web/questionnaire-web-service/match/my/list?${newData}`,
    method: "GET"
  };
  return http.request(data);
};

// 新增报名学员
export const addMatchStudent = (params) => {
  const data = {
    url: `/web/questionnaire-web-service/match/student/add`,
    method: "POST",
    data: params
  };
  return http.request(data);
};

// 修改报名状态
export const updateMatchStudentStatus = (params) => {
  const data = {
    url: `/web/questionnaire-web-service/match/student/update/status`,
    method: "POST",
    data: params
  };
  return http.request(data);
};

// 获取报名学员详情
export const getMatchStudentDetail = (params) => {
  const newData = qs.stringify(params, { arrayFormat: "repeat" });
  const data = {
    url: `/web/questionnaire-web-service/match/student/detail?${newData}`,
    method: "GET",
    data: params
  };
  return http.request(data);
};

// 获取默认学员信息
export const getDefaultStudentInfo = (params) => {
  const newData = qs.stringify(params, { arrayFormat: "repeat" });
  const data = {
    url: `/web/questionnaire-web-service/match/student/info?${newData}`,
    method: "GET"
  };
  return http.request(data);
};

// 赛事绑定学员
export const bindMatchStudent = (params) => {
  const data = {
    url: `/web/questionnaire-web-service/match/bind/student`,
    method: "POST",
    data: params
  };
  return http.request(data);
};

// 绑定判断 - 是否需要绑定赛事
export const isNeedBindMatch = (params) => {
  const newData = qs.stringify(params, { arrayFormat: "repeat" });
  const data = {
    url: `/web/questionnaire-web-service/match/bind/judge?${newData}`,
    method: "GET"
  };
  return http.request(data);
};

// 获取取消报名原因
export const getCancelReasonList = (params) => {
  const data = {
    url: `/web/questionnaire-web-service/match/cancel-reason`,
    method: "GET",
    data: params
  };
  return http.request(data);
};
