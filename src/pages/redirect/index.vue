<template>
  <view class="container"> </view>
</template>

<script>
import updateManager from "@/utils/updateManager";
import {
  getOpenId,
  getSchoolData,
  getPermission,
  miniprogramLogin,
  getLoginLast
} from "@/services/login";
import { bindVisitor } from "@/services/student/home";
import { arouseLogin } from "@/utils/user";
export default {
  name: "redirectIndex",
  methods: {
    // 教师登录完之后的首页
    toTeacherHome() {
      uni.setStorageSync("portType", "TEACHER");
      uni.redirectTo({ url: "/pages/teacher/sort/index" });
    },
    // 教师登录页面
    toTeacherLoginPage() {
      uni.redirectTo({ url: "/pages/teacher/login/index" });
    },
    // 选择身份页面
    toIndex() {
      uni.hideLoading();
      uni.hideToast();
      uni.redirectTo({ url: "/pages/index/index" });
    },
    async goClient(type) {
      // 教师端
      if (type === 2) {
        this.teacherLogin();
      } else if (type === 1) {
        uni.setStorageSync("portType", "STUDENT");
        try {
          const { session, role, open_id } = await arouseLogin();
          if (session) {
            uni.setStorageSync("session", session);
            uni.setStorageSync("role", role);
            uni.setStorageSync("openid", open_id);
          }
          if (role === "default") {
            const bindRes = await bindVisitor({
              open_id,
              UNAUTHORIZED: true
            });
            if (bindRes.code === 0 && bindRes.data && bindRes.data.length > 0) {
              uni.redirectTo({
                url: "/pages/student/studentPage/index?prospective=login"
              });
              return;
            }
          }
          uni.switchTab({ url: "/pages/student/home/<USER>" });
        } catch (error) {
          console.error("初始化数据失败:", error);
          uni.$u.toast("登录失败,请重新登录！");
          uni.switchTab({ url: "/pages/student/login/index" });
        }

        // login({ open_id: uni.getStorageSync("openid") || "" }).then(
        //   async (res) => {
        //     const { code } = res;
        //     if (code === 0) {
        //       const role =
        //         !res.data.is_student && !res.data.is_customer
        //           ? "default"
        //           : res.data.is_student
        //           ? "student"
        //           : "customer";
        //       if (role === "default") {
        //         const bindRes = await bindVisitor({
        //           open_id: res.data.openid,
        //           UNAUTHORIZED: true
        //         });
        //         if (
        //           bindRes.code === 0 &&
        //           bindRes.data &&
        //           bindRes.data.length > 0
        //         ) {
        //           uni.navigateTo({
        //             url: "/pages/student/studentPage/index?prospective=login"
        //           });
        //           return;
        //         }
        //       }
        //       uni.switchTab({ url: "/pages/student/home/<USER>" });
        //     }
        // }
        // );
      } else {
        uni.hideLoading();
        this.toIndex();
      }
    },
    // 通过openid自动进入上次登录的客户类型
    openidLoginType() {
      uni.showLoading({
        title: "加载中..."
      });
      uni.login({
        provider: "weixin",
        onlyAuthorize: true,
        success: (event) => {
          const { code } = event;
          getOpenId({
            code,
            UNAUTHORIZED: true // 不需要token校验
          })
            .then((res) => {
              const { code, data } = res;
              console.log("getOpenId :>> ", res);
              if (res.data.is_fist_login) {
                uni.navigateTo({ url: "/pages/index/index" });
                return;
              }
              if (code === 0 && data) {
                uni.setStorageSync("openid", data.openid);

                getLoginLast({
                  open_id: data.openid,
                  UNAUTHORIZED: true // 不需要token校验
                })
                  .then((res) => {
                    const { code, data } = res;
                    if (code === 0) {
                      // 上次登录类型 identity=1 学员 identity=2 教师
                      this.goClient(data.identity);
                    } else {
                      this.toIndex();
                    }
                  })
                  .catch(() => {
                    this.toIndex();
                  });
              } else {
                this.toIndex();
                this.$refs.uToast.show({
                  type: "error",
                  message: "获取openid失败！"
                });
              }
            })
            .catch(() => {
              this.toIndex();
              this.$refs.uToast.show({
                type: "error",
                message: "获取openid失败！"
              });
            });
        }
      });
    },
    getSchool() {
      return new Promise((resolve, reject) => {
        getSchoolData()
          .then((res) => {
            resolve(res);
          })
          .catch((err) => {
            reject(err);
          });
      });
    },
    getUserPermission() {
      return new Promise((resolve, reject) => {
        getPermission()
          .then((res) => {
            resolve(res);
          })
          .catch((err) => {
            reject(err);
          });
      });
    },
    setSingSchool(school_data) {
      if (school_data) {
        if (school_data[0].child && school_data[0].child.length === 1) {
          uni.setStorageSync("singSchoolData", school_data[0].child[0]);
        }
      }
    },
    teacherLogin() {
      const open_id = uni.getStorageSync("openid") || "";
      if (open_id) {
        // 教师免登录
        miniprogramLogin({
          login_type: "miniprogram",
          open_id,
          UNAUTHORIZED: true // 不需要token校验
        })
          .then((res) => {
            uni.hideToast();
            if (res.code !== 0) {
              this.toTeacherLoginPage();
              return;
            }
            const { token, full_office_post } = res.data;
            uni.setStorageSync("token", token);
            uni.setStorageSync("tokenTime", new Date().getTime());
            uni.setStorageSync("user", res.data);
            const { getUserPermission, getSchool } = this;
            Promise.all([getUserPermission(), getSchool()])
              .then((result) => {
                uni.hideToast();
                uni.setStorageSync("permission", result[0]);
                uni.setStorageSync("schoolData", result[1].school_data);
                // const checkedSchool = uni.getStorageSync("checkedSchool");
                const arr = [];
                // if (!checkedSchool) {
                result[1].school_data.forEach((item1) => {
                  item1.child.forEach((item2) => {
                    const { id, name } = item2;
                    arr.push({
                      id,
                      name
                    });
                  });
                });
                // 多校区时，默认设置第一个
                const { department_id, department_name, department_type } =
                  full_office_post;
                const defaultSchool = {
                  id: department_id,
                  name: department_name
                };
                console.log(
                  result[1].school_data[0].child[0],
                  department_type,
                  "result[1].school_data-----redirect"
                );
                const { id, name } = result[1].school_data[0].child[0];
                const firstSchool = { id, name };
                uni.setStorageSync(
                  "checkedSchool",
                  department_type !== "school" ? [firstSchool] : [defaultSchool]
                );
                // 设置单校区数据
                this.setSingSchool(result[1].school_data);
                // }
                setTimeout(() => {
                  this.toTeacherHome();
                }, 1000);
              })
              .catch(() => {
                uni.hideToast();
                uni.showToast("跳转失败！");
                uni.setStorageSync("permission", []);
                this.toTeacherHome();
              });
          })
          .catch(() => {
            console.log("6666 :>> ", 666);
            uni.hideLoading();
            uni.hideToast();
            this.toTeacherLoginPage();
          });
      }
    }
  },
  onLoad: function () {
    // this.openidLoginType();
  },
  onShow: function () {
    this.openidLoginType();
    updateManager();
    console.log("App Show");
  },
  onHide: function () {
    console.log("App Hide");
  }
};
</script>

<style scoped lang="scss">
.container {
  height: 100vh;
  width: 100vw;
}
</style>
