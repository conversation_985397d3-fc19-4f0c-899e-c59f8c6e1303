export const genders = {
  male: "https://tg-prod.oss-cn-beijing.aliyuncs.com/4dd7c4d9-f085-45f8-aca5-ade085484a31.png",
  female:
    "https://tg-prod.oss-cn-beijing.aliyuncs.com/bc3eed46-9cf2-48f8-9a09-70ef7a82d666.png",
  default:
    "https://tg-prod.oss-cn-beijing.aliyuncs.com/a9a09be0-3462-4249-80ac-e7bc515c67bd.png"
};
export const genders_list = {
  1: "https://tg-prod.oss-cn-beijing.aliyuncs.com/707bbace-3dbe-47e0-8523-67eef4cbf618.png",
  2: "https://tg-prod.oss-cn-beijing.aliyuncs.com/28332296-bfee-4554-854c-090687d61de3.png",
  3: "https://tg-prod.oss-cn-beijing.aliyuncs.com/6017390e-0ffc-4c7f-9e82-103f10d051e4.png"
};
export const btnImages = {
  cancel:
    "https://tg-prod.oss-cn-beijing.aliyuncs.com/a981ee6d-c8a6-4b63-8550-388b3b6b6931.png",
  save: "https://tg-prod.oss-cn-beijing.aliyuncs.com/88a163f2-c9b5-43ad-a312-eb9197149a87.png"
};

export const menus = [
  // {
  //   name: "优惠券",
  //   role: "student",
  //   value: "coupon",
  //   url: "/pages/student/subpages/coupon/index",
  //   icon: "https://tg-prod.oss-cn-beijing.aliyuncs.com/070b99ce-b39f-41e8-affa-76d0efb5c7eb.png"
  // },
  // {
  //   name: "问卷",
  //   value: "questionnaire",
  //   role: "student",
  //   url: "/pages/student/questionnaireSurvey/index?role=student",
  //   icon: "https://tg-prod.oss-cn-beijing.aliyuncs.com/1a31ad0e-d17d-4b31-b2aa-87f58d13890d.png"
  // },
  // {
  //   name: "评价",
  //   role: "student",
  //   url: "",
  //   icon: "https://tg-prod.oss-cn-beijing.aliyuncs.com/ae04cdd9-4d11-4362-b1ed-fd5a45da73e2.png"
  // },
  {
    name: "我的订单",
    value: "myOrder",
    url: "/pages/student/subpages/order/index",
    icon: "https://tg-prod.oss-cn-beijing.aliyuncs.com/6d2c201e-5d09-45bc-804c-1da40801cb79.png",
    noRout: false,
    isShowDot: false
  },
  {
    name: "电子合同",
    value: "e-sign",
    url: "/pages/student/subpages/e-sign/index",
    icon: "https://tg-prod.oss-cn-beijing.aliyuncs.com/2f09bd4f-60b6-4445-bf4c-a81a0df22ff7.png",
    noRout: false,
    isShowDot: false
  },
  {
    name: "时光相册",
    value: "timeAlbum",
    url: "/pages/student/subpages/timeAlbum/index",
    icon: "https://tg-prod.oss-cn-beijing.aliyuncs.com/8ea45ada-4e50-4097-857b-d725b0152125.png",
    noRout: true,
    isShowDot: false
  },
  {
    name: "赛事报名",
    value: "report",
    url: "/pages/student/subpages/competition/index",
    icon: "https://tg-prod.oss-cn-beijing.aliyuncs.com/ccce53eb-05a4-4154-bbb6-3ff5990243ad.png",
    noRout: false,
    isShowDot: false
  }
  // {
  //   name: "意向度调研",
  //   value: "intention",
  //   role: "customer",
  //   url: "/pages/student/questionnaireSurvey/index?role=customer",
  //   icon: "https://tg-prod.oss-cn-beijing.aliyuncs.com/1a31ad0e-d17d-4b31-b2aa-87f58d13890d.png"
  // }
];
export const menus_bot = [
  // 家庭组
  {
    name: "家庭组",
    value: "family_group",
    role: "student",
    url: "/pages/student/subpages/family/index",
    icon: "https://tg-prod.oss-cn-beijing.aliyuncs.com/4a49dbaf-094e-4752-99c6-4d1fdd3137e5.png",
    noRout: true
  },
  // 优惠券
  {
    name: "优惠券",
    value: "coupon",
    role: "student",
    url: "/pages/student/subpages/coupon/index",
    icon: "https://tg-prod.oss-cn-beijing.aliyuncs.com/53bf76ec-482c-41d5-9017-00587be9b8ce.png",
    noRout: true
  },
  // 调查问卷
  {
    name: "调查问卷",
    value: "questionnaire",
    role: "student",
    url: "/pages/student/questionnaireSurvey/index",
    icon: "https://tg-prod.oss-cn-beijing.aliyuncs.com/86523e4d-40ca-429d-95f8-4bbb61392097.png",
    noRout: true
  },
  // 反馈与投诉
  {
    name: "反馈与投诉",
    value: "feedback",
    role: "student",
    url: "/pages/student/subpages/feedback/index",
    icon: "https://tg-prod.oss-cn-beijing.aliyuncs.com/5454daec-2c1e-4c7d-a7f8-9ca074d6aa55.png",
    noRout: false
  },
  // 消息通知设置
  {
    name: "消息通知设置",
    value: "message_setting",
    role: "student",
    url: "/pages/student/subpages/messageConfig/index",
    icon: "https://tg-prod.oss-cn-beijing.aliyuncs.com/5f30e92d-a2dd-46ab-8f6f-e42dc9146599.png",
    noRout: false
  },
  // APP下载
  {
    name: "APP下载",
    value: "app_download",
    role: "student",
    url: "",
    icon: "https://tg-prod.oss-cn-beijing.aliyuncs.com/cd43b209-b288-4412-a891-f2a3e19a80dd.png",
    noRout: false
  },
  // 清除缓存
  {
    name: "清除缓存",
    value: "clear_cache",
    role: "student",
    url: "",
    icon: "https://tg-prod.oss-cn-beijing.aliyuncs.com/d239e671-549d-4633-b12a-b0372eb34729.png",
    noRout: false
  }
];
export const rowIds = {
  default: "student_id",
  student: "student_id",
  customer: "customer_id"
};

export const studentType = {
  1: "在读",
  2: "休学",
  3: "试听",
  4: "临时",
  0: "意向"
};
export const studentTag = {
  1: "yel",
  2: "bul",
  3: "purp",
  4: "gre",
  0: "pink"
};
