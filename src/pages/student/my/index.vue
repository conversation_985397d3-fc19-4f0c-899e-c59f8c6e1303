<!-- eslint-disable no-tabs -->
<template>
  <div class="my-page">
    <div class="header">
      <div class="header-top">
        <div class="student-info" @click="openProfile">
          <div class="avatar">
            <u-image
              width="120rpx"
              height="120rpx"
              mode="aspectFit"
              :showMenuByLongpress="false"
              :src="avatar"
            ></u-image>
          </div>
          <div>
            <template v-if="haveStudent">
              <div style="display: flex; align-items: baseline">
                <div class="student-name">
                  {{ curCheckedStudent.student_name || ""
                  }}{{
                    curCheckedStudent.role_chn !== "学生本人"
                      ? curCheckedStudent.role_chn || ""
                      : ""
                  }}
                  <!-- <img
                  src="https://tg-prod.oss-cn-beijing.aliyuncs.com/d443b5de-6216-4a26-90a3-161cbdd86ea3.png"
                  v-if="studentList.length > 1"
                  alt=""
                /> -->
                </div>
                <u-icon
                  size="26rpx"
                  color="#fff"
                  class="arrow"
                  name="arrow-right"
                  bold
                ></u-icon>
              </div>

              <div class="school-name">
                {{ curCheckedStudent.department_name || "" }}
              </div>
            </template>
            <template v-else>
              <div class="tips">
                <div>未绑定学员</div>
                <u-icon
                  size="26rpx"
                  color="#fff"
                  class="arrow"
                  name="arrow-right"
                  bold
                ></u-icon>
              </div>
            </template>
          </div>
        </div>
        <div class="operation" @click="openBindStudentModalRole">
          <!-- <u-icon
          name="plus-people-fill"
          v-if="['default', 'student'].includes(role)"
          @click="open"
          color="#ffffff"
        ></u-icon> -->
          切换/绑定
        </div>
      </div>
      <div class="header-bottom">
        <div class="menu-list">
          <div
            class="menu-item"
            v-for="item in menuList"
            @click="handleMenuClick(item)"
            :key="item.url"
          >
            <div class="dot" v-if="item.isShowDot"></div>
            <div class="menu-icon">
              <u-icon :name="item.icon" alt="" size="94rpx" height="94rpx" />
            </div>
            <p class="menu-name">{{ item.name }}</p>
          </div>
        </div>
      </div>
    </div>
    <div class="main">
      <div class="main-wrap">
        <div class="cell-title">我的服务</div>
        <div class="cell-list">
          <div
            class="cell"
            v-for="(item, index) in menus_bot"
            :key="index"
            @click="handleMenuClick(item)"
          >
            <div class="cell-left">
              <u-icon :name="item.icon" alt="" size="44rpx" />
              <span class="cell-name">{{ item.name }}</span>
            </div>
            <div class="cell-right">
              <u-icon
                name="https://tg-prod.oss-cn-beijing.aliyuncs.com/3c638734-affc-4fa0-971e-c468dbb737a1.webp"
                color="#999999"
                width="30rpx"
                height="30rpx"
              ></u-icon>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="logout" @click="logout">
      <div class="logout-btn">退出登录</div>
    </div>
    <!-- start 切换学院 -->
    <u-popup
      :show="isShowBindStudentModal"
      bgColor="transparent"
      mode="center"
      @close="handleClose"
      @open="handleOpen"
    >
      <u-toast ref="uToast2" style="z-index: 999999 !important"></u-toast>
      <div class="bind-student-wrap">
        <div class="close" @click="handleClose">
          <img
            src="https://tg-prod.oss-cn-beijing.aliyuncs.com/79860fd9-cb32-41f4-9cd2-31e248dcbd4b.png"
            alt=""
          />
        </div>
        <div class="modal-title">
          {{ studentList.length === 1 ? "我的信息" : "切换学员" }}
        </div>
        <div class="student-list">
          <div
            v-for="item in studentList"
            :key="item[rowIdField]"
            :class="[
              'student-item',
              curCheckedStudent[rowIdField] === item[rowIdField]
                ? 'checked'
                : ''
            ]"
            @click="handleBindChange(item)"
          >
            <div class="avatar">
              <u-avatar
                :src="genders_list[item.choose_head] || genders_list[1]"
              ></u-avatar>
            </div>
            <div class="student-info">
              <div class="student-info-item">
                <div class="label">姓名</div>
                <u-line class="line" direction="col"></u-line>
                <div class="value">{{ item.student_name }}</div>
              </div>
              <div class="student-info-item" v-if="item.student_number">
                <div class="label">学号</div>
                <u-line class="line" direction="col"></u-line>
                <div class="value">{{ item.student_number }}</div>
              </div>
              <div class="student-info-item">
                <div class="label">校区</div>
                <u-line class="line" direction="col"></u-line>
                <div class="value">{{ item.department_name }}</div>
              </div>
            </div>
            <div class="radio">
              <div class="is-radio-checked"></div>
            </div>
          </div>
        </div>
      </div>
    </u-popup>
    <!-- end 切换学院 -->
    <!-- start 自定义弹窗（暂未被用到） -->
    <u-popup :show="isShowBindPhoneModal" bgColor="transparent" mode="center">
      <div class="modal-wrap">
        <div class="modal-header">
          <div class="header-wrap">
            <div class="dot left"></div>
            <div class="dot right"></div>
          </div>
          <div class="header-content">提示</div>
        </div>
        <div class="modal-content">
          <div class="text">{{ tipsMsg }}</div>
          <div class="modal-footer">
            <div class="btns">
              <div class="btn" @click="cancel">
                <img :src="btnImages.cancel" alt="" />
              </div>
              <div class="btn" @click="submit">
                <img :src="btnImages.save" alt="" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </u-popup>
    <!-- end 自定义弹窗（暂未被用到） -->
    <!-- start 绑定学员 -->
    <u-popup
      :show="showAddStudent"
      :closeOnClickOverlay="false"
      bgColor="transparent"
      mode="center"
      @close="cancel"
      @open="open"
    >
      <u-toast ref="uToast" style="z-index: 999999 !important"></u-toast>
      <div class="modal-wrap">
        <div class="modal-header">
          <div class="header-wrap">
            <div class="dot left"></div>
            <div class="dot right"></div>
          </div>
          <div class="header-content">添加学员</div>
        </div>
        <div class="modal-content">
          <u--form
            labelPosition="left"
            :model="formModel"
            :rules="rules"
            ref="uForm"
          >
            <u-form-item prop="mobile" ref="item1">
              <u--input
                v-model="formModel.mobile"
                type="number"
                maxlength="11"
                placeholder="请输入手机号"
              ></u--input>
            </u-form-item>
            <u-form-item prop="code" ref="item1">
              <div style="display: flex">
                <u--input
                  v-model="formModel.code"
                  maxlength="6"
                  type="number"
                  placeholder="请输入验证码"
                ></u--input>
                <view class="code-block">
                  <u-code
                    ref="uCode2"
                    @change="codeChange2"
                    keep-running
                    change-text="Xs后重发"
                    seconds="60"
                    start-text="获取验证码"
                  ></u-code>
                  <text
                    @tap="getCode2"
                    :text="tips2"
                    :style="{ color: codeTextColor }"
                    class="u-page__code-text"
                    >{{ tips2 }}</text
                  >
                </view>
              </div>
            </u-form-item>
            <u-form-item>
              <div class="btns">
                <div class="btn" @click="cancel">
                  <img :src="btnImages.cancel" alt="" />
                </div>
                <div class="btn" @click="submit">
                  <img :src="btnImages.save" alt="" />
                </div>
              </div>
            </u-form-item>
          </u--form>
        </div>
      </div>
    </u-popup>
    <!-- end 绑定学员 -->
    <!-- <TabBar ref="tabbar" :key="tabBarKey" @changeDot="handleChangeDot"></TabBar> -->
    <bind-student-popup
      ref="bindStudentPopup"
      v-model="showBindStudent"
      @confirm="handleBindConfirm"
    ></bind-student-popup>
    <confirm
      :visible="confirmVisible"
      :content="confirmContent"
      :confirm-text="confirmButtonText"
      :cancel-text="cancelButtonText"
      @confirm="handleConfirm"
      @close="handleCancel"
    ></confirm>
  </div>
</template>

<script>
import Confirm from "@/components/confirm/index.vue";
// import TabBar from "../components/TabBar.vue";
import {
  sendSms,
  getStudentDetail,
  getIntentDetail,
  getCustomerDetail,
  cancelElectronContractMessage
} from "@/services/student/my";
import { getBindStudent } from "@/services/student/home";
import {
  genders,
  btnImages,
  menus,
  rowIds,
  menus_bot,
  genders_list
} from "./config/index";
import BindStudentPopup from "../home/<USER>/BindStudentPopup.vue";
import {
  arouseLogin,
  getDataByRole,
  processUserList,
  getCheckedStudentInfo,
  updateCheckedUserInfo
} from "@/utils/user";

export default {
  name: "studentMineIndex",
  components: { BindStudentPopup, Confirm },
  data() {
    return {
      confirmVisible: false,
      confirmContent: "此操作将重启小程序，确定要清除吗？",
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      tabBarKey: 0,
      showAddStudent: false,
      tips2: "",
      role: "",
      isShowDot: "",
      show: false,
      studentList: [],
      session: {},
      tips: "",
      value: "",
      haveStudent: false,
      genders,
      btnImages,
      formModel: {
        mobile: "",
        code: ""
      },
      rowIdField: "",
      rowIds,
      codeTextColor: "#00BDFF",
      curCheckedStudent: {},
      tipsMsg: "",
      isShowNotify: false,
      isShowBindPhoneModal: false,
      isShowBindStudentModal: false,
      menus,
      rules: {
        mobile: [
          { required: true, message: "请输入手机号", trigger: "blur" },
          {
            validator: (rule, value, callback) => {
              return uni.$u.test.mobile(value);
            },
            message: "手机号码不正确",
            // 触发器可以同时用blur和change
            trigger: ["change", "blur"]
          }
        ],
        code: {
          required: true,
          message: "请填写六位数的验证码",
          trigger: ["blur", "change"]
        }
      },
      menus_bot,
      genders_list,
      showBindStudent: false,
      curCheckedInfo: {}
    };
  },
  computed: {
    avatar() {
      return (
        genders_list[this.curCheckedStudent.choose_head] || genders_list[1]
      );
    },
    menuList() {
      return this.menus;
    }
  },
  methods: {
    async initData() {
      try {
        // 登录获取用户信息
        const { session, role, openId } = await arouseLogin();
        this.session = session;
        this.role = role;
        this.rowIdField = this.rowIds[role];

        // 获取选中的学生/客户信息
        const result = await getCheckedStudentInfo(
          openId,
          role,
          session.operation_id
        );
        if (result.success) {
          this.curCheckedStudent = result.curStudentInfo || {};
          // this.$refs.tabbar.setMenuList();
        }

        // 获取用户列表并处理
        await getDataByRole(this, role, openId, (code, data, message) => {
          processUserList(code, data, message, this, openId);
        });
        // console.log(this.curCheckedStudent);
      } catch (error) {
        console.error("初始化数据失败:", error);
        // uni.$u.toast("初始化数据失败,请重新打开小程序");
      }
    },
    async handleMenuClick({ url, noRout, value }) {
      // 如果是游客的话 一些菜单不允许查看需要绑定学员
      if (!uni.getStorageSync("curStudentInfo")) {
        if (noRout) {
          this.showBindStudent = true;
          return;
        }
      }
      if (url.indexOf("/pages/student/subpages/e-sign/index") !== -1) {
        const student_status =
          this.session.role === "student"
            ? 3
            : this.session.role === "customer"
            ? 2
            : 1;
        const student_id =
          this.session.role === "student"
            ? this.curCheckedStudent.student_id
            : this.session.role === "customer"
            ? this.curCheckedStudent.customer_id
            : this.session.open_id;
        cancelElectronContractMessage({
          student_id,
          type: "contract",
          student_status
        });
      }
      if (value === "clear_cache") {
        this.confirmVisible = true;
        return;
      }
      if (value === "timeAlbum") {
        uni.navigateTo({
          url: "/pages/student/subpages/timeAlbum/index"
        });
        return;
      }
      uni.navigateTo({ url });
    },
    // 清除缓存,重启小程序
    handleConfirm() {
      uni.clearStorageSync();
      this.handleCancel();
      uni.showLoading({ title: "清除缓存中..." });
      setTimeout(() => {
        uni.reLaunch({ url: "/pages/index/index" });
      }, 1000);
    },
    handleCancel() {
      this.confirmVisible = false;
    },
    checkPhoneNum(rule, value, callback) {
      if (uni.$u.test.mobile(value)) {
        callback();
      } else {
        callback(new Error("手机号码格式不正确"));
      }
      // return uni.$u.test.mobile(value);
    },
    codeChange2(text) {
      if (text === "重新获取") {
        this.codeTextColor = "#00BDFF";
      }
      this.tips2 = text;
    },
    getCode2() {
      if (this.$refs.uCode2.canGetCode) {
        this.$refs.uForm.validateField("mobile", async (err) => {
          if (err?.length === 0) {
            // 模拟向后端请求验证码
            uni.showLoading({
              title: "正在获取验证码"
            });
            const params = {
              template_code: "SMS_465317333",
              sign_name: "聂卫平围棋道场",
              mobile: this.formModel.mobile
            };
            const { code, message } = await sendSms(params);
            if (code === 0) {
              uni.hideLoading();
              uni.$u.toast("验证码已发送");
              this.codeTextColor = "#BFC1C5";
              // 通知验证码组件内部开始倒计时
              this.$refs.uCode2.start();
            } else {
              uni.hideLoading();
              this.$refs.uToast.show({ type: "error", message });
            }
          }
        });
      } else {
        uni.$u.toast("倒计时结束后再发送");
      }
    },
    async handleBindChange(n) {
      const curStudentInfo = this.studentList.find(
        (i) => i[this.rowIdField] === n[this.rowIdField]
      );

      this.curCheckedStudent = curStudentInfo;
      // 更新选中的用户信息
      await updateCheckedUserInfo(
        this.role,
        n[this.rowIdField],
        this.session.open_id
      );

      uni.setStorageSync("curStudentInfo", curStudentInfo);
      this.handleClose();
      // this.$refs.tabbar.setMenuList();
    },
    submit() {
      this.$refs.uForm
        .validate()
        .then((res) => {
          this.handleBindStudent();
        })
        .catch((errors) => {
          // uni.$u.toast("请填写手机号以及验证码");
        });
    },
    cancel() {
      this.formModel = {
        mobile: "",
        code: ""
      };
      const validateList = ["mobile", "code"];
      setTimeout(() => {
        this.$refs.uForm.clearValidate(validateList);
      }, 10);
      this.showAddStudent = false;
    },
    async handleBindConfirm() {
      try {
        const formData = await this.$refs.bindStudentPopup.validate();
        this.handleBindStudent(formData);
      } catch (error) {
        console.error("表单验证失败:", error);
      }
    },
    async handleBindStudent(formData) {
      try {
        uni.showLoading({ title: "查找中..." });
        const { code, data, message } = await getBindStudent(formData);
        uni.hideLoading();

        if (code === 0) {
          uni.navigateTo({
            url:
              "/pages/student/studentPage/index?prospective=" +
              JSON.stringify(data)
          });
          this.$refs.bindStudentPopup.handleClose();
        } else {
          uni.showToast({
            title: message,
            icon: "none",
            duration: 2000
          });
        }
      } catch (error) {
        uni.hideLoading();
        console.error("绑定学生失败:", error);
      }
    },
    handleClose() {
      this.isShowBindStudentModal = false;
    },
    handleOpen() {
      this.isShowBindStudentModal = true;
    },
    logout() {
      uni.removeStorageSync("curStudentInfo");
      uni.removeStorageSync("session");
      uni.redirectTo({ url: "/pages/index/index" });
    },
    handleChangeDot(val, myHas) {
      this.menuList[1].isShowDot = myHas.contract.has_new || false;
    },
    openBindStudentModalRole() {
      uni.navigateTo({ url: "/pages/student/subpages/bindStudent/index" });
    },
    openProfile() {
      uni.navigateTo({ url: "/pages/student/subpages/profile/index" });
    },
    async onPullDownRefresh() {
      await this.initData();
      uni.stopPullDownRefresh();
    },
    // 获取当前学员信息
    async getStudentDetaill() {
      let res;
      if (this.session.role === "student") {
        res = await getStudentDetail({
          student_id: this.curCheckedInfo.student_id
        });
      } else if (this.session.role === "customer") {
        res = await getIntentDetail({
          customer_id: this.curCheckedInfo.customer_id
        });
      }
      // console.log(res, this.session);
      // uni.setStorageSync("curStudentInfo", res.data || "");
      this.curCheckedStudent = res.data || "";
    },
    handleOnLoad() {
      // this.initData();
      this.session = uni.getStorageSync("session");
      this.curCheckedInfo = uni.getStorageSync("curStudentInfo");
      if (this.session.role !== "default") {
        this.haveStudent = true;
        // this.curCheckedStudent = uni.getStorageSync("curStudentInfo");
        this.getStudentDetaill();
      } else {
        // if (this.session.role === "default") {
        this.haveStudent = true;
        getCustomerDetail({
          open_id: this.student_id
        })
          .then((res) => {
            // console.log(res);
            this.curCheckedStudent = {
              student_name: res.data.student_name,
              student_mobile: res.data.student_mobile,
              choose_head: res.data.choose_head,
              department_name: ""
            };
          })
          .catch((err) => {
            this.haveStudent = false;
            console.log(err);
          });
        // }
        this.initData();
      }
    }
  },
  watch: {},

  // 页面周期函数--监听页面加载
  onLoad() {
    // this.initData();
  },
  // 页面周期函数--监听页面初次渲染完成
  onReady() {
    // 如果需要兼容微信小程序，并且校验规则中含有方法等，只能通过setRules方法设置规则
    this.$refs.uForm.setRules(this.rules);
  },
  onLaunch: function () {
    uni.hideTabBar();
  },
  // 页面周期函数--监听页面显示(not-nvue)
  mounted() {
    if (uni.getStorageSync("fromDTB")) {
      uni.setStorageSync("portType", "STUDENT");
      uni.removeStorageSync("curStudentInfo");
      uni.removeStorageSync("fromDTB");
    }
    // this.initData();
    this.session = uni.getStorageSync("session");
    this.curCheckedInfo = uni.getStorageSync("curStudentInfo");
    if (this.session.role !== "default") {
      this.haveStudent = true;
      // this.curCheckedStudent = uni.getStorageSync("curStudentInfo");
      this.getStudentDetaill();
    } else {
      // if (this.session.role === "default") {
      this.haveStudent = true;
      getCustomerDetail({
        open_id: this.student_id
      })
        .then((res) => {
          // console.log(res);
          this.curCheckedStudent = {
            student_name: res.data.student_name,
            student_mobile: res.data.student_mobile,
            choose_head: res.data.choose_head,
            department_name: ""
          };
        })
        .catch((err) => {
          this.haveStudent = false;
          console.log(err);
        });
      // }
      this.initData();
    }
    // this.tabBarKey++;
    // this.$refs.tabbar.setMenuList();
  },
  // 页面周期函数--监听页面隐藏
  onHide() {},
  // 页面周期函数--监听页面卸载
  onUnload() {}
  // 页面处理函数--监听用户下拉动作
  // onPullDownRefresh() { uni.stopPullDownRefresh(); },
  // 页面处理函数--监听用户上拉触底
  // onReachBottom() {},
  // 页面处理函数--监听页面滚动(not-nvue)
  // onPageScroll(event) {},
  // 页面处理函数--用户点击右上角分享
  // onShareAppMessage(options) {},
};
</script>

<style lang="scss" scoped>
::v-deep .u-input {
  background: #f6f8fb !important;
  border-radius: 33px !important;
  border: none !important;
}

.my-page {
  width: 100%;
  // min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f4f8fc;
  overflow-y: auto;

  .header {
    position: relative;
    background-image: url(https://tg-prod.oss-cn-beijing.aliyuncs.com/e34c130e-ea63-495a-8608-4afbfd401257.webp);
    background-size: 100% 100%;
    padding-top: 160rpx;

    .header-top {
      display: flex;
      justify-content: space-between;
      padding: 12rpx 32rpx 29rpx 32rpx;

      .student-info {
        display: flex;

        ::v-deep .u-avatar {
          img {
            width: 112rpx;
            height: 112rpx;
          }
        }

        .avatar {
          margin-right: 18rpx;
          // border: 3px solid #fff;
          border-radius: 50%;
          // img {
          //   width: 112rpx;
          //   height: 112rpx;
          // }
        }

        .student-name {
          color: #fff;
          font-size: 32rpx;
          font-style: normal;
          font-weight: 500;
          line-height: normal;
          margin-top: 25rpx;
          margin-bottom: 4rpx;
          margin-right: 10rpx;

          img {
            width: 30rpx;
            height: 30rpx;
            margin-left: 10rpx;
          }
        }

        .school-name {
          color: #fff;
          font-size: 26rpx;
          font-style: normal;
          font-weight: 400;
          line-height: normal;
        }

        .tips {
          display: flex;
          align-items: center;
          font-size: 32rpx;
          font-weight: 500;
          height: 100%;
          color: #fff;
        }
      }

      .operation {
        padding: 10rpx 20rpx;
        width: 148rpx;
        height: 54rpx;
        border-radius: 43rpx;
        background: #fff;
        color: #fb0;
        font-size: 24rpx;
        font-style: normal;
        font-weight: 500;
        line-height: 54rpx;
        margin-top: 45rpx;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }

    .header-bottom {
      width: 686rpx;
      height: 192rpx;
      border-radius: 24rpx;
      background: #fff;
      box-shadow: 0px 0px 50px 0px rgba(124, 143, 166, 0.1);
      margin: 0 auto;

      .menu-list {
        display: flex;
        height: 100%;
        justify-content: center;
        padding: 30rpx 39rpx;

        .menu-item {
          text-align: center;
          // margin-right: 40rpx;
          position: relative;
          // width: 33.33%;
          margin-right: 70rpx;

          // margin-bottom: 40rpx;
          &:last-child {
            margin-right: 0;
          }

          .dot {
            position: absolute;
            top: 10rpx;
            right: -10rpx;
            width: 16rpx;
            height: 16rpx;
            border-radius: 50%;
            background: #f56c6c;
            z-index: 999;
          }

          .menu-icon {
            width: 94rpx;
            height: 94rpx;
            margin: auto;

            image {
              width: 100%;
              height: 100%;
            }
          }

          .menu-name {
            color: #333;
            font-size: 24rpx;
            font-style: normal;
            font-weight: 500;
            line-height: normal;
            line-height: 34rpx;
          }
        }
      }
    }
  }

  .main {
    height: 100%;

    .main-wrap {
      border-top-right-radius: 54rpx;
      border-top-left-radius: 54rpx;
      position: relative;
      // height: calc(100% - 149rpx);
      padding: 0 37rpx 0 33rpx;

      .cell-title {
        color: #333;
        font-size: 32rpx;
        font-style: normal;
        font-weight: 500;
        line-height: normal;
        padding: 35rpx 0 24rpx 0rpx;
      }

      .cell-list {
        // margin-top: 40rpx;
        border-radius: 24rpx;
        background: #fff;
        box-shadow: 0px 0px 50px 0px rgba(124, 143, 166, 0.1);

        .cell {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 31rpx 24rpx;
          width: 100%;
          height: 100rpx;

          .cell-left {
            display: flex;

            // img {
            //   width: 42rpx;
            //   height: 42rpx;
            // }

            .cell-name {
              color: #333;
              font-size: 28rpx;
              font-style: normal;
              font-weight: 400;
              line-height: 42rpx;
              /* 150% */
              margin-left: 23rpx;
            }
          }

          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
  }

  .logout {
    margin-top: 30rpx;
    padding: 0 37rpx 0 33rpx;

    .logout-btn {
      width: 100%;
      height: 92rpx;
      line-height: 92rpx;
      border-radius: 80px;
      border: 1px solid #fb0;
      background: #fff;
      color: #fb0;
      box-shadow: 0px 4px 20px 0px rgba(254, 197, 36, 0.12);
      color: #fb0;
      text-align: center;
      font-size: 32rpx;
      font-style: normal;
      font-weight: 500;
      line-height: 40rpx;
      /* 125% */
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 36rpx;
    }
  }

  .bind-student-wrap {
    width: 576rpx;
    height: 760rpx;
    border-radius: 55rpx;
    background: #fff;
    box-shadow: 0px -4px 15px 0px #ccfaff inset,
      0px 6px 20px 0px rgba(0, 0, 0, 0.1);
    position: relative;
    padding: 36rpx 28rpx;
    display: flex;
    flex-direction: column;

    .close {
      width: 56rpx;
      height: 56rpx;
      position: absolute;
      top: 0;
      right: -64rpx;

      img {
        width: 100%;
        height: 100%;
      }
    }

    .modal-title {
      color: #333;
      text-align: center;
      font-size: 28rpx;
      font-weight: 600;
      padding-bottom: 24rpx;
    }

    .student-list {
      height: 100%;
      overflow: auto;

      .student-item {
        width: 520rpx;
        height: 240rpx;
        border-radius: 32rpx;
        background: #e8edf6;
        padding: 40rpx 21rpx;
        display: flex;
        align-items: center;
        margin-bottom: 16rpx;

        &:last-child {
          margin-bottom: 0;
        }

        &.checked {
          background: #d2f6ff;

          .is-radio-checked {
            width: 100%;
            height: 100%;
            background: #0ec1ff;
            border-radius: 50%;
          }
        }

        .avatar {
          border-radius: 50%;
          border: 2px solid #fff;
          box-shadow: 0px 7.2px 12px 0px rgba(0, 124, 199, 0.1);

          img {
            width: 100rpx;
            height: 100rpx;
          }
        }

        .student-info {
          margin: 0 28rpx 0 16rpx;

          .student-info-item {
            width: 298rpx;
            height: 48rpx;
            margin-bottom: 8rpx;
            border-radius: 30rpx;
            background: #fff;
            display: flex;
            align-items: center;
            padding: 12rpx 20rpx;

            &:last-child {
              margin-bottom: 0;
            }

            ::v-deep .u-line {
              margin: 0 15rpx !important;
            }

            .label,
            .value {
              color: #333;
              font-size: 20rpx;
              font-weight: 600;
            }
          }
        }

        .radio {
          width: 35rpx;
          height: 35rpx;
          border-radius: 50%;
          border: 4rpx solid #0ec1ff;
          padding: 5rpx;
        }
      }
    }

    .footer {
      padding: 24rpx 70rpx 0 70rpx;

      .btn {
        width: 100%;
        padding: 14rpx 0;
        color: #fff;
        border-radius: 60rpx;
        background: linear-gradient(180deg, #2dd5ff 6.06%, #0cf 93.83%);
        box-shadow: 0px 8px 8px 0px #96e6ff inset,
          0px -8px 8px 0px #00bdff inset, 0px 4px 8px 0px rgba(0, 56, 79, 0.1);
        text-align: center;
        font-size: 28rpx;
        font-weight: 500;
      }
    }
  }

  .add-student {
    display: flex;
    align-items: center;

    span {
      margin-left: 15rpx;
    }
  }

  .item {
    font-size: 32rpx;
    font-weight: 500;
    margin-left: 88rpx;
    height: 56rpx;
    line-height: 56rpx;

    .label {
      color: #ffbf0d;
      display: inline-block;
      vertical-align: middle;
    }

    .content {
      color: #475669;
      display: inline-block;
      vertical-align: middle;
    }
  }
}

.modal-wrap {
  width: 626rpx;
  min-height: 308rpx;
  border-radius: 61.44rpx;
  background: linear-gradient(180deg, #74beff 0%, #39a1ff 100%);
  box-shadow: 0px -3.072px 4.915px 0px #2e7dff inset,
    0px 3.072px 6.144px 0px rgba(151, 227, 255, 0.65) inset,
    0px 3.686px 18.432px 0px rgba(0, 0, 0, 0.1);
  padding: 12rpx 13rpx;
  position: relative;

  .modal-header {
    .header-wrap {
      position: absolute;
      top: -22px;
      left: 50%;
      z-index: 1;
      transform: translateX(-50%);
      width: 408rpx;
      height: 54rpx;
      border-radius: 60.322rpx 60.322rpx 0rpx 0rpx;
      background: #0084ff;
      box-shadow: 0px 3.686px 3.686px 0px rgba(96, 212, 255, 0.4) inset,
        0px 3.686px 7.373px 0px rgba(101, 210, 255, 0.7) inset,
        0px 1.843px 3.686px 1.229px rgba(0, 96, 255, 0.5);

      .dot {
        width: 22.646rpx;
        height: 22.636rpx;
        background: linear-gradient(180deg, #fdffff 19.61%, #8ae4ff 100%);
        box-shadow: 0px -1.229px 0.614px 0px rgba(255, 255, 255, 0.5) inset;
        border-radius: 50%;
        position: absolute;
        top: 50%;
        transform: translateY(-50%);

        // filter: drop-shadow(0px 1.229px 2.458px rgba(0, 96, 255, 0.60));
        &.left {
          left: 22rpx;
        }

        &.right {
          right: 22rpx;
        }
      }
    }

    .header-content {
      width: 294rpx;
      height: 88rpx;
      position: absolute;
      left: 50%;
      top: -33px;
      z-index: 2;
      text-align: center;
      border-radius: 34.406rpx;
      background: linear-gradient(180deg, #4eb2ff 42.38%, #40acff 100%);
      box-shadow: 0px 3.686px 3.686px 0px #60d4ff inset,
        0px -0.614px 0.614px 0.614px #2484f0 inset,
        0px 3.686px 7.373px 0px #65d2ff inset,
        0px -1.229px 9.83px 0px #0080ff inset,
        0px 1.843px 3.686px 0px rgba(0, 83, 192, 0.5);
      color: #fff;
      font-size: 32rpx;
      font-weight: 600;
      letter-spacing: 0.369px;
      line-height: 88rpx;
      transform: translateX(-50%);
    }
  }

  .modal-content {
    padding: 70rpx 54rpx 50rpx 54rpx;
    border-radius: 49.152rpx;
    background: #fafffb;
    box-shadow: 0px -3.072px 4.915px 0px #f4e1c9 inset,
      0px 1.229px 3.686px 2.458px rgba(0, 96, 255, 0.5);

    // position: absolute;
    // z-index: 1;
    // width: calc(100% - 26rpx);
    // height: calc(100% - 24rpx);
    .text {
      color: #435588;
      text-align: center;
      font-size: 28rpx;
      font-weight: 600;
      letter-spacing: 0.307px;
      width: 100%;
      text-align: center;
    }

    .modal-footer {
      display: flex;

      .btn {
        width: 181.242px;
        height: 59.679px;
        color: #fff;
        text-align: center;
        -webkit-text-stroke-width: 1.8432000875473022;
        -webkit-text-stroke-color: #008200;
        font-family: JiangChengYuanTi;
        font-size: 24px;
        font-style: normal;
        font-weight: 600;
        line-height: 28px;
        /* 116.667% */
        letter-spacing: 0.307px;
      }
    }

    .code-block {
      width: 160rpx;
      height: 66rpx;
      border-radius: 33px;
      background: #f6f8fb;
      color: #00bdff;
      text-align: center;
      font-size: 24rpx;
      font-weight: 500;
      line-height: 66rpx;
      margin-left: 16rpx;

      .u-page__code-text {
        color: #bfc1c5;
      }
    }

    .btns {
      display: flex;
      justify-content: space-between;

      .btn {
        width: 205.329rpx;
        height: 75.889rpx;

        img {
          width: 100%;
          height: 100%;
        }
      }
    }
  }
}
</style>
