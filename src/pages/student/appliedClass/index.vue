<template>
  <div class="applied-class-page">
    <div class="applied-class-list">
      <div
        class="applied-class-item"
        v-for="(item, index) in classList"
        :key="index"
      >
        <div class="applied-class-watermark">
          <img :src="watermarkUrl" alt="" />
        </div>
        <div class="class-name">{{ item.classroom_alias_name }}</div>
        <div class="class-time">
          <div class="class-item-left">
            <img class="icon" :src="naozhongIcon" alt="" />
            <div class="label">上课时间：</div>
          </div>
          <div class="class-time-list">
            <div
              class="time-item"
              v-for="(item2, index) in item.class_time"
              :key="index"
            >
              {{ item2 || "--" }}
            </div>
          </div>
        </div>
        <div class="class-teacher">
          <div class="class-teacher-item">
            <div
              class="teacher-avatar"
              style="background: #ffbb36; border-color: #ffc52566"
            >
              <img :src="teacherIcon" alt="" />
            </div>
            <div class="teacher-name">任课老师：{{ item.teacher_id_chn }}</div>
          </div>
          <div class="class-teacher-item">
            <div
              class="teacher-avatar"
              style="background: #57d1fd; border-color: #57d1fd66"
            >
              <img :src="teacherIcon" alt="" />
            </div>
            <div class="teacher-name">
              班主任：{{ item.header_teacher_id_chn }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 空组件 -->
    <view v-if="showEmpty && classList.length === 0" class="empty-box">
      <empty text="暂无报读班级~" />
    </view>
  </div>
</template>

<script>
import { getClassroomList } from "@/services/student/course";
import Empty from "@/components/empty";
export default {
  name: "AppliedClassIndex",
  components: { Empty },
  data() {
    return {
      naozhongIcon:
        "https://tg-prod.oss-cn-beijing.aliyuncs.com/a4a2c07a-6746-44bc-bd98-1bd8f7318d8d.png",
      watermarkUrl:
        "https://tg-prod.oss-cn-beijing.aliyuncs.com/520468fb-bfec-4423-9710-f40501991556.png",
      teacherIcon:
        "https://tg-prod.oss-cn-beijing.aliyuncs.com/2b2711d3-8efd-49a5-9744-b7e8ff7970d1.png",
      curStudentInfo: {},
      session: {},
      classList: [],
      showEmpty: false
    };
  },
  computed: {},
  methods: {
    async getClassroomList() {
      const params = {
        student_id: this.curStudentInfo.student_id
      };
      try {
        const { code, data } = await getClassroomList(params);
        console.log("data :>> ", data);
        if (code === 0) {
          data?.map((item) => {
            item.class_time = item.class_time.split(",");
          });
          this.classList = data || [];
          this.showEmpty = this.classList.length === 0;
          uni.stopPullDownRefresh();
        } else {
          this.showEmpty = true;
        }
      } catch (error) {
        this.showEmpty = true;
      }
    }
  },
  watch: {},

  // 页面周期函数--监听页面加载
  onLoad() {},
  // 页面周期函数--监听页面初次渲染完成
  onReady() {},
  // 页面周期函数--监听页面显示(not-nvue)
  onShow() {
    this.curStudentInfo = uni.getStorageSync("curStudentInfo");
    this.session = uni.getStorageSync("session");
    console.log("appliedClass :>> ", this.curStudentInfo);
    this.getClassroomList();
  },
  // 页面周期函数--监听页面隐藏
  onHide() {},
  // 页面周期函数--监听页面卸载
  onUnload() {},
  // 页面处理函数--监听用户下拉动作
  onPullDownRefresh() {
    this.getClassroomList();
  }
  // 页面处理函数--监听用户上拉触底
  // onReachBottom() {},
  // 页面处理函数--监听页面滚动(not-nvue)
  // onPageScroll(event) {},
  // 页面处理函数--用户点击右上角分享
  // onShareAppMessage(options) {},
};
</script>

<style lang="scss" scoped>
.applied-class-page {
  padding: 30rpx 32rpx;
  padding-bottom: env(safe-area-inset-bottom);
  min-height: 100vh;
  height: auto;
  background: #f5f5f5;
  .applied-class-list {
    .applied-class-item {
      border-radius: 24rpx;
      background: #fff;
      box-shadow: 0px 0px 50px 0px rgba(124, 143, 166, 0.1);
      position: relative;
      overflow: hidden;
      padding: 30rpx 24rpx 30rpx 24rpx;
      margin-bottom: 30rpx;
      &:last-child {
        margin-bottom: 0;
      }
      .applied-class-watermark {
        width: 134rpx;
        height: 121rpx;
        position: absolute;
        right: -10px;
        top: -25px;
        img {
          width: 100%;
          height: 100%;
        }
      }
      .class-name {
        font-size: 30rpx;
        color: #333;
        font-weight: 500;
        margin-bottom: 22rpx;
      }
      .class-time {
        padding: 20rpx 14rpx;
        border-radius: 12px;
        background: #fff1b933;
        color: #be8f0c;
        font-size: 26rpx;
        font-weight: 400;
        margin-bottom: 24rpx;
        display: flex;
        .class-item-left {
          display: flex;
          flex-shrink: 0;
          .icon {
            width: 30rpx;
            height: 28rpx;
            margin-right: 11rpx;
            margin-top: 3rpx;
          }
        }
        .class-time-list {
          display: flex;
          flex-flow: wrap;
          .time-item {
            margin-right: 20rpx;
            width: 220rpx;
            &:nth-child(even) {
              margin-right: 0;
              width: auto;
            }
          }
        }
      }
      .class-teacher {
        display: flex;
        align-items: center;
        justify-content: space-between;
        align-items: space-between;
        .class-teacher-item {
          display: flex;
          align-items: center;
          .teacher-avatar {
            width: 44rpx;
            height: 44rpx;
            border-radius: 50%;
            img {
              width: 100%;
              height: 100%;
            }
          }
          .teacher-name {
            margin-left: 10rpx;
            font-size: 24rpx;
            font-weight: 400;
            color: #999;
          }
        }
      }
    }
  }
}
.empty-box {
  display: flex;
  align-items: center;
  height: 80vh;
  justify-content: center;
}
</style>
