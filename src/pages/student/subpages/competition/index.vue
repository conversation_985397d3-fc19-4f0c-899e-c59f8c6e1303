<template>
  <view class="competition-page">
    <!-- <u-navbar
      title="赛事报名"
      bgColor="#fff"
      leftIconSize="25px"
      leftIconColor="#333333"
      :titleStyle="{
        color: '#333333',
        fontSize: '34rpx',
        fontWeight: '500',
        lineHeight: '40rpx'
      }"
      :autoBack="true"
      placeholder
    >
      <view class="nav-letf" slot="left">
        <image
          src="https://tg-prod.oss-cn-beijing.aliyuncs.com/e9ac0f4b-6282-4bac-b602-189057c45c90.webp"
        ></image>
      </view>
    </u-navbar> -->
    <!-- <view class="content-container"> -->
    <!-- <view class="fixed-header"> -->
    <div class="tab">
      <!-- <div class="tab-list">
        <div
          :class="[
            'tab-item',
            listParams.status === item.value ? 'active' : ''
          ]"
          @click="handleTabChange(item)"
          v-for="item in tabList"
          :key="item.value"
        >
          <span class="tab-item__text">{{ item.label }}</span>
        </div>
      </div> -->
      <custom-tabs
        :list="tabList"
        :current="current"
        @change="handleTabChange"
      ></custom-tabs>
    </div>
    <view class="date-selector" @click="showDatePicker = true">
      <text v-if="listParams.month_date"
        >{{ currentYear }}年{{ currentMonth }}月</text
      >
      <text v-else>全部时间</text>
      <u-icon
        name="https://tg-prod.oss-cn-beijing.aliyuncs.com/1df5cbe4-ec23-4d7c-aecf-90b3e2fd66a8.webp"
        width="18rpx"
        height="12rpx"
      ></u-icon>
    </view>
    <view @touchmove.stop.prevent="">
      <u-picker
        ref="uPicker"
        :show="showDatePicker"
        :columns="[years, months]"
        @confirm="confirmDate"
        @cancel="showDatePicker = false"
        :defaultIndex="[yearIndex, monthIndex]"
        confirmColor="#333"
      ></u-picker>
    </view>

    <!-- </view> -->

    <!-- </view> -->
    <!-- 赛事列表 -->
    <view class="competition-list">
      <view class="list-content">
        <scroll-view
          v-if="list.length"
          scroll-y
          @scrolltolower="scrolltolower"
          refresher-enabled
          :refresher-triggered="isRefreshing"
          @refresherrefresh="onRefresh"
          refresher-background="#f5f5f5"
          :scroll-top="scrollTop"
        >
          <view v-for="item in list" :key="item.id" class="item-wrapper">
            <view class="competition-item" @click="handleItemClick(item)">
              <u-image
                class="item-icon"
                :src="
                  item.cover_url + '?x-oss-process=image/resize,h_160,w_160'
                "
                mode="scaleToFill"
                width="160rpx"
                height="160rpx"
                radius="24rpx"
                :showMenuByLongpress="false"
              />
              <view class="item-content">
                <view class="title">
                  <div class="title-left">{{ item.match_type_name }}</div>
                  <div class="title-right">
                    {{ item.name }}
                  </div></view
                >
                <text class="time"
                  >报名时间：{{ item.sign_up_start_time }}至{{
                    item.sign_up_end_time
                  }}</text
                >
                <view class="item-right">
                  <view
                    class="status-tag"
                    :class="{
                      registering: item.sign_status === 1,
                      'in-progress': item.sign_status === 2,
                      ended: item.sign_status === 3 || item.sign_status === 4,
                      registersucc: item.sign_status === 6
                    }"
                  >
                    {{ getSignStatus(item.sign_status) }}
                  </view>
                </view>
              </view>
            </view>
          </view>
          <u-loadmore
            :status="status"
            lineColor="#DADADA"
            line
            color="#999999"
            :fontSize="'26rpx'"
            :loadmoreText="loadmoreText"
            :marginTop="'70rpx'"
          />
        </scroll-view>
        <view
          style="
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
          "
          v-else
        >
          <loading-animation
            :width="300"
            :characterSize="260"
            :textSize="32"
            v-if="status === 'loading'"
          />
          <u-empty
            text="暂无赛事~"
            marginTop="-42rpx"
            :width="'163rpx'"
            textColor="#999999"
            :height="'230rpx'"
            icon="https://tg-prod.oss-cn-beijing.aliyuncs.com/d02814d0-bfd3-48bf-8d9d-80cda362b89a.webp"
            v-else
          >
          </u-empty>
        </view>
      </view>
    </view>
    <view class="bottom-btn u-safe-area-inset-bottom">
      <button class="register-btn" @click="handleRegisterClick">
        我的报名
      </button>
    </view>
    <NeedBindMatch
      v-model="showBindPopup"
      @confirm="handleBindConfirm"
      @cancel="handleBindCancel"
    />
  </view>
</template>

<script>
import {
  getMatchList,
  isNeedBindMatch,
  bindMatchStudent
} from "@/services/student/match";
import NeedBindMatch from "./components/needBindMatch.vue";
import CustomTabs from "@/components/tabs/index.vue";
import LoadingAnimation from "@/components/common/LoadingAnimation.vue";
import { getYearList } from "@/utils/date";
export default {
  name: "CompetitionIndex",
  components: {
    NeedBindMatch,
    CustomTabs,
    LoadingAnimation
  },
  data() {
    return {
      status: "loading",
      list: [],
      listParams: {
        page: 1,
        page_size: 5,
        status: 1
      },
      total: 0,
      loadmoreText: "上拉加载更多",
      curStudentInfo: {},
      session: {},
      student_id: "",
      showBindPopup: false,
      tabList: [
        {
          name: "报名中",
          value: 1
        },
        {
          name: "待开始",
          value: 2
        },
        {
          name: "已结束",
          value: 3
        }
      ],
      current: 0,
      isRefreshing: false,
      scrollTop: 0.01,
      showDatePicker: false,
      currentYear: "",
      currentMonth: "",
      years: [...getYearList()],
      months: Array.from({ length: 12 }, (_, i) =>
        (i + 1).toString().padStart(2, "0")
      ),
      yearIndex: 0,
      monthIndex: 0
    };
  },
  methods: {
    confirmDate(e) {
      const [year, month] = e.value;
      // if (this.currentYear !== year) {
      this.currentYear = year;
      // }

      // if (this.currentMonth !== month) {
      this.currentMonth = month;
      // }
      this.listParams.page = 1;
      this.list = [];
      this.listParams.month_date = `${this.currentYear}-${this.currentMonth}`;
      this.getCompetitionList();
      this.showDatePicker = false;
    },
    initYears(isInit = true) {
      this.currentYear = new Date().getFullYear();
      this.currentMonth = (new Date().getMonth() + 1)
        .toString()
        .padStart(2, "0");
      // // 找到当前年份的索引
      this.yearIndex = this.years.indexOf(this.currentYear);
      // // 找到当前月份的索引
      this.monthIndex = this.months.indexOf(this.currentMonth);
      if (isInit) {
        this.listParams.month_date = `${this.currentYear}-${this.currentMonth}`;
      }
      console.log("🚀 ~ initYears ~ years:", this.years);
    },
    getSignStatus(status) {
      return {
        1: "去报名",
        2: "待开始",
        3: "已结束",
        4: "已结束",
        6: "已报名"
      }[status];
    },
    async getCompetitionList() {
      this.status = "loading";
      // TODO: 调用获取赛事列表的API
      const { code, data } = await getMatchList({
        ...this.listParams,
        student_id: this.student_id,
        identity:
          this.session.role === "student"
            ? 3
            : this.session.role === "customer"
            ? 2
            : 1
      });
      console.log(data);
      if (code === 0) {
        if (this.listParams.page === 1) {
          this.list = data.results;
        } else {
          for (let i = 0; i < data.results.length; i++) {
            this.list.push(data.results[i]);
          }
        }
        this.total = data.count;
        if (this.list.length >= this.total) {
          this.status = "nomore";
        } else {
          this.status = "loadmore";
        }
      }
    },
    async scrolltolower() {
      if (this.list.length >= this.total) {
        this.status = "nomore";
        return;
      }
      this.listParams.page++;
      this.getCompetitionList();
    },
    handleItemClick(item) {
      if ([3, 4].includes(item.sign_status)) {
        uni.showToast({
          title: `该${item.sign_status === 3 ? "报名" : "赛事"}已结束`,
          icon: "none"
        });
        return;
      }
      uni.navigateTo({
        url: `/pages/student/subpages/competition/detail?id=${item.id}&&sign_up_id=${item.sign_up_id}`
      });
    },
    handleRegisterClick(item) {
      uni.navigateTo({
        url: `/pages/student/subpages/competition/register?id=${item.id}`
      });
    },
    async isNeedBindMatch() {
      if (this.session.role === "default") {
        return;
      }
      const { code, data, message } = await isNeedBindMatch({
        student_id: this.student_id
      });
      if (code === 0) {
        console.log(data);
        this.showBindPopup = data.need_bind_status;
      } else {
        uni.showToast({
          title: message,
          icon: "none"
        });
      }
    },
    async handleBindConfirm() {
      // 处理确认逻辑
      const { code, message } = await bindMatchStudent({
        student_id: this.student_id,
        identity:
          this.session.role === "student"
            ? 3
            : this.session.role === "customer"
            ? 2
            : 1,
        open_id: this.session.open_id
      });
      if (code === 0) {
        uni.showToast({
          title: "绑定成功",
          icon: "none"
        });
        this.showBindPopup = false;
        this.getCompetitionList();
      } else {
        uni.showToast({
          title: message,
          icon: "none"
        });
      }
    },
    handleBindCancel() {
      // 处理取消逻辑
    },
    handleTabChange(item) {
      this.listParams.status = this.tabList[item].value;
      // this.initYears();
      this.listParams.page = 1;
      this.list = [];
      this.getCompetitionList();
    },
    async onRefresh() {
      this.isRefreshing = true;
      this.listParams.page = 1;
      this.list = [];
      await this.getCompetitionList();
      setTimeout(() => {
        this.isRefreshing = false;
      }, 500);
    }
  },
  onLoad() {
    this.curStudentInfo = uni.getStorageSync("curStudentInfo");
    this.session = uni.getStorageSync("session");
    console.log(this.curStudentInfo, this.session);
    this.student_id =
      this.session.role === "student"
        ? this.curStudentInfo.student_id
        : this.session.role === "customer"
        ? this.curStudentInfo.customer_id
        : this.session.open_id;
    this.initYears(false);
    // this.getCompetitionList();
    // this.isNeedBindMatch();
  },
  onShow() {
    this.listParams.page = 1;
    this.scrollTop = 0.01;
    this.getCompetitionList();
    this.isNeedBindMatch();
  },
  onLaunch() {}
};
</script>

<style lang="scss" scoped>
.competition-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  height: 100vh;
  // overflow: hidden;
  // display: flex;
  // flex-direction: column;
}
.nav-letf {
  width: 40rpx;
  height: 40rpx;
  image {
    width: 100%;
    height: 100%;
  }
}
.content-container {
  // flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
  // overflow: hidden;
}

.fixed-header {
  position: relative;
  width: 100%;
  z-index: 10;
}

.tab {
  width: 100%;
  height: 92rpx;
  .tab-list {
    background: #fff;
    box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.05);
    display: flex;
    color: #999;
    height: 100%;
    .tab-item {
      flex: 1;
      text-align: center;
      position: relative;
      transition: color 0.3s ease-in-out;
      &.active {
        color: #ffc525;
        .tab-item__text {
          position: relative;
          font-weight: 400;
          font-size: 30rpx;
          &::after {
            content: "";
            position: absolute;
            bottom: 18rpx;
            left: 50%;
            transform: translateX(-50%) scaleX(1);
            width: 28rpx;
            height: 6rpx;
            background: #ffbf0d;
            border-radius: 11rpx;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            opacity: 1;
          }
        }
      }
      .tab-item__text {
        padding: 28rpx 0;
        display: inline-block;
        font-size: 30rpx;
        font-style: normal;
        font-weight: 400;
        position: relative;
        &::after {
          content: "";
          position: absolute;
          bottom: -6rpx;
          left: 50%;
          transform: translateX(-50%) scaleX(0);
          width: 40rpx;
          height: 6rpx;
          background: #ffbf0d;
          border-radius: 3rpx;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          opacity: 0;
        }
      }
    }
  }
}
.date-selector {
  display: inline-flex;
  padding: 9rpx 32rpx;
  gap: 10rpx;
  border-radius: 12rpx;
  background: #fff;
  font-size: 26rpx;
  font-weight: 500;
  line-height: normal;
  color: #666;
  height: 56rpx;
  align-items: center;
  margin: 24rpx 0 30rpx 20rpx;

  text {
    color: #666;
    font-size: 26rpx;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
  }
}
.competition-list {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 0rpx 32rpx;
  // margin-top: 30rpx;
  margin-bottom: 180rpx;
  overflow: auto;
  height: calc(100% - 148rpx - 180rpx - 54rpx);
  // overflow: hidden;
  .list-content {
    flex: 1;
    height: 100%;
  }
  .item-wrapper:first-child {
    // margin-top: 30rpx;
  }
  .competition-item {
    width: 686rpx;
    height: 200rpx;
    border-radius: 24rpx;
    background: #fff;
    box-shadow: 0px 0px 50rpx 0px rgba(124, 143, 166, 0.1);
    padding: 20rpx;
    display: flex;
    margin-bottom: 30rpx;
    position: relative;
    align-items: center;
    .item-icon {
      width: 160rpx;
      height: 160rpx;
      border-radius: 24rpx;
      margin-right: 24rpx;
    }
    .item-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 7rpx;
      margin-left: 24rpx;
      .title {
        color: #333;
        font-size: 30rpx;
        font-style: normal;
        font-weight: 500;
        line-height: normal;
        display: flex;
        align-items: center;
        width: 100%;
        .title-left {
          color: #ff7300;
          font-size: 22rpx;
          font-style: normal;
          font-weight: 400;
          border: 1rpx solid #ff7300;
          border-radius: 4rpx;
          padding: 1rpx 4rpx 0 4rpx;
          margin-right: 12rpx;
          width: 80rpx;
          text-align: center;
          flex-shrink: 0;
          line-height: 30rpx;
        }
        .title-right {
          flex: 1;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          display: block;
          width: 355rpx;
        }
      }
      .time {
        color: #666;
        font-size: 26rpx;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
        margin-bottom: 18rpx;
        white-space: nowrap;
      }
    }

    .item-right {
      .status-tag {
        width: 136rpx;
        height: 54rpx;
        border-radius: 32rpx;
        background: linear-gradient(15deg, #ffbf0d 18.1%, #ffcb3c 83.29%);
        box-shadow: 0rpx -4rpx 8rpx 0rpx #eaac00 inset;
        color: #fff;
        font-size: 24rpx;
        font-weight: 600;
        line-height: 32rpx;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
        &.registering {
          background: linear-gradient(90deg, #ffbf0d 0%, #ffcb3c 100%);
          color: #ffffff;
          box-shadow: 0px -4px 8px 0px #eaac00 inset;
        }
        &.registersucc {
          border-radius: 32rpx;
          background: #fdf4d9;
          color: #ffc525;
          font-size: 24rpx;
          font-style: normal;
          font-weight: 500;
          line-height: 32rpx;
          box-shadow: 0 0 0 0;
        }
        &.in-progress {
          background: linear-gradient(15deg, #ff6da3 18.1%, #ff92ba 83.29%);
          box-shadow: 0px -4px 8px 0px #f7367d inset;
        }

        &.ended {
          border-radius: 32rpx;
          background: #d4d4d4;
          color: #fff;
          font-size: 24rpx;
          font-style: normal;
          font-weight: 500;
          line-height: 32rpx;
          box-shadow: 0 0 0 0;
        }
      }
    }
  }
}
.bottom-btn {
  display: flex;
  width: 750rpx;
  // height: 128rpx;
  justify-content: center;
  flex-shrink: 0;
  background-color: #fff;
  position: fixed;
  bottom: 0;
  left: 0;
  .register-btn {
    width: 686rpx;
    height: 92rpx;
    border-radius: 71px;
    background: linear-gradient(15deg, #ffbf0d 18.1%, #ffcb3c 83.29%);
    box-shadow: 0px -10rpx 18rpx 0px #f3b300 inset,
      0px 4rpx 10rpx 0px rgba(254, 197, 36, 0.47);
    color: #fff;
    text-align: center;
    font-size: 32rpx;
    font-style: normal;
    font-weight: 600;
    line-height: 40rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 18rpx 0;
  }
}
::v-deep .u-loadmore {
  padding-bottom: 20rpx;
}
::v-deep .u-popup__content {
  border-radius: 24rpx;
}
</style>
