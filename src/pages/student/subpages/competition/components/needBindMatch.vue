<template>
  <u-popup
    :show="show"
    @close="handleClose"
    mode="center"
    :round="10"
    :customStyle="{
      borderRadius: '32rpx',
      background: '#FFFFFF',
      fontSize: '34rpx',
      width: '582rpx'
    }"
    :safeAreaInsetBottom="false"
  >
    <view class="bind-popup">
      <view class="content">
        <text class="title"
          >您以游客身份报名赛事，是否将赛事绑定到当前用户{{
            curStudentInfo.student_name || ""
          }}?</text
        >
        <view class="btn-group">
          <view class="cancel-btn" @tap="handleClose">取消</view>
          <view class="confirm-btn" @tap="handleConfirm">确定</view>
        </view>
      </view>
    </view>
  </u-popup>
</template>

<script>
export default {
  name: "NeedBindMatch",
  props: {
    value: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      curStudentInfo: {},
      session: {}
    };
  },
  computed: {
    show: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit("input", val);
      }
    }
  },
  methods: {
    handleClose() {
      this.show = false;
      this.$emit("cancel");
    },
    handleConfirm() {
      this.$emit("confirm");
      this.show = false;
    }
  },
  async mounted() {
    this.curStudentInfo = uni.getStorageSync("curStudentInfo");
    this.session = uni.getStorageSync("session");
  }
};
</script>

<style lang="scss" scoped>
.bind-popup {
  padding: 48rpx 40rpx 0;

  .content {
    display: flex;
    flex-direction: column;
    align-items: center;

    .title {
      color: #333;
      text-align: center;
      font-family: PingFang SC;
      font-size: 32rpx;
      font-weight: 400;
      line-height: 48rpx;
      margin-bottom: 48rpx;
    }

    .btn-group {
      display: flex;
      gap: 20rpx;
      width: 100%;
      margin-bottom: 60rpx;
      .cancel-btn,
      .confirm-btn {
        flex: 1;
        height: 88rpx;
        border-radius: 44rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 32rpx;
        font-weight: 500;
        transition: transform 0.2s ease;

        &:active {
          transform: scale(0.98);
        }
      }

      .cancel-btn {
        // background: #fff;
        // box-shadow: 0px 8rpx 40rpx 0px rgba(0, 0, 0, 0.1);
        color: #ffbb00;
        border: 3rpx solid #ffbb00;
      }

      .confirm-btn {
        background: linear-gradient(15deg, #ffbf0d 18.1%, #ffcb3c 83.29%);
        box-shadow: 0px -5rpx 12rpx 0px #fc0 inset,
          0px 9rpx 20rpx 0px #fff7e1 inset;
        filter: drop-shadow(0px 4rpx 4rpx rgba(255, 192, 18, 0.11));
        color: #fff;
      }
    }
  }
}
</style>
