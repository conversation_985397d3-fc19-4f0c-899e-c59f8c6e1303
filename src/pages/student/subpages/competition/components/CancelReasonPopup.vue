<template>
  <u-popup :show="value" mode="bottom" borderRadius="24" @close="handleClose">
    <view class="cancel-reason-popup">
      <view class="popup-header">
        <view class="popup-title">请选择取消原因</view>
        <!-- <view >×</view> -->
        <image class="close-icon" @click="handleClose" src="https://tg-prod.oss-cn-beijing.aliyuncs.com/00da00aa-8dad-47b0-a791-6da585b133a9.webp" mode="widthFix"></image>
      </view>
      <view class="popup-content">
        <view
          v-for="(item, index) in cancelReasonList"
          :key="index"
          class="reason-item"
          @click="handleSelectReason(item.id)"
        >
          <view class="reason-text">{{ item.name }}</view>
          <view class="reason-check" :class="{ active: selectedReason === item.id }">
            <view class="reason-checkmark" v-if="selectedReason === item.id"></view>
          </view>
        </view>

        <view class="reason-textarea-box">
          <textarea
            class="reason-textarea"
            v-model="reasonText"
            placeholder="请输入取消原因"
            :maxlength="100"
            placeholder-style="color: #999999;font-size: 28rpx;"
          ></textarea>
          <view class="textarea-counter">{{ reasonText.length ? reasonText.length : 0 }}/100</view>
        </view>
      </view>
      <view class="popup-footer">
        <view class="submit-btn" :class="{ disabled: !selectedReason || disabledBut }" @click="handleSubmit">提交</view>
      </view>
    </view>
  </u-popup>
</template>

<script>
export default {
  name: "CancelReasonPopup",
  props: {
    value: {
      type: Boolean,
      default: false
    },
    cancelReasonList: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    disabledBut() {
      return this.cancelReasonList.find(item => item.id === this.selectedReason).must === 1 && !this.reasonText.trim();
    }
  },
  data() {
    return {
      selectedReason: "",
      reasonText: ""
    };
  },
  methods: {
    handleSelectReason(value) {
      this.selectedReason = value;
    },
    handleClose() {
      this.$emit("input", false);
    },
    handleSubmit() {
      const reason = this.selectedReason;
      let explanation = "";
        if (!this.selectedReason) {
            uni.showToast({
                title: "请选择取消原因",
                icon: "none"
            });
            return;
        }
        if (!this.reasonText.trim() && this.cancelReasonList.find(item => item.id === this.selectedReason).must === 1) {
          uni.showToast({
            title: "请填写取消原因",
            icon: "none"
          });
          return;
        }
        explanation = this.reasonText.trim();

      this.$emit("confirm", {
        reason,
        explanation
      });
    //   this.handleClose();
    }
  }
};
</script>

<style lang="scss" scoped>
.cancel-reason-popup {
  width: 100%;
  padding-top: 30rpx;
  border-radius: 24rpx 24rpx 0 0;

  .popup-header {
    position: relative;
    text-align: center;
    padding-bottom: 30rpx;

    .popup-title {
        color: #333;
        text-align: center;
        font-size: 32rpx;
        font-style: normal;
        font-weight: 500;
        line-height: 48rpx
    }

    .close-icon {
      position: absolute;
      right: 30rpx;
      top: 0;
      width: 36rpx;
      height: 36rpx;
      text-align: center;
      color: #999;
      cursor: pointer;
    }
  }

  .popup-content {
    padding: 0 30rpx;

    .reason-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      //   padding: 24rpx 0;
      height: 100rpx;
      border-bottom: 1px solid #EEEEEE;

      &.active {
        .reason-text {
          color: #FFC525;
        }
      }

      .reason-text {
        color:  #333;
        font-size: 30rpx;
        font-style: normal;
        font-weight: 400;
        line-height: 40rpx;
      }

      .reason-check {
        width: 36rpx;
        height: 36rpx;
        border-radius: 50%;
        background-color: #FFFFFF;
        border: 2rpx solid #BDBDBD;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;

        &.active {
          background-color: #FFC525;
          border-color: #FFC525;
        }

        .reason-checkmark {
          position: absolute;
          top: 50%;
          left: 50%;
          width: 12rpx;
          height: 20rpx;
          border: solid #FFFFFF;
          border-width: 0 4rpx 4rpx 0;
          transform: translate(-50%, -60%) rotate(45deg);
        }
      }
    }

    .reason-textarea-box {
      margin-top: 50rpx;
      position: relative;

      .reason-textarea {
        width: 100%;
        height: 216rpx;
        background-color: #F5F5F5;
        border-radius: 14rpx;
        padding: 16rpx;
        font-size: 26rpx;
        color: #333;
        font-style: normal;
        font-weight: 400;
        box-sizing: border-box;
        line-height: 38rpx;
      }

      .textarea-counter {
        position: absolute;
        right: 20rpx;
        bottom: 20rpx;
        font-size: 24rpx;
        color: #999999;
      }
    }
  }

  .popup-footer {
    padding-top: 40rpx;
    display: flex;
    justify-content: center;

    .submit-btn {
      width: 686rpx;
      height: 92rpx;
      background: linear-gradient(15deg, #FFBF0D 18.1%, #FFCB3C 83.29%);
    box-shadow: 0px -10px 18px 0px #F3B300 inset, 0rpx 4rpx 20rpx 0px rgba(254, 197, 36, 0.47);
      border-radius: 80rpx;
      font-size: 32rpx;
      font-weight: 500;
      color: #FFFFFF;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}
.disabled{
    opacity: 0.5;
}
</style>
