<template>
  <view class="form-wrapper">
    <u-form
      :model="formData"
      :rules="rules"
      ref="uForm"
      label-position="left"
      label-width="136rpx"
      :labelStyle="{
        color: '#666',
        fontSize: '28rpx',
        fontWeight: '400',
        lineHeight: '40rpx'
      }"
    >
      <!-- 校区选择 -->
      <u-form-item
        label="所属校区"
        prop="departmentName"
        v-if="detail.f_department_ids === 1"
        :required="detail.f_department_must === 1"
      >
        <u-input
          v-model="formData.departmentName"
          type="select"
          :select-open="false"
          placeholder="请选择校区"
          :disabled="true"
          :color="'#BABABA'"
          :customStyle="disabledIput"
        />
      </u-form-item>
      <!-- :customStyle="competitionSuccess ? disabledIput : styleIput" -->
      <!-- 姓名 -->
      <u-form-item
        label="姓名"
        prop="f_name"
        v-if="detail.f_name === 1"
        required
      >
        <u-input
          v-model="formData.f_name"
          placeholder="请输入姓名"
          :border="true"
          maxlength="20"
          :color="competitionSuccess || studentInfo.name ? '#BABABA' : ''"
          :disabled="competitionSuccess || studentInfo.name"
          fontSize="28rpx"
          :customStyle="
            competitionSuccess || studentInfo.name ? disabledIput : styleIput
          "
        />
      </u-form-item>

      <!-- 手机号 -->
      <u-form-item
        label="手机号"
        prop="f_mobile"
        v-if="detail.f_mobile === 1"
        required
      >
        <u-input
          v-model="formData.f_mobile"
          placeholder="请输入手机号"
          type="number"
          :border="true"
          maxlength="11"
          fontSize="28rpx"
          :color="competitionSuccess || studentInfo.mobile ? '#BABABA' : ''"
          :disabled="competitionSuccess || studentInfo.mobile"
          :customStyle="
            competitionSuccess || studentInfo.mobile ? disabledIput : styleIput
          "
        />
      </u-form-item>

      <!-- 聂道棋力 -->
      <u-form-item
        label="聂道棋力"
        :prop="detail.f_rank_level_must === 1 ? 'f_rank_level' : ''"
        v-if="detail.f_rank_level === 1"
        :required="detail.f_rank_level_must === 1"
      >
        <view
          type="select"
          :select-open="false"
          placeholder="请选择聂道棋力"
          @click="handleRankClick"
          :disabled="competitionSuccess || studentInfo.rank_level"
          class="rank-input u-input"
          :class="
            competitionSuccess || studentInfo.rank_level ? 'disabledIput' : ''
          "
        >
          <text
            :style="
              formData.f_rank_level
                ? 'font-size: 28rpx;'
                : 'color:#BABABA;font-size: 28rpx;'
            "
            >{{ formData.f_rank_level || "请选择聂道棋力" }}</text
          >
          <image
            v-if="!competitionSuccess"
            src="https://tg-prod.oss-cn-beijing.aliyuncs.com/98087d55-eaff-4aaf-9dba-7e92d2e7d1eb.webp"
            alt=""
          />
        </view>
      </u-form-item>

      <!-- 自定义字段 -->
      <div class="custom-field">
        <template v-for="(field, index) in detail.customize_result">
          <u-form-item
            :key="index"
            :label="field.name"
            :prop="field.id"
            :required="field.must_is === 1"
            labelPosition="top"
          >
            <u--textarea
              v-model="formData[field.id]"
              :disableDefaultPadding="true"
              :adjustPosition="true"
              :ignoreCompositionEvent="true"
              :placeholder="'请输入' + field.name"
              :border="true"
              :height="'140rpx'"
              :disabled="competitionSuccess"
              :color="competitionSuccess ? '#BABABA' : ''"
              maxlength="48"
              :customStyle="competitionSuccess ? disabledIput : styleIput"
            />
          </u-form-item>
        </template>
      </div>
    </u-form>
  </view>
</template>

<script>
export default {
  name: "SignUpForm",
  options: {
    styleIsolation: "shared"
  },
  props: {
    // 赛事详情配置
    detail: {
      type: Object,
      default: () => ({})
    },
    competitionSuccess: {
      type: Boolean,
      default: false
    },
    // 学生信息
    studentInfo: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    styleIput() {
      return "width: 425rpx; height: 56rpx; border-radius: 12rpx; border: 1rpx solid #E4DB33; background: #FFF;font-size: 28rpx;";
    },
    disabledIput() {
      return "width: 425rpx; height: 56rpx; border-radius: 12rpx; border: 1rpx solid #BABABA !important; background: #FFF;color:#BABABA !important;opacity: 0.6;font-size: 28rpx;";
    }
  },
  data() {
    return {
      formData: {
        f_department_ids: "",
        f_name: "",
        f_mobile: "",
        f_rank_level: "",
        departmentName: ""
      },
      rules: {
        departmentName: [
          {
            required: true,
            message: "请输入所属校区",
            trigger: ["change", "blur"]
          }
        ],
        f_name: [
          {
            required: true,
            message: "请输入姓名",
            trigger: ["change", "blur"]
          }
        ],
        f_mobile: [
          {
            required: true,
            message: "请输入手机号",
            trigger: ["change", "blur"]
          },
          {
            min: 11,
            max: 11,
            message: "请输入正确的手机号",
            trigger: ["change", "blur"]
          }
        ],
        f_rank_level: [
          {
            required: true,
            message: "请选择聂道棋力",
            trigger: ["change", "blur"]
          }
        ]
      }
    };
  },
  methods: {
    validate() {
      return new Promise((resolve, reject) => {
        this.$refs.uForm
          .validate()
          .then((valid) => {
            if (valid) {
              resolve(this.formData);
            } else {
              reject(new Error("表单验证失败"));
            }
          })
          .catch((err) => {
            reject(err, "111");
          });
      });
    },
    resetForm() {
      this.$refs.uForm.resetFields();
    },
    initFormData() {
      // 初始化固定字段
      // const session = uni.getStorageSync("session");
      const curStudentInfo = uni.getStorageSync("curStudentInfo");
      console.log("🚀 ~ initFormData ~ this.studentInfo:", this.studentInfo);
      const { customize_result = [] } = this.studentInfo;
      this.formData = {
        departmentName:
          this.studentInfo.department_name === ""
            ? "空"
            : this.studentInfo.department_name,
        f_department_ids:
          this.studentInfo.department_id || curStudentInfo.department_id,
        f_name: this.studentInfo.name || "",
        f_mobile: this.studentInfo.mobile || "",
        f_rank_level: this.studentInfo.rank_level || ""
      };
      customize_result.forEach((item) => {
        this.$set(this.formData, item.id, item.value);
      });
    },
    updateRules() {
      // 更新必填规则
      this.rules.f_department_ids[0].required =
        this.detail.f_department_must === 1;
      this.rules.f_rank_level[0].required = this.detail.f_rank_level_must === 1;
    },
    handleRankClick() {
      if (this.competitionSuccess || this.studentInfo.rank_level) return;
      this.$emit("showRankPicker");
    },
    setRankLevel(value) {
      this.formData.f_rank_level = value;
    }
  },
  watch: {
    "detail.customize_result": {
      handler(newVal) {
        if (newVal && newVal.length) {
          newVal.forEach((field) => {
            if (field.must_is === 1) {
              this.$set(this.rules, field.id, [
                {
                  required: true,
                  message: `请输入${field.name}`,
                  trigger: ["change", "blur"]
                }
              ]);
            }
          });
        }
      },
      immediate: true
    },
    detail: {
      handler(newVal) {
        if (newVal) {
          this.initFormData();
          this.updateRules(); // 更新验证规则
        }
      },
      immediate: true,
      deep: true
    }
  }
};
</script>

<style lang="scss" scoped>
.form-wrapper {
  padding-left: 20rpx;
  margin-top: 30rpx;
  ::v-deep .u-form-item {
    margin-bottom: 28rpx;

    &__body__left__content {
      padding-right: 10rpx;
    }
    .u-form-item__body {
      padding: 0 !important;
    }
    .u-form-item__body__right__message {
      margin-top: 12rpx;
      color: #fe4f37;
      font-family: "PingFang SC";
      font-size: 24rpx;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
    }
  }
}
::v-deep .u-input {
  border-radius: 6rpx;
  border: 1rpx solid #e4db33;
  height: 56rpx;
  background: #fff;

  &--disabled {
    background-color: #f5f7fa;
    border-color: #e4e7ed;
    color: #c0c4cc;
    cursor: not-allowed;

    .u-input__input {
      color: #c0c4cc;
      cursor: not-allowed;
    }
  }
}
.u-form-item__body {
  padding-bottom: 24rpx;
}
.rank-input {
  font-size: 15px;
  text-align: left;
  line-height: 26px;
  width: 425rpx;
  height: 67rpx;
  border-radius: 6px;
  border: 1rpx solid #e4db33;
  background: #fff;
  padding-top: 6px;
  padding-bottom: 6px;
  padding-left: 9px;
  padding-right: 9px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  image {
    width: 30rpx;
    height: 30rpx;
  }
}
::v-deep .disabledIput {
  border: 1rpx solid #bababa !important;
  color: #bababa !important;
  opacity: 0.6;
}
.custom-field {
  ::v-deep .u-form-item {
    margin-bottom: 28rpx;
    .u-form-item__body__left {
      width: 100% !important;
      margin-bottom: 14.4rpx !important;
    }
    .u-form-item__body__right__message {
      margin-left: 0 !important;
      position: relative;
      left: -20rpx;
    }
    .u-textarea {
      width: 605.839rpx !important;
      height: 140rpx !important;
      flex: none;
      position: relative;
      left: -20rpx;
      z-index: 0;
    }
    &__body {
      display: flex;
      flex-direction: column;
      padding: 0 !important;
    }

    &__body__left {
      margin-bottom: 12rpx;

      &__content {
        padding-right: 0;
        color: #666;
        font-size: 26rpx;
        font-weight: 400;
        line-height: 40rpx;
      }
    }

    &__body__right {
      width: 100%;

      &__message {
        margin-top: 12rpx;
        color: #fe4f37;
        font-family: "PingFang SC";
        font-size: 24rpx;
        font-weight: 400;
        line-height: normal;
      }
    }
  }

  .u-input {
    width: 100% !important;
    height: 56rpx;
    border-radius: 6rpx;
    border: 1rpx solid #e4db33;
    background: #fff;

    &--disabled {
      border-color: #bababa !important;
      color: #bababa !important;
    }
  }
}
::v-deep .u-textarea {
  padding: 14rpx 16rpx;
  line-height: 40rpx;
}
::v-deep .u-textarea__field {
  font-size: 28rpx;
  height: 100% !important;
}
</style>
