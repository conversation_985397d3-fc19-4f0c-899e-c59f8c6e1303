<template>
  <view class="competition-page">
    <!-- <u-navbar
      title="我的报名"
      bgColor="#fff"
      leftIconSize="25px"
      leftIconColor="#333333"
      :titleStyle="{
        color: '#333333',
        fontSize: '34rpx',
        fontWeight: '500',
        lineHeight: '40rpx'
      }"
      :autoBack="true"
      placeholder
    >
      <view class="nav-letf" slot="left">
        <image
          src="https://tg-prod.oss-cn-beijing.aliyuncs.com/e9ac0f4b-6282-4bac-b602-189057c45c90.webp"
        ></image>
      </view>
    </u-navbar> -->
    <!-- 赛事列表 -->
    <view class="competition-list">
      <view class="list-content">
        <scroll-view
          v-if="list.length"
          scroll-y
          @scrolltolower="scrolltolower"
          refresher-enabled
          :refresher-triggered="isRefreshing"
          @refresherrefresh="onRefresh"
          refresher-background="#f5f5f5"
        >
          <view v-for="item in list" :key="item.id">
            <view class="competition-item" @click="handleItemClick(item)">
              <u-image
                class="item-icon"
                :src="item.cover_url + '?x-oss-process=image/resize,h_160,w_160'"
                mode="scaleToFill"
                width="160rpx"
                height="160rpx"
                radius="24rpx"
                :showMenuByLongpress="false"
              />
              <view class="item-content">
                <view>
                  <view class="title">
                    <div class="title-left">{{ item.match_type_name }}</div>
                    <div class="title-right">{{ item.name }}</div></view
                  >
                  <text class="time"
                    >比赛时间：{{ item.hold_start_time }}至{{
                      item.hold_end_time
                    }}</text
                  >
                </view>
                <view class="item-right">
                  <view
                    class="status-tag"
                    :class="{
                      registering: item.sign_up_status === 1,
                      'in-progress': item.sign_up_status === 'x',
                      ended:
                        item.sign_up_status === 2 ||
                        item.sign_up_status === 5 ||
                        item.status === 5
                    }"
                  >
                    {{
                      getSignStatus(item.sign_up_status, item.status) ||
                      "已完赛"
                    }}
                  </view>
                </view>
              </view>
            </view>
          </view>
          <u-loadmore
            :status="status"
            lineColor="#DADADA"
            line
            color="#999999"
            :fontSize="'26rpx'"
            :loadmoreText="loadmoreText"
            :marginTop="'70rpx'"
          />
        </scroll-view>
        <view
          style="
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
          "
          v-else
        >
          <loading-animation
            :width="300"
            :characterSize="260"
            :textSize="32"
            v-if="status === 'loading'"
          />
          <u-empty
            text="暂无赛事~"
            marginTop="-42rpx"
            :width="'163rpx'"
            textColor="#999999"
            :height="'230rpx'"
            icon="https://tg-prod.oss-cn-beijing.aliyuncs.com/d02814d0-bfd3-48bf-8d9d-80cda362b89a.webp"
            v-else
          ></u-empty>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { getMyMatchList } from "@/services/student/match";
import LoadingAnimation from "@/components/common/LoadingAnimation.vue";
export default {
  components: {
    LoadingAnimation
  },
  name: "CompetitionRegister",
  data() {
    return {
      status: "loading",
      list: [],
      listParams: {
        page: 1,
        page_size: 7
      },
      total: 0,
      loadmoreText: "上拉加载更多",
      student_id: "",
      isRefreshing: false
    };
  },
  methods: {
    //  状态 1 已报名 2 已取消
    getSignStatus(sign_up_status, status) {
      if (status === 5) {
        return "已完赛";
      }
      return {
        1: "已报名",
        2: "已取消"
      }[sign_up_status];
    },
    async getCompetitionList() {
      this.status = "loading";
      // TODO: 调用获取赛事列表的API
      const { code, data } = await getMyMatchList({
        ...this.listParams,
        student_id: this.student_id
      });
      if (code === 0) {
        if (this.listParams.page === 1) {
          this.list = data.results;
        } else {
          for (let i = 0; i < data.results.length; i++) {
            this.list.push(data.results[i]);
          }
        }
        this.total = data.count;
        if (this.list.length >= this.total) {
          this.status = "nomore";
        } else {
          this.status = "loadmore";
        }
      }
    },
    scrolltolower() {
      if (this.list.length >= this.total) {
        this.status = "nomore";
        return;
      }
      this.listParams.page++;
      this.getCompetitionList();
    },
    async onRefresh() {
      this.isRefreshing = true;
      this.listParams.page = 1;
      this.list = [];
      await this.getCompetitionList();
      setTimeout(() => {
        this.isRefreshing = false;
      }, 500);
    },
    handleItemClick(item) {
      uni.navigateTo({
        url: `/pages/student/subpages/competition/detail?id=${item.id}&status=my&list_status=${item.sign_up_status}&sign_up_id=${item.sign_up_id}`
      });
    },
    handleRegisterClick(item) {
      uni.navigateTo({
        url: `/pages/student/subpages/competition/register?id=${item.id}`
      });
    }
  },
  onLoad(options) {
    // 从路由参数或者缓存中获取 student_id
    const curStudentInfo = uni.getStorageSync("curStudentInfo");
    const session = uni.getStorageSync("session");
    this.student_id =
      session.role === "student"
        ? curStudentInfo.student_id
        : session.role === "customer"
        ? curStudentInfo.customer_id
        : session.open_id;
    if (!this.student_id) {
      uni.showToast({
        title: "获取学员信息失败",
        icon: "none",
        duration: 2000
      });
    }
  },
  onShow() {
    this.listParams.page = 1;
    this.getCompetitionList();
  }
};
</script>

<style lang="scss" scoped>
.competition-page {
  min-height: 100vh;
  height: 100vh;
  background-color: #f5f5f5;
}

.competition-list {
  // height: calc(100vh - 224rpx);
  height: 100%;
  overflow: auto;
  display: flex;
  flex-direction: column;
  padding: 30rpx 32rpx;
  // margin-top: 186rpx;
  .list-content {
    flex: 1;
    height: 100%;
  }

  .competition-item {
    width: 686rpx;
    height: 200rpx;
    border-radius: 24rpx;
    background: #fff;
    box-shadow: 0px 0px 50rpx 0px rgba(124, 143, 166, 0.1);
    padding: 20rpx;
    display: flex;
    margin-bottom: 30rpx;
    position: relative;
    align-items: center;
    .item-icon {
      width: 160rpx;
      height: 160rpx;
    }
    .item-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      margin-left: 24rpx;
      justify-content: space-between;
      height: 100%;
      .title {
        color: #333;
        font-size: 30rpx;
        font-style: normal;
        font-weight: 500;
        line-height: normal;
        display: flex;
        align-items: center;
        width: 100%;
        .title-left {
          color: #ff7300;
          font-size: 22rpx;
          font-style: normal;
          font-weight: 400;
          line-height: 28rpx; /* 127.273% */
          border: 1rpx solid #ff7300;
          border-radius: 4rpx;
          padding: 1rpx 4rpx 0 4rpx;
          margin-right: 12rpx;
          width: 80rpx;
          text-align: center;
          flex-shrink: 0;
        }
        .title-right {
          flex: 1;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          display: block;
          width: 355rpx;
        }
      }
      .time {
        color: #666;
        font-size: 26rpx;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
        // margin-bottom: 36rpx;
        margin-top: 12rpx;
      }
    }

    .item-right {
      .status-tag {
        height: 54rpx;
        color: #ffbf0d;
        font-size: 28rpx;
        font-style: normal;
        font-weight: 500;
        line-height: 32rpx;
        display: table-cell;
        vertical-align: bottom;
        &.registering {
          //   background: linear-gradient(90deg, #ffbf0d 0%, #ffcb3c 100%);
          //   color: #ffffff;
          //   box-shadow: 0px -4px 8px 0px #eaac00 inset;
          color: #ffbf0d;
        }

        &.in-progress {
          //   background: linear-gradient(
          //     90deg,
          //     rgba(255, 191, 13, 0.1) 0%,
          //     rgba(255, 203, 60, 0.1) 100%
          //   );
          color: #999999;
        }

        &.ended {
          color: #999999;
        }
      }
    }
  }
}
.bottom-btn {
  display: flex;
  width: 750rpx;
  height: 168rpx;
  //   padding: 142rpx 241rpx 16rpx 241rpx;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
  background-color: #fff;
  .register-btn {
    width: 686rpx;
    height: 100rpx;
    border-radius: 71px;
    background: linear-gradient(15deg, #ffbf0d 18.1%, #ffcb3c 83.29%);
    box-shadow: 0px -10px 18px 0px #f3b300 inset,
      0px 4px 10px 0px rgba(254, 197, 36, 0.47);
    color: #fff;
    text-align: center;
    font-size: 36rpx;
    font-style: normal;
    font-weight: 600;
    line-height: 40rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
.nav-letf {
  width: 40rpx;
  height: 40rpx;
  image {
    width: 100%;
    height: 100%;
  }
}
</style>
