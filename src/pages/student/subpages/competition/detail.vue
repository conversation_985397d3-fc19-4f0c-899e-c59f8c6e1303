<template>
  <view class="competition-detail">
    <!-- <u-navbar
      title="赛事报名"
      bgColor="#fff"
      leftIconSize="25px"
      leftIconColor="#333333"
      :titleStyle="{
        color: '#333333',
        fontSize: '34rpx',
        fontWeight: '500',
        lineHeight: '40rpx'
      }"
      :autoBack="true"
      placeholder
    >
      <view class="nav-letf" slot="left">
        <image
          src="https://tg-prod.oss-cn-beijing.aliyuncs.com/e9ac0f4b-6282-4bac-b602-189057c45c90.webp"
        ></image>
      </view>
    </u-navbar> -->
    <u-toast ref="uToast" style="z-index: 999999 !important"></u-toast>
    <!-- 背景图 -->
    <!-- <image
      class="bg-image"
      src="https://tg-prod.oss-cn-beijing.aliyuncs.com/d0d7aedb-fedc-4d44-bfc9-718c85b8eade.png"
      mode="aspectFill"
    /> -->
    <view class="detail-bg" :style="{
      backgroundImage: `url(${detail.back_url})`,
      backgroundRepeat: 'no-repeat',
      backgroundSize: '100% 100%'
    }">
      <div style="height: 100%; padding-bottom: 58rpx">
        <!-- 头部信息 -->
        <view class="header">
          <image class="banner-image" :src="detail.title_url + '?x-oss-process=image/resize,h_650,w_750'" mode="aspectFill" />
        </view>
        <view class="title-section">
          <image class="title-image"
            src="https://tg-prod.oss-cn-beijing.aliyuncs.com/5eb4c222-effa-4389-85e9-62208cffec1c.png"
            mode="aspectFit" />
          <view class="title-container">
            <view class="title">
              <text class="text">{{ detail.name || "" }}</text>
            </view>
            <view class="info">
              <view class="info-item">
                <view class="label">
                  <image src="https://tg-prod.oss-cn-beijing.aliyuncs.com/e34a7131-e6d9-4f0d-bb8f-58504d622fa5.png">
                  </image>
                </view>
                <text class="value">{{ detail.sign_up_start_time || "" }}至{{
                  detail.sign_up_end_time || ""
                }}</text>
              </view>
              <view class="info-item">
                <view class="label bs">
                  <image src="https://tg-prod.oss-cn-beijing.aliyuncs.com/7513f6c5-1925-4e1a-abef-e9f99b8f1a23.png">
                  </image>
                </view>
                <text class="value">{{ detail.hold_start_time || "" }}至{{
                  detail.hold_end_time || ""
                }}</text>
              </view>
              <view class="info-item">
                <view class="label ms">
                  <image src="https://tg-prod.oss-cn-beijing.aliyuncs.com/11841698-b125-42fd-af67-d548c6427b54.png">
                  </image>
                </view>
                <!-- <view class="value delt" style="text-overflow: ellipsis;white-space: nowrap;">
                  参赛用户类型：<text
                    v-for="(item, index) in detail.user_types || []"
                    :key="index"
                    >{{ user_types[item]
                    }}{{
                      index !== (detail.user_types || []).length - 1 ? "," : ""
                    }}</text
                  >
                </view> -->
                <view class="value delt">
                  参聂道棋力限制：<text>
                    {{ rank_level_limit[detail.rank_level_limit]
                    }}{{ detail.rank_level }}
                  </text>
                </view>
                <view class="value delt">
                  赛事报名人数限制：<text>
                    {{
                      detail.sign_up_num_limit != 4
                        ? detail.sign_up_num + "人"
                        : "不限制"
                    }}
                  </text>
                </view>
                <view class="value delt" style="margin-bottom: 14rpx" v-if="detail.cancel_sign_up === 1">
                  取消报名时间：<text>
                    {{ detail.cancel_sign_up_start_time }}至{{
                      detail.cancel_sign_up_end_time
                    }}
                  </text>
                </view>
                <view class="value delt" style="margin-bottom: 24rpx" v-else>
                  不允许取消报名
                </view>
                <text class="value delt" style="line-height: 42rpx;">
                  {{ detail.remark || "" }}
                </text>
              </view>
            </view>
          </view>
        </view>
        <!-- 赛事详情 -->
        <view class="title-section" style="margin-top: 30rpx">
          <view class="title-container" style="margin-top: 30rpx">
            <view class="info">
              <view class="info-item">
                <view class="label tx" style="margin-bottom: 0">
                  <image src="https://tg-prod.oss-cn-beijing.aliyuncs.com/28a89fb7-577b-4b06-a81a-c38f9c53e3cb.png">
                  </image>
                </view>
                <view class="title-container" style="margin-top: 0; padding-left: 0">
                  <sign-up-form :class="competitionSuccess ? 'op' : ''" ref="signUpForm" :detail="detail"
                    :studentInfo="studentInfo" :competitionSuccess="competitionSuccess"
                    @showRankPicker="handleShowRankPicker" v-if="studentInfo" />
                </view>
              </view>
            </view>
          </view>
        </view>
        <!-- <view class="section-bot" style="height: 1rpx"></view> -->
      </div>
      <!-- 底部按钮 -->
      <view class="bottom-btn u-safe-area-inset-bottom">
        <view class="register-btn" :class="{ disabled: isDisabled }" @click="handleRegister">
          {{ buttonText || "报名" }}
        </view>
      </view>
    </view>
    <view @touchmove.stop.prevent="">
      <u-picker :show="showRankPicker" :columns="[rankLevels]" @confirm="handleRankConfirm" @cancel="handleRankCancel"
        keyName="label" valueName="value" title="请选择聂道棋力" confirmColor="#333" />
      <!-- <ConfirmPopup v-model="showConfirmPopup" title="确定取消报名？" @confirm="handleBindConfirm"
        @cancel="handleBindCancel" /> -->
      <cancel-reason-popup v-model="showConfirmPopup" @confirm="handleCancelConfirm" :cancelReasonList="cancelReasonList"></cancel-reason-popup>
    </view>
  </view>
</template>

<script>
import {
  getMatchDetail,
  addMatchStudent,
  updateMatchStudentStatus,
  getMatchStudentDetail,
  getDefaultStudentInfo,
  getCancelReasonList
} from "@/services/student/match";
import SignUpForm from "./components/sign-up-form.vue";
import CancelReasonPopup from "./components/CancelReasonPopup.vue";
// import ConfirmPopup from "@/components/ConfirmPopup";
export default {
  components: {
    SignUpForm,
    CancelReasonPopup
    // ConfirmPopup
  },
  name: "CompetitionDetail",
  data() {
    return {
      id: "",
      detail: {},
      curStudentInfo: {},
      session: {},
      competitionSuccess: false,
      showRankPicker: false,
      showCancelReason: false,
      cancelReason: "",
      cancelExplanation: "",
      rankLevels: [
        { label: "N1", value: "N1" },
        { label: "N2", value: "N2" },
        { label: "N3", value: "N3" },
        { label: "N4", value: "N4" },
        { label: "N5", value: "N5" },
        { label: "N6-N8", value: "N6-N8" }
      ],
      studentInfo: null,
      signUpId: null,
      student_id: null,
      pageRoute: "",
      showConfirmPopup: false,
      list_status: "",
      rank_level_limit: {
        1: "大于",
        2: "等于",
        3: "小于",
        4: "不限制"
      },
      user_types: {
        in_school: "在读",
        audition: "试听",
        out_school: "休学",
        temp: "临时",
        customer: "意向",
        tourist: "游客",
        drop_school: "退学"
      },
      cancelReasonList: []
    };
  },
  computed: {
    buttonText() {
      console.log(this.detail);
      //  状态 1 报名开始 2 报名待开始 3 报名结束 4 赛事结束 5 报名人数已达上线 6 已报名 7 未报名
      if (this.detail.sign_status === 6 && !this.pageRoute) {
        return "已报名";
      } else if (this.detail.sign_status === 6) {
        this.getCancelReasonList();
      }
      return {
        1: "报名",
        2: "待开始",
        3: "已结束",
        4: "已完赛",
        5: "报名人数已达上限",
        6: "取消报名",
        8: "已取消"
      }[this.detail.sign_status];
    },
    isDisabled() {
      return (
        console.log(
          this.detail.sign_status,
          this.isCancelTimeInCurrentTime(),
          this.detail.sign_status === 6
        ),
        [2, 3, 4, 5, 8].includes(this.detail.sign_status) ||
        (this.isCancelTimeInCurrentTime() && this.detail.sign_status === 6) ||
        this.buttonText === "已报名"
      );
    }
  },
  async onLoad(options) {
    this.signUpId = options.sign_up_id || "";
    this.competitionSuccess = options.status === "my";
    this.pageRoute = options.status || "";
    this.curStudentInfo = uni.getStorageSync("curStudentInfo");
    this.session = uni.getStorageSync("session");
    this.list_status = options.list_status || "";
    // this.session.role student 学员 customer意向客户 default 游客
    this.student_id =
      this.session.role === "student"
        ? this.curStudentInfo.student_id
        : this.session.role === "customer"
          ? this.curStudentInfo.customer_id
          : this.session.open_id;
    this.id = options.id;
    await this.getDetail();
    console.log(
      "🚀 ~ onLoad ~ this.detail:",
      this.detail.sign_status === 6,
      this.detail.sign_status === 7,
      this.competitionSuccess
    );
    // 当状态为取消报名时 报名信息不可获取
    // if (this.list_status === "2" && this.pageRoute) {
    //   this.competitionSuccess = false;
    // }
    if (
      this.detail.sign_status === 6 ||
      this.detail.sign_status === 7 ||
      this.competitionSuccess
    ) {
      // 当已报名 时 不允许修改报名信息
      if (this.detail.sign_status === 6 || this.detail.sign_status === 7) {
        this.competitionSuccess = true;
      } else {
        this.competitionSuccess = false;
      }
      if (!this.signUpId) {
        this.signUpId = this.detail.sign_up_id;
      }
      // 报名成功后  获取的报名信息
      this.getMatchStudentDetail();
    } else {
      if (this.detail.sign_status === 2) {
        this.competitionSuccess = true;
      } else {
        this.competitionSuccess = false;
      }
      // 未报名  获取的默认信息
      this.getDefaultStudentInfo();
    }
  },
  methods: {
    async getDetail() {
      // TODO: 调用获取详情API
      // const student = {};
      // student.student_id =
      //   this.curStudentInfo.student_id ?? this.session.open_id;
      const { code, data, message } = await getMatchDetail({
        id: this.id,
        student_id: this.student_id
      });
      if (code === 0) {
        this.detail = data;
      } else {
        uni.$u.toast(message);
      }
    },
    async getMatchStudentDetail() {
      const { code, data, message } = await getMatchStudentDetail({
        id: this.signUpId,
        identity:
          this.session.role === "student"
            ? 3
            : this.session.role === "customer"
              ? 2
              : 1
      });
      if (code === 0) {
        this.studentInfo = data;
      } else {
        this.$refs.uToast.show({
          message,
          icon: "none",
          duration: 2000
        });
      }
    },
    async getDefaultStudentInfo() {
      // identity 1游客 2意向 3学员
      // this.session.role student 学员 customer意向客户 default 游客
      const { code, data, message } = await getDefaultStudentInfo({
        student_id: this.student_id,
        identity:
          this.session.role === "student"
            ? 3
            : this.session.role === "customer"
              ? 2
              : 1
      });
      if (code === 0) {
        this.studentInfo = data || {
          name: "",
          mobile: "",
          rank_level: "",
          department_id: "",
          department_name: ""
        };
      } else {
        this.$refs.uToast.show({
          message,
          icon: "none",
          duration: 2000
        });
        this.studentInfo = {
          name: "",
          mobile: "",
          rank_level: "",
          department_id: "",
          department_name: ""
        };
      }
    },
    async handleRegister() {
      const identity =
        this.session.role === "student"
          ? 3
          : this.session.role === "customer"
            ? 2
            : 1;
      console.log(!this.isDisabled, this.detail.sign_status, !this.pageRoute);
      if (!this.isDisabled) {
        // 按钮不是灰色
        if (this.detail.sign_status === 6) {
          // 报名状态为6 显示取消报名弹窗
          this.showConfirmPopup = true;
          return;
        }
      } else {
        // 如果 当前状态是 已报名 并且是从首页列表进入的 不需要提示
        if (this.detail.sign_status === 6 && !this.pageRoute) return;
        // 按钮是灰色
        switch (this.detail.sign_status) {
          case 2:
            uni.showToast({
              title: "报名待开始",
              icon: "none",
              duration: 2000
            });
            return;
          case 3:
            uni.showToast({
              title: "报名已结束",
              icon: "none",
              duration: 2000
            });
            return;
          case 4:
            uni.showToast({
              title: "赛事已结束",
              icon: "none",
              duration: 2000
            });
            return;
          case 5:
            uni.showToast({
              title: "报名人数已达上限",
              icon: "none",
              duration: 2000
            });
            return;
          case 6:
            uni.showToast({
              title: "当前范围内不允许取消报名",
              icon: "none",
              duration: 2000
            });
            return;
          case 8:
            // uni.showToast({
            //   title: "已取消报名",
            //   icon: "none",
            //   duration: 2000
            // });
            return;
        }
      }
      try {
        const formData = await this.$refs.signUpForm.validate();
        if (formData) {
          uni.showLoading({
            title: "提交中..."
          });
        }
        const baseFields = [
          "f_department_ids",
          "f_name",
          "f_mobile",
          "f_rank_level"
        ];
        console.log("🚀 ~ handleRegister ~ formData:", formData);
        const customize_result = Object.entries(formData)
          .filter(([key]) => !baseFields.includes(key))
          .map(([id, value]) => ({
            id,
            value
          }));
        const submitData = {
          name: formData.f_name,
          mobile: formData.f_mobile,
          rank_level: formData.f_rank_level,
          department_id: formData.f_department_ids,
          customize_result,
          match_index: this.detail.match_index,
          student_id: this.student_id,
          identity
        };
        console.log("提交的报名数据：", submitData);
        const { code, message } = await addMatchStudent(submitData);
        if (code === 0) {
          this.$refs.uToast.show({
            message: "报名成功",
            icon: "none",
            duration: 2000
          });
          await this.getDetail();
          this.signUpId = this.detail.sign_up_id;
          // await this.getMatchStudentDetail();
          //   this.$refs.signUpForm.resetForm();
          this.competitionSuccess = true;
          setTimeout(() => {
            if (this.pageRoute) {
              // 后退
              uni.navigateBack();
            } else {
              uni.redirectTo({
                url: `/pages/student/subpages/competition/register`
              });
            }
          }, 500);
        } else {
          this.$refs.uToast.show({
            message,
            icon: "none",
            duration: 2000
          });
        }
        uni.hideLoading({ noCanflict: true });
      } catch (error) {
        uni.hideLoading({ noCanflict: true });
        // 滑动到底部
        uni.pageScrollTo({
          scrollTop: 10000,
          duration: 300
        });
        console.error("表单验证失败:", error);
      }
    },
    handleRankConfirm(e) {
      const selectedRank = e.value[0];
      this.$refs.signUpForm.setRankLevel(selectedRank.value);
      this.showRankPicker = false;
    },
    handleRankCancel() {
      this.showRankPicker = false;
    },
    handleShowRankPicker() {
      this.showRankPicker = true;
    },
    // 取消报名的开始时间 与 结束时间 是否在当前时间段内
    isCancelTimeInCurrentTime() {
      const { cancel_sign_up_start_time, cancel_sign_up_end_time } =
        this.detail;
      if (!cancel_sign_up_start_time || !cancel_sign_up_end_time) return false;

      const now = new Date();
      // 设置开始时间为当天 00:00:00
      const cancelStartTime = new Date(cancel_sign_up_start_time);
      cancelStartTime.setHours(0, 0, 0, 0);

      // 设置结束时间为当天 23:59:59
      const cancelEndTime = new Date(cancel_sign_up_end_time);
      cancelEndTime.setHours(23, 59, 59, 999);
      // 是否允许取消报名
      if (this.detail.cancel_sign_up === 2) {
        return true;
      }
      return !(now >= cancelStartTime && now <= cancelEndTime);
    },
    async handleBindConfirm() {
      uni.showLoading({
        title: "提交中..."
      });
      const { code, message } = await updateMatchStudentStatus({
        sign_up_id: this.signUpId,
        status: 2,
        identity:
          this.session.role === "student"
            ? 3
            : this.session.role === "customer"
              ? 2
              : 1
      });
      if (code === 0) {
        this.competitionSuccess = false;
        this.$refs.uToast.show({
          message: "已取消报名",
          icon: "none",
          duration: 2000
        });
        this.showConfirmPopup = false;
        this.getDetail();
        setTimeout(() => {
          if (this.pageRoute) {
            // 后退
            uni.navigateBack();
          } else {
            uni.redirectTo({
              url: `/pages/student/subpages/competition/register`
            });
          }
        }, 500);
      } else {
        this.$refs.uToast.show({
          message,
          icon: "none",
          duration: 2000
        });
        this.showConfirmPopup = false;
      }
      uni.hideLoading({ noCanflict: true });
      this.showConfirmPopup = false;
    },
    handleBindCancel() {
      this.showConfirmPopup = false;
    },
    // 处理取消报名确认
    handleCancelConfirm(data) {
      this.cancelReason = data.reason;
      this.cancelExplanation = data.explanation;
      this.handleCancelSignUp();
    },
    // 执行取消报名操作
    async handleCancelSignUp() {
      uni.showLoading({
        title: "提交中..."
      });
      const { code, message } = await updateMatchStudentStatus({
        sign_up_id: this.signUpId,
        status: 2,
        identity:
          this.session.role === "student"
            ? 3
            : this.session.role === "customer"
              ? 2
              : 1,
        cancel_reason_id: this.cancelReason,
        cancel_explanation: this.cancelExplanation
      }).catch(err => {
        console.log(err);
        uni.hideLoading({ noCanflict: true });
      });
      if (code === 0) {
        this.competitionSuccess = false;
        this.$refs.uToast.show({
          message: "已取消报名",
          icon: "none",
          duration: 2000
        });
        this.getDetail();
        setTimeout(() => {
          if (this.pageRoute) {
            // 后退
            uni.navigateBack();
          } else {
            uni.redirectTo({
              url: `/pages/student/subpages/competition/register`
            });
          }
        }, 500);
        this.showConfirmPopup = false;
      } else {
        this.$refs.uToast.show({
          message,
          icon: "none",
          duration: 2000
        });
      }
      uni.hideLoading({ noCanflict: true });
    },
    async getCancelReasonList() {
      const { code, data, message } = await getCancelReasonList();
      if (code === 0) {
        this.cancelReasonList = data;
      } else {
        this.$refs.uToast.show({
          message,
          icon: "none",
          duration: 2000
        });
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.competition-detail {
  // min-height: 100vh;
  padding-bottom: 180rpx;
  background: #f5f5f5;
  // padding-top: 176rpx;
  position: relative;

  .detail-bg {
    // margin-top: -176rpx;
    position: relative;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
    // overflow: auto;
  }

  .header {
    position: relative;
    z-index: 1;
    width: 750rpx;
    height: 650rpx;

    .banner-image {
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
      z-index: 1;
    }
  }

  .section-bot {
    width: 686rpx;
    margin: 0 auto;
    fill: #fffee7;
    stroke-width: 6rpx;
    stroke: #ffdeb1;
    // background: #fffee7;
    border-radius: 24rpx;
    // border: 6rpx solid #ffdeb1;
    position: relative;
    top: 424rpx;
    z-index: 3;
  }

  .title-section {
    width: 686rpx;
    margin: 0 auto;
    fill: #fffee7;
    stroke-width: 6rpx;
    stroke: #ffdeb1;
    background: #fffee7;
    border-radius: 24rpx;
    border: 6rpx solid #ffdeb1;
    position: relative;
    margin-top: -250rpx;
    z-index: 3;

    .title-image {
      position: absolute;
      top: -26rpx;
      left: 189.5rpx;
      width: 300rpx;
      height: 80rpx;
    }

    .title-container {
      position: relative;
      margin-top: 87.96rpx;
      padding: 0rpx 30rpx 0rpx 30rpx;
    }

    .title {
      margin-bottom: 30.57rpx;

      .text {
        flex: 1;
        font-size: 34rpx;
        font-weight: 700;
        line-height: 43rpx;
        color: #9e4732;
      }
    }

    .info {
      .info-item {
        margin-bottom: 30.5rpx;

        // &:last-child {
        //   margin-bottom: 0;
        // }

        .label {
          margin-bottom: 16.57rpx;
          width: 206rpx;
          height: 51rpx;

          image {
            width: 100%;
            height: 100%;
          }
        }

        .bm {
          // background: url(https://tg-prod.oss-cn-beijing.aliyuncs.com/b42725e1-3493-4ced-a7b1-4ce931c4b947.webp)
          //   no-repeat center center;
          // background-size: 100% 100%;
        }

        .bs {
          // background: url(https://tg-prod.oss-cn-beijing.aliyuncs.com/2229b6c1-62a7-4158-b9c8-dd16d39a69cf.webp)
          //   no-repeat center center;
          // background-size: 100% 100%;
        }

        .ms {
          // background: url(https://tg-prod.oss-cn-beijing.aliyuncs.com/a2f9c483-9e5d-499d-bf04-64384f913c21.webp)
          //   no-repeat center center;
          // background-size: 100% 100%;
        }

        .tx {
          // background: url(https://tg-prod.oss-cn-beijing.aliyuncs.com/cff9288d-2ef6-47fc-9f41-e3be1458256d.webp)
          //   no-repeat center center;
          // background-size: 100% 100%;
        }

        .value {
          color: #a16007;
          font-size: 30rpx;
          font-weight: 500;
          line-height: 40rpx;

          text {
            color: #ff522a;
            font-size: 28rpx;
            font-style: normal;
            font-weight: 400;
            line-height: 34rpx;
            // letter-spacing: 0.2rpx;
          }
        }

        .delt {
          font-weight: 400 !important;
          line-height: 60rpx;
        }
      }
    }
  }

  .detail-section {
    position: relative;
    z-index: 4;
    margin: 0 32rpx;
    padding: 40rpx 32rpx;
    background: #ffffff;
    border-radius: 24rpx;
    margin-bottom: 168rpx;

    .section-title {
      font-size: 32rpx;
      font-weight: 500;
      color: #333;
      margin-bottom: 32rpx;
    }

    .content {
      color: #666;
      font-size: 28rpx;
      line-height: 1.6;
    }
  }

  .bottom-btn {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    // height: 180rpx;
    background: #ffffff;
    display: flex;
    // align-items: center;
    justify-content: center;
    z-index: 4;
    transition: transform 0.2s ease;

    &:active {
      transform: scale(1.05);
    }

    .register-btn {
      width: 686rpx;
      height: 92rpx;
      border-radius: 71rpx;
      background: linear-gradient(15deg, #ffbf0d 18.1%, #ffcb3c 83.29%);
      box-shadow: 0px -10px 18px 0px #f3b300 inset,
        0px 4px 10px 0px rgba(254, 197, 36, 0.47);
      color: #ffffff;
      font-size: 32rpx;
      font-weight: 600;
      text-align: center;
      line-height: 100rpx;
      border: none;
      margin: 18rpx 0;

      // margin-bottom: 70rpx;
      &.disabled {
        // background: linear-gradient(15deg, #cecece 18.1%, #cecece 83.29%);
        opacity: 0.5;
        // box-shadow: 0px -4px 8px 0px #a2a2a2 inset;
      }
    }
  }

  .form-section {
    padding: 20rpx;

    ::v-deep .u-form-item {
      margin-bottom: 20rpx;

      &__body__left__content {
        padding-right: 10rpx;
      }
    }
  }
}

::v-deep .u-navbar__content {
  z-index: 9;
}

.nav-letf {
  width: 40rpx;
  height: 40rpx;

  image {
    width: 100%;
    height: 100%;
  }
}

::v-deep .u-popup__content {
  border-radius: 22rpx;
}

::v-deep .u-toolbar__title {
  font-weight: 500;
  font-size: 32rpx;
  line-height: 100%;
  letter-spacing: 0rpx;
  text-align: center;
  color: #333;
}

::v-deep .u-picker__view__column__item {
  font-weight: 500 !important;
  font-size: 32rpx;
}

::v-deep .u-toolbar {
  margin-top: 36rpx;
  height: 48rpx !important;
}
</style>
