<template>
  <view class="feedback-detail">
    <!-- 处理进度时间线 -->
    <view class="timeline-container">
      <view class="timeline">
        <view v-for="(item, index) in timelineData" :key="index" class="timeline-item" :class="{
          'timeline-item-active': item.reply || index === 2
        }">
          <!-- 时间点 -->
          <view class="timeline-dot-container">
            <view class="timeline-dot" :class="{
              'timeline-dot-active': index === 0 || item.reply || index === 2
            }"></view>
            <!-- 虚线 -->
            <view v-if="index !== timelineData.length - 1" :class="[
              'timeline-line',
              { 'timeline-line-gray': index === 0 && !timelineData[1].reply }
            ]"></view>
          </view>

          <!-- 内容 -->
          <view class="timeline-content">
            <view class="timeline-header">
              <view class="timeline-title" :style="(index === 1 && !item.reply) ? 'color:#999' : ''">{{
                item.title }}</view>
              <view v-if="item.date" class="timeline-date">{{
                $u.timeFormat(item.date, "yyyy-mm-dd hh:MM:ss")
              }}</view>
            </view>
            <view v-if="index === 0" class="user-content">
              <view class="user-content-item">
                <span class="user-content-label">反馈校区</span>
                <span class="user-content-value">{{
                  item.department_name
                }}</span>
              </view>
              <view class="user-content-item">
                <span class="user-content-label">反馈类型</span>
                <span class="user-content-value">{{ item.category_name }}</span>
              </view>
              <view class="user-content-item">
                <span class="user-content-label">反馈内容</span>
                <span class="user-content-value">{{ item.description }}</span>
              </view>
              <view v-if="item.images.length > 0" class="user-content-images">
                <u-album multipleSize="82rpx" rowCount="6" space="10rpx" :urls="item.images"></u-album>
              </view>
            </view>
            <view v-if="item.reply" class="reply-content">
              <view class="reply-content-desc">
                <text>{{ item.reply }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { getFeedbackDetail } from "@/services/student/userFeedback.js";
export default {
  name: "FeedbackDetail",
  data() {
    return {
      // 时间线数据
      timelineData: [
        {
          title: "",
          date: "",
          description: "",
          images: [],
          department_name: "",
          category_name: "",
          reply: ""
        },
        {
          title: "",
          date: "",
          reply: ""
        }
      ],
      order_no: ""
    };
  },
  onLoad(options) {
    this.order_no = options.order_no;
    this.getFeedbackDetail();
  },
  methods: {
    // 可以添加处理方法
    async getFeedbackDetail() {
      try {
        const res = await getFeedbackDetail({
          order_no: this.order_no
        });
        if (res.code === 0) {
          this.timelineData[0].title = "提交反馈";
          this.timelineData[0].images = res.data.feedback_attachment;
          this.timelineData[0].description = res.data.feedback;
          this.timelineData[0].date = res.data.created_at;
          this.timelineData[0].department_name = res.data.department_name;
          this.timelineData[0].category_name = res.data.category_name;

          this.timelineData[1].title = res.data.reply ? "答复内容" : "受理中";
          this.timelineData[1].reply = res.data.reply;
          this.timelineData[1].date = res.data.reply ? res.data.reply_time : "";
          if (res.data.reply) {
            this.$set(this.timelineData, 2, {
              title: "受理完成"
            });
          }
        } else {
          uni.showToast({
            title: res?.message || "获取反馈详情失败",
            icon: "none",
            duration: 2000
          });
        }
      } catch (error) {
        console.error(error);
        uni.showToast({
          title: "获取反馈详情失败",
          icon: "none",
          duration: 2000
        });
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.feedback-detail {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 28rpx;
}

// 卡片样式
.feedback-card {
  background: #ffffff;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0px 0px 50px 0px rgba(168, 150, 118, 0.1);

  &-date {
    font-size: 24rpx;
    color: #999999;
  }

  &-item {
    display: flex;
    margin-bottom: 12rpx;
    padding-left: 88rpx;

    &:last-child {
      margin-bottom: 0;
    }

    .item-label {
      font-size: 28rpx;
      color: #999999;
      width: 140rpx;
      flex-shrink: 0;
    }

    .item-value {
      font-size: 28rpx;
      color: #333333;
      flex: 1;
    }
  }
}

// 时间线容器
.timeline-container {
  background: #ffffff;
  border-radius: 24rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
  box-shadow: 0px 0px 50px 0px rgba(168, 150, 118, 0.1);
  padding-top: 44rpx;
}

// 时间线样式
.timeline {
  position: relative;
}

.timeline-item {
  display: flex;
  position: relative;
  margin-bottom: 40rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.timeline-dot-container {
  position: relative;
  width: 30rpx;
  margin-right: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.timeline-dot {
  width: 24rpx;
  height: 24rpx;
  border-radius: 50%;
  background-color: #fff;
  border: 8rpx solid #ddd;
  box-sizing: border-box;
  z-index: 2;

  &-active {
    border: 8rpx solid #ffc525;
  }
}

.timeline-line {
  position: absolute;
  top: 20rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 2rpx;
  height: calc(100% + 40rpx);
  border-left: 2rpx dashed #ffc525; // 虚线
  z-index: 1;

  &.timeline-line-gray {
    border-left: 2rpx dashed #ddd;
  }
}

.timeline-content {
  flex: 1;
  margin-top: -10rpx;
  width: 94%;
}

.user-content {
  margin-top: 4rpx;
  background-color: #fafafa;
  padding: 24rpx;
  border-radius: 24rpx;
}

.reply-content {
  margin-top: 20rpx;
  background-color: #fafafa;
  padding: 24rpx;
  border-radius: 24rpx;
  width: 100%;

  .reply-content-desc {
    font-size: 28rpx;
    color: #666;
    line-height: 42rpx;
    word-break: break-all;
  }
}

.user-content-item {
  display: flex;
  margin-bottom: 10rpx;
  word-break: break-all;
}

.user-content-label {
  font-size: 28rpx;
  color: #999999;
  width: 140rpx;
  flex-shrink: 0;
}

.user-content-value {
  font-size: 28rpx;
  color: #333333;
  flex: 1;
}

.timeline-header {
  display: flex;
  flex-direction: column;

  margin-bottom: 10rpx;
}

.timeline-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  line-height: 38rpx;
}

.timeline-date {
  font-size: 24rpx;
  color: #999;
  line-height: 36rpx;
}

.timeline-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

// 活跃状态
.timeline-item-active {
  .timeline-title {
    // font-weight: 600;
  }

  .timeline-desc {
    color: #333;
  }
}
</style>
<style lang="scss">
.feedback-detail .u-album__row {
  //   justify-content: space-between;
}

.feedback-detail .u-album__row__wrapper {
  border-radius: 8rpx;
  overflow: hidden;
}

::v-deep .u-album__row__wrapper image {
  height: 82rpx !important;
  width: 82rpx !important;
  border-radius: 8rpx;
}
</style>
