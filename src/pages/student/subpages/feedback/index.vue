<template>
  <view class="feedback">
    <!-- 页面内容 -->
    <view class="feedback-content">
      <!-- 说明文字 -->
      <view class="feedback-desc">
        <view class="feedback-desc-text">
          感谢您对聂卫平围棋道场的信任与支持!为持续优化服务与课程体验，如您在学员学习或使用过程中有任何意见或建议，均可通过此入口提交反馈。您的每一条信息将直达总部专项团队，我们承诺在48小时内响应并跟进处理。期待您的宝贵建议，共同助力孩子成长!
        </view>
      </view>
      <!-- 分组表单项容器 -->
      <view class="feedback-form-item">
        <!-- 当前校区 -->
        <view class="feedback-form-group-item school-name">
          <view class="feedback-form-item-title">当前校区</view>
          <text>{{ curStudentInfo.department_name }}</text>
        </view>

        <!-- 分隔线 -->
        <view class="feedback-form-divider"></view>

        <!-- 反馈类型 -->
        <view class="feedback-form-group-item feedback-type" id="feedback-type">
          <view class="feedback-form-item-title"
            >反馈类型<span>*</span>
            <span v-if="showFeedbackTypeError" class="error-text"
              >请选择反馈类型</span
            >
          </view>
          <view
            class="feedback-form-item-field"
            @click="openFeedbackTypeSelector"
          >
            <text
              class="feedback-form-item-field-text"
              :class="{
                'field-placeholder': !selectedFeedbackType
              }"
              >{{ selectedFeedbackType || "请选择反馈类型" }}</text
            >
            <view class="feedback-form-item-arrow"></view>
          </view>
          <view v-if="selectFeedbackObj.scene" class="feedback-form-tip"
            >适用场景：{{ selectFeedbackObj.scene }}</view
          >
        </view>
        <view @touchmove.stop.prevent="">
          <u-action-sheet
            :actions="feedbackTypes"
            :cancelText="'取消'"
            @select="selectClick"
            @close="cancelClick"
            :show="showTypeSelector"
            round="20rpx"
          ></u-action-sheet>
        </view>
        <!-- 分隔线 -->
        <view class="feedback-form-divider"></view>

        <!-- 匿名提交 -->
        <view
          class="feedback-form-group-item anonymous-submit"
          id="anonymous-submit"
          :style="isAnonymous ? 'padding-bottom: 0rpx' : ''"
        >
          <view class="feedback-form-item-title">匿名提交<span>*</span></view>
          <view class="feedback-form-item-radio-group">
            <view
              class="feedback-form-item-radio"
              @click="handleAnonymous(false)"
            >
              <view
                class="radio-icon"
                :class="{ 'radio-selected': isAnonymous === false }"
              ></view>
              <text>否</text>
            </view>
            <view
              class="feedback-form-item-radio"
              @click="handleAnonymous(true)"
              style="margin-right: 0rpx"
            >
              <view
                class="radio-icon"
                :class="{ 'radio-selected': isAnonymous }"
              ></view>
              <text>是</text>
            </view>
          </view>
        </view>
        <view v-if="anonymousError" class="feedback-form-error"
          >请选择是否匿名提交</view
        >

        <!-- 分隔线 -->
        <view class="feedback-form-divider" v-if="!isAnonymous"></view>

        <!-- 联系方式 -->
        <view
          v-if="!isAnonymous"
          style="padding-bottom: 0rpx"
          class="feedback-form-group-item"
        >
          <view class="feedback-form-item-title"
            >联系方式<span>*</span
            ><span
              v-if="showContactPhoneError"
              class="error-text"
              style="color: #bababa"
              >请输入联系方式</span
            ></view
          >
          <input
            v-model="contactPhone"
            class="feedback-input"
            :class="{ placeholder: !contactPhone }"
            placeholder="请输入手机号"
            type="number"
            maxlength="11"
            @input="handleContactPhoneInput"
          />
        </view>
      </view>
      <!-- 反馈内容输入 -->
      <view class="feedback-form-item" id="feedback-content">
        <view class="feedback-form-item-title"
          >请输入您的意见或建议<span>*</span></view
        >
        <view class="feedback-form-item-textarea">
          <textarea
            v-model="feedbackContent"
            class="feedback-textarea"
            :class="{ placeholder: !feedbackContent }"
            placeholderStyle="color:#C7C7C7"
            :placeholder="
              selectFeedbackObj.example
                ? `示例问题：${selectFeedbackObj.example}`
                : '请输入内容'
            "
            @input="handleTextareaInput"
          ></textarea>
          <view
            class="feedback-textarea-counter"
            :class="{ 'counter-error': textareaError }"
            >{{ textLength }}/100
          </view>
        </view>
        <view
          v-if="showTextareaError"
          style="margin-top: 10rpx; margin-left: 0"
          class="error-text"
        >
          <view v-if="textLength > 100">
            输入内容不能超过100个字哦，当前为{{ textLength }}
          </view>
          <view v-else> 请输入您的意见或建议 </view>
        </view>
      </view>

      <!-- 上传图片 -->
      <view
        class="feedback-form-item"
        id="feedback-upload"
        style="max-height: 456rpx"
      >
        <view class="feedback-form-item-header">
          <text class="feedback-form-item-title">上传图片</text>
          <span class="feedback-form-item-subtitle"
            >(不超过<span style="color: #fe4f37">{{ maxUploadCount }}</span
            >张)</span
          >
        </view>
        <view class="feedback-upload-area">
          <image-uploader
            v-model="uploadedImages"
            :max-count="maxUploadCount"
            @error="errorUploadAlert"
            @select="handleImageSelect"
            @success="handleImageSuccess"
            @delete="handleImageDelete"
          />
        </view>
      </view>

      <!-- 提交按钮 -->
      <view
        class="feedback-submit"
        @tap="$u.throttle(handleSubmit, 500)"
        :class="{ disabled: disabledSubmit }"
      >
        提交
      </view>
      <view @click="handleFeedbackList" class="feedback-submit-text"
        >我的反馈</view
      >
    </view>

    <!-- 弹窗组件 -->
    <view @touchmove.stop.prevent="">
      <alert
        :visible="alertVisible"
        :content="alertContent"
        :button-text="alertButtonText"
        @confirm="handleAlertConfirm"
        @close="handleAlertClose"
      />
      <confirm
        :visible="confirmVisible"
        :content="confirmContent"
        :confirm-text="confirmButtonText"
        :cancel-text="cancelButtonText"
        @confirm="handleConfirm"
        @close="handleClose"
      />
    </view>
  </view>
</template>

<script>
import Alert from "@/components/alert/index.vue";
import Confirm from "@/components/confirm/index.vue";
import ImageUploader from "@/components/image-uploader/index.vue";
import {
  getFeedbackCategories,
  createFeedback
} from "@/services/student/userFeedback.js";
export default {
  name: "FeedbackIndex",
  components: {
    Alert,
    Confirm,
    ImageUploader
  },
  data() {
    return {
      showFeedbackTypeError: false,
      showContactPhoneError: false,
      showTextareaError: false,
      // 弹窗相关
      alertVisible: false,
      alertContent: "",
      alertButtonText: "我知道了",
      confirmVisible: false,
      confirmContent: "",
      confirmButtonText: "确定",
      cancelButtonText: "取消",

      // 表单数据
      feedbackContent: "",
      uploadedImages: [],
      selectedFeedbackType: "",
      selectFeedbackObj: {},
      schoolName: "刘家窑校区", // 默认校区
      isAnonymous: false, // 是否匿名：null未选择，true是，false否
      contactPhone: "",

      // 表单验证
      textareaError: false,
      feedbackTypeError: false,
      anonymousError: false,

      // 选择器相关
      feedbackTypes: [],
      showTypeSelector: false,
      selectIndex: null,
      curStudentInfo: {
        department_name: ""
      },
      maxUploadCount: 6
    };
  },
  computed: {
    textLength() {
      return this.feedbackContent.length;
    },
    disabledSubmit() {
      return (
        !this.selectIndex ||
        (!this.isAnonymous && !this.contactPhone) ||
        !this.feedbackContent ||
        !this.feedbackContent
      );
    }
  },

  created() {
    this.curStudentInfo = uni.getStorageSync("curStudentInfo");
    this.getFeedbackType();
    this.Oss.getAliyunOfStudent();
    uni.hideShareMenu();
  },
  methods: {
    // 获取反馈类型
    async getFeedbackType() {
      const res = await getFeedbackCategories({
        user_id: this.curStudentInfo.student_id
      });
      if (res.code === 0) {
        this.feedbackTypes = res?.data ?? [];
      }
    },
    // 我的反馈
    handleFeedbackList() {
      uni.navigateTo({
        url: "/pages/student/subpages/feedback/list"
      });
    },
    // 导航栏相关
    handleBack() {
      uni.navigateBack();
    },

    // 表单相关
    handleContactPhoneInput() {
      this.showContactPhoneError = !this.contactPhone;
    },
    handleTextareaInput() {
      this.textareaError = this.feedbackContent.length > 100;
      this.showTextareaError = !this.feedbackContent;
    },

    // 图片选择事件
    handleImageSelect(res) {
      console.log("图片选择：", res);
    },
    errorUploadAlert(err) {
      this.showAlert(err);
    },
    // 图片上传成功事件
    handleImageSuccess(url) {
      console.log("图片上传成功：", url);
    },

    // 图片删除事件
    handleImageDelete(image, index) {
      console.log("删除图片：", image, index);
    },

    // 反馈类型选择器
    openFeedbackTypeSelector() {
      this.showTypeSelector = true;
    },
    cancelClick() {
      this.showTypeSelector = false;
    },

    selectClick(obj) {
      console.log("selectClick", obj);
      this.selectIndex = obj.id;
      this.selectFeedbackObj = obj;
      this.selectedFeedbackType = obj.name;
      this.showTypeSelector = false;
      this.showFeedbackTypeError = false;
    },

    // 匿名选择
    handleAnonymous(value) {
      this.isAnonymous = value;
      this.anonymousError = false;
    },
    // 手机号校验
    checkPhone() {
      if (this.contactPhone) {
        if (!/^(?:(?:\+|00)86)?1\d{10}$/.test(this.contactPhone)) {
          uni.showToast({
            title: "请输入正确的手机号",
            icon: "none",
            duration: 2000
          });
          return false;
        }
      }
      return true;
    },

    // 表单提交
    async handleSubmit() {
      // 表单校验通过，准备提交数据
      const formData = {
        category_id: this.selectIndex,
        feedback: this.feedbackContent,
        feedback_attachment: this.uploadedImages,
        is_anonymous: this.isAnonymous ? 1 : 2,
        user_id: this.curStudentInfo.student_id
      };
      if (!formData.category_id) {
        uni.showToast({
          title: "请选择反馈类型",
          icon: "none",
          duration: 2000
        });
        this.showFeedbackTypeError = true;
        // const feedbackType = uni.createSelectorQuery().select("#feedback-type");
        // feedbackType.boundingClientRect((data) => {
        //   uni.pageScrollTo({
        //     scrollTop: data.top,
        //     duration: 10,
        //     success: function () {

        //     }
        //   });
        // }).exec();
        return;
      }
      if (!this.isAnonymous) {
        formData.contact_phone = this.contactPhone;
        if (!this.contactPhone) {
          uni.showToast({
            title: "请输入联系方式",
            icon: "none",
            duration: 2000
          });
          this.showContactPhoneError = true;
        }
        if (!this.checkPhone()) {
          return;
        }
      }
      // 反馈内容校验
      if (!this.feedbackContent) {
        uni.showToast({
          title: "请输入反馈内容",
          icon: "none",
          duration: 2000
        });
        this.showTextareaError = true;
        return;
      }
      if (this.feedbackContent.length > 100) {
        uni.showToast({
          title: "反馈内容不能超过100字符",
          icon: "none",
          duration: 2000
        });
        this.showTextareaError = true;
        return;
      }
      try {
        const res = await createFeedback(formData);
        if (res.code === 0) {
          uni.showToast({
            title: "提交成功",
            icon: "success",
            duration: 1000
          });
          setTimeout(() => {
            uni.navigateTo({
              url: "/pages/student/subpages/feedback/list"
            });
          }, 1000);
        } else {
          uni.showToast({
            title: res?.message || "提交失败",
            icon: "none",
            duration: 2000
          });
        }
      } catch (error) {
        uni.showToast({
          title: "提交失败，请稍后再试！",
          icon: "none",
          duration: 2000
        });
      } finally {
        // 重置表单
        this.resetForm();
      }
    },

    resetForm() {
      this.feedbackContent = "";
      this.uploadedImages = [];
      this.selectedFeedbackType = "";
      this.isAnonymous = false;
      this.contactPhone = "";
      this.selectIndex = null;
      this.selectFeedbackObj = {};
      this.showFeedbackTypeError = false;
      this.showContactPhoneError = false;
      this.showTextareaError = false;
    },

    // 弹窗相关
    showAlert(content) {
      this.alertContent = content;
      this.alertVisible = true;
    },

    handleAlertConfirm() {
      this.alertVisible = false;
    },

    handleAlertClose() {
      this.alertVisible = false;
    },

    showConfirm(content, confirmText = "确定", cancelText = "取消") {
      this.confirmContent = content;
      this.confirmButtonText = confirmText;
      this.cancelButtonText = cancelText;
      this.confirmVisible = true;
    },

    handleConfirm() {
      this.confirmVisible = false;
    },

    handleClose() {
      this.confirmVisible = false;
    }
  },
  beforeUnmount() {
    this.alertVisible = false;
    this.confirmVisible = false;
  }
};
</script>

<style lang="scss" scoped>
.feedback {
  width: 100%;
  min-height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;

  // 页面内容
  &-content {
    padding: 32rpx;
    flex: 1;
  }

  // 说明文字
  &-desc {
    width: 100%;
    background-color: #fff;
    border-radius: 24rpx;
    padding: 30rpx 24rpx;
    margin-bottom: 30rpx;

    &-text {
      font-size: 28rpx;
      color: #333;
      line-height: 1.5;
      text-indent: 2em;
    }
  }

  // 表单项
  &-form-item {
    width: 100%;
    background-color: #fff;
    border-radius: 24rpx;
    padding: 30rpx 24rpx;
    margin-bottom: 30rpx;

    &-header {
      display: flex;
      align-items: center;
      margin-bottom: 30rpx;
    }

    &-title {
      font-size: 28rpx;
      font-weight: 500;
      color: #333;

      span {
        color: #fe4f37;
      }
    }

    &-subtitle {
      font-size: 26rpx;
      color: #999;
      margin-left: 10rpx;
    }

    &-field {
      width: 100%;
      height: 80rpx;
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-bottom: 1rpx solid #eee;
      color: #333;

      .feedback-form-item-field-text {
        color: #333;
      }

      .field-placeholder {
        color: #bababa;
      }
    }

    &-arrow {
      width: 16rpx;
      height: 16rpx;
      border-top: 2rpx solid #999;
      border-right: 2rpx solid #999;
      transform: rotate(135deg);
      font-weight: bold;
    }

    &-radio-group {
      display: flex;
      align-items: center;
    }

    &-radio {
      display: flex;
      align-items: center;
      margin-right: 60rpx;

      text {
        font-size: 28rpx;
        color: #333;
        margin-left: 10rpx;
      }
    }

    .error-text {
      font-size: 24rpx;
      color: #fe4f37;
      margin-left: 20rpx;
      font-weight: 400;
    }
  }

  // 文本框
  &-form-item-textarea {
    width: 100%;
    background-color: #f5f5f5;
    border: 1rpx solid #e9e9e9;
    border-radius: 14rpx;
    padding: 15rpx;
    position: relative;
    margin-top: 20rpx;
  }

  &-textarea {
    width: 100%;
    height: 200rpx;
    font-size: 26rpx;
    color: #333;
    line-height: 1.5;

    &.placeholder {
      color: #bababa;
    }
  }

  &-textarea-counter {
    position: absolute;
    right: 20rpx;
    bottom: 20rpx;
    font-size: 22rpx;
    color: #999;

    &.counter-error {
      color: #fe4f37;
    }
  }

  // 表单错误提示
  &-form-error {
    font-size: 24rpx;
    color: #fe4f37;
    text-align: center;
    margin-top: 10rpx;
  }

  // 表单提示文字
  &-form-tip {
    font-size: 24rpx;
    color: #999;
    margin-top: 14rpx;
  }

  // 图片上传
  &-upload-area {
    display: flex;
    flex-wrap: wrap;
    margin-top: 20rpx;
  }

  &-upload-button {
    width: 160rpx;
    height: 160rpx;
    background-color: #fafafa;
    border-radius: 16rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-right: 20rpx;
    margin-bottom: 20rpx;
  }

  &-upload-icon {
    width: 50rpx;
    height: 50rpx;
    background-image: url("https://tg-prod.oss-cn-beijing.aliyuncs.com/0b05b29f-763a-4326-8493-f9223c99e6b0.png");
    background-size: cover;
    margin-bottom: 4rpx;
  }

  &-upload-text {
    font-size: 24rpx;
    color: #999;
  }

  &-image-list {
    display: flex;
    flex-wrap: wrap;
  }

  &-image-item {
    width: 160rpx;
    height: 160rpx;
    border-radius: 16rpx;
    margin-right: 20rpx;
    margin-bottom: 20rpx;
    position: relative;
    // overflow: hidden;
    background-color: #fafafa;
  }

  &-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 16rpx;
    overflow: hidden;
  }

  &-image-delete {
    position: absolute;
    top: -10rpx;
    right: -10rpx;
    width: 32rpx;
    height: 32rpx;
    // background-color: rgba(0, 0, 0, 0.5);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-image: url(https://tg-prod.oss-cn-beijing.aliyuncs.com/7d2b660a-78e4-45ee-9bd7-1450f284e706.png);
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    opacity: 0.8;
  }

  // 输入框
  &-input {
    height: 40rpx;
    font-size: 28rpx;
    color: #333;
    line-height: 80rpx;
    text-align: right;

    &.placeholder {
      color: #bababa;
    }
  }

  // 单选按钮样式
  .radio-icon {
    width: 32rpx;
    height: 32rpx;
    border: 2rpx solid #999;
    border-radius: 50%;
    position: relative;
    box-sizing: border-box;

    &.radio-selected {
      border-color: #ffc525;

      &::after {
        content: "";
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 18rpx;
        height: 18rpx;
        background-color: #ffc525;
        border-radius: 50%;
      }
    }
  }

  // 提交按钮
  .feedback-submit {
    width: 686rpx;
    height: 90rpx;
    background: linear-gradient(15deg, #ffbf0d 18.1%, #ffcb3c 83.29%);
    box-shadow: 0px -10px 18px 0px #f3b300 inset,
      0px 4px 20px 0px rgba(254, 197, 36, 0.47);
    border-radius: 80rpx;
    font-size: 32rpx;
    font-weight: 500;
    color: #fff;
    text-align: center;
    line-height: 90rpx;
    margin: 40rpx auto;
    margin-bottom: 0rpx;
    transition: all 0.3s ease;

    &:active {
      transform: scale(0.95);
      opacity: 0.8;
      background: linear-gradient(15deg, #ffbf0d 18.1%, #ffcb3c 83.29%);
    }
  }

  .feedback-submit-disabled {
    width: 686rpx;
    height: 90rpx;
    background: #999;
    border-radius: 80rpx;
    font-size: 32rpx;
    font-weight: 500;
    color: #fff;
    text-align: center;
    line-height: 90rpx;
    margin: 40rpx auto;
    margin-bottom: 0rpx;
  }

  &-submit-text {
    font-size: 28rpx;
    color: #333;
    text-align: center;
    margin-top: 50rpx;
    text-decoration: underline;
    text-align: center;
    padding-bottom: env(safe-area-inset-bottom);
  }

  // 分组表单项样式
  &-form-group-item {
    display: flex;
    width: 100%;
    padding: 30rpx 0;
    justify-content: space-between;
    align-items: center;

    &.school-name {
      justify-content: space-between;
      padding-top: 0rpx;

      text {
        font-size: 28rpx;
        font-weight: 400;
        color: #333;
      }
    }

    &.feedback-type {
      padding-top: 0rpx;
      display: block;

      .feedback-form-item-title {
        padding-top: 30rpx;
        padding-bottom: 24rpx;

        span {
          color: #fe4f37;
        }
      }

      .feedback-form-item-field {
        background-color: #f5f5f5;
        border-radius: 9rpx;
        height: 88rpx;
        line-height: 88rpx;
        font-size: 26rpx;
        color: #bababa;
        padding-right: 30rpx;
        padding-left: 20rpx;
      }
    }
  }

  &-form-divider {
    width: 100%;
    height: 1rpx;
    background-color: #eee;
  }

  .field-error {
    color: #fe4f37;
  }
}

.disabled {
  opacity: 0.5;
}

::v-deep .upload-button {
  margin-bottom: 0rpx !important;
}
</style>
