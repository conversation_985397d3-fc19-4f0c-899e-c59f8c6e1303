<!-- 反馈列表 -->

<template>
  <view class="feedback-list" :class="{ 'empty-list': showEmpty }">
    <!-- 反馈列表 -->
    <view class="feedback-card" v-for="(item, index) in feedbackList" :key="index" @click="checkResult(item)">
      <!-- 状态栏 -->
      <view class="feedback-card-header">
        <view class="feedback-card-status-wrapper">
          <view class="feedback-card-status">
            <u-image width="60rpx" height="60rpx" class="status-icon" :showMenuByLongpress="false" :src="item.reply
              ? 'https://tg-prod.oss-cn-beijing.aliyuncs.com/2333db2d-d81e-4832-bc3d-0c8b58d787d3.webp'
              : 'https://tg-prod.oss-cn-beijing.aliyuncs.com/490a0871-4f22-4a5e-8fd2-99a59b861ec8.png'
              " mode="aspectFit"></u-image>
            <view class="status-text-wrapper">
              <view class="status-text">{{
                item.reply ? "处理完成" : "受理中"
              }}</view>
              <view class="feedback-card-date">{{
                $u.timeFormat(item.created_at, "yyyy-mm-dd hh:MM:ss")
              }}</view>
            </view>
          </view>
        </view>
        <text class="result-link">结果查询</text>
      </view>
      <!-- 反馈内容 -->
      <view class="feedback-card-content">
        <view class="feedback-card-item">
          <text class="item-label">提交方式</text>
          <text class="item-value">{{
            item.is_anonymous === 1 ? "匿名提交" : "非匿名提交"
          }}</text>
        </view>
        <view class="feedback-card-item">
          <text class="item-label">反馈校区</text>
          <text class="item-value">{{ item.department_name }}</text>
        </view>
        <view class="feedback-card-item">
          <text class="item-label">反馈类型</text>
          <text class="item-value">{{ item.category_name }}</text>
        </view>
        <view class="feedback-card-item">
          <text class="item-label">反馈内容</text>
          <text class="item-value">{{ item.feedback }}</text>
        </view>
      </view>
    </view>
    <empty text="暂无反馈数据~" v-if="showEmpty && feedbackList.length === 0" />
  </view>
</template>

<script>
import {
  getFeedbackList,
  getFeedbackCategories
} from "@/services/student/userFeedback.js";
import Empty from "@/components/empty/index.vue";
export default {
  name: "FeedbackList",
  components: {
    Empty
  },
  data() {
    return {
      feedbackList: [
        // {
        //   status: "提交成功",
        //   date: "2025-03-17",
        //   submitType: "匿名提交",
        //   school: "刘家窑校区",
        //   type: "课程投诉",
        //   content: "课程进度过快、知识点讲解不清"
        // },
        // {
        //   status: "提交成功",
        //   date: "2025-03-17",
        //   submitType: "匿名提交",
        //   school: "刘家窑校区",
        //   type: "课程投诉",
        //   content: "课程进度过快、知识点讲解不清"
        // }
        // 可以添加更多反馈数据
      ],
      curStudentInfo: {},
      feedbackTypes: [],
      showEmpty: false
    };
  },
  async onLoad() {
    this.curStudentInfo = uni.getStorageSync("curStudentInfo");
    await this.getFeedbackType();
    await this.getFeedbackList();
  },

  methods: {
    // 获取反馈类型
    async getFeedbackType() {
      const res = await getFeedbackCategories({
        user_id: this.curStudentInfo.student_id
      });
      if (res.code === 0) {
        this.feedbackTypes = res?.data ?? [];
      }
    },

    // 获取反馈列表
    async getFeedbackList() {
      try {
        this.showEmpty = false;
        uni.showLoading({
          title: "查询中..."
        });
        const res = await getFeedbackList({
          user_id: this.curStudentInfo.student_id,
          page: 1,
          page_size: 999
        });
        if (res.code === 0) {
          this.feedbackList = res.data?.results || [];
          this.showEmpty = this.feedbackList.length === 0;
        } else {
          uni.showToast({
            title: res?.message || "获取反馈列表失败",
            icon: "none",
            duration: 2000
          });
          this.showEmpty = true;
        }
      } catch (error) {
        this.showEmpty = true;
        uni.showToast({
          title: "获取反馈列表失败",
          icon: "none",
          duration: 2000
        });
      } finally {
        uni.hideLoading();
      }
    },
    checkResult(item) {
      // 导航到反馈结果详情页
      // 反馈单号
      const order_no = item.order_no;
      uni.navigateTo({
        url: `/pages/student/subpages/feedback/detail?order_no=${order_no}`
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.feedback-list {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 32rpx;
  display: flex;
  flex-direction: column;

  &.empty-list {
    justify-content: center;
  }
}

.feedback-card {
  background: #ffffff;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0px 0px 50px 0px rgba(168, 150, 118, 0.1);

  &-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;
    padding: 10rpx 0;
  }

  &-status-wrapper {
    display: flex;
    flex-direction: column;
  }

  &-status {
    display: flex;
    align-items: center;

    .status-icon {
      width: 40rpx;
      height: 40rpx;
    }

    .status-text-wrapper {
      display: flex;
      flex-direction: column;
      justify-content: center;
      margin-left: 28rpx;
    }

    .status-text {
      font-size: 30rpx;
      font-weight: 500;
      color: #333333;
      line-height: 36rpx;
    }
  }

  &-date {
    font-size: 24rpx;
    color: #999999;
  }

  .result-link {
    font-size: 28rpx;
    color: #ffc525;
    font-weight: 500;
  }

  &-content {
    // border-bottom: 1rpx solid #eeeeee;
    // padding-bottom: 24rpx;
  }

  &-item {
    display: flex;
    margin-bottom: 12rpx;
    padding-left: 88rpx;

    &:last-child {
      margin-bottom: 0;
    }

    .item-label {
      font-size: 28rpx;
      color: #999999;
      width: 140rpx;
      flex-shrink: 0;
    }

    .item-value {
      font-size: 28rpx;
      color: #333333;
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}
</style>
