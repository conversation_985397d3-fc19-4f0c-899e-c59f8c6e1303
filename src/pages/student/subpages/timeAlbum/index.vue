<template>
  <view>
    <web-view
      ref="webview"
      @message="handleMessage"
      :src="`${host}/tg-minigram/timeAlbum?token=${token}&choose_head=${choose_head}&stu_id=${stu_id}&from=${from}&student_name=${student_name}&visitor=${visitor}&operation_id=${operation_id}`"
    />
  </view>
</template>

<script>
export default {
  name: "timeAlbum",
  components: {},
  data() {
    return {
      choose_head: 1,
      stu_id: "",
      from: "",
      student_name: "",
      department_id: "",
      token: "",
      visitor: "",
      operation_id: "",
      host: ""
    };
  },
  computed: {},
  methods: {
    handleMessage(e) {
      console.log("e :>> ", e);
    },
    postMessage(data) {
      const webview = this.selectComponent("webview");
      webview.postMessage(data);
    }
  },
  watch: {},

  // 页面周期函数--监听页面加载
  onLoad(options) {
    const { student_id, choose_head, from, student_name } = options;
    this.from = from;
    // this.host = "http://*************:9000";
    this.host = process.env.VUE_APP_TG_H5_HOST;
    if (from === "1") {
      this.stu_id = student_id;
      // this.department_id = department_id;
      this.choose_head = choose_head || 1;

      this.student_name = student_name;
      this.token = uni.getStorageSync("token");
    } else {
      const studentInfo = uni.getStorageSync("curStudentInfo");
      this.stu_id = studentInfo.student_id;
      // this.department_id = studentInfo.department_id;
      this.student_name = studentInfo.student_name;
      this.choose_head = studentInfo.choose_head || 1;
      const session = uni.getStorageSync("session");
      this.token = session.token;
      this.from = "2";
      this.visitor = session.role === "customer" ? 2 : 3;
      this.operation_id = session.operation_id;
      // this.avatar = options.avatar;
    }
    // this.postMessage({
    //   type: "delItem",
    //   data: "123"
    // });
  },
  // 页面周期函数--监听页面初次渲染完成
  onReady() {},
  // 页面周期函数--监听页面显示(not-nvue)
  onShow() {},
  // 页面周期函数--监听页面隐藏
  onHide() {},
  // 页面周期函数--监听页面卸载
  onUnload() {}
  // 页面处理函数--监听用户下拉动作
  // onPullDownRefresh() { uni.stopPullDownRefresh(); },
  // 页面处理函数--监听用户上拉触底
  // onReachBottom() {},
  // 页面处理函数--监听页面滚动(not-nvue)
  // onPageScroll(event) {},
  // 页面处理函数--用户点击右上角分享
  // onShareAppMessage(options) {},
};
</script>

<style lang="scss" scoped></style>
