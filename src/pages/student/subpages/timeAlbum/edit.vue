<template>
  <div v-if="isShow">
    <page-container
      :show="isShow"
      :overlay="false"
      @beforeleave="beforeBack"
      position="right"
    >
      <div class="edit-loading-container" v-if="isAnimationLoading">
        <LoadingAnimation />
      </div>
      <div class="time-album-edit" v-else>
        <view class="uni-form-item uni-column">
          <div class="uni-input-container">
            <input
              class="uni-input"
              placeholder-style="color:#BABABA;font-weight:400;font-size:30rpx;line-height:48rpx;"
              placeholder="请输入标题"
              v-model="title"
              maxlength="10"
              @blur="handleBlur"
              @input="handleInput"
            />
            <div class="uni-input-right">
              <span
                class="clear-icon"
                v-if="title.length > 0"
                @click="clearTitle"
              >
                <u--image
                  width="32rpx"
                  height="32rpx"
                  src="https://tg-prod.oss-cn-beijing.aliyuncs.com/395e504f-8af4-4dab-ae3e-92f1f83588cd.png"
                  alt="清除"
                />
              </span>
              <span class="count-text">{{ 10 - title.length }}</span>
            </div>
          </div>
          <div class="uni-textarea-container">
            <textarea
              @blur="handleTextareaBlur"
              type="text"
              class="uni-textarea"
              placeholder="请输入内容"
              placeholderStyle="color:#BABABA"
              v-model="content"
              border="none"
              autoHeight
              maxlength="-1"
            />
            <div v-if="content.length > 1000" class="uni-textarea-count">
              最多输入1000字，当前字数{{ content.length }}
            </div>
          </div>
        </view>
        <div class="image-video-upload">
          <image-video-upload
            :maxCount="5"
            :rowCount="3"
            :imageSpacing="20"
            :imageSize="196"
            v-model="fileList"
            :resourceHub="true"
            @textHub="handleTextHub"
          />
        </div>
        <!-- 标签 -->
        <div
          @click="handleTagClick"
          style="margin-top: 120rpx"
          class="uni-tag-container"
        >
          <div class="uni-tag">
            <text style="color: #333; font-weight: 500">标签</text>
          </div>
          <div class="uni-tag">
            <text>{{ checkTag.name || "请选择" }}</text>
            <u--image
              src="https://tg-prod.oss-cn-beijing.aliyuncs.com/d0194361-43af-4a91-bd22-af080ff12641.png"
              width="30rpx"
              height="30rpx"
            ></u--image>
          </div>
        </div>
        <view @touchmove.stop.prevent="">
          <u-picker
            ref="uPicker"
            :show="showTypeSelector"
            :columns="[actionsList]"
            keyName="name"
            valueName="id"
            @confirm="selectClick"
            @cancel="showTypeSelector = false"
            :defaultIndex="[0]"
            title="请选择标签"
            confirmColor="#333"
          ></u-picker>
        </view>
        <!-- <u-action-sheet
          :actions="actionsList"
          :cancelText="'取消'"
          @select="selectClick"
          @close="cancelClick"
          :show="showTypeSelector"
          round="20rpx"
        ></u-action-sheet> -->
        <!-- 记录时间 -->
        <div
          @click="dateShow = true"
          style="border-bottom: 1px solid rgb(238 238 238 / 63%)"
          class="uni-tag-container"
        >
          <div class="uni-tag">
            <text style="color: #333; font-weight: 500">记录时间</text>
          </div>
          <div class="uni-tag">
            <text>{{ dateValue }}</text>
            <u--image
              src="https://tg-prod.oss-cn-beijing.aliyuncs.com/d0194361-43af-4a91-bd22-af080ff12641.png"
              width="30rpx"
              height="30rpx"
            ></u--image>
          </div>
        </div>

        <div class="date-picker-container">
          <u-datetime-picker
            ref="datetimePicker"
            title="选择记录时间"
            :show="dateShow"
            v-model="date"
            mode="date"
            :formatter="formatter"
            :maxDate="new Date().getTime()"
            confirmColor="#333"
            @confirm="confirmDate"
            @cancel="cancelDate"
          ></u-datetime-picker>
        </div>

        <div class="uni-button-container">
          <button
            :disabled="submitDisabled || isLoading"
            class="uni-button"
            :class="{
              'button-active': isButtonActive,
              'button-disabled': submitDisabled || isLoading
            }"
            @touchstart="handleTouchStart"
            @touchend="handleTouchEnd"
            @touchcancel="handleTouchEnd"
          >
            {{ id ? "保存" : "发布" }}
          </button>
        </div>
      </div>
    </page-container>
    <confirm
      :visible="confirmVisible"
      :content="
        id ? '新纪录还未保存，确定放弃吗？' : '新纪录还未发布，确定放弃吗？'
      "
      :confirm-text="confirmButtonText"
      :cancel-text="cancelButtonText"
      @confirm="handleConfirm"
      @close="handleClose"
    />
    <!-- 批量发送弹窗 -->
    <u-popup
      :show="batchSendVisible"
      mode="center"
      :round="10"
      :customStyle="{
        borderRadius: '32rpx',
        background: '#FFFFFF',
        fontSize: '32rpx',
        width: '680rpx',
        minHeight: '600rpx'
      }"
      :safeAreaInsetBottom="false"
      :closeOnClickOverlay="false"
    >
      <view class="batch-send-popup">
        <view class="popup-header">
          <view class="popup-title">发送时光相册</view>
          <span v-if="batchSended" class="close-icon">
            <u-icon
              name="close"
              size="32rpx"
              color="#999"
              @click="closeBatchSendPopup"
            ></u-icon
          ></span>
        </view>
        <view class="popup-content">
          <view class="table-header">
            <text class="header-item student-name">学员姓名</text>
            <text class="header-item student-id">学号</text>
            <text class="header-item send-status">状态</text>
          </view>
          <scroll-view class="table-body" scroll-y>
            <view
              class="table-row"
              v-for="student in batchStudents"
              :key="student.student_id"
            >
              <text class="row-item student-name">{{
                student.student_name
              }}</text>
              <text class="row-item student-id">{{
                student.student_number || "--"
              }}</text>
              <view class="row-item send-status">
                <view
                  v-if="student.sendStatus === 'pending'"
                  class="status-pending"
                >
                  <u-loading-icon mode="circle" size="32rpx"></u-loading-icon>
                  <!-- <text class="status-text">发送中</text> -->
                </view>
                <view
                  v-else-if="student.sendStatus === 'success'"
                  class="status-success"
                >
                  <u-icon
                    name="checkmark-circle-fill"
                    color="#19be6b"
                    size="32rpx"
                  ></u-icon>
                  <!-- <text class="status-text">已发送</text> -->
                </view>
                <view
                  v-else-if="student.sendStatus === 'error'"
                  class="status-error"
                >
                  <u-icon
                    name="close-circle-fill"
                    color="#fa3534"
                    size="32rpx"
                  ></u-icon>
                  <text class="status-text">{{
                    student.errorMessage || "发送失败"
                  }}</text>
                </view>
              </view>
            </view>
          </scroll-view>
        </view>
      </view>
    </u-popup>
  </div>
</template>

<script>
import imageVideoUpload from "@/components/imageVideoUpload";
import LoadingAnimation from "@/components/common/LoadingAnimation.vue";
import crypto from "crypto-js";
import Confirm from "@/components/confirm/index.vue";
// import { time_album_tags } from "@/utils/dict";
import {
  getAlbumTypeList,
  createTimeAlbum,
  updateTimeAlbum,
  getTimeAlbumDetail,
  batchAddTimeAlbum
} from "@/services/student/timeAlbum";
export default {
  name: "timeAlbumEdit",
  components: {
    imageVideoUpload,
    LoadingAnimation,
    Confirm
  },
  data() {
    return {
      id: "",
      title: "",
      content: "",
      fileList: [],
      isButtonActive: false,
      actionsList: [],
      showTypeSelector: false,
      checkTag: "",
      dateShow: false,
      date: new Date().getTime(),
      options: {},
      isLoading: false,
      employee_id: "",
      isAnimationLoading: false,
      initMd5: "",
      isShow: true,
      confirmVisible: false,
      confirmContent: "新记录还未保存，确定放弃吗？",
      confirmButtonText: "取消",
      cancelButtonText: "放弃",
      // 批量发送相关数据
      batchSendVisible: false,
      batchStudents: [],
      isBatchMode: false,
      studentData: [],
      batchSended: false,
      errCount: 0,
      resourceHubVisible: false
    };
  },
  watch: {},
  computed: {
    dateValue() {
      return uni.$u.timeFormat(this.date, "yyyy-mm-dd");
    },

    checkTagValue() {
      return this?.checkTag?.id || "";
    },
    submitDisabled() {
      return (
        this.title.length === 0 ||
        this.content.length === 0 ||
        this.fileList.length === 0 ||
        this.checkTagValue === "" ||
        this.dateValue === "" ||
        this.isLoading
      );
    }

    // checkTagDisabled() {
    //   return (
    //     this.allActionsList.find((item) => item.id === this.checkTagValue)
    //       .status === 1
    //   );
    // }
  },
  methods: {
    // 教师获取时间相册详情
    async getInfo() {
      this.isAnimationLoading = true;
      const res = await getTimeAlbumDetail({
        id: this.id,
        from: this.options.from
      });
      this.isAnimationLoading = false;
      if (res.code === 0) {
        this.fillInfo(res.data);
        this.isShow = true;
      } else {
        uni.$u.toast(res.message);
      }
    },
    fillInfo(data) {
      this.title = data.album_title;
      this.content = data.album_values;
      this.employee_id = data.employee_id;
      const arr = [];
      data.album_picture_video.forEach((item) => {
        arr.push({
          url: item.video_url ? item.video_url : item.image_url,
          poster: item.image_url || "",
          type: item.video_url ? "video" : "image",
          width: item.width,
          height: item.height,
          size: item.size
        });
      });
      this.fileList = arr;
      this.checkTag = this.actionsList.find(
        (item) => item.id === data.album_type
      );
      if (this.checkTag.status === 1) {
        this.actionsList = this.actionsList.filter((item) => item.status === 1);
      } else {
        this.actionsList = this.actionsList.filter((item) => item.status === 2);
      }
      this.date = data.event_time;
    },
    showAlert() {
      const params = {
        title: this.title,
        content: this.content,
        checkTagValue: this.checkTagValue,
        dateValue: this.dateValue,
        fileList: this.fileList
      };
      if (this.batchSended) {
        return false;
      }
      if (this.initMd5 === crypto.MD5(JSON.stringify(params)).toString()) {
        return false;
      } else {
        return true;
      }
    },
    md5Params() {
      const params = {
        title: this.title,
        content: this.content,
        checkTagValue: this.checkTagValue,
        dateValue: this.dateValue,
        fileList: this.fileList
      };
      this.initMd5 = crypto.MD5(JSON.stringify(params)).toString();
    },
    handleBlur(e) {
      if (this.title.length > 10) {
        this.title = this.title.substring(0, 10);
        uni.$u.toast("标题最多输入10字哦");
      }
      this.title = uni.$u.trim(e.detail.value);
    },
    handleInput(e) {
      if (e.detail.value.length === 10) {
        uni.$u.toast("标题最多输入10字哦");
      }
    },
    handleTextareaBlur(e) {
      this.content = uni.$u.trim(e.detail.value);
    },
    formatter(type, value) {
      if (type === "year") {
        return `${value}年`;
      }
      if (type === "month") {
        return `${value}月`;
      }
      if (type === "day") {
        return `${value}日`;
      }
      return value;
    },
    clearTitle() {
      this.title = "";
    },
    handleTouchStart() {
      if (!this.title.length) {
        uni.$u.toast("请输入标题");
        return;
      }
      if (!this.content.length) {
        uni.$u.toast("请输入内容");
        return;
      }
      if (!this.fileList.length) {
        uni.$u.toast("请上传图片或视频");
        return;
      }

      if (!this.checkTagValue) {
        uni.$u.toast("请选择标签类型");
        return;
      }
      if (this.submitDisabled) return;
      this.isButtonActive = true;
      this.submitForm();
    },
    async submitForm() {
      if (this.title.length > 10) {
        uni.$u.toast("标题最多输入10字哦");
        return;
      }
      if (this.content.length > 1000) {
        uni.$u.toast("内容最多输入1000字哦");
        return;
      }
      if (this.isBatchMode) {
        this.showBatchSendPopup();
        return;
      }
      const params = {
        id: this.id ? +this.id : "",
        album_title: this.title,
        album_values: this.content,
        album_type: this.checkTagValue,
        event_time: this.dateValue,
        student_id: this.options.stu_id
        // department_id: "4c9d3c21-c2da-4fb5-aa55-2874a6a0d825",
        // student_number: "S1015227"
      };
      const fileList = [];
      this.fileList.forEach((item) => {
        fileList.push({
          width: item.width,
          height: item.height,
          size: item.size,
          image_url: item.type === "image" ? item.url : item.poster || "",
          video_url: item.type === "video" ? item.url : ""
        });
      });
      params.album_picture_video = fileList;
      console.log(params);
      try {
        let res;
        this.isLoading = true;
        uni.showLoading({
          title: "提交中..."
        });
        // 测试用延迟3s
        // await new Promise((resolve) => setTimeout(resolve, 3000));
        if (this.id) {
          params.create_employee_id = this.employee_id;
          res = await updateTimeAlbum(params);
        } else {
          res = await createTimeAlbum(params);
        }
        console.log(res);

        if (res.code === 0) {
          uni.$u.toast(this.id ? "保存成功" : "发布成功");
          this.isShow = false;
          const timer = setTimeout(() => {
            this.isLoading = false;
            uni.navigateBack();
            clearTimeout(timer);
          }, 1000);
        } else {
          this.isLoading = false;
          uni.hideLoading();
          uni.$u.toast(res.message);
        }
      } catch (error) {
        uni.$u.toast(error.message);
      } finally {
        this.isButtonActive = false;
        uni.hideLoading();
      }
    },

    handleConfirm() {
      this.isShow = false;
      setTimeout(() => {
        this.isShow = true;
      }, 10);
      this.confirmVisible = false;
    },
    handleClose() {
      this.isShow = false;
      uni.navigateBack();
    },
    beforeBack() {
      // this.isShow = false;
      if (this.showAlert()) {
        this.confirmVisible = true;
      } else {
        this.isShow = false;
        uni.navigateBack();
      }
    },
    handleTouchEnd() {
      if (this.submitDisabled) return;
      this.isButtonActive = false;
    },
    handleTagClick() {
      if (this?.checkTag?.status === 1) {
        uni.$u.toast("自动生成的标签，无法修改！");
        return;
      }
      this.showTypeSelector = true;
    },
    selectClick(e) {
      this.checkTag = e.value[0];
      this.showTypeSelector = false;
    },
    cancelClick() {
      this.showTypeSelector = false;
    },

    confirmDate(e) {
      console.log(e);
      this.cancelDate();
    },
    cancelDate() {
      this.dateShow = false;
    },
    async getAlbumTypeList() {
      const res = await getAlbumTypeList({
        UNAUTHORIZED: true // 不需要token
      });
      if (res.code === 0) {
        this.actionsList = res.data;
        if (!this.id) {
          this.actionsList = this.actionsList.filter(
            (item) => item.status === 2
          );
        }
      }
    },

    // 显示批量发送弹窗
    showBatchSendPopup() {
      this.batchStudents = this.studentData.map((student) => ({
        ...student,
        sendStatus: "waiting",
        errorMessage: ""
      }));
      this.batchSendVisible = true;
      this.startBatchSend();
    },

    // 关闭批量发送弹窗
    closeBatchSendPopup() {
      this.batchSendVisible = false;
      uni.navigateBack();
    },

    // 开始批量发送
    async startBatchSend() {
      let err_count = 0;

      for (let i = 0; i < this.batchStudents.length; i++) {
        const student = this.batchStudents[i];

        // 设置为发送中状态
        this.$set(this.batchStudents, i, {
          ...student,
          sendStatus: "pending"
        });

        try {
          await this.sendToSingleStudent(student);

          // 发送成功
          this.$set(this.batchStudents, i, {
            ...student,
            sendStatus: "success"
          });
        } catch (error) {
          err_count++;

          // 发送失败
          this.$set(this.batchStudents, i, {
            ...student,
            sendStatus: "error",
            errorMessage: error.message || "发送失败"
          });
        }

        // 添加延迟，避免请求过于频繁
        if (i < this.batchStudents.length - 1) {
          await this.delay(500);
        }
      }
      this.batchSended = true;
      this.errCount = err_count;
    },

    // 发送给单个学员
    async sendToSingleStudent(student) {
      const params = {
        album_title: this.title,
        album_values: this.content,
        album_type: this.checkTagValue,
        event_time: this.dateValue,
        student_id: student.student_id
      };

      const fileList = [];
      this.fileList.forEach((item) => {
        fileList.push({
          width: item.width,
          height: item.height,
          size: item.size,
          image_url: item.type === "image" ? item.url : item.poster || "",
          video_url: item.type === "video" ? item.url : ""
        });
      });
      params.album_picture_video = fileList;

      const res = await batchAddTimeAlbum(params);
      // console.log("res===》》》》》", res);
      if (res.code !== 0) {
        throw new Error(res.message);
      }
      return res;
    },

    // 延迟函数
    delay(ms) {
      return new Promise((resolve) => setTimeout(resolve, ms));
    },
    handleTextHub(content) {
      this.content = content;
    }
  },

  // 页面周期函数--监听页面加载
  async onLoad(options) {
    this.id = options.id;
    this.options = options;

    // 处理批量发送的学员数据
    if (options.student_data) {
      try {
        this.studentData = JSON.parse(decodeURIComponent(options.student_data));
        this.isBatchMode = true;
      } catch (error) {
        console.error("解析学员数据失败:", error);
        this.studentData = [];
      }
    }

    if (this.id) {
      this.isShow = false;
    }
    await this.getAlbumTypeList();
    if (this.id) {
      await this.getInfo();
    }
    this.md5Params();
    // uni.enableAlertBeforeUnload({
    //   message: "新纪录还未保存，确定放弃吗？",
    //   success: () => {
    //     uni.navigateBack();
    //   },
    //   fail: () => {}
    // });
    // uni.disableAlertBeforeUnload();
  },
  // 页面周期函数--监听页面初次渲染完成
  onReady() {},
  // 页面周期函数--监听页面显示(not-nvue)
  onShow() {},
  // 页面周期函数--监听页面隐藏
  onHide() {},
  // 页面周期函数--监听页面卸载
  onUnload() {}
  // 页面处理函数--监听用户下拉动作
  // onPullDownRefresh() { uni.stopPullDownRefresh(); },
  // 页面处理函数--监听用户上拉触底
  // onReachBottom() {},
  // 页面处理函数--监听页面滚动(not-nvue)
  // onPageScroll(event) {},
  // 页面处理函数--用户点击右上角分享
  // onShareAppMessage(options) {},
};
</script>
<style lang="scss">
.time-album-edit {
  .u-popup__content {
    border-top-left-radius: 22rpx;
    border-top-right-radius: 22rpx;
  }
  .u-toolbar__title {
    font-weight: 500 !important;
    font-size: 32rpx !important;
    color: #333 !important;
  }
}

.batch-send-popup {
  padding: 40rpx;
  height: 100%;
  display: flex;
  flex-direction: column;

  .popup-header {
    position: relative;

    margin-bottom: 30rpx;

    .popup-title {
      font-size: 32rpx;
      font-weight: 600;
      text-align: center;
      color: #333;
    }
    .close-icon {
      cursor: pointer;
      position: absolute;
      right: 0rpx;
      top: 0rpx;
    }
  }

  .popup-content {
    flex: 1;
    display: flex;
    flex-direction: column;

    .table-header {
      display: flex;
      padding: 20rpx 0;
      border-bottom: 2rpx solid #eee;

      .header-item {
        font-size: 28rpx;
        font-weight: 600;
        color: #333;

        &.student-name {
          flex: 2;
        }

        &.student-id {
          flex: 2;
          text-align: center;
        }

        &.send-status {
          flex: 2;
          text-align: center;
        }
      }
    }

    .table-body {
      max-height: 60vh;

      .table-row {
        display: flex;
        align-items: center;
        padding: 24rpx 0;
        border-bottom: 1rpx solid #f5f5f5;

        .row-item {
          font-size: 26rpx;
          color: #666;

          &.student-name {
            flex: 2;
            color: #333;
          }

          &.student-id {
            flex: 2;
            text-align: center;
          }

          &.send-status {
            flex: 2;
            display: flex;
            justify-content: center;

            .status-pending,
            .status-success,
            .status-error,
            .status-waiting {
              display: flex;
              align-items: center;
              gap: 8rpx;

              .status-text {
                font-size: 24rpx;
              }
            }

            .status-pending .status-text {
              color: #3c9cff;
            }

            .status-success .status-text {
              color: #19be6b;
            }

            .status-error .status-text {
              color: #fa3534;
              max-width: 120rpx;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }

            .status-waiting .status-text {
              color: #999;
            }
          }
        }
      }
    }
  }
}
</style>
<style lang="scss" scoped>
.edit-loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}
.time-album-edit {
  padding: 30rpx 60rpx;
  border-top: 1px solid #eee;
  height: 100vh;
  overflow: scroll;
  padding-bottom: 130rpx;
  .uni-input-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10rpx;
    height: 50rpx;
    .uni-input-right {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    .clear-icon {
      padding: 10rpx;
    }
    .count-text {
      font-size: 24rpx;
      color: #999;
      width: 32rpx;
      height: 32rpx;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
  .uni-input {
    font-size: 32rpx;
    line-height: 48rpx;
    font-weight: 500;
    color: #333;
    flex: 1;
    margin-right: 12rpx;
  }

  .uni-textarea {
    font-size: 30rpx;
    line-height: 42rpx;
    color: #666;
    width: 100%;
  }
  .image-video-upload {
    margin-top: 80rpx;
  }
  .uni-tag-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 28rpx 0;
    border-top: 1px solid rgb(238 238 238 / 63%);

    .uni-tag {
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 28rpx;
      line-height: 40rpx;
      color: #333;
      text {
        font-size: 28rpx;
        line-height: 40rpx;
        font-weight: 400;
        color: #666;
      }
    }
  }
  .uni-button-container {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 18rpx 60rpx 48rpx 60rpx;
    box-sizing: border-box;

    // margin-top: 170rpx;
    background-color: #fff;
    width: 100%;
    .uni-button {
      border-radius: 80px;
      background: linear-gradient(15deg, #ffbf0d 18.1%, #ffcb3c 83.29%);
      box-shadow: 0px -10rpx 18rpx 0px #f3b300 inset,
        0px 4rpx 20rpx 0px rgba(254, 197, 36, 0.47);
      color: #fff;
      font-size: 32rpx;
      font-weight: 500;
      width: 100%;
      height: 92rpx;
      line-height: 92rpx;
      margin: 0 auto;
      transition: all 0.1s ease;
    }
    .button-active {
      transform: scale(0.95);
      background: linear-gradient(15deg, #f0b50c 18.1%, #f0be31 83.29%);
      box-shadow: 0px -5rpx 10rpx 0px #e6ac00 inset,
        0px 2rpx 10rpx 0px rgba(254, 197, 36, 0.3);
      opacity: 0.9;
    }
    .button-disabled {
      opacity: 0.5;
    }
  }
  .uni-textarea-count {
    font-size: 24rpx;
    color: #fe4f37;
    margin-top: 6rpx;
  }
}
</style>
<style>
/* page {
  overflow: hidden;
} */
</style>
