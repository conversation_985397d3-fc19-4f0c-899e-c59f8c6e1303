<template>
  <div class="detail-loading-container" v-if="isLoading">
    <LoadingAnimation />
  </div>
  <div class="time-album-detail" v-else>
    <div class="swiper-container">
      <!-- <image
        v-if="
          swiperList.length === 1 &&
          swiperList[0].type === 'image' &&
          swiperList[0].width > swiperList[0].height
        "
        :src="swiperList[0].url"
        style="width: 100%"
        mode="widthFix"
      /> -->
      <swiper
        :style="{
          height: swiperHeight + 'rpx',
          minHeight: '500rpx',
          maxHeight: '997rpx'
        }"
        :current="current"
        :autoplay="false"
        :indicator-dots="false"
        @change="handleSwiperChange"
        circular
      >
        <swiper-item v-for="(item, index) in swiperList" :key="index">
          <view class="swiper-item-content">
            <image
              v-if="item.type === 'image'"
              :src="item.url"
              mode="aspectFit"
              class="media-content"
            />
            <video
              v-else-if="item.type === 'video'"
              :src="item.url"
              :poster="item.share_poster"
              class="media-content"
              controls
              :autoplay="false"
              :loop="false"
              :muted="false"
              :show-center-play-btn="true"
              :show-play-btn="true"
              :show-fullscreen-btn="true"
              object-fit="cover"
              :show-background-playback-button="false"
            />
          </view>
        </swiper-item>
      </swiper>
      <div class="swiper-indicator">
        {{ current + 1 }}/{{ swiperList.length }}
      </div>
      <view v-if="swiperList.length > 1" slot="indicator" class="indicator">
        <view
          class="indicator__dot"
          v-for="(item, index) in swiperList"
          :key="index"
          :class="[index === current && 'indicator__dot--active']"
        >
        </view>
      </view>
    </div>
    <div class="time-album-detail-content">
      <div class="date">
        {{ $u.timeFormat(timeAlbumDetail.event_time, "yyyy年mm月dd日") }}
      </div>
      <div class="title">
        <div class="title-left">
          <span class="mark">
            {{ getAlbumType(timeAlbumDetail.album_type) }}
          </span>
          <span class="text"> {{ timeAlbumDetail.album_title }} </span>
        </div>
        <span v-if="from === '1'" class="record-person">
          记录人：{{ timeAlbumDetail.final_employee_name || "自动生成" }}
        </span>
      </div>
      <text class="summary">
        {{ timeAlbumDetail.album_values }}
      </text>
      <div class="share-record" v-if="from == '2'">
        <span>
          记录人：{{ timeAlbumDetail.final_employee_name || "自动生成" }}
        </span>
        <div class="operation-btns">
          <span @tap="openSharePopup">
            <span class="icon-share"></span>
            分享
          </span>
        </div>
      </div>
      <div></div>
      <div class="share-record-teacher" v-if="from == '1'">
        <div
          class="operation-btns"
          :class="{ 'only-share': !canEdit && !canDelete }"
        >
          <div class="operation-btns-left">
            <span @tap="handleDel" v-if="canDelete" class="del-btn">
              <span class="icon-del"></span>
              删除
            </span>
            <span @tap="handleEdit" v-if="canEdit" class="edit-btn">
              <span class="icon-edit"></span>
              编辑
            </span>
          </div>
          <span
            v-if="$hasPermission(['time_album_front_share'])"
            @tap="openSharePopup"
            class="share-btn"
          >
            <span class="icon-share"></span>
            分享
          </span>
        </div>
      </div>
    </div>
    <share-popup
      :webviewPath="webviewPath"
      :webviewParams="webviewParams"
      :showSharePopup="isShowSharePopup"
      @closeSharePopup="closeSharePopup"
      @showPopup="showPopup"
    ></share-popup>
    <confirm
      :visible="confirmVisible"
      :content="confirmContent"
      :confirm-text="confirmButtonText"
      :cancel-text="cancelButtonText"
      @confirm="handleConfirm"
      @close="handleClose"
    />
  </div>
</template>

<script>
import SharePopup from "@/components/sharePopup/index.vue";
import Confirm from "@/components/confirm/index.vue";
import LoadingAnimation from "@/components/common/LoadingAnimation.vue";

import {
  getTimeAlbumDetail,
  deleteTimeAlbum,
  getAlbumTypeList
} from "@/services/student/timeAlbum";
export default {
  name: "timeAlbumDetail",
  components: {
    SharePopup,
    Confirm,
    LoadingAnimation
  },
  data() {
    return {
      isShowSharePopup: false,
      id: "",
      current: 0,
      swiperHeight: 1000,
      list: [],
      webviewPath: "tg-minigram/timeAlbum/shareCard",
      webviewParams: {},
      from: "",
      confirmVisible: false,
      confirmContent: "",
      confirmButtonText: "",
      cancelButtonText: "",
      timeAlbumDetail: {
        album_picture_video: [],
        album_type: "",
        album_title: "",
        album_values: "",
        employee_name: "",
        event_time: ""
      },
      allActionsList: [],
      imageProcess: "?x-oss-process=video/snapshot,t_2000,m_fast,w_320,ar_auto",
      isLoading: true
    };
  },
  computed: {
    swiperList() {
      if (this.timeAlbumDetail.album_picture_video.length) {
        const list = this.timeAlbumDetail.album_picture_video.map((item) => {
          let poster = "";
          if (item.video_url) {
            poster = item.image_url || item.video_url + this.imageProcess;
          }
          return {
            url: item.video_url ? item.video_url : item.image_url,
            share_poster: poster,
            type: item.video_url ? "video" : "image",
            width: item.width,
            height: item.height
          };
        });

        // 根据第一个媒体的高度设置swiperHeight
        if (list.length > 0) {
          const firstItem = list[0];
          if (firstItem.height) {
            // 直接使用第一个媒体的原始高度
            this.swiperHeight = firstItem.height;
            console.log(this.swiperHeight, "直接获取的高度");
          }
        }

        return list;
      }
      return [];
    },
    // 是否是自动节点
    isAutoType() {
      return (
        this.allActionsList.find(
          (item) => item.id === this.timeAlbumDetail.album_type
        )?.status === 1
      );
    },
    isCurrUser() {
      const curr = uni.getStorageSync("user");
      if (curr.employee_id === this.timeAlbumDetail.employee_id) {
        return true;
      }
      return false;
    },
    isAdmin() {
      const permission = uni.getStorageSync("permission");
      if (permission.includes("is_admin")) {
        return true;
      }
      return false;
    },
    canEdit() {
      // 只有教师端(from = 1)才能编辑
      if (this.from !== "1") {
        return false;
      }
      // 管理员可编辑任何节点
      if (this.isAdmin) {
        return true;
      }
      // 有权限的老师可编辑自己非自动节点
      if (!this.isAutoType && this.$hasPermission(["time_album_update"])) {
        return true;
      }
      // 老师可编辑自己创建的非自动节点
      if (this.isCurrUser && !this.isAutoType) {
        return true;
      }

      return false;
    },
    canDelete() {
      // 只有教师端(from = 1)才能删除
      if (this.from !== "1") {
        return false;
      }
      // 管理员可删除任何节点
      if (this.isAdmin) {
        return true;
      }
      // 有权限的老师可删除非自动节点
      if (!this.isAutoType && this.$hasPermission(["time_album_update"])) {
        return true;
      }
      // 创建人可删除自己创建的非自动节点
      if (this.isCurrUser && !this.isAutoType) {
        return true;
      }

      return false;
    }
  },
  mounted() {},
  methods: {
    async getAlbumTypeList() {
      const res = await getAlbumTypeList({
        UNAUTHORIZED: true // 不需要token
      });
      if (res.code === 0) {
        this.allActionsList = res.data;
      }
    },
    getAlbumType(type) {
      return this.allActionsList.find((item) => item.id === type)?.name || "";
    },
    // 教师获取时间相册详情
    async getInfo() {
      this.isLoading = true;
      const res = await getTimeAlbumDetail({
        id: this.id,
        from: this.from,
        student_id: this.stu_id,
        UNAUTHORIZED: true
      });
      this.isLoading = false;
      if (res.code === 0) {
        this.timeAlbumDetail = res.data || {};
      } else {
        uni.$u.toast(res.message);
        setTimeout(() => {
          uni.navigateBack();
        }, 1500);
      }
    },

    openSharePopup() {
      this.isShowSharePopup = true;
    },
    closeSharePopup() {
      this.isShowSharePopup = false;
    },
    handleConfirm() {
      this.confirmVisible = false;
    },
    handleClose() {
      this.confirmVisible = false;
      deleteTimeAlbum({
        id: this.id,
        student_id: this.stu_id,
        create_employee_id: this.timeAlbumDetail.employee_id
      }).then((res) => {
        if (res.code === 0) {
          uni.$u.toast("删除成功");
          const timer = setTimeout(() => {
            uni.navigateBack();
            clearTimeout(timer);
          }, 1000);
        } else {
          uni.$u.toast(res.message);
        }
      });
    },
    handleDel() {
      // 注释已经移除，因为权限控制逻辑已经在canDelete计算属性中实现
      this.confirmVisible = true;
      this.confirmContent = "删除本条记录？";
      this.confirmButtonText = "取消";
      this.cancelButtonText = "删除";
    },
    handleEdit() {
      uni.navigateTo({
        url: `/pages/student/subpages/timeAlbum/edit?id=${this.id}&from=${this.from}&type=edit`
      });
    },
    showPopup() {
      this.webviewParams.index = this.current;
    },

    handleSwiperChange(e) {
      this.current = e.detail.current;
    }
  },
  watch: {},

  // 页面周期函数--监听页面加载
  onLoad(options) {
    console.log(options);
    this.id = parseInt(options.id);
    this.avatar = options.avatar;
    this.from = options.from;
    this.stu_id = options.stu_id;
    // const studentInfo = uni.getStorageSync("curStudentInfo");
    let token = "";
    let visitor = "";
    let operation_id = "";
    if (this.from === "1") {
      token = uni.getStorageSync("token");
    } else {
      const session = uni.getStorageSync("session");
      token = session.token;
      visitor = session.role === "customer" ? 2 : 3;
      operation_id = session.operation_id;
    }
    this.webviewParams = {
      id: this.id,
      stu_id: this.stu_id,
      avatar: this.avatar,
      from: this.from, // 1教师，2、学员
      index: this.current,
      token,
      visitor,
      operation_id
    };
    this.getAlbumTypeList();
    this.getInfo();
  },
  onShareTimeline() {
    let imgUrl = "";
    this.closeSharePopup();
    if (this.swiperList.length) {
      const type = this.swiperList[this.current].type;
      if (type === "video") {
        imgUrl = this.swiperList[this.current].share_poster;
      } else {
        imgUrl = this.swiperList[this.current].url;
      }
    }
    return {
      title: this.timeAlbumDetail.album_title,
      imageUrl: imgUrl,
      query: `id=${this.id}&from=${this.from}&avatar=${this.avatar}&stu_id=${this.stu_id}&visitor=${this.visitor}&operation_id=${this.operation_id}`
    };
  },
  onShareAppMessage() {
    this.closeSharePopup();
    let imgUrl = "";
    if (this.swiperList.length) {
      const type = this.swiperList[this.current].type;
      if (type === "video") {
        imgUrl = this.swiperList[this.current].share_poster;
      } else {
        imgUrl = this.swiperList[this.current].url;
      }
    }
    return {
      title: this.timeAlbumDetail.album_title,
      imageUrl: imgUrl,
      query: `/pages/student/subpages/timeAlbum/detail.vue?id=${this.id}&from=${this.from}&avatar=${this.avatar}&stu_id=${this.stu_id}&visitor=${this.visitor}&operation_id=${this.operation_id}`
    };
  },
  // 页面周期函数--监听页面初次渲染完成
  onReady() {},
  // 页面周期函数--监听页面显示(not-nvue)
  onShow() {},
  // 页面周期函数--监听页面隐藏
  onHide() {},
  // 页面周期函数--监听页面卸载
  onUnload() {},
  // 页面处理函数--监听用户下拉动作
  onPullDownRefresh() {
    this.getInfo();
    const timer = setTimeout(() => {
      uni.stopPullDownRefresh();
      clearTimeout(timer);
    }, 1000);
  }
  // 页面处理函数--监听用户上拉触底
  // onReachBottom() {},
  // 页面处理函数--监听页面滚动(not-nvue)
  // onPageScroll(event) {},
  // 页面处理函数--用户点击右上角分享
  // onShareAppMessage(options) {},
};
</script>

<style lang="scss" scoped>
.detail-loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}
.time-album-detail {
  display: flex;
  flex-direction: column;
  min-height: 100vh;

  .swiper-container {
    position: relative;

    .swiper-item-content {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #fff;
    }

    .media-content {
      width: 100%;
      height: 100%;
      max-width: 100%;
      max-height: 100%;
    }

    .swiper-indicator {
      position: absolute;
      top: 20rpx;
      right: 20rpx;
      color: #fff;
      font-size: 26rpx;
      font-style: normal;
      font-weight: 500;
      width: 80rpx;
      height: 46rpx;
      line-height: 46rpx;
      border-radius: 16rpx 8rpx 8rpx 8rpx;
      background: rgba(0, 0, 0, 0.47);
      text-align: center;
      letter-spacing: 2rpx;
    }
    .indicator {
      @include flex(row);
      justify-content: center;
      margin-top: 24rpx;

      &__dot {
        width: 10rpx;
        height: 10rpx;
        border-radius: 100rpx;
        opacity: 0.4;
        background: #ffc525;
        transition: background-color 0.3s;
        margin-right: 10rpx;
        transition: all 0.3s linear;

        &--active {
          width: 18rpx;
          height: 10rpx;
          background-color: #ffc525;
          opacity: 1;
          border-radius: 20rpx;
        }
      }
    }
  }
  .time-album-detail-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 32rpx;
    padding-bottom: 140rpx; // 移除原有的底部padding

    .date {
      display: table;
      height: 44rpx;
      color: #333;
      font-size: 32rpx;
      font-weight: 500;
      line-height: 44rpx;
      background: linear-gradient(127deg, #ffb32f 15.34%, #ff8411ad 86.73%);
      background-size: 97% 10rpx;
      background-repeat: no-repeat;
      background-position: left 24rpx;
      width: fit-content;
      // -webkit-background-clip: text;
      // -webkit-text-fill-color: transparent;
    }

    .title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      color: #333;
      font-size: 32rpx;
      font-weight: 500;
      line-height: 44rpx;
      margin-top: 20rpx;
      .title-left {
        display: flex;
        align-items: center;
      }
      .record-person {
        color: #666;
        font-size: 24rpx;
        font-weight: 400;
        margin-left: 10rpx;
      }
      .mark {
        display: flex;
        align-items: center;
        justify-content: center;
        color: #999;
        font-size: 24rpx;
        font-weight: 400;
        border-radius: 16rpx 8rpx;
        padding: 0rpx 16rpx;
        color: #fff;
        background: linear-gradient(127deg, #ffb32f 15.34%, #ff8411 86.73%);
        margin-right: 10rpx;
        // line-height: 38rpx;
        font-weight: 500;
      }
      .text {
        color: #333;
        font-size: 32rpx;
        font-weight: 500;
      }
    }

    .summary {
      color: #333;
      font-size: 30rpx;
      margin: 16rpx 0;
      line-height: 44rpx;
      //两行省略
      // display: -webkit-box;
      // -webkit-line-clamp: 2;
      // -webkit-box-orient: vertical;
      // overflow: hidden;
    }

    .share-record {
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      display: flex;
      justify-content: space-between;
      align-items: center;
      color: #999;
      font-size: 28rpx;
      font-weight: 400;
      padding: 30rpx 32rpx;
      padding-bottom: Max(env(safe-area-inset-bottom), 40rpx);
      background-color: #fff;
      border-top: 1rpx solid #f0f0f0; // 添加顶部边框以区分内容
      z-index: 100; // 确保在其他元素之上

      .operation-btns {
        display: flex;
        align-items: center;
        &.only-share {
          justify-content: center;
        }
        .del-btn {
          display: flex;
          align-items: center;
          justify-content: center;
          color: #999;
          margin-right: 30rpx;
        }
        .edit-btn {
          display: flex;
          align-items: center;
          justify-content: center;
          color: #999;
          margin-right: 30rpx;
        }
        .share-btn {
          display: flex;
          align-items: center;
          justify-content: center;
          color: #999;
        }
        .icon-del {
          background-image: url("https://tg-prod.oss-cn-beijing.aliyuncs.com/f9cd1055-021e-4469-a99e-d7b13f784a76.png");
          background-size: cover;
          width: 30rpx;
          height: 30rpx;
          margin-right: 10rpx;
        }
        .icon-edit {
          background-image: url("https://tg-prod.oss-cn-beijing.aliyuncs.com/7c0257c3-369a-442e-b8a4-7aa93bd4243a.png");
          background-size: cover;
          width: 30rpx;
          height: 30rpx;
          margin-right: 10rpx;
        }
        .icon-share {
          background-image: url("https://tg-prod.oss-cn-beijing.aliyuncs.com/6e87f5f6-df0d-454e-ad85-d1bdd8026a94.png");
          background-size: cover;
          width: 30rpx;
          height: 30rpx;
          margin-right: 10rpx;
        }
      }
    }
    .share-record-teacher {
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      display: flex;
      justify-content: space-between;
      align-items: center;
      color: #999;
      font-size: 28rpx;
      font-weight: 400;
      padding: 30rpx 32rpx;
      padding-bottom: env(safe-area-inset-bottom); // 添加安全区域
      background-color: #fff;
      border-top: 1rpx solid #f0f0f0; // 添加顶部边框以区分内容
      z-index: 100; // 确保在其他元素之上

      .operation-btns {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        &.only-share {
          justify-content: center;
        }
        .operation-btns-left {
          display: flex;
          align-items: center;
        }

        .del-btn {
          display: flex;
          align-items: center;
          justify-content: center;
          color: #999;
          margin-right: 30rpx;
        }
        .edit-btn {
          display: flex;
          align-items: center;
          justify-content: center;
          color: #999;
          margin-right: 30rpx;
        }
        .share-btn {
          display: flex;
          align-items: center;
          justify-content: center;
          color: #fff;
          font-size: 32rpx;
          width: 400rpx;
          height: 92rpx;
          flex-shrink: 0;
          border-radius: 80rpx;
          background: linear-gradient(15deg, #ffbf0d 18.1%, #ffcb3c 83.29%);
          box-shadow: 0 -10rpx 18rpx 0 #f3b300 inset,
            0 4rpx 20rpx 0 rgba(254, 197, 36, 0.47);
        }
        .icon-del {
          background-image: url("https://tg-prod.oss-cn-beijing.aliyuncs.com/f9cd1055-021e-4469-a99e-d7b13f784a76.png");
          background-size: cover;
          width: 30rpx;
          height: 30rpx;
          margin-right: 10rpx;
        }
        .icon-edit {
          background-image: url("https://tg-prod.oss-cn-beijing.aliyuncs.com/7c0257c3-369a-442e-b8a4-7aa93bd4243a.png");
          background-size: cover;
          width: 30rpx;
          height: 30rpx;
          margin-right: 10rpx;
        }
      }
    }
  }
}
</style>
