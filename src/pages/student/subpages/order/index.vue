<template>
  <view class="order-container">
    <!-- <u-navbar
      title="我的订单"
      bgColor="#FFF"
      leftIconSize="25px"
      leftIconColor="#333333"
      :titleStyle="{
        color: '#333333',
        fontSize: '34rpx',
        fontWeight: '500',
        lineHeight: '40rpx'
      }"
      :autoBack="true"
      placeholder
    >
      <view class="nav-left" slot="left">
        <image
          src="https://tg-prod.oss-cn-beijing.aliyuncs.com/e9ac0f4b-6282-4bac-b602-189057c45c90.webp"
        ></image>
      </view>
    </u-navbar> -->
    <!-- 顶部标签导航 -->
    <tabList :list="tabs" :current="currentTab" @change="switchTab" />

    <!-- 订单列表 -->
    <view class="order-list-container">
      <scroll-view
        scroll-y
        class="order-list"
        @scrolltolower="loadMore"
        :refresher-triggered="refreshing"
        refresher-enabled
        @refresherrefresh="onRefresh"
        v-if="orderList.length > 0"
      >
        <!-- 订单项 -->
        <view
          class="order-item"
          v-for="(item, index) in orderList"
          :key="index"
          @click.stop="clickOrder(item)"
        >
          <!-- 待付款提示倒计时 -->
          <view
            class="countdown-notice"
            v-if="item.order_info.status === 'unpaid'"
          >
            <u-icon
              name="https://tg-prod.oss-cn-beijing.aliyuncs.com/ef1501aa-e370-4ca4-8b75-9cfc47c16c62.webp"
              color="#FF9500"
              width="36rpx"
              height="36rpx"
            ></u-icon>
            <text class="countdown-text">
              请于{{
                item.countdown || "00:00"
              }}内支付，逾期未支付将自动取消订单
            </text>
          </view>
          <view class="order-course">
            <u-image
              class="course-image"
              :src="item.order_items[0].goods_cover"
              :fade="true"
              duration="450"
              width="160rpx"
              height="160rpx"
              radius="20rpx"
              :lazy-load="true"
              :showMenuByLongpress="false"
            ></u-image>
            <view class="course-info">
              <view>
                <view class="info-top">
                  <view class="tag">{{
                    role[item.order_items[0].goods_extra.course_type]
                  }}</view>
                  <view class="course-name">{{
                    item.order_items[0].goods_name
                  }}</view>
                </view>
                <view class="campus-name">
                  <!-- {{ item.order_items[0].goods_num }}课时 -->
                  {{ item.user_info.department_name }}
                </view>
              </view>

              <view class="price-info">
                <view class="price">
                  <view class="price">
                    <text class="price-symbol">¥</text>
                    <text class="price-value">{{
                      item.order_info.actual_price
                    }}</text>
                  </view>
                  <view class="duration">
                    {{ item.order_items[0].goods_num }}课时
                    <!-- ¥{{ item.order_info.original_price }} -->
                  </view>
                </view>
                <view class="quantity"
                  >x{{ item.order_items.length || 1 }}</view
                >
              </view>
            </view>
            <view
              class="order-status"
              :style="item.order_info.status === 'failed' ? 'color:red' : ''"
              v-if="item.order_info.status !== 'unpaid'"
            >
              {{ getStatusText(item.order_info.status) }}
            </view>
          </view>

          <!-- 订单操作按钮 -->
          <view
            class="order-actions"
            v-if="item.order_info.status === 'unpaid'"
          >
            <button
              class="cancel-btn"
              @tap.stop="cancelOrder(item.order_info.order_id)"
            >
              取消订单
            </button>
            <button
              class="pay-btn"
              @tap.stop="
                payOrder(item.order_info, item.order_info.status.order_id)
              "
            >
              立即支付
            </button>
          </view>
        </view>
        <u-loadmore
          :status="status"
          lineColor="#DADADA"
          line
          color="#999999"
          :fontSize="'26rpx'"
          :loadmoreText="loadmoreText"
          :marginTop="'70rpx'"
        />
      </scroll-view>
      <!-- 无数据提示 -->
      <view class="no-data" v-else>
        <loading-animation
          :width="300"
          :characterSize="260"
          :textSize="32"
          v-if="status === 'loading'"
        />
        <u-empty
          v-else
          text="暂无订单~"
          :width="'163rpx'"
          :height="'230rpx'"
          textColor="#999999"
          marginTop="-45rpx"
          icon="https://tg-prod.oss-cn-beijing.aliyuncs.com/d02814d0-bfd3-48bf-8d9d-80cda362b89a.webp"
        >
        </u-empty>
      </view>
    </view>
    <ConfirmPopup
      v-model="showConfirm"
      @cancel="confirmCancel"
      @confirm="confirm"
      :confirmText="confirmPopup.confirmText"
      :cancelText="confirmPopup.cancelText"
      :title="confirmPopup.title"
      :dectitle="confirmPopup.dectitle"
    />
  </view>
</template>

<script>
import tabList from "@/components/tabs/index.vue";
import ConfirmPopup from "@/components/ConfirmPopup";
import LoadingAnimation from "@/components/common/LoadingAnimation.vue";
import { orderList, cancelOrder, payMin } from "@/services/student/order";
import { wxPay } from "@/utils/user";
export default {
  name: "OrderList",
  components: {
    tabList,
    ConfirmPopup,
    LoadingAnimation
  },
  data() {
    return {
      tabs: [
        { name: "全部", status: "" },
        { name: "待付款", status: "unpaid" },
        { name: "已付款", status: "paid" },
        { name: "已取消", status: "cancel" }
      ],
      currentTab: 0,
      refreshing: false,
      orderList: [],
      page: 1,
      page_size: 10,
      total: 0,
      showCountdown: false,
      status: "loading",
      loadmoreText: "加载中...",
      timer: null,
      showConfirm: null,
      orderId: "",
      confirmPopup: {
        show: false,
        title: "",
        description: "",
        confirmText: "",
        cancelText: ""
      },
      session: {},
      curStudentInfo: {},
      role: {
        1: "课时包",
        2: "试听课",
        3: "常规课"
      }
    };
  },
  onLoad() {
    // this.loadOrders();
    uni.hideShareMenu();
  },
  onShow() {
    this.session = uni.getStorageSync("session");
    this.curStudentInfo = uni.getStorageSync("curStudentInfo");
    this.page = 1;
    this.loadOrders();
    // 启动倒计时
    this.startCountdown();
    // this.confirmPopup = {
    //   show: true,
    //   title: "您手机号已有关联用户",
    //   description: "",
    //   confirmText: "联系客服",
    //   cancelText: "去绑定"
    // };
  },
  onHide() {
    // 清除定时器
    this.clearCountdownTimer();
  },
  onUnload() {
    // 清除定时器
    this.clearCountdownTimer();
  },
  methods: {
    switchTab(index) {
      if (this.currentTab === index) return;
      this.currentTab = index;
      this.page = 1;
      this.orderList = [];
      this.loadOrders();
    },

    async loadOrders() {
      // 模拟加载订单数据
      this.status = "loading";
      const status = this.tabs[this.currentTab].status;
      const { code, data, message } = await orderList({
        page: this.page,
        page_size: this.page_size,
        status,
        user_id:
          this.session.role === "student"
            ? this.curStudentInfo.student_id
            : this.session.role === "customer"
            ? this.curStudentInfo.customer_id
            : this.session.open_id
      });
      // if (code === 0) {
      //   this.orderList = data.results;
      //   this.total = data.count;
      // } else {
      //   uni.showToast({
      //     title: message,
      //     icon: "none"
      //   });
      // }
      if (code === 0) {
        if (this.page === 1) {
          this.orderList = data.results;
          this.total = data.count;
        } else {
          for (let i = 0; i < data.results.length; i++) {
            this.orderList.push(data.results[i]);
          }
        }
        this.total = data.count;
        if (this.orderList.length >= this.total) {
          this.status = "nomore";
        } else {
          this.status = "loadmore";
        }
      } else {
        uni.showToast({
          title: message,
          icon: "none"
        });
      }
      this.refreshing = false;
      this.startCountdown();
    },

    // 启动倒计时定时器
    startCountdown() {
      this.clearCountdownTimer(); // 先清除可能存在的定时器

      this.timer = setInterval(() => {
        let anyPending = false;

        this.orderList.forEach((order) => {
          if (
            order.order_info.status === "unpaid" &&
            order.order_info.pay_timer > 0
          ) {
            anyPending = true;

            // 每秒减少计时器
            order.order_info.pay_timer -= 1;
            this.updateOrderCountdown(order);

            // 检查是否过期
            if (order.order_info.pay_timer <= 0) {
              // 订单过期，自动取消
              order.order_info.status = "cancel";
              order.countdown = "00:00";
            }
          }
        });

        // 如果没有待付款订单，清除定时器
        if (!anyPending) {
          this.clearCountdownTimer();
        }

        // 强制更新视图
        this.$forceUpdate();
      }, 1000);
    },

    // 清除倒计时定时器
    clearCountdownTimer() {
      if (this.timer) {
        clearInterval(this.timer);
        this.timer = null;
      }
    },

    // 更新订单倒计时显示
    updateOrderCountdown(order) {
      const remainingSeconds = order.order_info.pay_timer;

      if (remainingSeconds <= 0) {
        order.countdown = "00:00";
        return;
      }

      // 计算分钟和秒数
      const minutes = Math.floor(remainingSeconds / 60);
      const seconds = remainingSeconds % 60;

      // 格式化为 MM分SS秒
      order.countdown = `${minutes.toString().padStart(2, "0")}分${seconds
        .toString()
        .padStart(2, "0")}秒`;
    },

    loadMore() {
      if (this.status === "nomore") return;
      this.page++;
      this.loadOrders();
    },

    onRefresh() {
      this.refreshing = true;
      this.page = 1;
      this.loadOrders();
    },

    getStatusText(status) {
      const statusMap = {
        unpaid: "待付款",
        paid: "已完成",
        cancel: "已取消",
        failed: "支付失败"
      };
      return statusMap[status] || "";
    },

    cancelOrder(orderId) {
      this.confirmPopup = {
        show: false,
        title: "取消后无法恢复，确认取消？",
        description: "温馨提示",
        confirmText: "我再想想",
        cancelText: "确认取消"
      };
      this.showConfirm = true;
      this.orderId = orderId;
    },
    async confirmCancel() {
      uni.showLoading({
        title: "取消中..."
      });
      // 移除空条件块，直接处理取消订单逻辑
      const id =
        this.session.role === "student"
          ? this.curStudentInfo.student_id
          : this.session.role === "customer"
          ? this.curStudentInfo.customer_id
          : this.session.open_id;
      const { code, message } = await cancelOrder({
        order_id: this.orderId,
        user_id: id
      });
      uni.hideLoading();
      if (code === 0) {
        uni.showToast({
          title: "取消成功！",
          icon: "none"
        });
        this.loadOrders();
      } else {
        uni.showToast({
          title: message,
          icon: "none"
        });
      }
      this.showConfirm = false;
    },
    clickOrder(item) {
      uni.navigateTo({
        url: `/pages/student/subpages/orderDetail/index?status=${item.order_info.status}&orderId=${item.order_info.order_id}`
      });
    },
    confirm() {
      if (this.confirmPopup.show) {
        // uni.navigateTo({
        //   url: "/pages/student/subpages/customerService/index"
        // });
      } else {
        this.showConfirm = false;
      }
      this.showConfirm = false;
    },
    async payOrder(orderInfo, orderId) {
      await this.payMin(orderInfo);
      // 跳转到订单支付页面
      // uni.navigateTo({
      //   url: `/pages/student/subpages/orderDetail/index?id=${orderId}&from=list`
      // });
    },
    // 前往支付
    async payMin(orderInfo) {
      console.log(orderInfo);
      const { data, code, message } = await payMin({
        open_id: this.session.open_id,
        order_id: orderInfo.msn,
        amount: orderInfo.actual_price,
        channel: "WECHAT"
      });
      if (code === 0) {
        wxPay(data)
          .then((res) => {
            const encodedParams = encodeURIComponent(
              JSON.stringify(orderInfo.msn)
            );
            // 支付成功后的业务逻辑
            uni.redirectTo({
              url:
                "/pages/student/subpages/paySuccess/index?orderId=" +
                encodedParams
            });
          })
          .catch((err) => {
            uni.showToast({
              title: "用户取消支付",
              icon: "none"
            });
            console.log(err);
          });
      } else {
        uni.showToast({
          title: message,
          icon: "none"
        });
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.nav-left {
  image {
    width: 40rpx;
    height: 40rpx;
  }
}

.order-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}

.order-list {
  flex: 1;
  padding: 30rpx 32rpx;
  padding-top: 0;
}

.order-list-container {
  height: 100%;
  overflow: hidden;
}

.countdown-notice {
  display: flex;
  align-items: center;
  //   background-color: #fff7e8;
  //   padding: 16rpx 24rpx;
  border-radius: 8rpx;
  margin-bottom: 34rpx;

  .countdown-text {
    color: #fe4f37;
    font-size: 26rpx;
    margin-left: 10rpx;
  }
}

.order-item:first-child {
  margin-top: 30rpx;
}

.order-item {
  background-color: #ffffff;
  border-radius: 24rpx;
  margin-bottom: 30rpx;
  overflow: hidden;
  padding: 30rpx 24rpx;
  padding-bottom: 0;

  .order-course {
    display: flex;
    // padding: 24rpx;
    position: relative;
    margin-bottom: 30rpx;

    .course-image {
      width: 160rpx;
      height: 160rpx;
      border-radius: 8rpx;
    }

    .course-info {
      margin-left: 20rpx;
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .info-top {
        display: flex;
        align-items: center;
        margin-bottom: 8rpx;

        .tag {
          // width: 80rpx;
          height: 38rpx;
          padding: 3rpx 16rpx;
          justify-content: center;
          align-items: center;
          gap: 10rpx;
          flex-shrink: 0;
          color: #fff;
          font-size: 24rpx;
          font-style: normal;
          font-weight: 400;
          line-height: normal;
          margin-right: 12rpx;
          border-radius: 16rpx 8rpx 16rpx 8rpx;
          background: linear-gradient(313deg, #ffb200 35.48%, #ffe32d 127.89%);
        }

        .course-name {
          color: #333;
          font-size: 32rpx;
          font-style: normal;
          font-weight: 500;
          line-height: normal;
          width: 258rpx;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }

      .campus-name {
        color: #333;
        font-size: 26rpx;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
      }

      .price-info {
        display: flex;
        justify-content: space-between;
        align-items: baseline;
        position: relative;

        .quantity {
          position: absolute;
          right: 0;
          bottom: 6rpx;
          color: #999999;
          font-weight: 400;
          font-size: 28rpx;
        }

        .price {
          display: flex;
          align-items: baseline;

          .price-symbol {
            color: #ff553a;
            font-size: 24rpx;
            font-style: normal;
            font-weight: 400;
            line-height: normal;
          }

          .price-value {
            color: #ff553a;
            font-size: 36rpx;
            font-style: normal;
            font-weight: 600;
            line-height: normal;
          }
        }

        .duration {
          color: #999;
          font-size: 26rpx;
          font-style: normal;
          font-weight: 400;
          line-height: normal;
          margin-left: 11rpx;
          // text-decoration: line-through;
        }
      }
    }

    .order-status {
      position: absolute;
      top: 6rpx;
      right: 0;
      // right: 24rpx;
      font-size: 26rpx;
      color: #fb0;
      font-size: 26rpx;
      font-style: normal;
      font-weight: 500;
      line-height: normal;
    }
  }

  .order-actions {
    display: flex;
    justify-content: flex-end;
    padding: 28rpx 0;
    border-top: 1rpx solid #f5f5f5;

    button {
      min-width: 160rpx;
      height: 64rpx;
      border-radius: 32rpx;
      font-size: 26rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-left: 20rpx;

      &::after {
        border: none;
      }
    }

    .cancel-btn {
      border: 1rpx solid #dddddd;
      background-color: #ffffff;
      color: #666666;
    }

    .pay-btn {
      background: linear-gradient(15deg, #ffbf0d 18.1%, #ffcb3c 83.29%);
      box-shadow: 0px -4rpx 8rpx 0px #eaac00 inset;
      color: #ffffff;
      font-weight: 500;
    }
  }
}

.no-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
  height: 100%;

  image {
    width: 200rpx;
    height: 200rpx;
    margin-bottom: 20rpx;
  }

  text {
    font-size: 28rpx;
    color: #999999;
  }
}
</style>
