<template>
  <view class="niedao-teacher-page">
    <!-- 背景和装饰图片 -->
    <view class="hero-section">
      <u-navbar
        title="聂道名师"
        :bgColor="navBgColor"
        leftIconSize="25px"
        :leftIconColor="navTextColor"
        :titleStyle="{
          color: navTextColor,
          fontSize: '34rpx',
          fontWeight: '500',
          lineHeight: '40rpx'
        }"
        :autoBack="true"
        placeholder
        fixed
      >
        <view class="nav-letf" slot="left">
          <image
            :src="
              navBgColor === '#ffffff'
                ? 'https://tg-prod.oss-cn-beijing.aliyuncs.com/e9ac0f4b-6282-4bac-b602-189057c45c90.webp'
                : 'https://tg-prod.oss-cn-beijing.aliyuncs.com/68798755-2327-4bac-8d65-3de4ed10a6e3.webp'
            "
          ></image>
        </view>
      </u-navbar>
      <!-- 橙色渐变背景 -->
      <view class="gradient-bg"></view>
      <!-- 标题区域 -->
      <view class="title-section">
        <view class="main-title">
          <image
            src="https://tg-prod.oss-cn-beijing.aliyuncs.com/0e1d2f75-aa0e-483b-a423-56206f81c610.png"
          />
        </view>
        <view class="sub-title"
          >汇聚{{ o_count }}位专业导师，<br />以匠心育棋才</view
        >
      </view>
      <!-- 地点信息 -->
      <view class="location-info">
        <view class="location-text" @click="showCityPicker">{{
          selectedCity || "全国"
        }}</view>
        <view class="location-icon">
          <u-icon
            name="https://tg-prod.oss-cn-beijing.aliyuncs.com/1497246a-a049-482b-b904-b2a4375f6443.webp"
            color="#fff"
            size="24rpx"
          ></u-icon>
        </view>
      </view>
      <!-- 装饰图片1 -->
      <image
        class="decoration-img1"
        src="https://tg-prod.oss-cn-beijing.aliyuncs.com/abb45bf7-b56e-4d56-93a2-04536cbc5373.webp"
        mode="aspectFill"
      />

      <!-- 人物图片 -->
      <image
        class="team-avatar"
        src="https://tg-prod.oss-cn-beijing.aliyuncs.com/fd3da420-1930-4f66-8cf2-9f2eff26c187.webp"
        mode="aspectFill"
      />
    </view>

    <!-- 白色内容区域 -->
    <view class="content-section">
      <!-- 师资卡片列表 -->
      <view class="teacher-list">
        <view
          v-for="(teacher, index) in teacherList"
          :key="index"
          class="teacher-card"
          @click="onTeacherClick(teacher)"
        >
          <!-- 头像 -->
          <view class="teacher-avatar-container">
            <image
              class="teacher-avatar"
              :src="teacher.photo_url"
              mode="aspectFill"
            />
          </view>

          <!-- 基本信息 -->
          <view class="teacher-info">
            <view style="width: 100%">
              <view class="teacher-name"
                >{{ teacher.teacher_name }}
                <!-- 标签组 -->
                <view class="teacher-tags">
                  <!-- 段位标签 -->
                  <view class="tag tag-level">
                    <view class="star-icon">
                      <u-icon
                        name="https://tg-prod.oss-cn-beijing.aliyuncs.com/a83bda02-0d6b-47b2-b913-3c85db3301ca.webp"
                        size="16"
                      ></u-icon>
                    </view>
                    <view class="tag-text">{{ teacher.dan_level_name }}</view>
                  </view>

                  <!-- 教龄标签 -->
                  <view class="tag tag-experience">
                    <view class="tag-text"
                      >教龄{{ teacher.teaching_year }}年</view
                    >
                  </view>
                </view></view
              >
              <view class="teacher-campus">{{
                teacher.department_names.join(",")
              }}</view>
            </view>

            <!-- 荣誉信息 -->
            <view class="honor-info">
              <view class="honor-text">荣誉：{{ teacher.honor }}</view>
            </view>
          </view>
        </view>
        <EmptyIcon v-if="teacherList.length === 0" text="暂无聂道名师~" />
      </view>

      <!-- 加载更多组件 -->
      <view class="loadmore-container" v-if="teacherList.length > 0">
        <u-loadmore
          :status="loadStatus"
          :load-text="{
            loadmore: '上拉加载更多',
            loading: '正在加载...',
            nomore: '没有更多数据了'
          }"
          :line="true"
          lineColor="#e6e6e6"
          :marginTop="20"
          :marginBottom="20"
        />
      </view>
    </view>
    <!-- 城市选择弹窗 -->
    <CityPicker
      :show="showCityModal"
      :cityList="cityList"
      :selectedCity="selectedCity"
      @close="closeCityPicker"
      @select="selectCity"
      :keys="['city']"
    />
  </view>
</template>

<script>
import CityPicker from "@/components/CityPicker/index.vue";
import {
  departmentCity,
  niedaoTeacherList
} from "@/services/student/niedaoTeacher";
import EmptyIcon from "@/components/empty";
export default {
  name: "niedao-teacher-page",
  components: {
    CityPicker,
    EmptyIcon
  },
  data() {
    return {
      showCityModal: false,
      selectedCity: "北京",
      cityList: [],
      teacherList: [],
      page: 1,
      page_size: 10,
      total: 0,
      loadStatus: "loadmore", // loadmore, loading, nomore
      isFirstLoad: true, // 是否首次加载
      navBgColor: "transparent", // 导航栏背景色
      navTextColor: "#FFFFFF", // 导航栏文字颜色
      scrollThreshold: 50, // 滚动阈值，超过这个值导航栏变白色
      o_count: 0
    };
  },
  watch: {
    selectedCity: {
      handler(newVal) {
        this.resetList();
        this.getNiedaoTeacherList();
      },
      immediate: true
    }
  },
  methods: {
    onBack() {
      uni.navigateBack();
    },
    onTeacherClick(teacher) {
      console.log("点击教师:", teacher);
      // 可以跳转到教师详情页
      uni.navigateTo({
        url: `/pages/student/subpages/campus/teacherDetail?id=${teacher.id}`
      });
    },
    showCityPicker() {
      this.showCityModal = true;
    },
    closeCityPicker() {
      this.showCityModal = false;
    },
    selectCity(city) {
      this.selectedCity = city;
      this.showCityModal = false;
    },
    async getDepartmentCity() {
      const { data, code, message } = await departmentCity();
      if (code === 0) {
        this.cityList = data;
        this.cityList.unshift({
          city: "全国"
        });
        this.selectedCity = "";
      } else {
        uni.showToast({
          title: message,
          icon: "none"
        });
      }
    },
    // 重置列表数据
    resetList() {
      this.teacherList = [];
      this.page = 1;
      this.total = 0;
      this.loadStatus = "loadmore";
      this.isFirstLoad = true;
    },
    // 加载更多教师数据
    async loadMoreTeachers() {
      // 检查是否还有更多数据
      if (this.teacherList.length >= this.total && this.total > 0) {
        this.loadStatus = "nomore";
        return;
      }

      this.loadStatus = "loading";
      await this.getNiedaoTeacherList();
    },
    async getNiedaoTeacherList() {
      try {
        const { data, code, message } = await niedaoTeacherList({
          city: this.selectedCity === "全国" ? "" : this.selectedCity,
          page: this.page,
          page_size: this.page_size,
          source: 1
        });

        if (code === 0) {
          if (this.isFirstLoad) {
            this.teacherList = data.results || [];
            this.isFirstLoad = false;
          } else {
            this.teacherList = [...this.teacherList, ...(data.results || [])];
          }

          this.total = data.count || 0;
          this.o_count = data.o_count || 0;
          this.page = (data.page || this.page) + 1;
          this.page_size = data.page_size || this.page_size;

          // 判断是否还有更多数据
          if (this.teacherList.length >= this.total) {
            this.loadStatus = "nomore";
          } else {
            this.loadStatus = "loadmore";
          }
        } else {
          this.loadStatus = "loadmore";
          uni.showToast({
            title: message || "加载失败",
            icon: "none"
          });
        }
      } catch (error) {
        console.error("获取教师列表失败:", error);
        this.loadStatus = "loadmore";
        uni.showToast({
          title: "网络错误，请重试",
          icon: "none"
        });
      }
    },
    // 处理页面滚动
    handlePageScroll(scrollTop) {
      if (scrollTop > this.scrollThreshold) {
        // 滚动超过阈值，导航栏变白色
        this.navBgColor = "#ffffff";
        this.navTextColor = "#333333";
      } else {
        // 滚动在阈值内，导航栏透明
        this.navBgColor = "transparent";
        this.navTextColor = "#FFFFFF";
      }
    }
  },
  // 页面触底事件
  onReachBottom() {
    if (this.loadStatus === "nomore" || this.loadStatus === "loading") {
      return;
    }
    this.loadMoreTeachers();
  },
  // 页面滚动事件
  onPageScroll(e) {
    this.handlePageScroll(e.scrollTop);
  },
  mounted() {
    this.getDepartmentCity();
  }
};
</script>

<style lang="scss" scoped>
.niedao-teacher-page {
  background: #f5f5f5;
  min-height: 100vh;
  font-family: PingFang SC, Arial, sans-serif;
  padding-bottom: env(safe-area-inset-bottom);
}

// Hero区域
.hero-section {
  width: 750rpx;
  height: 536rpx;
  position: relative;
  overflow: hidden;
}

.gradient-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #ff6e34 0%, #ffa51e 93.64%);
  z-index: 1;
}

.decoration-img1 {
  position: absolute;
  top: 335rpx;
  left: 30rpx;
  width: 495rpx;
  height: 219rpx;
  z-index: 2;
}

.team-avatar {
  position: absolute;
  top: 218rpx;
  right: 21rpx;
  width: 317rpx;
  height: 317rpx;
  border-radius: 50%;
  z-index: 3;
}

// 白色内容区域
.content-section {
  background: #f5f5f5;
  border-radius: 40rpx 40rpx 0 0;
  margin-top: -83rpx;
  position: relative;
  z-index: 4;
  min-height: calc(100vh - 453rpx);
}

// 标题区域
.title-section {
  padding: 38rpx 32rpx 0;
  position: relative;
  z-index: 5;
}

.main-title {
  font-size: 54rpx;
  font-weight: 700;
  color: #fff;
  line-height: 1.11;
  letter-spacing: 3.7%;
  margin-bottom: 20rpx;
  width: 390rpx;
  height: 60rpx;
  image {
    width: 100%;
    height: 100%;
  }
}

.sub-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #fff;
  line-height: 1.375;
}

// 地点信息
.location-info {
  position: absolute;
  top: 225rpx;
  right: 32rpx;
  display: flex;
  align-items: center;
  gap: 8rpx;
  z-index: 5;
}

.location-text {
  font-size: 28rpx;
  color: #fff;
  line-height: 1.43;
}

.location-icon {
  width: 24rpx;
  height: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

// 师资卡片列表
.teacher-list {
  padding: 48rpx 32rpx 0;
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.teacher-card {
  width: 686rpx;
  height: 240rpx;
  background: #fff;
  border-radius: 24rpx;
  padding: 0;
  position: relative;
  cursor: pointer;
  transition: transform 0.2s ease;
  box-shadow: 0rpx 2rpx 12rpx 0rpx rgba(0, 0, 0, 0.04);

  &:active {
    transform: scale(0.98);
  }
}

// 教师头像
.teacher-avatar-container {
  position: absolute;
  left: 0;
  top: 0;
  width: 180rpx;
  height: 240rpx;
  background: #f2f2f2;
  border-radius: 16rpx;
  overflow: hidden;
}

.teacher-avatar {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

// 教师信息
.teacher-info {
  margin-left: 204rpx;
  padding: 25rpx 24rpx 20rpx 0;
  height: 100%;
  display: flex;
  align-content: space-between;
  flex-direction: column;
  justify-content: space-between;
  flex-wrap: wrap;
}

.teacher-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  line-height: 1.4;
  display: flex;
}

.teacher-campus {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 100%;
}

// 标签组
.teacher-tags {
  display: flex;
  gap: 16rpx;
  margin-bottom: 16rpx;
  margin-left: 8rpx;
}

.tag {
  height: 44rpx;
  border-radius: 9rpx;
  border: 2rpx solid;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 12rpx;
  gap: 8rpx;
}

.tag-level {
  border-color: #ff9b00;
  background: rgba(255, 155, 0, 0.05);
}

.tag-experience {
  border-color: #578df9;
  background: rgba(87, 141, 249, 0.05);
}

.tag-text {
  font-size: 24rpx;
  font-weight: 400;
  line-height: 1.33;
  white-space: nowrap;

  .tag-level & {
    color: #ff9000;
  }

  .tag-experience & {
    color: #578df9;
  }
}

.star-icon {
  width: 30rpx;
  height: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;

  :deep(.u-icon) {
    background: linear-gradient(180deg, #ffbb00 0%, #ff9900 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
}

// 荣誉信息
.honor-info {
  background: rgba(247, 247, 247, 0.8);
  border-radius: 8rpx;
  padding: 15rpx 12rpx;
  overflow: hidden;
  height: 56rpx;
  width: 100%;
}

.honor-text {
  font-size: 24rpx;
  color: #666;
  line-height: 1.25;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 100%;
}

// 加载更多容器
.loadmore-container {
  padding: 0 32rpx 32rpx;
}

.nav-letf {
  width: 40rpx;
  height: 40rpx;
  image {
    width: 100%;
    height: 100%;
  }
}
::v-deep .empty-icon {
  margin-top: 300rpx;
}
</style>
