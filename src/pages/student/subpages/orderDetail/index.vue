<template>
  <view class="order-detail-container">
    <u-toast ref="uToast" style="z-index: 999999"></u-toast>
    <!-- 顶部导航栏和倒计时 -->
    <OrderHeader
      :title="title"
      :showCountdown="showCountdown"
      :countdownTime="globalCountdownTime"
    />

    <view class="order-detail-content">
      <!-- 课程信息 -->
      <CourseCard
        v-for="(item, index) in orderInfo.goods || []"
        :key="index"
        :item="item"
        :quantity="orderInfo.goods ? orderInfo.goods.length : 0"
        @handleCourseDetail="handleCourseDetail"
        :isOrderDetail="isOrderDetail"
      />

      <!-- 订单选项 -->
      <OrderOptions
        v-if="isOrderDetail"
        :isOrderDetail="isOrderDetail"
        :selectedCampusName="
          orderInfo.user_info.department_name || selectedCampusName
        "
        :selectedCouponAmount="discountNumber"
        :couponsAvailable="couponList ? couponList.length > 0 : false"
        :departmentAvailable="
          orderInfo.user_info.department_id && session.role !== 'default'
        "
        @showCampusSelector="showCampusSelector"
        @showCouponSelector="showCouponSelector"
      />

      <!-- 费用明细 -->
      <CostDetails
        :originalPrice="
          orderInfo.order_info ? orderInfo.order_info.original_price : 0
        "
        :totalDiscount="
          orderInfo.order_info ? orderInfo.order_info.total_discount : 0
        "
        :discountPrice="
          orderInfo.order_info ? orderInfo.order_info.actual_price : 0
        "
        :userInfo="orderInfo.user_info ? orderInfo.user_info : ''"
        :isOrderCompleted="isOrderCompleted"
        :isOrderDetail="isOrderDetail"
        :visible="paymentMethod"
      />

      <!-- 支付方式 -->
      <!-- <PaymentMethod :visible="paymentMethod" /> -->
    </view>

    <view class="plc" v-if="isOrderDetail">
      备注：如已有学员，请先绑定学员再下单
    </view>

    <!-- 底部提交区域 -->
    <OrderActions
      :visible="paymentMethod"
      :isOrderDetail="isOrderDetail"
      :totalAmount="
        orderInfo.order_info ? orderInfo.order_info.actual_price : 0
      "
      :selectedCampusName="selectedCampusName"
      @submit="$u.throttle(submitOrder, 1000)"
      @cancel="cancelOrder"
      @pay="payOrder"
    />
    <view @touchmove.stop.prevent="">
      <!-- 校区选择弹窗 -->
      <campus-selector
        :show="campusSelectorVisible"
        :currentCampus="selectedCampusId"
        @close="campusSelectorVisible = false"
        @select="handleCampusSelect"
        :schoolList="schoolList"
        @handleRegister="handleRegister"
      />

      <!-- 优惠券选择弹窗 -->
      <coupon-selector
        :show="couponSelectorVisible"
        :currentCoupon="selectedCoupon"
        :couponList="couponList"
        @close="couponSelectorVisible = false"
        @select="handleCouponSelect"
        @overlay="getOverlayList"
        :overlayList="overlayList"
        :discount="discount"
        @calculateCouponAmount="calculateCouponAmount"
        @getCouponList="getCouponList"
      />

      <!-- 添加系统繁忙弹窗 -->
      <system-busy-popup
        :show="showSystemBusy"
        :message="busyMessage"
        @close="showSystemBusy = false"
      />

      <ConfirmPopup
        v-model="showConfirm"
        :title="confirmPopup.title"
        :description="confirmPopup.description"
        :confirmText="confirmPopup.confirmText"
        :cancelText="confirmPopup.cancelText"
        @confirm="handleConfirm"
        @cancel="handleCancel"
      />
      <!-- 购买无权限时 -->
      <ContactPopup v-model="showContact" :image="qrCode" :text="qrName" />
    </view>
  </view>
</template>

<script>
import CampusSelector from "./components/CampusSelector.vue";
import CouponSelector from "./components/CouponSelector.vue";
import SystemBusyPopup from "@/components/SystemBusyPopup";
import ConfirmPopup from "@/components/ConfirmPopup";
import OrderHeader from "./components/OrderHeader.vue";
import CourseCard from "./components/CourseCard.vue";
import OrderOptions from "./components/OrderOptions.vue";
import CostDetails from "./components/CostDetails.vue";
import OrderActions from "./components/OrderActions.vue";
import ContactPopup from "../../home/<USER>/ContactPopup.vue";
import {
  detailOrder,
  calculateOrder,
  couponList,
  createOrder,
  cancelOrder,
  overlayList,
  getCouponPrice,
  getSchool,
  payMin
} from "@/services/student/order";
import { wxPay } from "@/utils/user";

export default {
  name: "OrderDetail",
  components: {
    CampusSelector,
    CouponSelector,
    SystemBusyPopup,
    ConfirmPopup,
    OrderHeader,
    CourseCard,
    OrderOptions,
    CostDetails,
    OrderActions,
    ContactPopup
  },
  data() {
    return {
      orderInfo: {},
      selectedCampusId: "",
      selectedCampusName: "",
      selectedCoupon: null,
      campusSelectorVisible: false,
      couponSelectorVisible: false,
      showSystemBusy: false,
      busyMessage: "",
      title: "订单详情",
      orderStatus: "",
      confirmPopup: {
        show: false,
        title: "",
        description: "",
        confirmText: "",
        cancelText: ""
      },
      courseId: "",
      showConfirm: false,
      globalCountdownTime: "00:00",
      showGlobalCountdown: false,
      countdownTimer: null,
      curStudentInfo: "",
      session: "",
      visitorInfo: "",
      courseName: "",
      standardNumb: "",
      couponList: [],
      showContact: false,
      qrCode: "",
      qrName: "购买失败，请联系管理员",
      minproCourseId: "",
      orderId: "",
      overlayList: [],
      discount: null,
      discountNumber: 0,
      schoolList: null,
      orderMsn: "",
      confirmPopOrderId: "",
      statusMap: {
        unpaid: "等待付款",
        paid: "交易成功",
        cancel: "已取消",
        delivered: "订单详情"
      }
    };
  },
  computed: {
    finalAmount() {
      let amount = this.orderInfo.price;
      if (this.selectedCoupon) {
        amount -= this.selectedCoupon.amount;
      }
      return amount > 0 ? amount : 0;
    },
    // 订单超时时间是否显示
    orderTime() {
      return this.orderStatus === "unpaid";
    },
    // 支付方式是否显示
    paymentMethod() {
      return this.orderStatus === "unpaid" || this.orderStatus === "delivered";
    },
    // 是否为订单详情页
    isOrderDetail() {
      return this.orderStatus === "delivered";
    },
    // 订单完成
    isOrderCompleted() {
      return this.orderStatus === "paid";
    },
    // 是否显示倒计时
    showCountdown() {
      return this.orderStatus === "unpaid";
    }
  },
  methods: {
    handleCampusSelect(campus) {
      console.log(campus);
      this.selectedCampusId = campus.department_id;
      this.selectedCampusName = campus.department_name;
    },
    handleCouponSelect(coupon) {
      this.selectedCoupon = coupon.map((coupon, index) => ({
        template_id: coupon.coupon_id || "",
        coupon_id: coupon.id || "",
        coupon_order: index,
        coupon_type: coupon.coupon_type,
        quota: coupon.quota,
        template_name: coupon.name
      }));
      this.discountNumber = this.discount;
      this.handleRegister();
    },
    async submitOrder() {
      try {
        // 验证必填项
        if (!this.selectedCampusName) {
          uni.showToast({
            title: "请选择校区",
            icon: "none"
          });
          return;
        }
        uni.showLoading({
          title: "提交中..."
        });
        // 调用提交订单接口
        this.orderInfo.payment = [
          {
            pay_method: "mini_program",
            pay_price: this.orderInfo.order_info.actual_price
          }
        ];
        this.orderInfo.user_info.department_id = this.selectedCampusId;
        this.orderInfo.user_info.department_name = this.selectedCampusName;
        const { data, code, message } = await createOrder({
          ...this.orderInfo
        });
        if (code === 0) {
          uni.hideLoading();
          console.log("提交订单成功", data);
          this.orderId = data.order_id;
          this.orderMsn = data.msn;
          await this.payMin();
          // uni.navigateTo({
          //   url:
          //     "/pages/student/subpages/paySuccess/index?orderId=" +
          //     data.order_id
          // });
        } else {
          uni.hideLoading();
          if (code === 2001003) {
            // 已有订单
            this.confirmPopup = {
              show: true,
              title: "您已有一笔待支付订单",
              description: "",
              confirmText: "前往支付",
              cancelText: "取消",
              type: 2
            };
            console.log(code);
            this.confirmPopOrderId = data.order_id;
            this.showConfirm = true;
            return;
          }
          uni.showToast({
            title: message,
            icon: "none"
          });
        }
        // 成功处理
        // if (result.success) {
        //   uni.navigateTo({
        //     url:
        //       "/pages/student/subpages/paySuccess/index?courseId=" +
        //       result.data.courseId
        //   });
        //   uni.hideLoading();
        // } else {
        //   // 显示错误信息
        //   this.busyMessage = result.message || "订单提交失败，请稍后再试";
        //   this.showSystemBusy = true;
        //   uni.hideLoading();
        // }
      } catch (error) {
        console.error("提交订单失败:", error);
        this.busyMessage = "系统繁忙，请稍后再试";
        this.showSystemBusy = true;
        uni.hideLoading();
      }
    },
    cancelOrder() {
      this.confirmPopup = {
        show: false,
        title: "取消后无法恢复，确认取消？",
        description: "温馨提示",
        confirmText: "我再想想",
        cancelText: "确认取消"
      };
      this.showConfirm = true;
    },
    payOrder() {
      // 支付逻辑
      console.log("支付订单", this.courseId);
      try {
        this.payMin();
      } catch (error) {
        console.log(error);
        this.busyMessage = "系统繁忙，请稍后再试";
        this.showSystemBusy = true;
      }

      // uni.navigateTo({
      //   url: "/pages/student/subpages/paySuccess/index?orderId=" + this.orderId
      // });
    },
    async handleCancel() {
      console.log("取消订单");
      if (this.confirmPopup.show) {
        if (this.confirmPopup.type === 1) {
          uni.redirectTo({
            url: "/pages/student/studentPage/index?prospective=login"
          });
        } else {
          this.showConfirm = false;
        }
      } else {
        const { code, message } = await cancelOrder({
          order_id: this.orderId,
          user_id:
            this.session.role === "student"
              ? this.curStudentInfo.student_id
              : this.session.role === "customer"
              ? this.curStudentInfo.customer_id
              : this.session.open_id
        });
        if (code === 0) {
          uni.showToast({
            title: "取消订单成功",
            icon: "none"
          });
          // 倒计时结束，清除定时器
          clearInterval(this.countdownTimer);
          this.globalCountdownTime = "00:00";
          this.showGlobalCountdown = false;

          // 订单超时处理
          this.handleOrderExpired();
        } else {
          uni.showToast({
            title: message,
            icon: "none"
          });
        }
      }
    },
    handleConfirm() {
      if (this.confirmPopup.show) {
        if (this.confirmPopup.type === 1) {
          this.showConfirm = false;
          this.showContact = true;
        } else {
          this.orderStatus = "unpaid";
          this.title = "等待付款";
          this.orderId = this.confirmPopOrderId;
          this.getOrderDetail();
        }
      }
      this.showConfirm = false;
    },
    // 启动倒计时
    startCountdown() {
      // 清除可能存在的计时器
      if (this.countdownTimer) {
        clearInterval(this.countdownTimer);
      }

      // 如果没有pay_timer或order_info，不启动倒计时
      if (
        !this.orderInfo ||
        !this.orderInfo.order_info ||
        !this.orderInfo.order_info.pay_timer
      ) {
        console.log("没有pay_timer或order_info未初始化");
        return;
      }

      this.countdownTimer = setInterval(() => {
        // 确保order_info仍然存在
        if (!this.orderInfo || !this.orderInfo.order_info) {
          this.clearCountdownTimer();
          return;
        }

        // 减少计时器
        this.orderInfo.order_info.pay_timer -= 1;
        this.updateCountdown();

        // 检查是否过期
        if (this.orderInfo.order_info.pay_timer <= 0) {
          // 倒计时结束，清除定时器
          clearInterval(this.countdownTimer);
          this.globalCountdownTime = "00:00";
          this.showGlobalCountdown = false;

          // 订单超时处理
          this.handleOrderExpired();
        }
      }, 1000);
    },

    // 更新倒计时显示
    updateCountdown() {
      // 添加安全检查
      if (!this.orderInfo || !this.orderInfo.order_info) {
        return;
      }

      const remainingSeconds = this.orderInfo.order_info.pay_timer;

      if (remainingSeconds <= 0) {
        this.globalCountdownTime = "00:00";
        return;
      }

      // 计算分钟和秒数
      const minutes = Math.floor(remainingSeconds / 60);
      const seconds = remainingSeconds % 60;

      // 格式化显示
      this.globalCountdownTime = `${this.padZero(minutes)}:${this.padZero(
        seconds
      )}`;
      this.showGlobalCountdown = true;
    },

    // 数字补零
    padZero(num) {
      return num < 10 ? `0${num}` : num;
    },

    // 处理订单过期
    handleOrderExpired() {
      // 可以在这里处理订单过期的逻辑
      // 例如提示用户或自动刷新页面
      // uni.showToast({
      //   title: "订单已超时自动取消",
      //   icon: "none"
      // });

      // 可以选择更新订单状态或刷新页面
      setTimeout(() => {
        this.orderStatus = "cancelled";
        this.title = "已取消";
      }, 1500);
    },

    // 获取订单详情API
    async getOrderDetail() {
      const id =
        this.session.role === "student"
          ? this.curStudentInfo.student_id
          : this.session.role === "customer"
          ? this.curStudentInfo.customer_id
          : this.session.open_id;
      const { data, code, message } = await detailOrder({
        order_id: this.orderId,
        user_id: id
      });
      if (code === 0) {
        this.orderInfo = data;
        this.orderMsn = data.order_info.msn;
        // this.selectedCampusName = data.user_info.department_name;
        // this.selectedCampusId = data.user_info.department_id;
        if (this.orderStatus === "unpaid") {
          this.startCountdown();
        }
      } else {
        uni.showToast({
          title: message,
          icon: "none"
        });
      }
    },
    // 计算订单
    async handleRegister() {
      const id =
        this.session.role === "student"
          ? this.curStudentInfo.student_id
          : this.session.role === "customer"
          ? this.curStudentInfo.customer_id
          : this.session.open_id;
      // 无专用接口 使用计算接口
      const { code, message, data } = await calculateOrder({
        discount: {
          coupons: this.selectedCoupon || []
        },
        goods: [
          {
            apportion: 0,
            expire_strategy: {
              end_time: "",
              start_time: "",
              strategy: "",
              validity_period: 0
            },
            fee_type: "",
            goods_id: this.courseId,
            goods_name: this.courseName,
            goods_num: Number(this.standardNumb),
            goods_type: "course",
            goods_unit: "",
            is_forced_sell: 0,
            parent_id: "",
            parent_type: "",
            related_goods_id: this.minproCourseId
          }
        ],
        order_info: {
          charge_date: "",
          inner_remark: "",
          outer_remark: "",
          push_parent: 0
        },
        payment: [],
        user_info: {
          department_id:
            this.curStudentInfo.department_id || this.selectedCampusId,
          student_mobile:
            this.session.role === "default"
              ? this.session.visitor_mobile
              : this.curStudentInfo.student_mobile || "",
          student_name:
            this.session.role === "default"
              ? this.visitorInfo.student_name
              : this.curStudentInfo.student_name || "",
          user_id: id,
          user_type:
            this.session.role === "default" ? "visitor" : this.session.role
        }
      });
      if (code === 0) {
        this.orderInfo = data;
        this.selectedCampusName = data.user_info.department_name;
        this.selectedCampusId = data.user_info.department_id;
      } else {
        //           {
        //   "0": "success",
        //   "1": "网络异常，请稍后再试",
        //   "1000000": "参数错误",
        //   "2001000": "您手机号已有关联用户",
        //   "2001001": "课程暂不支持购买",
        //   "2001002": "用户类型不能为空",
        //   "2001003": "您已有一笔待支付订单",
        //   "2001004": "用户ID不能为空",
        //   "2001005": "对不起，系统繁忙，请稍后再试",
        //   "2001006": "订单编号不能为空",
        //   "2001007": "未查询到订单",
        //   "2001008": "订单取消失败",
        //   "2001009": "商品信息已发生变化，请刷新页面后重试",
        //   "2001010": "课程对当前校区不可售卖，请更换其它校区",
        //   "2001011": "收款总金额与应交金额不相等",
        //   "2001012": "优惠券已被使用",
        //   "2001013": "用户与学员不存在绑定关系",
        //   "2001014": "用户类型不正确",
        //   "2001015": "用户身份标识不正确",
        //   "2001016": "用户所属校区不能为空",
        //   "2001017": "订单商品不能为空",
        //   "2001018": "商品类型不能为空",
        //   "2001019": "商品数量不能为空",
        //   "2001020": "优惠券ID不能为空",
        //   "2001021": "支付参数缺失",
        //   "2001022": "支付方式不能为空",
        //   "2001023": "订单金额不能为负数",
        //   "2001024": "商品ID不能为空"
        // }
        if (code === 2001000) {
          this.confirmPopup = {
            show: true,
            title: "您手机号已有关联用户",
            description: "",
            confirmText: "联系客服",
            cancelText: "去绑定",
            type: 1
          };
          // 去绑定用户
          this.showConfirm = true;
          this.qrCode = data.qr_code;
          this.qrName = `购买失败，请联系${data.name}`;
          uni.hideLoading();
          return;
        }
        if (code === 2001001) {
          if (data) {
            this.qrCode = data.qr_code;
            this.qrName = "课程暂不支持购买，请联系客服";
            this.showContact = true;
          } else {
            setTimeout(() => {
              uni.navigateBack();
            }, 2000);
            this.$refs.uToast.show({
              message,
              icon: "none",
              duration: 2000
            });
          }
          uni.hideLoading();
          uni.showToast({
            title: message,
            icon: "none",
            duration: 2000
          });
          return;
        }
        uni.showToast({
          title: message,
          icon: "none"
        });
      }
    },

    // 获取优惠券列表
    async getCouponList(used_coupon_ids) {
      uni.showLoading({
        title: "加载中..."
      });
      if (!this.orderInfo.charge_date) {
        uni.hideLoading();
      }
      const { code, message, data } = await couponList({
        charge_date: this.orderInfo.order_info.charge_date,
        course_list: [
          {
            course_id: this.orderInfo.goods[0].goods_id,
            couse_type: this.orderInfo.goods[0].goods_type,
            price: this.orderInfo.goods[0].original_total_price * 100
          }
        ],
        used_coupon_ids: used_coupon_ids || [],
        student_id:
          this.session.role === "student"
            ? this.curStudentInfo.student_id
            : this.session.role === "customer"
            ? this.curStudentInfo.customer_id
            : this.session.open_id
      });
      uni.hideLoading();
      if (code === 0) {
        this.couponList = data;
      } else {
        uni.showToast({
          title: message,
          icon: "none"
        });
      }
    },
    // 校区选择器
    showCampusSelector() {
      if (this.session.role === "default") {
        this.campusSelectorVisible = true;
      }
    },
    // 获取叠加列表
    async getOverlayList(couponId, coupons) {
      uni.showLoading({
        title: "加载中..."
      });
      const { code, message, data } = await overlayList({
        coupon_id: couponId,
        charge_date: this.orderInfo.order_info.charge_date,
        course_list: [
          {
            course_id: this.orderInfo.goods[0].goods_id,
            couse_type: this.orderInfo.goods[0].goods_type,
            price: this.orderInfo.goods[0].original_total_price * 100
          }
        ],
        used_coupon_ids: coupons,
        student_id:
          this.session.role === "student"
            ? this.curStudentInfo.student_id
            : this.session.role === "customer"
            ? this.curStudentInfo.customer_id
            : this.session.open_id
      });
      if (code === 0) {
        this.overlayList = data;
      } else {
        uni.showToast({
          title: message,
          icon: "none"
        });
      }
      uni.hideLoading();
    },
    // 计算优惠卷金额
    async calculateCouponAmount(item) {
      const coupons = item.map((coupon, index) => ({
        template_id: coupon.coupon_id || "",
        coupon_code: coupon.id || "",
        order: index
      }));
      const { data, code, message } = await getCouponPrice({
        coupons,
        items: [
          {
            business_id: this.orderInfo.goods[0].goods_id,
            business_type: this.orderInfo.goods[0].goods_type,
            due_collect_total_price:
              this.orderInfo.goods[0].original_total_price
          }
        ]
      });
      if (code === 0) {
        this.discount = data[this.orderInfo.goods[0].goods_id].use_coupons;
      } else {
        uni.showToast({
          title: message,
          icon: "none"
        });
      }
    },
    // 获取校区
    async getSchool() {
      const { data, code, message } = await getSchool({
        course_id: this.orderInfo.goods[0].goods_id
      });
      if (code === 0) {
        this.schoolList = data;
      } else {
        uni.showToast({
          title: message,
          icon: "none"
        });
      }
    },
    // 前往支付
    async payMin() {
      const encodedParams = encodeURIComponent(JSON.stringify(this.orderMsn));
      if (this.orderInfo.order_info.actual_price > 0) {
        const { data, code, message } = await payMin({
          open_id: this.session.open_id,
          order_id: this.orderMsn,
          amount: this.orderInfo.order_info.actual_price,
          channel: "WECHAT"
        });
        if (code === 0) {
          // // 支付成功后的业务逻辑
          // const encodedParams = encodeURIComponent(JSON.stringify(this.orderMsn));
          await wxPay(data)
            .then((res) => {
              uni.redirectTo({
                url:
                  "/pages/student/subpages/paySuccess/index?orderId=" +
                  encodedParams
              });
            })
            .catch((err) => {
              uni.showToast({
                title: "用户取消支付",
                icon: "none"
              });
              // 订单待支付
              this.orderStatus = "unpaid";
              this.title = "等待付款";
              this.orderId = data.orderId;
              this.getOrderDetail();
              console.log(err);
            });
        } else {
          uni.showToast({
            title: message,
            icon: "none"
          });
        }
      } else {
        uni.redirectTo({
          url:
            "/pages/student/subpages/paySuccess/index?orderId=" +
            encodedParams +
            "&isFree=true"
        });
      }
    },
    // 优惠卷弹窗
    async showCouponSelector() {
      await this.getCouponList(
        this.orderInfo.discount.coupons.map((coupon) => coupon.coupon_id)
      );
      this.couponSelectorVisible = true;
    },
    // 课程详情
    handleCourseDetail(courseId) {
      if (this.isOrderDetail) return;
      uni.redirectTo({
        url: `/pages/student/subpages/courseDetail/index?course_id=${courseId}&&show=false`
      });
    }
  },
  onShow() {
    const encodedParams = encodeURIComponent(
      JSON.stringify("123213asdasdzxcqwe_======")
    );
    console.log(encodedParams);
  },
  async onLoad(options) {
    uni.showLoading({
      title: "加载中..."
    });
    this.courseId = options.course_id;
    this.courseName = options.course_name;
    this.standardNumb = options.standard_numb;
    this.minproCourseId = options.minpro_course_id;
    this.statusMap = {
      unpaid: "等待付款",
      paid: "交易成功",
      cancel: "已取消",
      delivered: "订单详情"
    };
    this.orderStatus = options.status || "delivered";
    if (this.statusMap[this.orderStatus]) {
      this.title = this.statusMap[this.orderStatus];
    }
    this.session = uni.getStorageSync("session");
    this.curStudentInfo = uni.getStorageSync("curStudentInfo");
    this.visitorInfo = uni.getStorageSync("visitorInfo");
    console.log(uni.getStorageSync("visitorInfo"), "游客信息");
    // 如果是订单详情页进入的 则视为第一次下单
    if (this.orderStatus === "delivered") {
      await this.handleRegister();
    } else {
      this.orderId = options.orderId;
      await this.getOrderDetail();
    }

    // 获取优惠券列表
    if (this.session.role !== "default" && this.orderStatus === "delivered") {
      await this.getCouponList();
    } else {
      this.couponList = [];
      await this.getSchool();
    }
    uni.hideLoading();
    uni.hideShareMenu();
  },
  onUnload() {
    // 在页面卸载时清除定时器
    if (this.countdownTimer) {
      clearInterval(this.countdownTimer);
      this.countdownTimer = null;
    }
  }
};
</script>

<style lang="scss" scoped>
.order-detail-container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 160rpx; /* 为底部按钮留出空间 */
}

.order-detail-content {
  padding: 30rpx 30rpx;
  padding-bottom: 0;
}

.plc {
  padding: 0 30rpx;
  margin-bottom: 20rpx;
  font-size: 24rpx;
  color: #999;
}

/* 保留独立样式，其他样式已移至组件内 */
</style>
