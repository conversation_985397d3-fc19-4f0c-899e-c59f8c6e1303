<template>
  <u-popup
    :show="showPopup"
    mode="bottom"
    round="20rpx"
    height="70%"
    @close="closePopup"
    :closeOnClickOverlay="true"
  >
    <view class="coupon-popup">
      <!-- 标题栏 -->
      <view class="popup-header">
        <text class="popup-title">优惠券</text>
      </view>

      <!-- 内容区域 -->
      <scroll-view scroll-y class="popup-content">
        <view v-if="couponList.length === 0" class="no-coupon">
          <image
            src="https://tg-prod.oss-cn-beijing.aliyuncs.com/empty-coupon.png"
            mode="aspectFit"
          ></image>
          <text>暂无可用优惠券</text>
        </view>

        <view style="height: 686rpx" v-else>
          <view
            class="coupon-item"
            v-for="(item, index) in couponList"
            :key="index"
            @tap="selectCoupon(item)"
            :class="{
              'discount-type': !canSelectCoupon(item) && !isSelected(item.id)
            }"
          >
            <!-- 左侧金额/折扣区域 -->
            <view class="coupon-left">
              <view class="coupon-value">
                <text v-if="item.coupon_type !== 2" class="symbol">¥</text>
                <view class="number"
                  >{{
                    item.coupon_type === 1
                      ? item.quota / 100
                      : item.quota / 10
                  }}
                  <view class="text-num">
                    {{ item.coupon_type === 2 ? "折" : "" }}
                  </view>
                </view>
              </view>
              <view class="coupon-condition" v-if="item.used_threshold > 0">
                满{{ item.used_threshold / 100 }}元可用</view
              >
              <view class="coupon-condition" v-else> 无使用门槛</view>
            </view>

            <!-- 右侧详情区域 -->
            <view class="coupon-right">
              <view class="couponCount">
                <view class="coupon-name">{{ item.name }}</view>
              <view class="coupon-validity"
                >有效期:{{ formatTime(item.valid_time_start) }} -
                {{ formatTime(item.valid_time_end) }}</view
              >
              </view>

              <!-- 选中标记 -->
              <view
                class="select-mark"
                v-if="selectedCouponIds.includes(item.id)"
              >
                <u-icon
                  name="https://tg-prod.oss-cn-beijing.aliyuncs.com/6f20a858-c7da-483e-92b0-bad34e9bba44.webp"
                  color="#FF9500"
                  width="40rpx"
                  height="40rpx"
                ></u-icon>
              </view>
            </view>
          </view>
        </view>
      </scroll-view>

      <!-- 底部汇总栏 -->
      <view class="coupon-summary">
        <view class="selected-info" v-if="selectedCount">
          <view style="line-height: 40rpx;">
            已选{{ selectedCount }}张，可优惠
          </view>
          <view class="discount-amount"
            >¥
            <view class="discount-amount-num">
              {{ discount }}
            </view>
          </view>
        </view>
        <view class="selected-info" v-else>未选择优惠卷</view>
        <button class="confirm-btn" @tap="confirmSelection">确定</button>
      </view>
    </view>
  </u-popup>
</template>

<script>
export default {
  name: "CouponSelector",
  props: {
    show: {
      type: Boolean,
      default: false
    },
    currentCoupon: {
      type: [String, Number, Array],
      default: () => []
    },
    couponList: {
      type: Array,
      default: () => []
    },
    overlayList: {
      type: Array,
      default: () => []
    },
    discount: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      showPopup: false,
      selectedCoupons: []
    };
  },
  computed: {
    selectedCount() {
      return this.selectedCoupons.length;
    },
    selectedCouponIds() {
      return this.selectedCoupons.map((coupon) => coupon.id);
    }
  },
  watch: {
    show(val) {
      this.showPopup = val;
      if (val) {
        // 当弹窗显示时，初始化已选择的优惠券
        this.initSelectedCoupons();
      }
    },
    showPopup(val) {
      if (val !== this.show) {
        this.$emit("update:show", val);
      }
    }
  },
  methods: {
    // 添加初始化已选择优惠券的方法
    initSelectedCoupons() {
      console.log(this.currentCoupon);
      this.selectedCouponIds = this.currentCoupon.map((item) => item.coupon_id);
      console.log(this.selectedCoupons);
    },
    closePopup() {
      this.showPopup = false;
      this.$emit("close");
    },
    // 修改 isSelected 方法
    isSelected(id) {
      return this.selectedCouponIds.includes(id);
    },
    canSelectCoupon(coupon) {
      if (this.selectedCoupons.length === 0) {
        return true;
      }

      return this.overlayList.some((item) => item.id === coupon.id);
    },
    async selectCoupon(coupon) {
      const isAlreadySelected = this.isSelected(coupon.id);
      const isInOverlayList = this.overlayList.some(
        (item) => item.id === coupon.id
      );

      if (isAlreadySelected) {
        this.selectedCoupons = this.selectedCoupons.filter(
          (c) => c.id !== coupon.id
        );
        this.$emit("overlay", "", this.selectedCouponIds);
        if (this.selectedCoupons.length === 0) {
          this.$emit("getCouponList", "");
        }
      } else {
        if (isInOverlayList || this.selectedCoupons.length === 0) {
          this.selectedCoupons.push(coupon);
          this.$emit("overlay", coupon.id, this.selectedCouponIds);
        } else {
          uni.showToast({
            title: "该优惠券不可与已选优惠券叠加使用",
            icon: "none",
            duration: 2000
          });
          return;
        }
      }
      this.$emit("calculateCouponAmount", this.selectedCoupons);
    },
    confirmSelection() {
      this.$emit("select", this.selectedCoupons);
      this.closePopup();
    },
    formatTime(time) {
      if (!time) return "";
      const date = new Date(time);
      const year = date.getFullYear();
      const month = date.getMonth() + 1;
      const day = date.getDate();
      return `${year}.${month}.${day}`;
    }
  }
};
</script>

<style lang="scss" scoped>
.coupon-popup {
  display: flex;
  flex-direction: column;
  height: 100%;
  border-radius: 22px 22px 0px 0px;
  background: #fff;
  border-radius: 22rpx 22rpx 0px 0px;
  .popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24rpx;
    padding-top: 36rpx;
    background-color: #ffffff;
    border-radius: 22rpx 22rpx 0px 0px;
    .popup-title {
      color: #333;
      text-align: center;
      font-size: 32rpx;
      font-style: normal;
      font-weight: 500;
      line-height: normal;
      width: 100%;
    }
  }

  .popup-content {
    flex: 1;
    padding: 20rpx 30rpx 0 30rpx;
    height: 686rpx;
    overflow: auto;
    .no-coupon {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 100rpx 0;

      image {
        width: 200rpx;
        height: 200rpx;
        margin-bottom: 20rpx;
      }

      text {
        font-size: 28rpx;
        color: #999999;
      }
    }
    .discount-type {
      opacity: 0.5;
    }
    .coupon-item {
      display: flex;
      margin-bottom: 30rpx;
      border-radius: 12rpx;
      overflow: hidden;
      background-color: #ffffff;
      position: relative;
      height: 174rpx;
      .coupon-left {
        width: 210rpx;
        // background: linear-gradient(to right, #ffc107, #ff9800);
        color: #ffffff;
        padding: 20rpx;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        background: url("https://tg-prod.oss-cn-beijing.aliyuncs.com/77c32a7a-b1d3-4e5c-a21f-4e829645f679.webp")
          no-repeat;
        background-size: 100% 100%;

        .coupon-value {
          display: flex;
          align-items: baseline;

          .symbol {
            color: #fff;
            font-size: 36rpx;
            font-style: normal;
            font-weight: 400;
            line-height: normal;
          }

          .number {
            color: #fff;
            font-size: 56rpx;
            font-style: normal;
            font-weight: 500;
            line-height: normal;
            display: flex;
            align-items: center;
            justify-content: center;
          }
          .text-num {
            color: #fff;
            font-size: 36rpx;
            font-style: normal;
            font-weight: 500;
            line-height: normal;
            margin-left: 4rpx;
          }
        }

        .coupon-condition {
          color: #fff;
          text-align: center;
          font-size: 26rpx;
          font-style: normal;
          font-weight: 400;
          line-height: normal;
        }
      }

      .coupon-right {
        flex: 1;
        padding: 24rpx;
        position: relative;
        width: 476rpx;
        background: url("https://tg-prod.oss-cn-beijing.aliyuncs.com/e20db7bb-8a7f-4e55-bb83-373ef53fe0ad.webp")
          no-repeat;
        background-size: 100% 100%;
        justify-content: space-evenly;
        display: flex;
        flex-direction: column;
        // align-items: center;
        .couponCount{

        }
        .coupon-name {
          color: #333;
          font-size: 30rpx;
          font-style: normal;
          font-weight: 500;
          line-height: normal;
          margin-bottom: 12rpx;
          width: 100%;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          overflow: hidden;
          text-overflow: ellipsis;
          word-break: break-word;
        }

        .coupon-validity {
          color: #666;
          font-size: 26rpx;
          font-style: normal;
          font-weight: 400;
          line-height: normal;
        }

        .select-mark {
          position: absolute;
          right: 24rpx;
          bottom: 0;
          transform: translateY(-50%);
        }
      }
    }
  }

  .coupon-summary {
    display: flex;
    align-items: center;
    padding: 20rpx 32rpx;
    background-color: #ffffff;
    border-top: 1rpx solid #eeeeee;
    height: 124rpx;
    .selected-info {
      flex: 1;
      color: #333;
      font-size: 26rpx;
      font-style: normal;
      font-weight: 400;
      line-height: 40rpx;
      display: flex;
      align-items: center;
      .discount-amount {
        color: #ff553a;
        font-size: 24rpx;
        font-style: normal;
        font-weight: 400;
        margin-left: 12rpx;
        display: inline-flex;
        align-items: center;
        line-height: 40rpx;
        .discount-amount-num {
          font-size: 36rpx;
          font-style: normal;
          font-weight: 500;
          line-height: 40rpx;
          margin-left: 5rpx;
        }
      }
    }

    .confirm-btn {
      width: 160rpx;
      height: 64rpx;
      flex-shrink: 0;
      border-radius: 32rpx;
      background: linear-gradient(15deg, #ffbf0d 18.1%, #ffcb3c 83.29%);
      box-shadow: 0px -4px 8px 0px #eaac00 inset;
      display: flex;
      align-items: center;
      justify-content: center;
      border: none;
      color: #fff;

      text-align: center;
      font-size: 24rpx;
      font-style: normal;
      font-weight: 600;
      line-height: 32px;
      &::after {
        border: none;
      }
    }
  }
}
</style>
