<template>
  <view class="order-options">
    <!-- 校区选择 -->
    <view class="option-item" @tap="handleCampusClick">
      <text class="option-label">
        {{ departmentAvailable && isOrderDetail ? "当前校区" : "校区" }}</text
      >
      <view class="option-value">
        <text :class="{ 'option-value-text': !selectedCampusName }">{{
          selectedCampusName || "请选择校区"
        }}</text>
        <u-icon
          v-if="!departmentAvailable && isOrderDetail"
          name="arrow-right"
          color="#999"
          size="30rpx"
          bold
        ></u-icon>
      </view>
    </view>

    <!-- 优惠券选择 -->
    <view class="option-item" @tap="handleCouponClick">
      <text class="option-label">优惠券</text>
      <view class="option-value">
        <view class="discount-amount" v-if="couponsAvailable">
          <text v-if="selectedCouponAmount">
            - ¥ {{ selectedCouponAmount.toFixed(2) }}
          </text>
          <text style="color: #999" v-else> 使用优惠卷 </text>
        </view>
        <text class="option-value-text" v-else>无可使用的优惠卷</text>
        <u-icon name="arrow-right" color="#999" size="30rpx" bold></u-icon>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: "OrderOptions",
  props: {
    isOrderDetail: {
      type: Boolean,
      default: false
    },
    departmentAvailable: {
      type: Boolean,
      default: false
    },
    selectedCampusName: {
      type: String,
      default: ""
    },
    selectedCouponAmount: {
      type: Number,
      default: 0
    },
    couponsAvailable: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    handleCampusClick() {
      // if (!this.isOrderDetail) return;
      this.$emit("showCampusSelector");
    },
    handleCouponClick() {
      if (!this.isOrderDetail || !this.couponsAvailable) return;
      this.$emit("showCouponSelector");
    }
  }
};
</script>

<style lang="scss" scoped>
.order-options {
  background-color: #fff;
  border-radius: 24rpx;
  padding: 0 30rpx;
  margin-bottom: 20rpx;

  .option-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx 0;
    border-bottom: 1rpx solid #eeeeee;

    &:last-child {
      border-bottom: none;
    }

    .option-label {
      font-size: 28rpx;
      color: #333;
    }

    .option-value {
      display: flex;
      align-items: center;
      font-size: 28rpx;
      .option-value-text {
        font-size: 28rpx;
        color: #999;
        margin-right: 8rpx;
      }

      .discount-amount {
        font-size: 28rpx;
        color: #ff553a;
        margin-right: 5rpx;
      }
    }
  }
}
</style>
