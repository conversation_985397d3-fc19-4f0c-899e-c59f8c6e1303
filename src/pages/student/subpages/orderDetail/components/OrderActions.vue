<template>
  <view class="submit-bar" v-if="visible">
    <view v-if="isOrderDetail" style="width: 100%; display: contents;">
      <view class="total-amount">
        <text>合计</text>
        <view class="amount">
          <text class="symbol">¥</text>
          <text class="number">{{ totalAmount }}</text>
        </view>
      </view>
      <button
        class="submit-btn"
        :class="{ disabled: !selectedCampusName }"
        @tap="handleSubmit"
      >
        提交订单
      </button>
    </view>
    <view style="width: 100%;padding-bottom:27rpx" v-else>
      <view class="order-actions">
        <button class="cancel-btn" @tap="handleCancel">取消订单</button>
        <button class="pay-btn" @tap="handlePay">立即支付</button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: "OrderActions",
  props: {
    visible: {
      type: Boolean,
      default: true
    },
    isOrderDetail: {
      type: <PERSON>olean,
      default: false
    },
    totalAmount: {
      type: [Number, String],
      default: 0
    },
    selectedCampusName: {
      type: String,
      default: ""
    }
  },
  methods: {
    handleSubmit() {
      this.$emit("submit");
    },
    handleCancel() {
      this.$emit("cancel");
    },
    handlePay() {
      this.$emit("pay");
    }
  }
};
</script>

<style lang="scss" scoped>
.submit-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 0rpx 30rpx;
  // box-shadow: 0 -4rpx 10rpx rgba(0, 0, 0, 0.05);
  display: flex;
  padding-top: 24rpx;
  // align-items: center;
  z-index: 100;
  // height: 92rpx;
  padding-bottom: env(safe-area-inset-bottom);
  .total-amount {
    flex: 1;
    display: flex;
    align-items: baseline;
    margin-top: 8rpx;
    padding-bottom:27rpx;
    text {
      font-size: 28rpx;
      color: #333;
      margin-right: 10rpx;
    }

    .amount {
      .symbol {
        font-size: 24rpx;
        color: #ff553a;
      }
      .number {
        font-size: 36rpx;
        font-weight: 500;
        color: #ff553a;
      }
    }
  }

  .submit-btn {
    width: 160rpx;
    height: 64rpx;
    line-height: 64rpx;
    border-radius: 32px;
    background: linear-gradient(15deg, #ffbf0d 18.1%, #ffcb3c 83.29%);
    box-shadow: 0px -4px 8px 0px #eaac00 inset;
    border-radius: 32rpx;
    font-size: 28rpx;
    color: #fff;
    text-align: center;
    border: none;
    font-weight: 500;

    &.disabled {
      background: linear-gradient(15deg, #ffbf0d 18.1%, #ffcb3c 83.29%);
      box-shadow: 0px -4px 8px 0px #eaac00 inset;
      color: #fff;
      opacity: 0.5;
    }
  }

  .order-actions {
    display: flex;
    width: 100%;
    justify-content: flex-end;

    .cancel-btn {
      width: 160rpx;
      height: 64rpx;
      line-height: 64rpx;
      border-radius: 40rpx;
      font-size: 24rpx;
      color: #666666;
      border: 1rpx solid #dddddd;
      background-color: #ffffff;
      text-align: center;
      margin-right: 20rpx;
      // border: none;
    }

    .pay-btn {
      width: 160rpx;
      height: 64rpx;
      line-height: 64rpx;
      background: linear-gradient(15deg, #ffbf0d 18.1%, #ffcb3c 83.29%);
      box-shadow: 0px -4px 8px 0px #eaac00 inset;
      border-radius: 40rpx;
      font-size: 24rpx;
      color: #fff;
      text-align: center;
      border: none;
      font-weight: 500;
    }
  }
}
</style>
