<template>
  <u-popup :show="showPopup" mode="bottom" round="20" height="60%">
    <view class="campus-popup">
      <!-- 标题栏 -->
      <view class="popup-header">
        <text class="popup-title">请选择校区</text>
      </view>

      <!-- 内容区域 - 使用原生picker-view组件 -->
      <view class="picker-wrapper">
        <!-- 使用原生picker-view组件 -->
        <picker-view
          :value="[currentIndex]"
          class="campus-picker-view"
          @change="onChange"
          :immediate-change="true"
          :indicator-style="indicatorStyle"
        >
          <picker-view-column>
            <view
              class="picker-item"
              v-for="(item, index) in schoolList"
              :key="index"
            >
              <text class="campus-name">{{ item.department_name }}</text>
            </view>
          </picker-view-column>
        </picker-view>
      </view>

      <!-- 底部按钮 -->
      <view class="popup-footer">
        <button class="cancel-btn" @tap="closePopup">取消</button>
        <button class="confirm-btn" @tap="confirmSelection">确定</button>
      </view>
    </view>
  </u-popup>
</template>

<script>
export default {
  name: "CampusSelector",
  props: {
    show: {
      type: Boolean,
      default: false
    },
    currentCampus: {
      type: [String, Number],
      default: ""
    },
    schoolList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      showPopup: false,
      selectedCampus: "",
      currentIndex: 0,
      indicatorStyle:
        "height: 112rpx;color:#333;font-size:32rpx;font-weight:500;",
      maskStyle: "background: #F5F5F5;border-radius: 20rpx;"
      // schoolList: [
      //   { id: 1, name: "亦庄校区" },
      //   { id: 2, name: "刘家窑校区" },
      //   { id: 3, name: "亦庄校区" },
      //   { id: 4, name: "亦庄校区" },
      //   { id: 5, name: "西城校区" }
      // ]
    };
  },
  watch: {
    show(val) {
      this.showPopup = val;
      if (val) {
        this.$nextTick(() => {
          // 初始化选中项
          if (this.selectedCampus) {
            const index = this.schoolList.findIndex(
              (item) => item.department_id === this.selectedCampus
            );
            if (index !== -1) {
              this.currentIndex = index;
            }
          }
        });
      }
    },
    showPopup(val) {
      if (val !== this.show) {
        this.$emit("update:show", val);
      }
    },
    currentCampus: {
      immediate: true,
      handler(val) {
        this.selectedCampus = val;
      }
    }
  },
  methods: {
    closePopup() {
      this.showPopup = false;
      this.$emit("close");
    },
    // 监听picker-view的change事件
    onChange(e) {
      this.currentIndex = e.detail.value[0];
    },
    confirmSelection() {
      // 使用当前选中的索引获取校区
      const selectedCampusObj = this.schoolList[this.currentIndex];
      this.selectedCampus = selectedCampusObj.department_id;
      this.$emit("select", selectedCampusObj);
      this.$emit("handleRegister");
      this.closePopup();
    }
  }
};
</script>

<style lang="scss" scoped>
.campus-popup {
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  height: 100%;
  border-radius: 22rpx 22rpx 0px 0px;
  .popup-header {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 30rpx 0;

    .popup-title {
      color: #333;
      text-align: center;
      font-size: 32rpx;
      font-style: normal;
      font-weight: 500;
      line-height: normal;
    }
  }

  .picker-wrapper {
    // flex: 1;
    position: relative;
    height: 500rpx;

    .campus-picker-view {
      width: 100%;
      height: 100%;
    }

    .picker-item {
      line-height: 100rpx;
      display: flex;
      justify-content: center;
      align-items: center;

      .campus-name {
        font-size: 30rpx;
        color: #333333;
        text-align: center;
        height: 112rpx;
      }
    }
  }

  .popup-footer {
    display: flex;
    padding: 30rpx;
    gap: 20rpx;

    .cancel-btn,
    .confirm-btn {
      flex: 1;
      height: 80rpx;
      border-radius: 40rpx;
      font-size: 32rpx;
      font-weight: 500;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: transform 0.2s ease;
      width: 238rpx;
      &::after {
        border: none;
      }
    }

    .cancel-btn {
      background-color: #ffffff;
      color: #333333;
      border: 1px solid #e6e6e6;
    }

    .confirm-btn {
      background: linear-gradient(15deg, #ffbf0d 18.1%, #ffcb3c 83.29%);
      box-shadow: 0px -5rpx 12rpx 0px #fc0 inset,
        0px 9rpx 20rpx 0px #fff7e1 inset;
      filter: drop-shadow(0px 4rpx 4rpx rgba(255, 192, 18, 0.11));
      color: #fff;
    }
  }
}
</style>
