<template>
  <view class="cost-details">
    <view class="cost-warpp" v-if="!isOrderDetail">
      <view class="cost-title"> 所选校区 </view>
      <view class="cost-item">
        <text class="cost-label">校区</text>
        <text class="cost-value">{{ userInfo.department_name }}</text>
      </view>
    </view>
    <view class="cost-title">费用明细</view>

    <view class="cost-item">
      <text class="cost-label">商品金额</text>
      <text class="cost-value">¥ {{ originalPrice }}</text>
    </view>

    <view class="cost-item">
      <text class="cost-label">优惠金额</text>
      <text class="cost-value" :class="{ discount: hasDiscount }">
        {{ totalDiscount ? "-" : "" }} ¥ {{ totalDiscount }}
      </text>
    </view>

    <view class="cost-item">
      <text class="cost-label">{{
        isOrderCompleted ? "实付金额" : "应交金额"
      }}</text>
      <text class="cost-value">¥ {{ discountPrice }}</text>
    </view>

    <view class="cost-item" v-if="isOrderCompleted">
      <text class="cost-label">支付方式</text>
      <text class="cost-value">微信支付</text>
    </view>
    <PaymentMethod :visible="visible" />
  </view>
</template>

<script>
import PaymentMethod from "./PaymentMethod.vue";
export default {
  name: "CostDetails",
  components: {
    PaymentMethod
  },
  props: {
    originalPrice: {
      type: [Number, String],
      default: 0
    },
    totalDiscount: {
      type: [Number, String],
      default: 0
    },
    discountPrice: {
      type: [Number, String],
      default: 0
    },
    isOrderCompleted: {
      type: Boolean,
      default: false
    },
    userInfo: {
      type: Object,
      default: () => {}
    },
    isOrderDetail: {
      type: Boolean,
      default: () => {}
    },
    visible: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    hasDiscount() {
      return Number(this.totalDiscount) > 0;
    }
  }
};
</script>

<style lang="scss" scoped>
.cost-details {
  background-color: #fff;
  border-radius: 24rpx;
  padding: 30rpx;
  padding-bottom: 1rpx;
  margin-bottom: 20rpx;
  .cost-warpp {
    border-bottom: 1rpx solid #eee;
    padding-bottom: 34rpx;
    margin-bottom: 34rpx;
  }
  .cost-title {
    font-size: 30rpx;
    font-weight: 500;
    color: #333;
    margin-bottom: 30rpx;
  }

  .cost-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 30rpx;

    &:last-child {
      margin-bottom: 0;
    }

    .cost-label {
      font-size: 30rpx;
      color: #666;
    }

    .cost-value {
      font-size: 30rpx;
      color: #333;

      &.discount {
        color: #ff553a;
      }
    }
  }
}
</style>
