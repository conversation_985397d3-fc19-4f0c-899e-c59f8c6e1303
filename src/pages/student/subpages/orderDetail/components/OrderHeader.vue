<template>
  <view>
    <!-- 顶部导航栏 -->
    <u-navbar
      :title="title"
      bgColor="#FFF"
      leftIconSize="25px"
      leftIconColor="#333333"
      :titleStyle="{
        color: '#333333',
        fontSize: '34rpx',
        fontWeight: '500',
        lineHeight: '40rpx'
      }"
      :autoBack="true"
      placeholder
    >
      <!-- <view class="action-icons" slot="right">
        <u-icon name="more-dot-fill" size="40" color="#333"></u-icon>
      </view> -->
    </u-navbar>
    <!-- 全局倒计时条 - 仅在待付款标签或有待付款订单时显示 -->
    <view class="global-countdown" v-if="showCountdown">
      <text class="countdown-text"
        >还剩
        <text class="time-text">{{ countdownTime }}</text>
        订单自动取消</text
      >
    </view>
  </view>
</template>

<script>
export default {
  name: "OrderHeader",
  props: {
    title: {
      type: String,
      default: "订单详情"
    },
    showCountdown: {
      type: Boolean,
      default: false
    },
    countdownTime: {
      type: String,
      default: "00:00"
    }
  }
};
</script>

<style lang="scss" scoped>
.nav-left {
  width: 40rpx;
  height: 40rpx;
  image {
    width: 100%;
    height: 100%;
  }
}

.global-countdown {
  //   background-color: #fff7e6;
  //   padding: 16rpx 30rpx;
  width: 100%;
  // height: 174rpx;
  //   background-color: #fff7e6;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: #333;
  text-align: center;
  font-size: 36rpx;
  font-style: normal;
  font-weight: 500;
  // line-height: 54rpx; /* 150% */
  margin: 60rpx 0 40rpx 0;
  .time-text {
    color: #fe4f37;
    font-size: 36rpx;
    font-style: normal;
    font-weight: 500;
    line-height: 54rpx;
    padding: 0 8rpx;
    // .time-text {
    //   font-weight: bold;
    //   margin: 0 4rpx;
    // }
  }
}
</style>
