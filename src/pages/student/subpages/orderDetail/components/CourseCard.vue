<template>
  <view class="course-card">
    <view class="course-info" @click="handleCourseDetail">
      <image
        class="course-image"
        :src="item.goods_cover + '?x-oss-process=image/resize,h_160,w_160'"
        mode="aspectFill"
      ></image>
      <view class="course-details">
        <view>
          <view class="tag-row">
            <view class="tag tag-yel">{{
              role[item.goods_extra.course_type]
            }}</view>
            <view class="course-name course-hid"
              ><text class="text">
                {{ item.goods_name }}
              </text>
              <div class="cell-right" v-if="!isOrderDetail">
                <u-icon
                  name="https://tg-prod.oss-cn-beijing.aliyuncs.com/3c638734-affc-4fa0-971e-c468dbb737a1.webp"
                  color="#999999"
                  width="30rpx"
                  height="30rpx"
                ></u-icon>
              </div>
            </view>
          </view>
          <view class="course-time"
            >{{ item.goods_num }}{{ item.goods_unit }}</view
          >
        </view>

        <view class="price-row">
          <view class="price">
            <text class="symbol">¥</text>
            <text class="amount">{{ item.original_total_price }}</text>
          </view>
          <view class="quantity">x{{ quantity }}</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: "CourseCard",
  data() {
    return {
      role: {
        1: "课时包",
        2: "试听课",
        3: "常规课"
      }
    };
  },
  props: {
    item: {
      type: Object,
      required: true
    },
    quantity: {
      type: Number,
      default: 1
    },
    isOrderDetail: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    handleCourseDetail() {
      console.log("🚀 ~ handleCourseDetail ~ this.item:", this.item);
      this.$emit("handleCourseDetail", this.item.related_goods_id);
    }
  }
};
</script>

<style lang="scss" scoped>
.course-card {
  background-color: #fff;
  border-radius: 24rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;

  .course-info {
    display: flex;

    .course-image {
      width: 160rpx;
      height: 160rpx;
      border-radius: 20rpx;
      background-color: #f5f5f5;
    }

    .course-details {
      flex: 1;
      margin-left: 24rpx;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .tag-row {
        display: flex;
        align-items: center;
        margin-bottom: 8rpx;

        .tag {
          font-size: 20rpx;
          padding: 4rpx 10rpx;
          border-radius: 6rpx;
          margin-right: 10rpx;
          border-radius: 16rpx 8rpx 16rpx 8rpx;
          color: #fff;
          &.tag-yel {
            background: linear-gradient(
              313deg,
              #ffb200 35.48%,
              #ffe32d 127.89%
            );
          }
        }

        .course-name {
          font-size: 32rpx;
          font-weight: 500;
          color: #333;
          flex: 1;
          display: flex;
          align-items: center;
          .text {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
        .cell-right {
          // margin-left: 16rpx;
        }
        .course-hid {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          width: 200rpx;
        }
      }

      .course-time {
        font-size: 26rpx;
        color: #999;
      }

      .price-row {
        display: flex;
        justify-content: space-between;
        align-items: baseline;

        .price {
          display: flex;
          align-items: baseline;
          .symbol {
            font-size: 24rpx;
            color: #ff553a;
            margin-right: 5rpx;
            font-weight: 400;
          }
          .amount {
            font-size: 32rpx;
            font-weight: 500;
            color: #ff553a;
          }
        }

        .quantity {
          font-size: 26rpx;
          color: #999;
        }
      }
    }
  }
}
</style>
