<template>
  <view class="payment-method" v-if="visible">
    <view class="cost-title">支付方式</view>

    <view class="method-item selected">
      <view class="method-icon">
        <image
          src="https://tg-prod.oss-cn-beijing.aliyuncs.com/4586b616-e3e2-4a3a-b114-d6d11792daff.webp"
          mode="aspectFit"
        ></image>
      </view>
      <text class="method-name">微信支付</text>
      <u-icon
        name="https://tg-prod.oss-cn-beijing.aliyuncs.com/21911f38-63a5-4f1b-9f7d-24a588939b2a.webp"
        color="#FFBB00"
        width="40rpx"
        height="40rpx"
      ></u-icon>
    </view>
  </view>
</template>

<script>
export default {
  name: "PaymentMethod",
  props: {
    visible: {
      type: Boolean,
      default: true
    }
  }
};
</script>

<style lang="scss" scoped>
.payment-method {
  background-color: #fff;
  // border-radius: 24rpx;
  padding: 34rpx 0;
  border-top: 1rpx solid #eeeeee;
  // margin-bottom: 20rpx;
  padding-bottom: 0;

  .cost-title {
    font-size: 30rpx;
    font-weight: 500;
    color: #333;
    margin-bottom: 30rpx;
  }

  .method-item {
    display: flex;
    align-items: center;
    padding:0 0 20rpx 0;

    &.selected {
      .method-name {
        color: #333;
      }
    }

    .method-icon {
      width: 48rpx;
      height: 48rpx;
      margin-right: 20rpx;

      image {
        width: 100%;
        height: 100%;
      }
    }

    .method-name {
      flex: 1;
      font-size: 28rpx;
      color: #666;
    }
  }
}
</style>
