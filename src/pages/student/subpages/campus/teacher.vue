<template>
  <view class="teacher-team-page">
    <!-- 师资卡片网格 -->
    <view class="teacher-grid" v-if="teacherList.length > 0">
      <view
        v-for="(teacher, index) in teacherList"
        :key="index"
        class="teacher-card"
        @tap="onTeacherClick(teacher)"
      >
        <!-- 头像区域 -->
        <view class="avatar-container">
          <image
            class="teacher-avatar"
            :src="teacher.photo_url"
            mode="aspectFill"
          />
        </view>

        <!-- 信息区域 -->
        <view class="teacher-info">
          <view class="teacher-name">{{ teacher.teacher_name }}</view>
          <view class="teacher-detail">
            <view class="star-icon">
              <u-icon
                name="https://tg-prod.oss-cn-beijing.aliyuncs.com/c51953dc-ff05-45f0-84a9-62dd53e680cf.png"
                size="20"
              ></u-icon>
            </view>
            <view class="teacher-title">{{ teacher.dan_level_name }}</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 加载更多组件 -->
    <view class="loadmore-container" v-if="teacherList.length > 0">
      <u-loadmore
        :status="loadStatus"
        :load-text="{
          loadmore: '上拉加载更多',
          loading: '正在加载...',
          nomore: '没有更多数据了'
        }"
        :line="true"
        lineColor="#e6e6e6"
        :marginTop="20"
        :marginBottom="20"
      />
    </view>

    <!-- 空状态 -->
    <EmptyIcon
      v-if="teacherList.length === 0 && !isFirstLoad"
      text="暂无师资信息~"
    />
  </view>
</template>

<script>
import { niedaoTeacherList } from "@/services/student/niedaoTeacher";
import EmptyIcon from "@/components/empty";

export default {
  name: "teacher-team-page",
  components: {
    EmptyIcon
  },
  data() {
    return {
      department_id: "",
      teacherList: [],
      // 分页相关属性
      page: 1,
      page_size: 10,
      total: 0,
      loadStatus: "loadmore", // loadmore, loading, nomore
      isFirstLoad: true
    };
  },
  onLoad(options) {
    this.department_id = options.department_id;
    this.resetTeacherList();
    this.getNiedaoTeacherList();
  },
  // 页面触底事件
  onReachBottom() {
    if (this.loadStatus === "nomore" || this.loadStatus === "loading") {
      return;
    }
    this.loadMoreTeachers();
  },
  methods: {
    onBack() {
      uni.navigateBack();
    },
    onTeacherClick(teacher) {
      console.log("点击老师:", teacher);
      // 这里可以跳转到老师详情页
      uni.navigateTo({
        url: `/pages/student/subpages/campus/teacherDetail?id=${teacher.id}`
      });
    },
    // 重置教师列表数据
    resetTeacherList() {
      this.teacherList = [];
      this.page = 1;
      this.total = 0;
      this.loadStatus = "loadmore";
      this.isFirstLoad = true;
    },
    // 加载更多教师数据
    async loadMoreTeachers() {
      // 检查是否还有更多数据
      if (this.teacherList.length >= this.total && this.total > 0) {
        this.loadStatus = "nomore";
        return;
      }

      this.loadStatus = "loading";
      await this.getNiedaoTeacherList();
    },
    async getNiedaoTeacherList() {
      try {
        const { data, code, message } = await niedaoTeacherList({
          department_ids: this.department_id,
          page: this.page,
          page_size: this.page_size,
          source: 2
        });

        if (code === 0) {
          const newResults = data.results || [];

          // 如果是第一页，直接替换；否则追加数据
          if (this.page === 1) {
            this.teacherList = newResults;
          } else {
            this.teacherList = [...this.teacherList, ...newResults];
          }

          this.total = data.count || 0;

          // 判断是否还有更多数据
          if (this.teacherList.length >= this.total) {
            this.loadStatus = "nomore";
          } else {
            this.loadStatus = "loadmore";
          }

          // 成功获取数据后才递增页码
          this.page++;
          this.isFirstLoad = false;
        } else {
          this.loadStatus = "loadmore";
          uni.showToast({
            title: message || "获取数据失败",
            icon: "none"
          });
        }
      } catch (error) {
        console.error("获取教师列表失败:", error);
        this.loadStatus = "loadmore";
        uni.showToast({
          title: "网络错误，请重试",
          icon: "none"
        });
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.teacher-team-page {
  background: #fff;
  min-height: 100vh;
  font-family: PingFang SC, Arial, sans-serif;
  padding-bottom: 168rpx; // 为底部菜单留出空间
}

.teacher-grid {
  padding: 30rpx 32rpx 0;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30rpx;
}

.teacher-card {
  background: #f2f2f2;
  border-radius: 16rpx;
  // overflow: hidden;
  position: relative;
  cursor: pointer;
  transition: transform 0.2s ease;

  &:active {
    transform: scale(0.98);
  }
}

.avatar-container {
  width: 100%;
  height: 437rpx; // 根据设计稿比例计算：437/328 * 328 ≈ 437
  position: relative;
  overflow: hidden;
  border-radius: 16rpx;
}

.teacher-avatar {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.teacher-info {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 80rpx;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20rpx);
  // border: 1rpx solid #e6e6e6;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20rpx;
}

.teacher-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  line-height: 1.4;
}

.teacher-detail {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.teacher-title {
  font-size: 24rpx;
  color: #333;
  line-height: 1.33;
}

.star-icon {
  width: 30rpx;
  height: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loadmore-container {
  padding: 0 32rpx;
  margin-top: 20rpx;
  margin-bottom: 20rpx;
}
</style>
