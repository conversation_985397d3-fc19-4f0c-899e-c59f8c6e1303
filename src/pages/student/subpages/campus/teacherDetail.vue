<template>
  <view class="teacher-detail-page">
    <u-navbar
      title="教师主页"
      bgColor="transparent"
      leftIconSize="25px"
      leftIconColor="#fff"
      :titleStyle="{
        color: '#fff',
        fontSize: '34rpx',
        fontWeight: '500',
        lineHeight: '40rpx'
      }"
      :autoBack="true"
      placeholder
    >
      <view class="nav-letf" slot="left">
        <image
          src="https://tg-prod.oss-cn-beijing.aliyuncs.com/68798755-2327-4bac-8d65-3de4ed10a6e3.webp"
        ></image>
      </view>
    </u-navbar>
    <!-- 渐变背景 -->
    <view class="background-gradient"></view>

    <!-- 教师信息卡片 -->
    <view class="teacher-info-card">
      <!-- 背景水印 -->
      <view class="watermark-bg">
        <image
          src="https://tg-prod.oss-cn-beijing.aliyuncs.com/ec10f798-41f0-4eab-bbcd-00bf26a259e7.webp"
          mode="aspectFill"
        />
      </view>

      <!-- 主要内容区域 -->
      <view class="card-content">
        <!-- 上半部分：头像和基本信息 -->
        <view class="top-section">
          <!-- 教师头像 -->
          <view class="avatar-section">
            <view class="avatar-container">
              <image
                class="teacher-avatar"
                :src="teacherData.photo_url"
                mode="aspectFill"
                @click="imgPreview(teacherData.photo_url)"
              />
            </view>
          </view>

          <!-- 教师基本信息 -->
          <view class="teacher-info">
            <view class="teacher-name-row">
              <text class="teacher-name">{{ teacherData.teacher_name }}</text>
              <view class="level-badge">
                <view class="star-icon">
                  <u-icon
                    name="https://tg-prod.oss-cn-beijing.aliyuncs.com/c51953dc-ff05-45f0-84a9-62dd53e680cf.png"
                    size="30rpx"
                  ></u-icon>
                </view>
                <text class="level-text">{{ teacherData.dan_level_name }}</text>
              </view>
            </view>
            <view class="teacher-details">
              <text class="detail-text"
                >教龄：{{ teacherData.teaching_year }}</text
              >
              <text class="detail-text"
                >校区：{{ teacherData.department_names.join(",") }}</text
              >
            </view>
          </view>
        </view>

        <!-- 下半部分：荣誉区域 -->
        <view class="honor-section">
          <view class="honor-background">
            <!-- <view class="honor-icon-wrapper"> </view> -->
            <view class="honor-text"
              ><image
                class="honor-icon"
                src="https://tg-prod.oss-cn-beijing.aliyuncs.com/95554e49-35cd-4a68-ab17-9ab66c87b9dc.png"
              /><text>获得荣誉：</text>{{ teacherData.honor }}</view
            >
          </view>
        </view>
      </view>
    </view>

    <!-- 教师介绍卡片 -->
    <view class="intro-card">
      <view class="intro-title">教师介绍</view>
      <view class="intro-content">{{ teacherData.introduce }}</view>
    </view>
  </view>
</template>

<script>
import { niedaoTeacherDetail } from "@/services/student/niedaoTeacher";

export default {
  name: "teacher-detail-page",
  data() {
    return {
      teacherData: {}
    };
  },
  onLoad(options) {
    this.getTeacherDetail(options.id);
  },
  methods: {
    imgPreview(url) {
      uni.previewImage({
        urls: [url]
      });
    },
    async getTeacherDetail(id) {
      const { data, code, message } = await niedaoTeacherDetail({ id });
      if (code === 0) {
        this.teacherData = data;
      } else {
        uni.showToast({
          title: message,
          icon: "none"
        });
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.teacher-detail-page {
  min-height: 100vh;
  background: #f5f5f5;
  position: relative;
  padding-top: 73rpx; // 导航栏高度
}

// 渐变背景
.background-gradient {
  position: absolute;
  top: 0;
  left: 0;
  width: 750rpx;
  height: 886rpx;
  background: linear-gradient(
    171deg,
    #ff860d 12.09%,
    rgba(255, 169, 30, 0.6) 54.02%,
    rgba(255, 165, 30, 0) 90.23%
  );
  z-index: 1;
}

// 教师信息卡片
.teacher-info-card {
  position: relative;
  margin: 0 32rpx 32rpx;
  background: #ffffff;
  border-radius: 24rpx;
  // box-shadow: 0rpx 4rpx 20rpx 0rpx rgba(0, 0, 0, 0.1);
  z-index: 2;
  // overflow: hidden;
}

// 背景水印
.watermark-bg {
  position: absolute;
  top: 221rpx;
  right: -9rpx;
  width: 446rpx;
  height: 69rpx;
  z-index: 1;
  // opacity: 0.1;

  image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

// 主要内容区域
.card-content {
  padding: 30rpx 24rpx;
  display: flex;
  flex-direction: column;
  gap: 60rpx;
  position: relative;
  z-index: 2;
}

// 上半部分：头像和基本信息
.top-section {
  display: flex;
  gap: 30rpx;
  align-items: flex-start;
}

// 教师头像区域
.avatar-section {
  flex-shrink: 0;
}

.avatar-container {
  width: 260rpx;
  height: 346rpx;
  background: #ffffff;
  border: 6rpx solid #ffffff;
  border-radius: 16rpx;
  box-shadow: 0rpx 4rpx 20rpx 0rpx rgba(0, 0, 0, 0.15);
  // overflow: hidden;
  margin-top: -70rpx;
}

.teacher-avatar {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 16rpx;
}

// 教师基本信息
.teacher-info {
  flex: 1;
  // padding-top: 43rpx;
}

.teacher-name-row {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
}

.teacher-name {
  font-family: "PingFang SC";
  font-weight: 500;
  font-size: 44rpx;
  line-height: 1.4;
  color: #333333;
  margin-right: 12rpx;
}

.level-badge {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 153rpx;
  height: 50rpx;
  border: 2rpx solid #ff9000;
  border-radius: 9rpx;
  padding: 0 10rpx;
  position: relative;
  background: rgba(255, 155, 0, 0.05);
}

.star-icon {
  // width: 16px;
  // height: 16px;
  margin-right: 2rpx;
}

.level-text {
  font-weight: 400;
  font-size: 24rpx;
  line-height: 1.33;
  color: #ff9000;
  text-align: center;
  white-space: nowrap;
}

.teacher-details {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.detail-text {
  font-weight: 400;
  font-size: 28rpx;
  line-height: 1.5;
  color: #666666;
}

// 荣誉区域
.honor-section {
  // padding: 0 24rpx;
  margin-top: -40rpx;
}

.honor-background {
  background: rgba(255, 247, 235, 0.67);
  border-radius: 20rpx;
  padding: 20rpx 24rpx;
  display: flex;
  align-items: flex-start;
  // min-height: 120rpx;
}

.honor-icon-wrapper {
  position: relative;
  width: 44rpx;
  height: 44rpx;
  margin-right: 12rpx;
  flex-shrink: 0;
}

.honor-icon {
  // position: relative;
  width: 44rpx;
  height: 44rpx;
  z-index: 1;
  display: inline-block;
  vertical-align: middle;
  margin-right: 5rpx;
}

.honor-text {
  // font-family: "PingFang SC";
  font-weight: 400;
  font-size: 26rpx;
  line-height: 42rpx;
  color: #333333;
  flex: 1;
  word-wrap: break-word;
  // padding-left: 12rpx;
  text {
    color: #ff9c01;
    font-weight: 500;
  }
}

// 教师介绍卡片
.intro-card {
  position: relative;
  margin: 0 32rpx 32rpx;
  background: #ffffff;
  border-radius: 24rpx;
  padding: 30rpx 24rpx;
  // box-shadow: 0rpx 4rpx 20rpx 0rpx rgba(0, 0, 0, 0.1);
  z-index: 2;
}

.intro-title {
  font-family: "PingFang SC";
  font-weight: 500;
  font-size: 32rpx;
  line-height: 1.4;
  color: #333333;
  margin-bottom: 20rpx;
}

.intro-content {
  font-family: "PingFang SC";
  font-weight: 400;
  font-size: 28rpx;
  line-height: 1.5;
  color: #666666;
}
.nav-letf {
  width: 40rpx;
  height: 40rpx;
  image {
    width: 100%;
    height: 100%;
  }
}
</style>
