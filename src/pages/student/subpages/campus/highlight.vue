<template>
  <view class="highlight-detail-page">
    <u-navbar
      :title="module_type === '2' ? '校区亮点' : '学员评价'"
      bgColor="#fff"
      leftIconSize="25px"
      leftIconColor="#333333"
      :titleStyle="{
        color: '#333333',
        fontSize: '34rpx',
        fontWeight: '500',
        lineHeight: '40rpx'
      }"
      :autoBack="true"
      placeholder
    >
      <view class="nav-letf" slot="left">
        <image
          src="https://tg-prod.oss-cn-beijing.aliyuncs.com/e9ac0f4b-6282-4bac-b602-189057c45c90.webp"
        ></image>
      </view>
    </u-navbar>
    <!-- 主图轮播区域 -->
    <view class="main-content">
      <view class="swiper-container" :style="{ height: swiperHeight + 'rpx' }">
        <swiper
          class="image-swiper"
          :indicator-dots="false"
          :autoplay="false"
          :circular="true"
          @change="onSwiperChange"
          :style="{ height: swiperHeight + 'rpx' }"
        >
          <swiper-item v-for="(item, index) in imageList" :key="index">
            <image
              v-if="index === 0"
              class="swiper-image first-image"
              :src="item.image_url + '?x-oss-process=image/resize,w_750'"
              mode="aspectFit"
              @load="onFirstImageLoad"
              @click="imgPreview(item.image_url)"
            />
            <image
              v-else
              class="swiper-image"
              :src="item.image_url + '?x-oss-process=image/resize,w_750'"
              mode="widthFix"
              @click="imgPreview(item.image_url)"
            />
          </swiper-item>
        </swiper>

        <!-- 图片数量标识 -->
        <view class="image-count"
          >{{ currentIndex + 1 }}/{{ imageList.length }}</view
        >

        <!-- 自定义轮播指示器 -->
        <!-- <view class="custom-indicators">
          <view
            v-for="(item, index) in imageList"
            :key="index"
            class="indicator-item"
            :class="{ active: index === currentIndex }"
          ></view>
        </view> -->
      </view>

      <!-- 底部内容 -->
      <view class="content-section">
        <view class="content-text">{{ highlightData.content }}</view>
        <view
          class="date-info"
          :style="!highlightData.content ? 'margin-top: 0rpx' : ''"
          >{{ highlightData.created_at }}</view
        >
      </view>
    </view>
    <!-- 空状态 -->
    <view :class="imgHeight >= 260 ? 'empty-max' : 'empty-min'">
      <EmptyIcon v-if="!highlightData.content" text="暂无更多内容啦~" />
    </view>
  </view>
</template>

<script>
import { getModuleDetail } from "@/services/student/department";
import EmptyIcon from "@/components/empty";
export default {
  name: "campus-highlight-detail",
  components: {
    EmptyIcon
  },
  data() {
    return {
      module_content_id: "",
      module_type: "",
      currentIndex: 0,
      swiperHeight: 1000, // 默认高度
      imageList: [],
      highlightData: {
        date: "2025年2月5日",
        content:
          "这是文案内容这是文案内容这是文案内容这是文案内容这是文案内容这是文案内容这是文案内容这是文案内容这是文案内容这是文案内容这是文案内容这是文案内容这是文案内容这是文案内容这是文案内容这是文案内容这是文案内容这是文案内容这是文案内容这是文案内容这是文案内容"
      },
      imgHeight: 0
    };
  },
  onLoad(options) {
    this.module_content_id = options.module_content_id;
    this.module_type = options.module_type;
    this.getModuleDetail();
  },
  methods: {
    onBack() {
      uni.navigateBack();
    },
    onSwiperChange(e) {
      this.currentIndex = e.detail.current;
    },
    onFirstImageLoad(e) {
      // 获取第一张图片的实际尺寸，设置轮播容器高度
      const { width, height } = e.detail;
      console.log(width, height, "width, height");
      if (width && height) {
        // 按750rpx宽度计算对应高度
        this.swiperHeight = Math.round((height / width) * 750);
      }
    },
    imgPreview(url) {
      const arr = [];
      this.imageList.forEach((item, index) => {
        if (item.image_url) {
          arr.push(item.image_url);
        }
      });
      const index = arr.findIndex((value) => value === url);
      uni.previewImage({
        current: index,
        urls: arr
      });
    },
    async getModuleDetail() {
      const { data, code, message } = await getModuleDetail({
        module_content_id: this.module_content_id,
        module_type: this.module_type
      });
      if (code === 0) {
        this.highlightData = data;
        this.imageList = data.media_list || [];
        this.imgHeight = this.imageList[0].height;
      } else {
        uni.showToast({
          title: message,
          icon: "none"
        });
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.highlight-detail-page {
  background: #fff;
  min-height: 100vh;
  font-family: PingFang SC, Arial, sans-serif;
}

.main-content {
  position: relative;
}

.swiper-container {
  position: relative;
  width: 100%;
  min-height: 312rpx;
  max-height: 1000rpx;
}

::v-deep .image-swiper {
  width: 100%;
  min-height: 312rpx;
  max-height: 1000rpx;
  swiper-item {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.swiper-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center center;

  &.first-image {
    object-fit: cover;
  }
}

.image-count {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  background: rgba(0, 0, 0, 0.47);
  color: #fff;
  padding: 6rpx 20rpx;
  border-radius: 16rpx 8rpx 8rpx 8rpx;
  font-size: 26rpx;
  font-weight: 500;
  z-index: 2;
}

.custom-indicators {
  position: absolute;
  bottom: 20rpx;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 8rpx;
  z-index: 2;
}

.indicator-item {
  //   width: 20rpx;
  //   height: 10rpx;
  //   background: rgba(255, 255, 255, 0.4);
  //   border-radius: 10rpx;
  transition: all 0.3s ease;
  width: 10rpx;
  height: 10rpx;
  border-radius: 100rpx;
  opacity: 0.4;
  background: #ffc525;
  transition: background-color 0.3s;
  margin-right: 10rpx;

  &.active {
    width: 24.193rpx;
    height: 10rpx;
    background-color: #ffc525;
    opacity: 1;
    border-radius: 20rpx;
  }
}

.content-section {
  padding: 24rpx 32rpx 50rpx;
}

.date-info {
  font-size: 26rpx;
  color: #999;
  margin-top: 20rpx;
  line-height: 1.23;
}

.content-text {
  font-size: 30rpx;
  color: #333;
  line-height: 1.47;
}
.nav-letf {
  width: 40rpx;
  height: 40rpx;
  image {
    width: 100%;
    height: 100%;
  }
}
::v-deep .empty-icon {
  // margin-top: 100rpx;
  padding-bottom: env(safe-area-inset-bottom);
}
.empty-max {
  margin-top: 100rpx;
}
.empty-min {
  margin-top: 200rpx;
}
</style>
