<template>
  <view class="campus-page">
    <!-- 顶部蓝色背景区域 -->
    <view class="header-section">
      <!-- 导航栏 -->
      <u-navbar
        title="校区介绍"
        :bgColor="navBgColor"
        leftIconSize="25px"
        :leftIconColor="navTextColor"
        :titleStyle="{
          color: navTextColor,
          fontSize: '34rpx',
          fontWeight: '500',
          lineHeight: '40rpx'
        }"
        :autoBack="true"
        placeholder
        fixed
      >
        <view class="nav-letf" slot="left">
          <image
            :src="
              navBgColor === '#ffffff'
                ? 'https://tg-prod.oss-cn-beijing.aliyuncs.com/e9ac0f4b-6282-4bac-b602-189057c45c90.webp'
                : 'https://tg-prod.oss-cn-beijing.aliyuncs.com/68798755-2327-4bac-8d65-3de4ed10a6e3.webp'
            "
          ></image>
        </view>
      </u-navbar>
      <view class="nav-bar">
        <view class="nav-title">
          <image
            src="https://tg-prod.oss-cn-beijing.aliyuncs.com/9d2ca481-b81e-4a1c-b993-098bf9454d6e.png"
          />
        </view>
        <view class="location" @click="showCityPicker">
          <text>{{ selectedCity || "全国" }}</text>
          <u-icon
            name="https://tg-prod.oss-cn-beijing.aliyuncs.com/1497246a-a049-482b-b904-b2a4375f6443.webp"
            color="#fff"
            size="24rpx"
          ></u-icon>
        </view>
      </view>

      <!-- 统计信息 -->
      <view class="stats-section">
        <view class="stats-item">
          <text class="stats-number">{{ campusCount }}</text>
          <text class="stats-label">家分校区</text>
        </view>
        <view class="stats-item">
          <text class="stats-text">覆盖</text>
          <text class="stats-number">{{ areaCount }}</text>
          <text class="stats-text">个行政区域</text>
        </view>
      </view>

      <!-- 城市建筑背景 -->
      <view class="city-bg"></view>
    </view>

    <!-- 校区列表 -->
    <view class="campus-list">
      <view v-if="campusList.length > 0">
        <view
          class="campus-item"
          v-for="(item, index) in campusList"
          :key="index"
          @click="handleCampusClick(item)"
        >
          <!-- 校区信息 -->
          <view class="campus-info">
            <view class="campus-info-top">
              <view class="campus-icon">
                <u-icon
                  color="#4A90E2"
                  size="36rpx"
                  name="https://tg-prod.oss-cn-beijing.aliyuncs.com/91a335c7-99ae-4b44-bf62-9910a929aaf3.webp"
                ></u-icon>
              </view>
              <view class="campus-name">{{ item.department_name }}</view>
            </view>
            <view class="campus-address">
              <text class="address-text">{{ item.department_address }}</text>
              <view class="action-btn" @click.stop="handleNavigation(item)">
                <u-icon
                  color="#666"
                  size="40rpx"
                  name="https://tg-prod.oss-cn-beijing.aliyuncs.com/31e851ca-e5c1-4c9d-9d7b-6508095f20f8.webp"
                ></u-icon>
              </view>
            </view>
            <view class="campus-phone">
              <text class="phone-label"
                >咨询电话：<text>{{ item.consult_phone }}</text></text
              >
              <view class="phone-number"
                ><view
                  class="action-btn"
                  style="margin-top: 0rpx"
                  @click.stop="handleCall(item.consult_phone)"
                >
                  <u-icon
                    color="#666"
                    size="40rpx"
                    name="https://tg-prod.oss-cn-beijing.aliyuncs.com/6fa27561-b3fe-4ce9-8156-06d0460344de.webp"
                  ></u-icon> </view
              ></view>
            </view>
          </view>

          <!-- 右侧操作按钮 -->
          <!-- <view class="campus-actions"> -->
          <!-- 导航按钮 -->
          <!-- <view class="action-btn" @click.stop="handleNavigation(item)">
              <u-icon
                color="#666"
                size="40rpx"
                name="https://tg-prod.oss-cn-beijing.aliyuncs.com/31e851ca-e5c1-4c9d-9d7b-6508095f20f8.webp"
              ></u-icon>
            </view> -->
          <!-- 电话按钮 -->
          <!-- <view
              class="action-btn"
              @click.stop="handleCall(item.consult_phone)"
            >
              <u-icon
                color="#666"
                size="40rpx"
                name="https://tg-prod.oss-cn-beijing.aliyuncs.com/6fa27561-b3fe-4ce9-8156-06d0460344de.webp"
              ></u-icon>
            </view> -->
          <!-- </view> -->
        </view>

        <!-- 加载更多组件 -->
        <view class="loadmore-container">
          <u-loadmore
            :status="loadStatus"
            :load-text="{
              loadmore: '上拉加载更多',
              loading: '正在加载...',
              nomore: '没有更多数据了'
            }"
            :line="true"
            lineColor="#e6e6e6"
            :marginTop="20"
            :marginBottom="20"
          />
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <EmptyIcon v-if="campusList.length === 0" text="暂无校区信息~" />

    <!-- 城市选择弹窗 -->
    <CityPicker
      :show="showCityModal"
      :cityList="cityList"
      :selectedCity="selectedCity"
      @close="closeCityPicker"
      @select="selectCity"
      :keys="['city']"
    />
  </view>
</template>

<script>
import CityPicker from "@/components/CityPicker/index.vue";
import EmptyIcon from "@/components/empty";
import { departmentCity } from "@/services/student/niedaoTeacher";
import { getDepartmentList } from "@/services/student/department";
export default {
  name: "CampusPage",
  components: {
    CityPicker,
    EmptyIcon
  },
  data() {
    return {
      campusList: [],
      showCityModal: false,
      selectedCity: uni.getStorageSync("location")?.city || "",
      cityList: [],
      navBgColor: "transparent", // 导航栏背景色
      navTextColor: "#FFFFFF", // 导航栏文字颜色
      scrollThreshold: 50, // 滚动阈值，超过这个值导航栏变白色
      loadStatus: "loadmore", // loadmore, loading, nomore
      page: 1,
      page_size: 10,
      total: 0,
      isFirstLoad: true,
      campusCount: 60,
      areaCount: 0
    };
  },
  computed: {
    // filteredCampusList() {
    //   return this.campusList.filter((item) =>
    //     item.address.includes(this.selectedCity)
    //   );
    // }
  },
  watch: {
    selectedCity: {
      handler(newVal) {
        console.log("watch", newVal);
        this.resetList();
        console.log("page", this.page);
        this.loadCampusList();
      },
      immediate: true
    }
  },
  methods: {
    handleCampusClick(item) {
      console.log("点击校区:", item);
      // 可以跳转到校区详情页
      uni.navigateTo({
        url: `/pages/student/subpages/campus/detail?id=${item.id}`
      });
    },
    handleNavigation(item) {
      console.log("导航到:", item.address);
      if (item.latitude && item.longitude) {
        // 调用地图导航
        uni.openLocation({
          latitude: item.latitude,
          longitude: item.longitude,
          name: item.department_name,
          address: item.address
        });
      } else {
        uni.showToast({
          title: "暂无地址信息",
          icon: "none"
        });
      }
    },
    handleCall(phone) {
      console.log("拨打电话:", phone);
      uni.makePhoneCall({
        phoneNumber: phone
      });
    },
    showCityPicker() {
      console.log("showCityPicker");
      this.showCityModal = true;
    },
    closeCityPicker() {
      this.showCityModal = false;
    },
    selectCity(city) {
      this.selectedCity = city;
      this.showCityModal = false;
    },
    // 重置列表数据
    resetList() {
      this.campusList = [];
      this.page = 1;
      this.total = 0;
      this.loadStatus = "loadmore";
      this.isFirstLoad = true;
    },
    // 处理页面滚动
    handlePageScroll(scrollTop) {
      if (scrollTop > this.scrollThreshold) {
        // 滚动超过阈值，导航栏变白色
        this.navBgColor = "#ffffff";
        this.navTextColor = "#333333";
      } else {
        // 滚动在阈值内，导航栏透明
        this.navBgColor = "transparent";
        this.navTextColor = "#FFFFFF";
      }
    },
    // 加载更多校区数据
    async loadMoreCampus() {
      // 检查是否还有更多数据
      if (this.campusList.length >= this.total && this.total > 0) {
        this.loadStatus = "nomore";
        return;
      }

      this.loadStatus = "loading";
      console.log("loadMoreCampus", this.page);
      await this.loadCampusList();
    },
    // 加载校区列表
    async loadCampusList() {
      try {
        const { data, code, message } = await getDepartmentList({
          page: this.page,
          page_size: this.page_size,
          city: this.selectedCity === "全国" ? "" : this.selectedCity // 添加城市筛选参数
        });

        if (code === 0) {
          const newResults = data.results || [];

          // 如果是第一页，直接替换；否则追加数据
          if (this.page === 1) {
            this.campusList = newResults;
          } else {
            this.campusList = [...this.campusList, ...newResults];
          }

          this.total = data.count || 0;
          this.campusCount = data.department_count || 0;
          // 判断是否还有更多数据
          if (this.campusList.length >= this.total) {
            this.loadStatus = "nomore";
          } else {
            this.loadStatus = "loadmore";
          }

          // 成功获取数据后才递增页码
          this.page++;
        } else {
          this.loadStatus = "loadmore";
          uni.showToast({
            title: message || "获取数据失败",
            icon: "none"
          });
        }
      } catch (error) {
        console.error("获取校区列表失败:", error);
        this.loadStatus = "loadmore";
        uni.showToast({
          title: "网络错误，请重试",
          icon: "none"
        });
      }
    },
    async getDepartmentCity() {
      const { data, code, message } = await departmentCity();
      if (code === 0) {
        this.cityList = data;
        this.areaCount = data.length;
        this.cityList.unshift({
          city: "全国"
        });
        // this.selectedCity = uni.getStorageSync("location")?.city || "";
      } else {
        uni.showToast({
          title: message,
          icon: "none"
        });
      }
    }
  },
  onLoad() {
    // this.page = 1;
    this.resetList();
    this.getDepartmentCity();
    // this.loadCampusList();
  },
  // 页面触底事件
  onReachBottom() {
    if (this.loadStatus === "nomore" || this.loadStatus === "loading") {
      return;
    }
    console.log("onReachBottom", this.page);
    this.loadMoreCampus();
  },
  // 页面滚动事件
  onPageScroll(e) {
    this.handlePageScroll(e.scrollTop);
  }
};
</script>

<style lang="scss" scoped>
.campus-page {
  min-height: 100vh;
  background: #f5f5f5;
  position: relative;
  // padding-bottom: env(safe-area-inset-bottom);
}

.header-section {
  background-image: url("https://tg-prod.oss-cn-beijing.aliyuncs.com/a94f822e-748d-4f1f-9a1e-f9473f251212.webp");
  background-size: 100% 100%;
  background-repeat: no-repeat;
  padding: 0 40rpx;
  padding-top: var(--status-bar-height, 44px);
  position: relative;
  overflow: hidden;

  .nav-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx 0;

    .nav-title {
      margin-left: -16rpx;
      color: #fff;
      text-shadow: 0px 4px 10px rgba(6, 105, 237, 0.93);
      font-size: 54rpx;
      font-style: normal;
      font-weight: 700;
      line-height: 60rpx;
      letter-spacing: 2rpx;
      width: 390rpx;
      height: 70rpx;
      image {
        width: 100%;
        height: 100%;
      }
    }

    .location {
      display: flex;
      align-items: center;
      gap: 8rpx;
      position: relative;
      z-index: 1;
      text {
        font-size: 28rpx;
        color: #fff;
      }
    }
  }

  .stats-section {
    padding: 0rpx 0 80rpx 0;

    .stats-item {
      .stats-number {
        color: #ffc525;
        font-size: 32rpx;
        font-style: normal;
        font-weight: 600;
        line-height: 50rpx;
      }

      .stats-label {
        color: #fff;
        font-size: 32rpx;
        font-style: normal;
        font-weight: 500;
        line-height: 50rpx;
      }

      .stats-text {
        color: #fff;
        font-size: 32rpx;
        font-style: normal;
        font-weight: 500;
        line-height: 50rpx;
      }
    }
  }

  .city-bg {
    position: absolute;
    right: -100rpx;
    bottom: -142rpx;
    width: 461rpx;
    height: 446rpx;
    background: url("https://tg-prod.oss-cn-beijing.aliyuncs.com/cf2024e3-5349-4130-b3aa-2976e4871254.webp")
      no-repeat;
    background-size: contain;
  }
}

.campus-list {
  padding: 30rpx 32rpx;
  padding-bottom: 40rpx;
  border-radius: 40rpx 40rpx 0px 0px;
  position: relative;
  z-index: 1;
  top: -30rpx;
  background: #f5f5f5;

  .campus-item {
    background: #fff;
    border-radius: 24rpx;
    padding: 30rpx 24rpx;
    margin-bottom: 28rpx;
    display: flex;
    align-items: flex-start;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);

    .campus-icon {
      width: 36rpx;
      height: 36rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 24rpx;
      flex-shrink: 0;
    }

    .campus-info {
      flex: 1;
      .campus-info-top {
        display: flex;
      }
      .campus-name {
        font-size: 30rpx;
        font-style: normal;
        font-weight: 500;
        color: #333;
        margin-bottom: 15rpx;
        line-height: 1.4;
      }

      .campus-address {
        color: #666;
        font-size: 28rpx;
        font-style: normal;
        font-weight: 400;
        line-height: 42rpx;
        margin-bottom: 15rpx;
        justify-content: space-between;
        display: flex;
        align-items: flex-start;

        .address-text {
          flex: 1;
          line-height: 42rpx;
          color: #333;
        }
      }
      .phone-number {
      }
      .campus-phone {
        font-size: 26rpx;
        color: #666;
        font-weight: 400;
        display: flex;
        justify-content: space-between;
      }
    }

    .campus-actions {
      display: flex;
      flex-direction: column;
      // gap: 20rpx;
      margin-left: 20rpx;
      // margin-top: 42rpx;
    }

    .action-btn {
      width: 40rpx;
      height: 40rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
      margin-left: 24rpx;
      margin-top: 5rpx; // 微调位置使图标与文字基线对齐
      &:active {
        background: #e8e8e8;
      }
    }

    &:active {
      background: #f9f9f9;
    }
  }
}

// 加载更多容器
.loadmore-container {
  padding: 0 0 32rpx;
}

.nav-letf {
  width: 40rpx;
  height: 40rpx;
  image {
    width: 100%;
    height: 100%;
  }
}

// 空状态样式
::v-deep .empty-icon {
  margin-top: 150rpx;
}
</style>
