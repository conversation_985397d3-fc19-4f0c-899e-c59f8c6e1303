<template>
  <view class="campus-detail-page">
    <!-- 顶部导航栏 -->
    <!-- <view class="nav-bar">
      <view class="nav-back" @click="onBack">
        <svg width="24" height="24" viewBox="0 0 24 24">
          <path
            d="M15 18l-6-6 6-6"
            stroke="#333"
            stroke-width="2"
            fill="none"
          />
        </svg>
      </view>
    </view> -->

    <!-- 校区主图轮播 -->
    <view class="main-img-box">
      <swiper
        class="main-swiper"
        :indicator-dots="false"
        :autoplay="false"
        :circular="true"
        @change="onSwiperChange"
      >
        <swiper-item v-for="(item, idx) in mainMediaList" :key="idx">
          <image
            v-if="item.image_url"
            class="main-img"
            :src="item.image_url"
            mode="aspectFill"
            @tap="imgPreview(item.image_url)"
          />
          <video
            v-else-if="item.video_url"
            class="main-img"
            :src="item.video_url"
            controls
            :poster="item.poster"
          />
        </swiper-item>
      </swiper>

      <!-- 图片数量标识 -->
      <view class="img-count">{{ mainMediaList.length }}项</view>

      <!-- 自定义轮播指示器 -->
      <view class="custom-indicators" v-if="mainMediaList.length > 1">
        <view
          v-for="(item, index) in mainMediaList"
          :key="index"
          class="indicator-item"
          :class="{ active: index === currentIndex }"
        ></view>
      </view>
    </view>

    <!-- 校区名称、地址、电话 -->
    <view class="campus-info">
      <view class="campus-title">{{ campusInfo.department_name }}</view>
      <view class="campus-address">
        <view class="address-box">
          <view class="action-btn" @click.stop="handleNavigation()">
            <u-icon
              top="4rpx"
              color="#666"
              size="30rpx"
              name="https://tg-prod.oss-cn-beijing.aliyuncs.com/7feb0227-0695-4ffc-91f5-715dc6c586d7.webp"
            ></u-icon>
          </view>
          {{ campusInfo.department_address }}
        </view>
        <view
          class="phone-btn"
          @click.stop="handleCall(campusInfo.consult_phone)"
        >
          <u-icon
            color="#666"
            size="42rpx"
            name="https://tg-prod.oss-cn-beijing.aliyuncs.com/6fa27561-b3fe-4ce9-8156-06d0460344de.webp"
          ></u-icon>
        </view>
      </view>
    </view>

    <!-- 渐变背景区域 -->
    <view class="gradient-bg">
      <!-- 师资团队 -->
      <view class="campus-section" v-if="niedaoTeacherList.length > 0">
        <view class="section-title" @click="handleTeacher"
          >师资团队({{ niedaoTeacherCount }})
          <u-icon
            width="10rpx"
            height="20rpx"
            name="https://tg-prod.oss-cn-beijing.aliyuncs.com/ff88152c-9b03-4b35-bedb-aca3724580a9.webp"
          ></u-icon
        ></view>
        <scroll-view
          class="teacher-list-scroll"
          scroll-x="true"
          show-scrollbar="false"
        >
          <view class="teacher-list-h">
            <view
              class="teacher-card"
              v-for="n in niedaoTeacherList"
              :key="n.teacher_id"
              @tap="handleTeacherDetail(n.id)"
            >
              <image class="teacher-img" :src="n.photo_url" mode="aspectFill" />
              <view class="teacher-info">
                <view class="teacher-name">{{ n.teacher_name }}</view>
                <view class="teacher-title">{{ n.dan_level_name }}</view>
              </view>
            </view>
          </view>
        </scroll-view>
      </view>

      <!-- 校区简介 -->
      <view class="campus-section">
        <view class="section-title">校区简介</view>
        <ExpandText
          v-if="campusInfo.department_intro"
          :text="campusInfo.department_intro"
          :lines="4"
        />
      </view>

      <!-- 校区亮点和学员评价标题行 -->
      <u-sticky :offset-top="0" bg-color="#ffffff">
        <view class="tabs-container">
          <u-tabs
            :list="tabList"
            :current="currentTab"
            @change="changeTab"
            lineColor="#FFBF0D"
            :activeStyle="{
              color: '#333',
              fontSize: '32rpx',
              fontWeight: '500'
            }"
            :itemStyle="{
              color: '#666',
              fontSize: '32rpx',
              fontWeight: '500',
              marginBottom: '16rpx'
            }"
            lineHeight="6rpx"
            lineWidth="36rpx"
            class="custom-tabs"
          ></u-tabs>
        </view>
      </u-sticky>

      <!-- 校区亮点卡片 -->
      <view class="highlight-grid-container">
        <view class="highlight-column">
          <view
            v-for="n in leftColumnItems"
            :key="n.module_content_id"
            class="highlight-card"
            @click="handleHighlight(n)"
          >
            <image
              v-if="
                n.media_list && n.media_list[0] && n.media_list[0].image_url
              "
              class="highlight-img"
              :src="n.media_list[0].image_url"
              :style="{
                height: (n.media_list[0].height || 254) + 'rpx'
              }"
              :mode="
                getImageMode(
                  n.media_list[0].height || 254,
                  n.media_list[0].originalWidth || n.media_list[0].width,
                  n.media_list[0].originalHeight || n.media_list[0].height
                )
              "
              @load="handleImageLoad(n, $event)"
            />
            <view class="highlight-text" v-if="n.content">{{ n.content }}</view>
          </view>
        </view>
        <view class="highlight-column">
          <view
            v-for="n in rightColumnItems"
            :key="n.module_content_id"
            class="highlight-card"
            @click="handleHighlight(n)"
          >
            <image
              v-if="
                n.media_list && n.media_list[0] && n.media_list[0].image_url
              "
              class="highlight-img"
              :src="n.media_list[0].image_url"
              :style="{
                height: (n.media_list[0].height || 200) + 'rpx'
              }"
              :mode="
                getImageMode(
                  n.media_list[0].height || 254,
                  n.media_list[0].originalWidth || n.media_list[0].width,
                  n.media_list[0].originalHeight || n.media_list[0].height
                )
              "
              @load="handleImageLoad(n, $event)"
            />
            <view
              class="highlight-text"
              :style="!n.content ? 'display: none' : ''"
              >{{ n.content }}</view
            >
          </view>
        </view>
      </view>

      <!-- 加载更多组件 -->
      <view class="loadmore-container" v-if="moduleList.length > 0">
        <u-loadmore
          :status="loadStatus"
          :load-text="{
            loadmore: '上拉加载更多',
            loading: '正在加载...',
            nomore: '没有更多数据了'
          }"
          :line="true"
          lineColor="#e6e6e6"
          :marginTop="20"
          :marginBottom="20"
        />
      </view>
      <EmptyIcon
        v-if="moduleList.length === 0"
        :text="currentTab === 0 ? '暂无校区亮点~' : '暂无学员评价~'"
      />
      <!-- 学员评价内容 -->
    </view>
  </view>
</template>

<script>
import ExpandText from "@/components/expandText/index.vue";
import EmptyIcon from "@/components/empty";
import {
  getDepartmentDetail,
  getModuleList
} from "@/services/student/department";
import { niedaoTeacherList } from "@/services/student/niedaoTeacher";
export default {
  name: "campus-detail-page",
  components: {
    ExpandText,
    EmptyIcon
  },
  data() {
    return {
      mainMediaList: [],
      currentIndex: 0, // 当前轮播图索引
      tabList: [
        {
          name: "校区亮点",
          count: 10,
          id: 2
        },
        {
          name: "学员评价",
          count: 10,
          id: 3
        }
      ],
      currentTab: 0,
      campusInfo: {},
      moduleList: [],
      imageSizes: {}, // 存储图片尺寸信息
      currentTabId: 2,
      // 分页相关属性
      page: 1,
      page_size: 10,
      total: 0,
      loadStatus: "loadmore", // loadmore, loading, nomore
      isFirstLoad: true,
      niedaoTeacherList: [],
      niedaoTeacherCount: 0
    };
  },
  computed: {
    leftColumnItems() {
      return this.distributeItems().left;
    },
    rightColumnItems() {
      return this.distributeItems().right;
    }
  },
  onLoad(options) {
    this.id = options.id;
    this.getDepartmentDetail();
    this.resetModuleList();
    this.getModuleList();
  },
  // 页面触底事件
  onReachBottom() {
    if (this.loadStatus === "nomore" || this.loadStatus === "loading") {
      return;
    }
    this.loadMoreModules();
  },
  methods: {
    onBack() {
      uni.navigateBack();
    },
    handleNavigation() {
      console.log("导航到校区");
      uni.openLocation({
        latitude: this.campusInfo.latitude,
        longitude: this.campusInfo.longitude,
        success: () => {},
        fail: () => {}
      });
    },
    handleCall(phone) {
      uni.makePhoneCall({
        phoneNumber: phone,
        success: () => {},
        fail: () => {}
      });
    },
    changeTab(data) {
      console.log(data);
      this.currentTab = data.index;
      this.currentTabId = data.id;
      this.resetModuleList();
      this.getModuleList();
    },
    // 重置模块列表数据
    resetModuleList() {
      this.moduleList = [];
      this.page = 1;
      this.total = 0;
      this.loadStatus = "loadmore";
      this.isFirstLoad = true;
      this.imageSizes = {}; // 清空图片尺寸缓存
    },
    load() {
      this.$refs.uReadMore.init();
    },
    handleHighlight(n) {
      uni.navigateTo({
        url: `/pages/student/subpages/campus/highlight?module_content_id=${n.module_content_id}&module_type=${n.module_type}`
      });
    },
    handleTeacher() {
      uni.navigateTo({
        url: `/pages/student/subpages/campus/teacher?department_id=${this.campusInfo.department_id}`
      });
    },
    async getDepartmentDetail() {
      const { data, code, message } = await getDepartmentDetail({
        id: this.id
      });
      if (code === 0) {
        this.mainMediaList = data.media_list || [];
        this.campusInfo = data;
        this.getNiedaoTeacherList();
      } else {
        uni.showToast({
          title: message,
          icon: "none"
        });
      }
    },
    async getModuleList() {
      try {
        const { data, code, message } = await getModuleList({
          department_introduce_id: this.id,
          module_type: this.currentTabId,
          page: this.page,
          page_size: this.page_size
        });

        if (code === 0) {
          const newResults = data.results || [];

          // 如果是第一页，直接替换；否则追加数据
          if (this.page === 1) {
            this.moduleList = newResults;
          } else {
            this.moduleList = [...this.moduleList, ...newResults];
          }

          this.total = data.count || 0;

          // 判断是否还有更多数据
          if (this.moduleList.length >= this.total) {
            this.loadStatus = "nomore";
          } else {
            this.loadStatus = "loadmore";
          }

          // 成功获取数据后才递增页码
          this.page++;
        } else {
          this.loadStatus = "loadmore";
          uni.showToast({
            title: message || "获取数据失败",
            icon: "none"
          });
        }
      } catch (error) {
        console.error("获取模块列表失败:", error);
        this.loadStatus = "loadmore";
        uni.showToast({
          title: "网络错误，请重试",
          icon: "none"
        });
      }
    },
    // 加载更多模块数据
    async loadMoreModules() {
      // 检查是否还有更多数据
      if (this.moduleList.length >= this.total && this.total > 0) {
        this.loadStatus = "nomore";
        return;
      }

      this.loadStatus = "loading";
      await this.getModuleList();
    },
    handleImageLoad(moduleItem, event) {
      // 获取图片的自然尺寸
      const imageId = moduleItem.module_content_id;
      const detail = event.detail;

      if (detail && detail.width && detail.height) {
        // 在小程序中，获取的是图片的像素尺寸
        // 需要根据设备像素比进行转换，或者直接使用比例计算
        const optimizedHeight = this.getOptimizedHeight(
          detail.width,
          detail.height
        );

        // 存储图片尺寸信息
        this.$set(this.imageSizes, imageId, {
          width: detail.width,
          height: detail.height,
          optimizedHeight
        });

        // 更新图片的media_list信息，包含原始尺寸
        if (moduleItem.media_list && moduleItem.media_list[0]) {
          moduleItem.media_list[0].height = optimizedHeight;
          moduleItem.media_list[0].originalWidth = detail.width;
          moduleItem.media_list[0].originalHeight = detail.height;
        }

        // 触发重新渲染
        this.$forceUpdate();
      }
    },
    async getNiedaoTeacherList() {
      const { data, code, message } = await niedaoTeacherList({
        department_ids: this.campusInfo.department_id,
        page: 1,
        page_size: 5,
        source: 2
      });
      if (code === 0) {
        this.niedaoTeacherList = data.results || [];
        this.niedaoTeacherCount = data.count || 0;
      } else {
        uni.showToast({
          title: message,
          icon: "none"
        });
      }
    },
    handleTeacherDetail(id) {
      uni.navigateTo({
        url: `/pages/student/subpages/campus/teacherDetail?id=${id}`
      });
    },
    onSwiperChange(e) {
      this.currentIndex = e.detail.current;
    },
    distributeItems() {
      const items = this.moduleList.slice();
      const left = [];
      const right = [];

      while (items.length > 0) {
        const item = items.shift();
        if (left.length <= right.length) {
          left.push(item);
        } else {
          right.push(item);
        }
      }

      return { left, right };
    },
    getImageMode(height, originalWidth, originalHeight) {
      // 小红书式图片显示规则：始终无白边
      const aspectRatio = originalHeight / originalWidth;

      // 小红书的核心原则：绝不留白边，优先视觉效果
      if (aspectRatio > 1.8) {
        // 超长图片：裁切显示，避免占用过多垂直空间
        return "aspectFill";
      } else if (aspectRatio < 0.7) {
        // 超宽图片：裁切显示，避免左右留白
        return "aspectFill";
      } else {
        // 常规比例图片：完美填充，无白边
        return "aspectFill";
      }
    },

    // 计算优化后的图片高度（小程序适配版本）
    getOptimizedHeight(originalWidth, originalHeight) {
      if (!originalWidth || !originalHeight) {
        return 254; // 默认高度
      }

      // 小程序中的容器宽度计算（rpx单位）
      // 屏幕宽度750rpx - 左右边距64rpx - 列间距24rpx = 662rpx
      // 每列宽度：662rpx / 2 = 331rpx
      const containerWidth = 331;

      // 计算宽高比
      const aspectRatio = originalHeight / originalWidth;
      let targetHeight = containerWidth * aspectRatio;

      // 小程序中的高度限制（rpx单位）
      const minHeight = 200;
      const maxHeight = 480; // 稍微降低最大高度，适应小程序屏幕

      // 针对不同宽高比的特殊处理
      if (aspectRatio > 2) {
        // 超长图片，强制限制高度
        targetHeight = Math.min(targetHeight, 400);
      } else if (aspectRatio < 0.6) {
        // 超宽图片，设置合理的最小高度
        targetHeight = Math.max(targetHeight, 180);
      }

      // 确保在合理范围内
      if (targetHeight < minHeight) {
        return minHeight;
      } else if (targetHeight > maxHeight) {
        return maxHeight;
      } else {
        return Math.round(targetHeight);
      }
    },
    imgPreview(url) {
      // 只获取图片URL，过滤掉视频
      const urls = this.mainMediaList
        .filter((item) => item.image_url)
        .map((item) => item.image_url);

      // 找到当前点击图片的索引
      const currentIndex = urls.findIndex((imageUrl) => imageUrl === url);

      uni.previewImage({
        urls,
        current: currentIndex >= 0 ? currentIndex : 0
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.campus-detail-page {
  background: #f2f2f2;
  min-height: 100vh;
  font-family: PingFang SC, Arial, sans-serif;
}
.nav-bar {
  display: flex;
  align-items: center;
  height: 88rpx;
  padding: 0 32rpx;
  background: #fff;
  border-bottom: 1px solid #f5f5f5;
}
.nav-back {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
}
.nav-title {
  flex: 1;
  text-align: center;
  font-size: 34rpx;
  font-weight: 500;
  color: #333;
}
.main-img-box {
  position: relative;
  width: 100%;
  height: 422rpx;
  overflow: hidden;
}
.main-swiper {
  width: 100%;
  height: 422rpx;
}
.main-img {
  width: 100%;
  height: 422rpx;
  object-fit: cover;
  background: #000;
}
.img-count {
  position: absolute;
  right: 20rpx;
  bottom: 20rpx;
  width: 80rpx;
  height: 46rpx;
  padding: 3rpx 10rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 5rpx;
  flex-shrink: 0;
  border-radius: 16rpx 8rpx 8rpx 8rpx;
  background: rgba(0, 0, 0, 0.47);
  font-size: 26rpx;
  font-style: normal;
  font-weight: 500;
  color: #fff;
  z-index: 2;
}
.campus-info {
  background: #fff;
  padding: 32rpx;
  border-bottom: 14rpx solid #f5f5f5;
  padding-bottom: 20rpx;
}
.campus-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 16rpx;
}
.address-box {
  display: flex;
  align-content: center;
}
.campus-address,
.campus-phone {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 8rpx;
  flex-direction: row;
  justify-content: space-between;
  .action-btn {
    margin-right: 12rpx;
    ::v-deep .u-icon {
      margin-top: 3rpx;
    }
  }
}
.campus-address svg,
.campus-phone svg {
  margin-right: 8rpx;
}
.gradient-bg {
  padding-bottom: 100rpx;
  background: linear-gradient(180deg, #ffffff 0%, #f2f2f2 100%);
}
.campus-section {
  padding: 32rpx 0 30rpx 32rpx;
  border-bottom: 14rpx solid #f5f5f5;
  background: #fff;
  ::v-deep .u-icon {
    margin-right: 20rpx;
  }
}
.section-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 16rpx;
  display: flex;
  justify-content: space-between;
}
.section-content {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
  position: relative;
  padding-right: 80rpx;
}
.expand-btn {
  position: absolute;
  right: 0;
  bottom: 0;
  color: #ffbb00;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  gap: 8rpx;
}
.teacher-list-scroll {
  width: 100%;
  overflow-x: auto;
  white-space: nowrap;
  padding-bottom: 8rpx;
}
.teacher-list-h {
  display: flex;
  flex-direction: row;
  gap: 24rpx;
}
.teacher-card {
  width: 200rpx;
  min-width: 200rpx;
  max-width: 200rpx;
  border-radius: 16rpx;
  overflow: hidden;
  position: relative;
  margin-right: 0;
}
.teacher-img {
  width: 200rpx;
  height: 300rpx;
  object-fit: cover;
}
.teacher-info {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 7rpx 0;
  text-align: center;
  border-radius: 12rpx;
  border: none;
  background: rgba(255, 255, 255, 0.9);
  // -webkit-backdrop-filter: blur(20rpx);
  backdrop-filter: blur(20rpx);
  // backdrop-filter: blur(10.1rpx);
}
.teacher-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}
.teacher-title {
  font-size: 24rpx;
  color: #666;
  // margin-top: 4rpx;
}
.tabs-container {
  padding: 30rpx 32rpx 20rpx 21rpx;
  background: #ffffff;
  border-bottom: 1rpx solid #f5f5f5;
}
.custom-read-more {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}
.custom-expand-btn {
  color: #ffbb00;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  gap: 8rpx;
  float: right;
  margin-top: -40rpx;
  background: #fff;
  padding-left: 20rpx;
}
.highlight-grid-container {
  padding: 0 32rpx;
  display: flex;
  gap: 24rpx;
  background: linear-gradient(180deg, #ffffff 0%, #f2f2f2 100%);
}
.highlight-column {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}
.highlight-card {
  background: #fff;
  border-radius: 12rpx;
  overflow: hidden;
  // box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  height: fit-content;
  margin-bottom: 0;
}
.highlight-img {
  width: 100%;
  min-height: 200rpx;
  max-height: 440rpx;
  flex-shrink: 0;
}
.highlight-text {
  padding: 18rpx 16rpx;
  font-size: 28rpx;
  color: #333;
  line-height: 1.32;
  flex: 1;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  padding-bottom: 0;
  margin-bottom: 16rpx;
}
.student-reviews {
  padding: 32rpx;
}
.review-card {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}
.review-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 16rpx;
}
.review-content {
  flex: 1;
}
.review-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 4rpx;
}
.review-text {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 4rpx;
}
.review-date {
  font-size: 24rpx;
  color: #999;
}
.loadmore-container {
  padding: 0 32rpx;
  margin-top: 20rpx;
  margin-bottom: 20rpx;
}
.custom-indicators {
  position: absolute;
  bottom: 20rpx;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 8rpx;
  z-index: 2;
}
.indicator-item {
  transition: all 0.3s ease;
  width: 10rpx;
  height: 10rpx;
  border-radius: 100rpx;
  opacity: 0.4;
  background: #ffc525;
  transition: background-color 0.3s;
  margin-right: 10rpx;

  &.active {
    width: 24.193rpx;
    height: 10rpx;
    background-color: #ffc525;
    opacity: 1;
    border-radius: 20rpx;
  }
}
::v-deep .empty-icon {
  margin-top: 120rpx;
}
</style>
