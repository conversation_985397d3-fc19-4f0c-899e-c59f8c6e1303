<template>
  <view class="search-page">
    <!-- <u-navbar
      title="搜索"
      bgColor="#fff"
      leftIconSize="25px"
      leftIconColor="#333333"
      :titleStyle="{
        color: '#333333',
        fontSize: '34rpx',
        fontWeight: '500',
        lineHeight: '40rpx'
      }"
      :autoBack="true"
      placeholder
    > -->
    <!-- <view class="nav-letf" slot="left">
        <image
          src="https://tg-prod.oss-cn-beijing.aliyuncs.com/e9ac0f4b-6282-4bac-b602-189057c45c90.webp"
        ></image>
      </view> -->
    <!-- </u-navbar> -->
    <!-- 顶部搜索栏 -->
    <view class="search-header">
      <!-- <view class="back-icon" @tap="goBack">
        <u-icon name="arrow-left" size="44rpx" color="#333"></u-icon>
      </view> -->

      <view class="search-bar">
        <view class="search-input-wrapper">
          <u-input
            class="search-input"
            v-model="keyword"
            placeholder="搜索商品名称"
            confirm-type="search"
            @confirm="handleSearch"
            focus
            :customStyle="{
              flex: 1,
              height: '72rpx',
              backgroundColor: '#F7F7F7',
              borderRadius: '36rpx',
              display: 'flex',
              alignItems: 'center',
              padding: '0 20rpx',
              border: 'none'
            }"
          >
            <u-icon
              name="https://tg-prod.oss-cn-beijing.aliyuncs.com/0def198b-5912-4bdc-be08-ef981047e719.webp"
              width="40rpx"
              height="40rpx"
              color="#999"
              class="search-icon"
              slot="prefix"
            ></u-icon>
          </u-input>
          <view class="searchDel">
            <u-icon
              v-if="keyword"
              @tap="handleClear"
              name="https://tg-prod.oss-cn-beijing.aliyuncs.com/ee2a33f8-b592-44a4-b624-a36589d0ad9a.webp"
              width="32rpx"
              height="32rpx"
              color="#999"
            ></u-icon>
          </view>

          <!-- <view class="clear-icon" v-if="keyword" @tap="clearKeyword">
            <u-icon name="close" color="#ccc" size="40rpx"></u-icon>
          </view> -->
        </view>
        <view class="search-btn" @tap="handleSearch">搜索</view>
      </view>
    </view>
    <view v-if="courseList.length > 0">
      <!-- 筛选项 -->
      <view class="filter-bar">
        <view
          class="filter-item"
          :class="{ active: currentTopFilter === 'sales_volume|desc' }"
          @tap="changeFilter('sales_volume|desc')"
        >
          销量
        </view>
        <view
          class="filter-item price-filter"
          :class="{ active: currentTopFilter === 'standard_price' }"
          @tap="changeFilter('standard_price')"
        >
          价格
          <view class="price-arrows">
            <u-icon
              name="arrow-up"
              size="10"
              :color="
                currentTopFilter === 'standard_price' &&
                priceAsc === 'standard_price|asc'
                  ? '#FB0'
                  : '#999'
              "
              bold
            ></u-icon>
            <u-icon
              name="arrow-down"
              size="10"
              :color="
                currentTopFilter === 'standard_price' &&
                priceAsc === 'standard_price|desc'
                  ? '#FB0'
                  : '#999'
              "
              bold
            ></u-icon>
          </view>
        </view>
      </view>

      <!-- 课程列表 -->
      <scroll-view
        scroll-y
        class="course-list"
        @scrolltolower="loadMore"
        show-scrollbar
        refresher-enabled
        :refresher-triggered="isRefreshing"
        @refresherrefresh="onRefresh"
      >
        <view
          class="course-item"
          v-for="(item, index) in courseList"
          :key="index"
          @tap="viewDetail(item)"
        >
          <image
            class="course-image"
            :src="item.cover_url"
            mode="aspectFill"
          ></image>
          <view class="course-info">
            <view>
              <view class="course-name">{{ item.course_name }}</view>
              <view class="course-duration">{{ item.standard_numb }}课时</view>
            </view>
            <view class="price-area">
              <view class="price-wrapper">
                <view
                  v-if="item.department_price > 0"
                  style="display: flex; align-items: center"
                >
                  <text class="price-symbol">¥</text>
                  <text class="course-price">{{
                    item.department_price || item.standard_price
                  }}</text>
                </view>
                <view class="free-tag" v-else>免费</view>
                <text
                  v-if="
                    item.is_show_price === 1 && item.minpro_course_type !== 4
                  "
                  class="original-price"
                >
                  ¥{{ item.standard_price }}
                </text>
              </view>
            </view>
          </view>
        </view>

        <u-loadmore
          :status="loadMoreStatus"
          lineColor="#DADADA"
          line
          color="#999999"
          fontSize="26rpx"
          loadmoreText="加载更多"
          marginTop="70rpx"
          marginBottom="40rpx"
        />
      </scroll-view>

      <!-- 空状态 -->
    </view>
    <view
      v-if="emptyShow && courseList.length === 0"
      style="
        height: 80vh;
        display: flex;
        justify-content: center;
        align-items: center;
      "
    >
      <loading-animation
        :width="300"
        :characterSize="260"
        :textSize="32"
        v-if="loadMoreStatus === 'loading'"
      />
      <u-empty
        v-else
        text="暂无符合条件的课程~"
        marginTop="-100rpx"
        width="163rpx"
        height="230rpx"
        textColor="#999999"
        icon="https://tg-prod.oss-cn-beijing.aliyuncs.com/d02814d0-bfd3-48bf-8d9d-80cda362b89a.webp"
      ></u-empty>
    </view>
  </view>
</template>

<script>
import { courseList } from "@/services/student/myCourse";
import LoadingAnimation from "@/components/common/LoadingAnimation.vue";
export default {
  name: "SearchPage",
  components: {
    LoadingAnimation
  },
  data() {
    return {
      keyword: "",
      currentTopFilter: "",
      priceAsc: false,
      courseList: [],
      page: 1,
      pageSize: 10,
      isRefreshing: false,
      loadMoreStatus: "loading",
      emptyShow: false
    };
  },
  computed: {},
  onLoad(options) {},
  methods: {
    goBack() {
      uni.navigateBack();
    },
    clearKeyword() {
      this.keyword = "";
    },
    async handleSearch() {
      if (!this.keyword.trim()) return;

      this.page = 1;
      this.currentTopFilter = "";
      this.priceAsc = "";
      this.courseList = [];
      this.loadMoreStatus = "loading";
      await this.fetchCourses();
      this.emptyShow = true;
    },
    changeFilter(filter) {
      this.courseList = [];
      this.page = 1;
      this.currentTopFilter = filter;
      if (filter === this.currentTopFilter && filter === "standard_price") {
        // 如果再次点击价格筛选，则切换排序方向
        this.priceAsc =
          this.priceAsc === "standard_price|asc"
            ? "standard_price|desc"
            : "standard_price|asc";
      } else {
        this.currentTopFilter = filter;
        this.priceAsc = "sales_volume|asc";
      }
      this.fetchCourses(false);
    },
    loadMore() {
      if (this.loadMoreStatus === "nomore") return;

      this.page++;
      this.fetchCourses();
    },
    onRefresh() {
      this.isRefreshing = true;
      this.page = 1;
      this.fetchCourses(true);
    },
    viewDetail(course) {
      uni.navigateTo({
        url: `/pages/student/subpages/courseDetail/index?course_id=${course.minpro_course_id}`
      });
    },
    // 模拟获取课程数据
    async fetchCourses() {
      console.log(this.loadMoreStatus);

      this.loadMoreStatus = "loading";
      const student_id = uni.getStorageSync("curStudentInfo").student_id;
      const department_id = uni.getStorageSync("curStudentInfo").department_id;
      const { code, data, message } = await courseList({
        student_id,
        department_id,
        page: this.page,
        page_size: this.pageSize,
        sort:
          this.currentTopFilter === "standard_price"
            ? this.priceAsc
            : this.currentTopFilter,
        minpro_course_name: this.keyword.trim()
      });
      if (code === 0) {
        if (this.page === 1) {
          this.courseList = data.results || [];
        } else {
          for (let i = 0; i < data.results.length; i++) {
            this.courseList.push(data.results[i]);
          }
        }
        this.total = data.count;
        if (this.courseList.length >= this.total) {
          this.loadMoreStatus = "nomore";
        } else {
          this.loadMoreStatus = "loadmore";
        }
        this.isRefreshing = false;
      } else {
        this.loadMoreStatus = "loadmore";
        this.courseList = [];
        uni.showToast({
          title: message,
          icon: "none"
        });
        this.isRefreshing = false;
      }
    },
    handleClear() {
      this.emptyShow = false;
      this.keyword = "";
      this.courseList = [];
      this.page = 1;
      this.loadMoreStatus = "loadmore";
    }
  }
};
</script>

<style lang="scss" scoped>
.nav-letf {
  width: 40rpx;
  height: 40rpx;
  image {
    width: 100%;
    height: 100%;
  }
}

.search-page {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
  position: relative;
  overflow: hidden;
}

.search-header {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #fff;
  height: 106rpx;
  //   border-bottom: 1rpx solid #f0f0f0;

  .back-icon {
    padding: 10rpx;
    margin-right: 20rpx;
  }

  .search-bar {
    flex: 1;
    display: flex;
    align-items: center;

    .search-input-wrapper {
      flex: 1;
      height: 72rpx;
      position: relative;
      //   background-color: #f5f5f5;
      //   border-radius: 36rpx;
      //   display: flex;
      //   align-items: center;
      //   padding: 0 20rpx;

      .search-icon {
        margin-right: 10rpx;
      }
      .searchDel {
        width: 32rpx;
        position: absolute;
        right: 25rpx;
        top: 20rpx;
      }
      .search-input {
        flex: 1;
        height: 100%;
        font-size: 28rpx;
      }
    }

    .search-btn {
      font-size: 30rpx;
      color: #333;
      margin-left: 32rpx;
      // padding: 0 10rpx;
    }
  }
}

.filter-bar {
  display: flex;
  height: 80rpx;
  background-color: #fff;
  padding: 0 30rpx;
  align-items: center;
  //   border-bottom: 1rpx solid #f0f0f0;

  .filter-item {
    font-size: 26rpx;
    color: #999;
    margin-right: 60rpx;
    display: flex;
    align-items: center;

    &.active {
      color: #fb0;
      font-weight: 500;
    }
  }

  .price-filter {
    display: flex;
    align-items: center;

    .price-arrows {
      display: flex;
      flex-direction: column;
      margin-left: 8rpx;
      //   height: 20rpx;
      line-height: 1;

      :first-child {
        margin-bottom: -1.7rpx;
      }
    }
  }
}

.course-list {
  flex: 1;
  padding: 0 32rpx 30rpx 32rpx;
  height: calc(100vh - 186rpx);
  .course-item:first-child {
    margin-top: 30rpx;
  }
  .course-item {
    display: flex;
    padding: 20rpx;
    background-color: #fff;
    border-radius: 24rpx;
    margin-bottom: 20rpx;

    .course-image {
      width: 160rpx;
      height: 160rpx;
      border-radius: 20rpx;
      flex-shrink: 0;
    }

    .course-info {
      flex: 1;
      margin-left: 24rpx;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .course-name {
        font-size: 32rpx;
        font-weight: 500;
        color: #333;
        line-height: 36rpx;
        margin-bottom: 4rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        width: 460rpx;
      }

      .course-duration {
        font-size: 26rpx;
        color: #999;
        margin-bottom: 20rpx;
      }

      .price-area {
        .free-tag {
          font-size: 32rpx;
          font-weight: 500;
          color: #ff553a;
        }

        .price-wrapper {
          display: flex;
          align-items: flex-end;

          .price-symbol {
            font-size: 24rpx;
            color: #ff553a;
            font-weight: 400;
            margin-right: 5rpx;
          }

          .course-price {
            font-size: 36rpx;
            font-weight: 500;
            color: #ff553a;
            display: flex;
            align-items: flex-end;
            line-height: 40rpx;
          }

          .original-price {
            font-size: 24rpx;
            color: #999;
            text-decoration: line-through;
            margin-left: 10rpx;
            display: flex;
            align-items: flex-end;
            line-height: 40rpx;
          }
        }
      }
    }
  }
}
</style>
