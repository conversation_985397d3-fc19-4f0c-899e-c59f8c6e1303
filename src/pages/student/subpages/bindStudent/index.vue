<template>
  <div class="bind-student-page">
    <!-- <u-navbar
      title="切换身份"
      bgColor="#fff"
      leftIconSize="25px"
      leftIconColor="#333333"
      :titleStyle="{
        color: '#333333',
        fontSize: '34rpx',
        fontWeight: '500',
        lineHeight: '40rpx'
      }"
      :autoBack="true"
      placeholder
    >
      <view class="nav-letf" slot="left">
        <image
          src="https://tg-prod.oss-cn-beijing.aliyuncs.com/e9ac0f4b-6282-4bac-b602-189057c45c90.webp"
        ></image>
      </view>
    </u-navbar> -->
    <u-toast ref="uToast" style="z-index: 999999 !important"></u-toast>
    <div class="header" v-if="curStudentInfo && session.role !== 'default'">
      <div class="user-info">
        <div class="avatar">
          <u-avatar
            size="107rpx"
            :src="genders_list[curStudentInfo.choose_head] || genders_list[1]"
          ></u-avatar>
        </div>
        <div class="info">
          <div class="name">
            <text>{{ curStudentInfo.student_name }}</text>
            <image
              v-if="curStudentInfo.student_type_int === 0"
              src="https://tg-prod.oss-cn-beijing.aliyuncs.com/4a542e1f-a030-44b7-b585-2c0b68cdabbf.webp"
            />
          </div>
          <div class="school">{{ curStudentInfo.department_name }}</div>
          <div class="id">{{ curStudentInfo.student_number }}</div>
        </div>
        <!-- @click="handleUnbind(curStudentInfo)" -->
        <div class="status">当前学员</div>
      </div>
    </div>

    <div class="student-list" :class="{ 'is-student': !curStudentInfo }">
      <div class="headerStudent">
        <div class="title">其他学员</div>
        <div
          class="delStudent"
          @click="delStudent"
          v-if="studentListArr.length > 0"
        >
          <!-- v-if="studentListArr.length > 0" -->
          <img
            :src="
              !isUnbind
                ? 'https://tg-prod.oss-cn-beijing.aliyuncs.com/5422417c-68b2-4450-bf72-239359595800.webp'
                : 'https://tg-prod.oss-cn-beijing.aliyuncs.com/f5793cc4-14d9-4e22-a538-e21edaf2a4aa.webp'
            "
            alt=""
          />
          <div>{{ !isUnbind ? "解绑学员" : "解绑完成" }}</div>
        </div>
      </div>
      <div class="list">
        <div
          class="student-item"
          v-for="(item, index) in studentListArr"
          :key="index"
        >
          <div class="avatar">
            <img
              :src="genders_list[item.choose_head] || genders_list[1]"
              alt=""
            />
          </div>
          <div class="info">
            <div class="name">
              <text>{{ item.student_name }}</text>
              <image
                v-if="item.student_type_int === 0"
                src="https://tg-prod.oss-cn-beijing.aliyuncs.com/5fb04b46-bf38-49d9-9190-f1398248498e.webp"
              />
            </div>
            <div class="school">{{ item.department_name }}</div>
          </div>
          <div
            class="status"
            :class="{ unbind: isUnbind }"
            @click="handleUnbind(item)"
          >
            {{ !isUnbind ? "切换身份" : "解绑学员" }}
          </div>
        </div>
      </div>
    </div>

    <div
      class="add-student"
      @click="addNewStudent"
      v-if="!isUnbind || studentListArr.length === 0"
    >
      <img
        src="https://tg-prod.oss-cn-beijing.aliyuncs.com/ed3fb8ab-eba2-4599-8b1d-739b2037b031.webp"
        alt=""
      />
      <div class="add-student-text">新增绑定学员</div>
    </div>
    <view @touchmove.stop.prevent="">
      <bind-student-popup
        ref="bindStudentPopup"
        v-model="showBindStudent"
        @confirm="handleBindStudent"
      ></bind-student-popup>
      <unbind-submit
        v-model="showUnbindSubmit"
        @confirm="handleUnbindConfirm"
        @cancel="handleUnbindCancel"
        :studentInfo="studentInfo"
      ></unbind-submit>
    </view>
  </div>
</template>

<script>
import { genders, genders_list } from "../../my/config";
import { getStudentList, unbindStudent } from "@/services/student/my";
import { getBindStudent, getOpenId } from "@/services/student/home";
import BindStudentPopup from "../../home/<USER>/BindStudentPopup.vue";
import UnbindSubmit from "./compoments/unbindSubmit.vue";
import { updateCheckedUserInfo } from "@/utils/user";
export default {
  name: "BindStudent",
  data() {
    return {
      curStudentInfo: {},
      session: {},
      student_id: "",
      role: "",
      genders,
      genders_list,
      openid: "",
      studentListArr: [],
      showBindStudent: false,
      isUnbind: false,
      showUnbindSubmit: false,
      studentInfo: {},
      rowIds: {
        student: "student_id",
        customer: "customer_id",
        default: "open_id"
      },
      rowIdField: ""
    };
  },
  computed: {},
  components: { BindStudentPopup, UnbindSubmit },
  methods: {
    addNewStudent() {
      // 处理新增学员逻辑
      this.showBindStudent = true;
    },
    delStudent() {
      // 处理解绑学员逻辑
      if (!this.curStudentInfo) return;
      this.isUnbind = !this.isUnbind;
    },
    getStudentList() {
      uni.login({
        provider: "weixin",
        onlyAuthorize: true, // 微信登录仅请求授权认证
        success: async (event) => {
          const { code } = event;
          const res = await getOpenId({ code, UNAUTHORIZED: true });
          if (res.code === 0) {
            this.openid = res.data.openid;
            // 获取学员列表
            getStudentList({
              open_id: this.openid
            }).then((res) => {
              console.log(
                this.curStudentInfo[this.rowIdField],
                this.rowIdField
              );
              this.studentListArr = res.data.filter(
                (item) =>
                  item[this.rowIdField] !== this.curStudentInfo[this.rowIdField]
              );
            });
          } else {
            uni.showToast({
              title: res.message,
              icon: "none"
            });
          }
        }
      });
    },
    async handleBindStudent() {
      try {
        const formData = await this.$refs.bindStudentPopup.validate();
        this.handleBindUser(formData);
      } catch (error) {
        console.error("表单验证失败:", error);
      }
    },
    async handleBindUser(formData) {
      uni.showLoading({
        title: "查找中..."
      });
      const { code, data, message } = await getBindStudent(formData);
      console.log(code, data, message);
      if (code === 0) {
        uni.hideLoading();
        uni.navigateTo({
          url:
            "/pages/student/studentPage/index?prospective=" +
            JSON.stringify(data)
        });
        this.$refs.bindStudentPopup.handleClose();
      } else {
        uni.hideLoading();
        uni.$u.toast(message);
      }
    },
    async handleUnbind(item) {
      if (!this.isUnbind) {
        uni.showLoading({
          title: "切换中..."
        });
        // 如果没有student_id 就是 意向 student'/'customer'/'default'
        const role = item.student_id ? "student" : "customer";
        const id = item.student_id || item.customer_id;
        await updateCheckedUserInfo(role, id, this.openid);
        this.curStudentInfo = item;
        await this.getStudentList();
        uni.setStorageSync("curStudentInfo", item);
        const session = uni.getStorageSync("session");
        session.operation_id =
          item.student_type_int === 0 ? item.customer_id : item.student_id;
        session.role = item.student_type_int === 0 ? "customer" : "student";
        uni.setStorageSync("session", session);
        this.rowIdField = this.rowIds[role];
        uni.hideLoading();
        uni.showToast({
          title: `切换成功，欢迎${item.student_name}同学`,
          icon: "none",
          duration: 2000
        });
      } else {
        this.studentInfo = item;
        this.showUnbindSubmit = true;
      }
    },
    handleUnbindCancel() {
      this.showUnbindSubmit = false;
    },
    async handleUnbindConfirm(studentInfo) {
      uni.showLoading({
        title: "解绑中..."
      });
      let id;
      if (studentInfo.student_type_int === 0) {
        id = {
          customer_id: studentInfo.customer_id
        };
      } else {
        id = {
          student_id: studentInfo.student_id
        };
      }
      const res = await unbindStudent(id);
      if (res.code === 0) {
        uni.hideLoading();
        uni.showToast({
          title: "已解绑学员！",
          icon: "none"
        });
        if (this.studentListArr && this.studentListArr.length > 0) {
          this.getStudentList();
        } else {
          uni.setStorageSync("curStudentInfo", "");
          uni.navigateBack();
        }
      } else {
        uni.$u.toast(res.message);
      }
    }
  },
  async onShow() {
    uni.showLoading({
      title: "加载中..."
    });
    this.session = uni.getStorageSync("session");
    this.role = this.session.role || "default";
    this.rowIdField = this.rowIds[this.role];
    console.log(this.curStudentInfo, this.session);
    this.curStudentInfo = uni.getStorageSync("curStudentInfo");
    await this.getStudentList();
    uni.hideLoading();
    uni.hideShareMenu();
  }
};
</script>

<style lang="scss" scoped>
.bind-student-page {
  min-height: 100vh;
  background: #f5f7fa;
  padding: 32rpx;

  .header {
    background: #ffffff;
    border-radius: 24rpx;
    padding: 32rpx;
    // margin-bottom: 50rpx;
    // margin-top: 30rpx;
    background: url("https://tg-prod.oss-cn-beijing.aliyuncs.com/fac47021-5a6a-4568-a2f1-ece90196e184.png")
      no-repeat center center;
    background-size: 100%;
    .user-info {
      display: flex;
      align-items: center;
      position: relative;

      .avatar {
        // width: 107rpx;
        // height: 107rpx;
        // border-radius: 50%;
        // overflow: hidden;
        margin-right: 24rpx;
        // border: 2rpx solid #f0f7ff;
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
      .info {
        flex: 1;

        .name {
          color: #fff;
          font-family: "PingFang SC";
          font-size: 32rpx;
          font-style: normal;
          font-weight: 500;
          line-height: normal;
          margin-bottom: 8rpx;
          display: flex;
          align-items: center;
          image {
            width: 80rpx;
            height: 38rpx;
            margin-left: 10rpx;
          }
        }

        .school {
          color: #fff;
          font-size: 28rpx;
          font-style: normal;
          font-weight: 400;
          line-height: 42rpx; /* 150% */
          margin-bottom: 4rpx;
        }

        .id {
          color: #fff;

          font-size: 28rpx;
          font-style: normal;
          font-weight: 400;
          line-height: 42rpx; /* 150% */
        }
      }

      .status {
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        display: inline-flex;
        padding: 12rpx;
        justify-content: center;
        align-items: center;
        border-radius: 10rpx;
        border: 1rpx solid #fff;
        color: #fff;
        font-size: 26rpx;
        font-style: normal;
        font-weight: 400;
        line-height: 28rpx;
      }
    }
  }

  .student-list {
    border-radius: 24rpx;
    margin-top: 50rpx;
    .headerStudent {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 30rpx;
      .title {
        color: #333;
        font-size: 32rpx;
        font-style: normal;
        font-weight: 500;
        line-height: normal;
      }
      .delStudent {
        display: flex;
        align-items: center;
        color: #666;
        font-size: 26rpx;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
        img {
          width: 30rpx;
          height: 30rpx;
          margin-right: 10rpx;
        }
      }
    }
    .list {
      .student-item {
        display: flex;
        align-items: center;
        position: relative;
        // padding: 24rpx 0;
        // border-bottom: 1px solid #eeeeee;
        height: 192rpx;
        border-radius: 24rpx;
        background: #fff;
        box-shadow: 0px 0px 50rpx 0px rgba(124, 143, 166, 0.1);
        margin-bottom: 24rpx;
        &:last-child {
          border-bottom: none;
        }

        .avatar {
          width: 107rpx;
          height: 107rpx;
          // border-radius: 50%;
          // overflow: hidden;
          margin-right: 18rpx;
          margin-left: 29rpx;
          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }

        .info {
          flex: 1;

          .name {
            color: #333;
            font-size: 32rpx;
            font-style: normal;
            font-weight: 500;
            line-height: normal;
            display: flex;
            align-items: center;
            image {
              width: 80rpx;
              height: 38rpx;
              margin-left: 10rpx;
            }
          }

          .school {
            color: #666;
            font-size: 28rpx;
            font-style: normal;
            font-weight: 400;
            line-height: normal;
          }
        }

        .status {
          width: 136rpx;
          height: 54rpx;
          border-radius: 32rpx;
          background: linear-gradient(15deg, #ffbf0d 18.1%, #ffcb3c 83.29%);
          box-shadow: 0px -4rpx 8rpx 0px #eaac00 inset;
          color: #fff;
          font-size: 24rpx;
          color: #fff;
          text-align: center;
          font-size: 24rpx;
          font-style: normal;
          font-weight: 600;
          line-height: 32rpx;
          margin-right: 30rpx;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .unbind {
          background: #fff;
          border: 2rpx solid #fb0;
          color: #fb0;
          box-shadow: 0px 0px 0rpx 0px #fff;
        }
      }
    }
  }

  .add-student {
    height: 192rpx;
    border-radius: 24rpx;
    background: #fff;
    box-shadow: 0px 0px 50px 0px rgba(124, 143, 166, 0.1);
    display: flex;
    align-items: center;
    color: #333;
    font-size: 30rpx;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    .add-student-text {
      color: #333;
      font-size: 30rpx;
      font-style: normal;
      font-weight: 500;
      line-height: normal;
    }
    img {
      width: 100rpx;
      height: 100rpx;
      margin: 0 30rpx;
    }
  }
  .is-student {
    // margin-top: 186rpx;
  }
}
.nav-letf {
  width: 40rpx;
  height: 40rpx;
  image {
    width: 100%;
    height: 100%;
  }
}
</style>
