<template>
  <div>
    <div class="detail">
      <div class="avatar">
        <img :src="momentDetail.title_img" alt="" />
      </div>
      <div class="content">
        <div class="name">{{ momentDetail.operator_name }}</div>
        <div class="value">{{ momentDetail.content }}</div>
        <div
          class="media"
          v-if="momentDetail.image_url && momentDetail.image_url.length"
          :class="{
            'four-grid': momentDetail.image_url.length === 4
          }"
        >
          <template v-if="momentDetail.image_type === 1">
            <div
              class="media-item"
              v-for="(imageItem, index) in momentDetail.image_url"
              :key="index"
            >
              <u-image
                :src="`${imageItem.url}${
                  imageItem.formatSize >= 10
                    ? '?x-oss-process=image/resize,w_500/quality,q_80'
                    : ''
                }`"
                radius="16rpx"
                @tap="handleImageClick(index, momentDetail)"
                :width="
                  momentDetail.image_url.length <= 1
                    ? getImgShowShape(momentDetail.image_url).width
                    : '150rpx'
                "
                :height="
                  momentDetail.image_url.length <= 1
                    ? getImgShowShape(momentDetail.image_url).height
                    : '150rpx'
                "
                :class="
                  momentDetail.image_url.length <= 1
                    ? getImgShowShape(momentDetail.image_url)
                    : 'multiple-img'
                "
                mode="aspectFill"
              >
                <template v-slot:loading>
                  <u-loading-icon size="14" color="#999"></u-loading-icon>
                </template>
              </u-image>
            </div>
          </template>
          <div
            class="video-wrap"
            :style="{
              width: getVideoShowShape(momentDetail).width,
              height: getVideoShowShape(momentDetail).height
            }"
            @tap="fullScreenPlayVideo"
            v-else-if="momentDetail.image_type === 2"
          >
            <img :src="playIcon" alt="" class="play-btn" />
            <video
              id="myVideo"
              :show-center-play-btn="false"
              direction="0"
              :controls="isShowControls"
              @fullscreenchange="fullscreenchange"
              object-fit="contain"
              class="video"
              :src="momentDetail.image_url[0].url"
              :poster="momentDetail.cover_url"
            >
              <view class="full-screen-mask" @tap="handleFullScreenMask"></view>
            </video>
          </div>
        </div>
        <div class="row-bar">
          <span class="post-time">{{ momentDetail.publish_time }}</span>
          <div v-if="source !== 'share'" class="controls">
            <div class="like" @tap="onLikeClick()">
              <div class="img">
                <img
                  :src="momentDetail.like_status === 1 ? likeIcon : notLikeIcon"
                  alt=""
                />
              </div>
              <div class="text">
                {{ formatWNumber(momentDetail.likes_count) }}
              </div>
            </div>
            <div class="share" @tap="openSharePopup(momentDetail)">
              <div class="img">
                <img :src="shareIcon" alt="" />
              </div>
              <div class="text">分享</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <view @touchmove.stop.prevent="">
      <share-popup
        :showSharePopup="isShowSharePopup"
        :shareFriendInfo="currentMoment"
        webviewPath="tg-minigram/niedaoCircle/shareCard"
        :webviewParams="webviewParams"
        @closeSharePopup="closeSharePopup"
        @share="handleShare"
      ></share-popup>
    </view>
  </div>
</template>

<script>
import {
  momentsDetail,
  shareMomentsDetail,
  likeMoment,
  viewMoment
} from "@/services/student/niedaoCircle";
import SharePopup from "@/components/sharePopup/index.vue";
export default {
  name: "studentNiedaoCircleDetail",
  components: {
    SharePopup
  },
  data() {
    return {
      isShowSharePopup: false,
      currentMoment: {},
      webviewParams: {},
      isShowControls: false,
      fields: "",
      isRequesting: false,
      session: {},
      curStudentInfo: {},
      showSharePopup: false,
      shareIcon:
        "https://tg-prod.oss-cn-beijing.aliyuncs.com/5e8ff99d-156c-4063-9ba0-509e0bb4170c.png",
      notLikeIcon:
        "https://tg-prod.oss-cn-beijing.aliyuncs.com/caafa28f-7bf1-4807-b632-0c2198f4dece.png",
      likeIcon:
        "https://tg-prod.oss-cn-beijing.aliyuncs.com/0ffb4f9a-b93e-40bb-8481-8c4610869879.png",
      playIcon:
        "https://tg-prod.oss-cn-beijing.aliyuncs.com/7c8ff79b-619f-4842-b640-87504abe4ed1.png",
      id: "",
      source: "",
      momentDetail: {},
      videoMuted: "",
      debounceTimer: null,
      debounceDelay: 500
    };
  },
  computed: {},
  onShareAppMessage(res) {
    if (res.from === "button") {
      // 来自页面内分享按钮
      console.log(res.target);
    }
    let imageUrl = "";
    if (this.momentDetail.image_type === 1) {
      if (this.momentDetail.image_url?.length) {
        imageUrl = this.momentDetail.image_url[0].url;
      } else {
        imageUrl =
          "https://tg-prod.oss-cn-beijing.aliyuncs.com/f860ad82-95fd-49c4-a8de-c629f069411b.png";
      }
    } else if (this.momentDetail.image_type === 2) {
      imageUrl =
        this.momentDetail.cover_url ||
        this.momentDetail.image_url[0].url +
          "?x-oss-process=video/snapshot,t_1000,f_jpg,w_0,h_0,m_fast";
    }
    this.isShowSharePopup = false;
    return {
      title:
        this.momentDetail.content ||
        this.momentDetail.operator_name + "的聂道圈详情",
      imageUrl,
      path: `/pages/student/subpages/niedaoCircle/detail?id=${this.id}&source=share`
    };
  },
  onShareTimeline(res) {
    let imageUrl = "";
    if (this.momentDetail.image_type === 1) {
      if (this.momentDetail.image_url?.length) {
        imageUrl = this.momentDetail.image_url[0].url;
      }
    } else if (this.momentDetail.image_type === 2) {
      if (this.momentDetail.cover_url) {
        imageUrl = this.momentDetail.cover_url;
      } else {
        imageUrl =
          "https://tg-prod.oss-cn-beijing.aliyuncs.com/50e99c07-ae26-4a69-ad0a-785ab4a2454f.png";
      }
    }
    return {
      title:
        this.momentDetail.content ||
        this.momentDetail.operator_name + "的聂道圈详情",
      imageUrl,
      query: `id=${this.id}&source=share`,
      path: `/pages/teacher/subpages/niedaoCircle/detail`
    };
  },
  methods: {
    formatWNumber(num) {
      if (num >= 10000) {
        return (num / 10000).toFixed(1) + "w";
      }
      return num;
    },
    getVideoShowShape(item) {
      const { width, height } = item.image_url[0];
      return {
        width: width > height ? "341rpx" : "256rpx",
        height: width > height ? "256rpx" : "341rpx"
      };
    },
    getDisplaySize(imgW, imgH) {
      // 判断类型
      if (imgH / imgW >= 3) {
        // 超长竖图
        return {
          width: 150,
          height: 454
        };
      } else if (imgW / imgH >= 3) {
        // 超长横图
        return {
          width: 454,
          height: 150
        };
      } else if (imgH > imgW) {
        // 普通竖图
        return {
          width: 256,
          height: 341
        };
      } else {
        // 普通横图
        return {
          width: 341,
          height: 256
        };
      }
    },
    getImgShowShape(image_url) {
      const { width, height } = image_url[0];
      const imgInfo = this.getDisplaySize(width, height);

      return {
        width: imgInfo.width + "rpx",
        height: imgInfo.height + "rpx"
      };
    },
    getMomentDetail() {
      momentsDetail({
        id: this.id,
        student_id: this.curStudentInfo[this.fields]
      }).then((res) => {
        console.log(res, "res");
        this.momentDetail = res.data;
        this.handleContentOpen(res.data);
      });
    },
    getShareMomentDetail() {
      shareMomentsDetail({
        id: this.id,
        student_id: this.curStudentInfo[this.fields]
      }).then((res) => {
        this.momentDetail = res.data;
      });
    },
    handleImageClick(current, urls) {
      uni.previewImage({
        current, // 当前点击的图片索引
        urls: urls.image_url.map((item) => item.url) // 所有图片的 URL 列表
      });
    },
    viewMomentFn(moment_id) {
      viewMoment({
        student_id: this.curStudentInfo[this.fields],
        moment_id
      });
    },
    handleContentOpen(item) {
      this.viewMomentFn(item.id);
    },
    openSharePopup(item) {
      console.log(item, "item");
      this.currentMoment = item;
      this.webviewParams = {
        id: item.id,
        token: this.session.token
      };
      this.isShowSharePopup = true;
    },
    closeSharePopup() {
      this.isShowSharePopup = false;
    },
    // 点赞/取消逻辑
    onLikeClick() {
      const item = this.momentDetail;
      uni.vibrateShort();
      // 1. 立即切换本地状态
      if (item.like_status === 2) {
        item.like_status = 1;
        item.likes_count += 1;
      } else {
        item.like_status = 2;
        item.likes_count -= 1;
      }

      // 2. 防抖定时器清除并重设
      if (this.debounceTimer) {
        clearTimeout(this.debounceTimer);
      }

      this.debounceTimer = setTimeout(() => {
        this.sendLikeStatus(item.id, item.like_status);
      }, this.debounceDelay);
    },

    // 发送点赞状态到后端
    async sendLikeStatus(moment_id, status) {
      try {
        const fields =
          this.session.role === "customer" ? "customer_id" : "student_id";
        const res = await likeMoment({
          student_id: this.curStudentInfo[fields],
          status,
          moment_id
        });

        // 如果后端有返回可用状态/数量，更新本地数据
        const updated = this.momentDetail;

        if (res.data && typeof res.data.like_status === "number") {
          updated.like_status = res.data.like_status;
        }

        if (res.data && typeof res.data.likes_count === "number") {
          updated.likes_count = res.data.likes_count;
        }
      } catch (error) {
        console.error("点赞接口失败", error);

        // 请求失败：可选回滚本地状态
        const item = this.momentDetail;
        if (item.like_status === 1) {
          item.like_status = 2;
          item.likes_count -= 1;
        } else {
          item.like_status = 1;
          item.likes_count += 1;
        }
      }
    },
    fullScreenPlayVideo(index) {
      const videoContext = uni.createVideoContext(`myVideo`, this);
      console.log(index, this.videoContext, "videoContext");
      this.isShowControls = true;
      videoContext.requestFullScreen();
    },
    fullscreenchange({ detail }) {
      console.log(detail, "detail");
      const videoContext = uni.createVideoContext(`myVideo`, this);
      if (detail.fullscreen) {
        videoContext.play();
        this.isShowControls = true;
      } else {
        videoContext.pause();
        this.isShowControls = false;
      }
    },
    handleFullScreenMask() {
      const videoContext = uni.createVideoContext(`myVideo`, this);
      // 关闭全屏
      videoContext.exitFullScreen();
    }
  },
  watch: {},

  // 页面周期函数--监听页面加载
  onLoad(options) {
    this.id = options.id;

    this.curStudentInfo = uni.getStorageSync("curStudentInfo");
    this.session = uni.getStorageSync("session");
    this.fields =
      this.session.role === "customer" ? "customer_id" : "student_id";
    this.source = options.source;
    console.log(options, "options");
    if (options.source === "share") {
      this.getShareMomentDetail();
    } else {
      this.getMomentDetail();
    }
  },
  // 页面周期函数--监听页面初次渲染完成
  onReady() {},
  // 页面周期函数--监听页面显示(not-nvue)
  onShow() {},
  // 页面周期函数--监听页面隐藏
  onHide() {},
  // 页面周期函数--监听页面卸载
  onUnload() {}
  // 页面处理函数--监听用户下拉动作
  // onPullDownRefresh() { uni.stopPullDownRefresh(); },
  // 页面处理函数--监听用户上拉触底
  // onReachBottom() {},
  // 页面处理函数--监听页面滚动(not-nvue)
  // onPageScroll(event) {},
  // 页面处理函数--用户点击右上角分享
  // onShareAppMessage(options) {},
};
</script>

<style lang="scss" scoped>
.detail {
  display: flex;
  padding: 29rpx 32rpx;
  padding-bottom: 18rpx;
  border-bottom: 1rpx solid #eee;
  .avatar {
    flex-shrink: 0;
    width: 85rpx;
    height: 85rpx;
    img {
      width: 100%;
      height: 100%;
      border-radius: 50%;
    }
  }
  .content {
    flex: 1;
    margin-left: 20rpx;

    .name {
      font-size: 32rpx;
      color: #333;
      font-weight: 500;
    }
    .value {
      margin-top: 10rpx;
      font-size: 30rpx;
      color: #333;
      margin-bottom: 18rpx;
    }
    .media {
      display: flex;
      flex-flow: wrap;
      .media-item {
        margin-right: 12rpx;
        margin-bottom: 12rpx;
        .single-img {
          width: 256rpx;
          height: 341rpx;
          border-radius: 16rpx;
        }
        .multiple-img {
          width: 150rpx;
          height: 150rpx;
          border-radius: 16rpx;
        }
      }
      .video-wrap {
        width: 256rpx;
        height: 341rpx;
        position: relative;
        .play-btn {
          width: 50rpx;
          height: 50rpx;
          position: absolute;
          top: 50%;
          left: 50%;
          z-index: 1;
          transform: translate(-50%, -50%);
        }
        .video {
          border-radius: 16rpx;
          width: 100%;
          height: 100%;
        }
      }
    }
    .row-bar {
      padding-top: 10rpx;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .is-top,
      .post-time {
        font-size: 26rpx;
        font-weight: 400;
        color: #999;
      }
      .controls {
        display: flex;
        align-items: center;
        .text {
          font-size: 26rpx;
          font-weight: 400;
          color: #999;
          padding-left: 12rpx;
        }
        .like {
          margin-right: 50rpx;
        }
        .share,
        .like {
          display: flex;
          align-items: center;
          .img {
            width: 30rpx;
            height: 30rpx;
            img {
              width: 100%;
              height: 100%;
            }
          }
        }
      }
    }
  }
}
.full-screen-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 150rpx;
}
.four-grid {
  display: grid !important;
  grid-template-columns: repeat(2, 150rpx);
  width: 100%;

  .media-item {
    margin-right: 0;

    .u-image {
      width: 100% !important;
      height: 100% !important;
    }
  }
}
</style>
