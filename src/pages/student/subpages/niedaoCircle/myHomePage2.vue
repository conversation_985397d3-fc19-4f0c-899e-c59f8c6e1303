<template>
  <view class="page-container">
    <scroll-view
      class="scroller"
      scroll-y
      :scroll-anchoring="true"
      :scroll-top="scrollTop"
      @scroll="handleScroll"
      @scrolltolower="handleScrollToLower"
      @scrolltoupper="handleScrollToUpper"
    >
      <!-- 顶部加载指示器 -->
      <view v-if="isLoadingMore" class="loading-indicator top-loading">
        <view class="loading-skeleton">
          <view class="skeleton-item" v-for="n in 3" :key="n"></view>
        </view>
      </view>

      <!-- 动态消息列表 -->
      <view
        v-for="message in messageList"
        :key="`message-${message.id}`"
        :id="`message-${message.id}`"
        class="message"
        :class="{ 'message-enter': message.isNew }"
      >
        <view class="message-content">
          {{ message.content }}
        </view>
        <view class="message-time">
          {{ formatTime(message.timestamp) }}
        </view>
      </view>

      <!-- 底部加载指示器 -->
      <view v-if="isLoadingBottom" class="loading-indicator bottom-loading">
        <view class="loading-text">加载中...</view>
      </view>

      <!-- 锚点元素 -->
      <view id="anchor" class="anchor"></view>
    </scroll-view>
  </view>
</template>

<style scoped>
.page-container {
  height: 100vh;
  background-color: #7fdbff;
  position: relative;
}

.scroller {
  height: 100%;
  overflow-anchor: auto;
  /* 启用硬件加速 */
  transform: translateZ(0);
  -webkit-overflow-scrolling: touch;
}

.scroller * {
  overflow-anchor: none;
}

.anchor {
  overflow-anchor: auto;
  height: 1px;
  opacity: 0;
}

.message {
  padding: 0.5em;
  border-radius: 1em;
  margin: 0.5em;
  line-height: 1.1em;
  background-color: white;
  contain: layout style paint;
  /* 添加平滑过渡效果 */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateZ(0);
  /* 防止重排 */
  will-change: transform, opacity;
  opacity: 1;
  transform: translateY(0);
}

/* 新消息进入动画 */
.message-enter {
  animation: messageSlideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes messageSlideIn {
  0% {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.message-content {
  font-size: 14px;
  color: #333;
  margin-bottom: 4px;
}

.message-time {
  font-size: 12px;
  color: #999;
  text-align: right;
}

/* 加载指示器样式 */
.loading-indicator {
  padding: 10px;
  text-align: center;
  /* 防止布局抖动 */
  min-height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.top-loading {
  background: linear-gradient(to bottom, rgba(127, 219, 255, 0.8), transparent);
}

.bottom-loading {
  background: linear-gradient(to top, rgba(127, 219, 255, 0.8), transparent);
}

.loading-text {
  color: #666;
  font-size: 12px;
}

/* 骨架屏效果 */
.loading-skeleton {
  width: 100%;
}

.skeleton-item {
  height: 60px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 1em;
  margin: 0.5em;
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* 优化滚动性能 */
.scroller::-webkit-scrollbar {
  display: none;
}

/* 防止内容跳动的关键样式 */
.message-list-container {
  /* 使用 flexbox 布局防止高度计算错误 */
  display: flex;
  flex-direction: column;
  min-height: 100%;
}

/* 响应式优化 */
@media (max-width: 750rpx) {
  .message {
    margin: 0.3em;
    padding: 0.4em;
  }

  .message-content {
    font-size: 13px;
  }
}

/* 减少重绘重排的优化 */
.message:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 内容保护区域，防止快速滚动时的视觉跳动 */
.content-protection {
  position: relative;
  z-index: 1;
}
</style>

<script>
export default {
  name: "myHomePage",
  data() {
    return {
      scrollTop: 0,
      lastVisibleItemId: null,
      messageList: [], // 消息列表
      timer: null, // 定时器
      time: null, // 时间戳
      isLoadingMore: false, // 顶部加载状态
      isLoadingBottom: false, // 底部加载状态
      messageIdCounter: 0, // 消息ID计数器
      lastScrollTop: 0, // 上次滚动位置
      scrollDirection: "down", // 滚动方向
      messages: [
        "I wondered why the baseball was getting bigger. Then it hit me.",
        "Police were called to a day care, where a three-year-old was resisting a rest.",
        "Did you hear about the guy whose whole left side was cut off? He's all right now.",
        "The roundest knight at King Arthur's round table was Sir Cumference.",
        "To write with a broken pencil is pointless.",
        "When fish are in schools they sometimes take debate.",
        "The short fortune teller who escaped from prison was a small medium at large.",
        "A thief who stole a calendar… got twelve months.",
        "A thief fell and broke his leg in wet cement. He became a hardened criminal.",
        "Thieves who steal corn from a garden could be charged with stalking.",
        "When the smog lifts in Los Angeles , U. C. L. A.",
        "The math professor went crazy with the blackboard. He did a number on it.",
        "The professor discovered that his theory of earthquakes was on shaky ground.",
        "The dead batteries were given out free of charge.",
        "If you take a laptop computer for a run you could jog your memory.",
        "A dentist and a manicurist fought tooth and nail.",
        "A bicycle can't stand alone; it is two tired.",
        "A will is a dead giveaway.",
        "Time flies like an arrow; fruit flies like a banana.",
        "A backward poet writes inverse.",
        "In a democracy it's your vote that counts; in feudalism, it's your Count that votes.",
        "A chicken crossing the road: poultry in motion.",
        "If you don't pay your exorcist you can get repossessed.",
        "With her marriage she got a new name and a dress.",
        "Show me a piano falling down a mine shaft and I'll show you A-flat miner.",
        "When a clock is hungry it goes back four seconds.",
        "The guy who fell onto an upholstery machine was fully recovered.",
        "A grenade fell onto a kitchen floor in France and resulted in Linoleum Blownapart.",
        "You are stuck with your debt if you can't budge it.",
        "Local Area Network in Australia : The LAN down under.",
        "He broke into song because he couldn't find the key.",
        "A calendar's days are numbered."
      ]
    };
  },
  mounted() {
    // 启动定时器，每秒添加一条消息
    setTimeout(() => {
      this.time = new Date().getTime();
    }, 1000);
    this.startAddingMessages();

    // 初始化一些消息
    this.initializeMessages();
  },
  beforeUnmount() {
    // 清理定时器
    if (this.timer) {
      clearInterval(this.timer);
    }
  },
  methods: {
    // 初始化消息列表
    initializeMessages() {
      for (let i = 0; i < 5; i++) {
        this.messageList.push(this.createMessage(false));
      }
    },

    // 创建消息对象
    createMessage(isNew = true) {
      const message = {
        id: ++this.messageIdCounter,
        content: this.randomMessage(),
        timestamp: new Date().getTime(),
        isNew
      };

      // 新消息动画效果在添加后短时间内移除
      if (isNew) {
        setTimeout(() => {
          const index = this.messageList.findIndex((m) => m.id === message.id);
          if (index !== -1) {
            this.messageList[index].isNew = false;
          }
        }, 400);
      }

      return message;
    },

    // 格式化时间
    formatTime(timestamp) {
      const date = new Date(timestamp);
      return `${date.getHours().toString().padStart(2, "0")}:${date
        .getMinutes()
        .toString()
        .padStart(2, "0")}`;
    },

    // 获取随机消息
    randomMessage() {
      return this.messages[Math.floor(Math.random() * this.messages.length)];
    },

    // 添加新消息（优化版本）
    addMessage() {
      const newMessage = this.createMessage(true);

      // 使用 requestAnimationFrame 确保平滑动画
      this.$nextTick(() => {
        // 记录当前滚动位置
        const currentScrollTop = this.scrollTop;

        // 添加消息到列表顶部
        this.messageList.unshift(newMessage);

        // 限制消息数量，避免内存泄漏
        if (this.messageList.length > 100) {
          this.messageList.splice(50); // 保留前50条
        }

        // 如果用户在顶部附近，保持滚动位置
        if (currentScrollTop < 100) {
          this.$nextTick(() => {
            this.lastVisibleItemId = `message-${newMessage.id}`;
          });
        }
      });
    },

    // 开始添加消息的定时器
    startAddingMessages() {
      this.timer = setInterval(() => {
        this.addMessage();
      }, 2000); // 改为2秒一次，减少频率
    },

    // 停止添加消息
    stopAddingMessages() {
      if (this.timer) {
        clearInterval(this.timer);
        this.timer = null;
      }
    },

    // 处理滚动事件（优化版本）
    handleScroll(e) {
      const currentScrollTop = e.detail.scrollTop;

      // 判断滚动方向
      this.scrollDirection =
        currentScrollTop > this.lastScrollTop ? "down" : "up";
      this.lastScrollTop = currentScrollTop;
      this.scrollTop = currentScrollTop;

      // 节流处理，避免频繁触发
      if (this.scrollTimer) {
        clearTimeout(this.scrollTimer);
      }

      this.scrollTimer = setTimeout(() => {
        console.log("滚动事件:", {
          scrollTop: currentScrollTop,
          direction: this.scrollDirection
        });
      }, 100);
    },

    // 处理滚动到底部（加载历史消息）
    async handleScrollToLower() {
      if (this.isLoadingBottom) return;

      console.log("滚动到底部");
      this.isLoadingBottom = true;

      // 模拟加载更多历史数据
      try {
        await this.loadMoreHistoryMessages();
      } finally {
        this.isLoadingBottom = false;
      }
    },

    // 处理滚动到顶部（加载最新消息）
    async handleScrollToUpper() {
      if (this.isLoadingMore) return;

      console.log("滚动到顶部");
      this.isLoadingMore = true;

      // 模拟加载更多最新数据
      try {
        await this.loadMoreRecentMessages();
      } finally {
        this.isLoadingMore = false;
      }
    },

    // 加载更多历史消息
    async loadMoreHistoryMessages() {
      return new Promise((resolve) => {
        setTimeout(() => {
          const newMessages = [];
          for (let i = 0; i < 5; i++) {
            newMessages.push(this.createMessage(false));
          }

          // 添加到列表末尾
          this.messageList.push(...newMessages);
          resolve();
        }, 1000);
      });
    },

    // 加载更多最新消息
    async loadMoreRecentMessages() {
      return new Promise((resolve) => {
        setTimeout(() => {
          const newMessages = [];
          for (let i = 0; i < 3; i++) {
            newMessages.push(this.createMessage(true));
          }

          // 添加到列表开头
          this.messageList.unshift(...newMessages);
          resolve();
        }, 800);
      });
    }
  }
};
</script>
