<template>
  <view class="niedao-circle-page">
    <u-toast ref="uToast"></u-toast>
    <view class="niedao-circle-title">
      <view class="title">聂道圈</view>
    </view>
    <div class="niedao-circle-content">
      <div class="niedao-circle-school">
        <div class="pisition">
          <img
            src="https://tg-prod.oss-cn-beijing.aliyuncs.com/8a33aef0-9633-4c42-bb32-7da5b494b4a2.png"
            alt=""
          />
        </div>
        <div class="school-name">{{ curStudentInfo.department_name }}</div>
      </div>
      <u-list
        v-if="list.length > 0"
        @refresherrefresh="refresh"
        @scrolltolower="loadMore"
        lowerThreshold="100"
        @scroll="onScroll"
        :refresher-enabled="true"
        :height="listHeight"
        :refresher-triggered="isRefreshing"
      >
        <!-- 列表内容 -->
        <u-list-item
          v-for="(item, index) in list"
          :key="item.id"
          class="ulist-item"
        >
          <div
            class="niedao-circle-content-item"
            :class="{
              'is-bottom': loadStatus === 'nomore' && index === list.length - 1
            }"
          >
            <div
              class="avatar"
              @tap="handleToMyHomePage(item.operate_id, item.operator_name)"
            >
              <img :src="item.title_img" alt="" />
            </div>
            <div class="content">
              <div class="teacher-name">{{ item.operator_name }}</div>
              <div class="value">
                <ExpandText :text="item.content" :lines="5" />
              </div>
              <div
                class="media"
                v-if="item.image_url && item.image_url.length"
                :class="{
                  'four-grid': item.image_url.length === 4
                }"
              >
                <template v-if="item.image_type === 1">
                  <div
                    class="media-item"
                    v-for="(imageItem, index1) in item.image_url"
                    :key="index1"
                  >
                    <u-image
                      :src="`${imageItem.url}${
                        imageItem.formatSize >= 10
                          ? '?x-oss-process=image/resize,w_500/quality,q_80'
                          : ''
                      }`"
                      lazyLoad
                      radius="16rpx"
                      @tap="handleImageClick(index1, item)"
                      :width="
                        item.image_url.length <= 1
                          ? getImgShowShape(item.image_url).width
                          : '150rpx'
                      "
                      :height="
                        item.image_url.length <= 1
                          ? getImgShowShape(item.image_url).height
                          : '150rpx'
                      "
                      :class="
                        item.image_url.length <= 1
                          ? 'single-img'
                          : 'multiple-img'
                      "
                      mode="aspectFill"
                    >
                      <template v-slot:loading>
                        <u-loading-icon size="14" color="#999"></u-loading-icon>
                      </template>
                    </u-image>
                  </div>
                </template>
                <div
                  class="video-wrap"
                  :style="{
                    width: getVideoShowShape(item).width,
                    height: getVideoShowShape(item).height
                  }"
                  @tap="fullScreenPlayVideo(item)"
                  v-else-if="item.image_type === 2"
                >
                  <img
                    v-if="item.isShowPlayIcon"
                    :src="playIcon"
                    alt=""
                    class="play-btn"
                  />
                  <video
                    :id="'video-' + item.id"
                    direction="0"
                    :controls="videoMuted === 'video-' + item.id"
                    :muted="videoMuted !== 'video-' + item.id"
                    :autoplay="false"
                    :loop="true"
                    :showCenterPlayBtn="false"
                    @fullscreenchange="fullscreenchange"
                    object-fit="contain"
                    class="video"
                    :data-id="item.id"
                    @play="onPlay(item.id)"
                    @pause="onPause(item.id)"
                    :src="item.image_url[0].url"
                    :poster="item.cover_url"
                    :enable-progress-gesture="false"
                    :custom-cache="false"
                  >
                    <view
                      class="full-screen-mask"
                      @tap="handleFullScreenMask(item.id)"
                    ></view>
                  </video>
                </div>
              </div>
              <div class="row-bar">
                <span class="is-top" v-if="item.top_status === 1"
                  >置顶内容</span
                >
                <span class="post-time" v-else>{{
                  formatPostTime(item.publish_time)
                }}</span>
                <div class="controls">
                  <div class="like" @tap="onLikeClick(index)">
                    <div class="img">
                      <img
                        :src="item.like_status === 1 ? likeIcon : notLikeIcon"
                        alt=""
                      />
                    </div>
                    <div class="text" style="padding-left: 1rpx">
                      {{ formatWNumber(item.likes_count) }}
                    </div>
                  </div>
                  <div class="share" @tap="openSharePopup(item)">
                    <div class="img">
                      <img :src="shareIcon" alt="" />
                    </div>
                    <div class="text">分享</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </u-list-item>

        <!-- 加载更多提示 -->
        <u-loadmore
          :status="loadStatus"
          :icon-type="iconType"
          :line="true"
          marginBottom="40"
          :load-text="loadText"
        />
      </u-list>
    </div>
    <loading-animation
      :width="300"
      :characterSize="260"
      :textSize="32"
      v-if="isLoading && list.length === 0"
    />
    <Empty
      :style="{ height: listHeight, marginTop: '-45rpx' }"
      :text="loadText.nomore"
      v-if="list.length === 0 && !isLoading"
    />
    <!-- <view class="tabbar-container">
      <TabBar
        ref="tabbar"
        :key="tabbarKey"
        @changeDot="handleChangeDot"
      ></TabBar>
    </view> -->
    <bind-student-popup
      ref="bindStudentPopup"
      v-model="showBindStudent"
      @confirm="handleBindStudent"
    ></bind-student-popup>
    <share-popup
      :showSharePopup="showSharePopup"
      :shareFriendInfo="currentMoment"
      webviewPath="tg-minigram/niedaoCircle/shareCard"
      :webviewParams="webviewParams"
      :isShowTimeLine="false"
      @closeSharePopup="closeSharePopup"
      @share="handleShare"
    ></share-popup>
  </view>
</template>

<script>
import LoadingAnimation from "@/components/common/LoadingAnimation.vue";
import BindStudentPopup from "@/pages/student/home/<USER>/BindStudentPopup.vue";
import { getBindStudent } from "@/services/student/home";
// import TabBar from "../../components/TabBar.vue";
import ExpandText from "@/components/expandText/index.vue";
import Empty from "@/components/empty/index.vue";
import SharePopup from "@/components/sharePopup/index.vue";
import {
  momentsList,
  viewMoment,
  likeMoment
} from "@/services/student/niedaoCircle";
export default {
  name: "studentNiedaoCircleIndex",
  components: {
    BindStudentPopup,
    // TabBar,
    Empty,
    ExpandText,
    SharePopup,
    LoadingAnimation
  },
  data() {
    return {
      currentMoment: {},
      webviewParams: {},
      showSharePopup: false,
      showBindStudent: false,
      tabbarKey: 0,
      shareIcon:
        "https://tg-prod.oss-cn-beijing.aliyuncs.com/5e8ff99d-156c-4063-9ba0-509e0bb4170c.png",
      notLikeIcon:
        "https://tg-prod.oss-cn-beijing.aliyuncs.com/caafa28f-7bf1-4807-b632-0c2198f4dece.png",
      likeIcon:
        "https://tg-prod.oss-cn-beijing.aliyuncs.com/0ffb4f9a-b93e-40bb-8481-8c4610869879.png",
      playIcon:
        "https://tg-prod.oss-cn-beijing.aliyuncs.com/7c8ff79b-619f-4842-b640-87504abe4ed1.png",
      shadowStyle: {
        backgroundImage: "none"
      },
      session: {},
      curStudentInfo: {},
      list: [],
      listParams: {
        page: 1,
        page_size: 20,
        view_type: 1
      },
      isShowDot: false,
      isRequesting: false,
      isRefreshing: false,
      loadStatus: "loadmore",
      iconType: "arrow",
      loadText: {
        loadmore: "上拉加载更多",
        loading: "正在加载...",
        nomore: "没有更多了"
      },
      observers: [],
      listHeight: 0,
      safeAreaInsets: {},
      niedaoCircleSchoolRect: 0,
      playingId: null, // 当前正在播放的 video id
      scrollTimer: null, // 节流用
      videoMuted: "",
      debounceDelay: 500, // 防抖延迟 ms
      isLoading: true
    };
  },
  // onShareAppMessage(res) {
  //   if (res.from === "button") {
  //     // 来自页面内分享按钮
  //     const item = res.target.dataset.item;
  //     let imageUrl = "";
  //     if (item.image_type === 1) {
  //       if (item.image_url?.length) {
  //         imageUrl = item.image_url[0].url;
  //       } else {
  //         imageUrl =
  //           "https://tg-prod.oss-cn-beijing.aliyuncs.com/f860ad82-95fd-49c4-a8de-c629f069411b.png";
  //       }
  //     } else if (item.image_type === 2) {
  //       if (item.cover_url) {
  //         imageUrl = item.cover_url;
  //       } else {
  //         imageUrl =
  //           item.image_url[0].url +
  //           "?x-oss-process=video/snapshot,t_1000,f_jpg,w_0,h_0,m_fast";
  //       }
  //     }

  //     return {
  //       title: item.content || item.operator_name + "的聂道圈详情",
  //       imageUrl,
  //       path: `/pages/student/subpages/niedaoCircle/detail?id=${item.id}&source=share`
  //     };
  //   }

  //   this.$refs.uToast.show({
  //     type: "default",
  //     duration: "2000",
  //     message: "请选择聂道圈进行分享！"
  //   });
  //   return false;
  // },
  methods: {
    formatWNumber(num) {
      if (num >= 10000) {
        return (num / 10000).toFixed(1) + "w";
      }
      return num.toString();
    },
    parseLoad() {
      this.$refs.uReadMore.forEach((item) => {
        item.init();
      });
    },
    async handleBindStudent() {
      try {
        const formData = await this.$refs.bindStudentPopup.validate();
        this.handleBindUser(formData);
      } catch (error) {
        console.error("表单验证失败:", error);
      }
    },
    async handleBindUser(formData) {
      uni.showLoading({
        title: "查找中..."
      });
      const { code, data, message } = await getBindStudent(formData);
      if (code === 0) {
        uni.hideLoading();
        uni.navigateTo({
          url:
            "/pages/student/studentPage/index?prospective=" +
            JSON.stringify(data)
        });
        this.$refs.bindStudentPopup.handleClose();
      } else {
        uni.hideLoading();
        uni.$u.toast(message);
      }
    },
    formatPostTime(postTime) {
      const compatibilityPostTime = (timeStr) => {
        // 替换 '-' 为 '/'，兼容 iOS
        const iosCompatibleStr = timeStr.replace(/-/g, "/");
        return new Date(iosCompatibleStr);
      };

      const now = new Date(); // 当前时间
      const postDate = new Date(compatibilityPostTime(postTime)); // 发布时间
      const timeDiff = now - postDate; // 时间差（毫秒）

      const seconds = Math.floor(timeDiff / 1000); // 转换为秒
      const minutes = Math.floor(seconds / 60); // 转换为分钟
      const hours = Math.floor(minutes / 60); // 转换为小时
      const days = Math.floor(hours / 24); // 转换为天数

      if (seconds < 60) {
        return "刚刚";
      } else if (minutes < 60) {
        return `${minutes}分钟前`;
      } else if (hours < 24) {
        return `${hours}小时前`;
      } else if (days === 1) {
        return "昨天";
      } else if (days > 30) {
        return `${postTime}`;
      } else {
        return `${days}天前`;
      }
    },
    async getMyCircle(source) {
      try {
        this.isLoading = true;
        const res = await momentsList(this.listParams);
        if (res.data.status === 1) {
          this.loadStatus = "nomore";
          this.loadText = {
            loadmore: "已退学，暂不可使用聂道圈",
            loading: "已退学，暂不可使用聂道圈",
            nomore: "已退学，暂不可使用聂道圈"
          };
          this.list = [];
          return false;
        }
        const results = res.data.results.map((item) => ({
          ...item,
          debounceTimer: null,
          isShowPlayIcon: true
        }));
        if (this.listParams.page === 1) {
          this.list = results; // 第一页直接覆盖
        } else {
          this.list = [...this.list, ...results]; // 追加数据
        }
        // 判断是否还有更多数据
        if (res.data.results.length < this.listParams.page_size) {
          this.loadStatus = "nomore";
        } else {
          this.loadStatus = "loadmore";
        }
        this.isLoading = false;
        if (source === "onShow") {
          this.$nextTick(() => {
            this.checkVideoInView();
          });
        }
        uni.stopPullDownRefresh();
      } catch (error) {
      } finally {
        this.isRefreshing = false; // 结束刷新状态
        this.isLoading = false;
      }
    },
    // 下拉刷新
    refresh() {
      this.isRefreshing = true;
      this.listParams.page = 1; // 重置页码
      this.getMyCircle(); // 重新加载数据
    },
    // 上拉加载更多
    loadMore() {
      if (this.loadStatus === "nomore") return; // 没有更多数据时不再加载
      this.loadStatus = "loading"; // 设置加载状态
      this.listParams.page += 1; // 页码加 1
      this.getMyCircle(); // 加载更多数据
    },
    handleChangeDot(val) {
      this.isShowDot = val;
    },
    openSharePopup(item) {
      this.currentMoment = item;
      this.showSharePopup = true;
      this.webviewParams = {
        id: item.id,
        token: this.session.token
      };
    },
    closeSharePopup() {
      this.currentMoment = {};
      this.showSharePopup = false;
    },
    handleShare(item) {
      if (item.type === "shareGraph") {
        this.shareGraph();
      }
    },
    shareGraph() {
      if (this.currentMoment.id) {
        uni.$u.route({
          url: "/pages/teacher/share/shareCard",
          params: {
            momentId: this.currentMoment.id
          }
        });
      } else {
        uni.$u.toast("分享失败，请稍后再试");
      }
    },
    // 点赞/取消逻辑
    onLikeClick(index) {
      const item = this.list[index];
      uni.vibrateShort();
      // 1. 立即切换本地状态
      if (item.like_status === 2) {
        item.like_status = 1;
        item.likes_count += 1;
      } else {
        item.like_status = 2;
        item.likes_count -= 1;
      }

      // 2. 防抖定时器清除并重设
      if (item.debounceTimer) {
        clearTimeout(item.debounceTimer);
      }

      item.debounceTimer = setTimeout(() => {
        this.sendLikeStatus(item.id, item.like_status, index);
      }, this.debounceDelay);
    },

    // 发送点赞状态到后端
    async sendLikeStatus(moment_id, status, index) {
      try {
        const fields =
          this.session.role === "customer" ? "customer_id" : "student_id";
        const res = await likeMoment({
          student_id: this.curStudentInfo[fields],
          status,
          moment_id
        });

        // 如果后端有返回可用状态/数量，更新本地数据
        const updated = this.list[index];

        if (res.data && typeof res.data.like_status === "number") {
          updated.like_status = res.data.like_status;
        }

        if (res.data && typeof res.data.likes_count === "number") {
          updated.likes_count = res.data.likes_count;
        }
      } catch (error) {
        console.error("点赞接口失败", error);

        // 请求失败：可选回滚本地状态
        const item = this.list[index];
        if (item.like_status === 1) {
          item.like_status = 2;
          item.likes_count -= 1;
        } else {
          item.like_status = 1;
          item.likes_count += 1;
        }
      }
    },
    handleToMyHomePage(operate_id, operator_name) {
      uni.navigateTo({
        url: `/pages/student/subpages/niedaoCircle/myHomePage?operate_id=${operate_id}&operator_name=${operator_name}`
      });
    },
    onScroll(e) {
      if (this.scrollTimer) return;
      this.scrollTimer = setTimeout(() => {
        // console.log("onScroll", e);
        this.checkVideoInView();
        this.scrollTimer = null;
      }, 200);
    },
    checkVideoInView() {
      const query = uni.createSelectorQuery().in(this);
      query
        .selectAll(".video")
        .boundingClientRect((rects) => {
          if (!rects || rects.length === 0) return;

          const windowHeight = uni.getSystemInfoSync().windowHeight;
          const screenCenter = windowHeight / 2;

          let minDistance = Infinity;
          let targetId = null;
          rects.forEach((rect) => {
            const top = rect.top;
            const bottom = rect.bottom;
            const id = rect.dataset.id;

            if (bottom > 0 && top < windowHeight) {
              const videoCenter = top + rect.height / 2;
              const distance = Math.abs(videoCenter - screenCenter);

              if (distance < minDistance) {
                minDistance = distance;
                targetId = id;
              }
            }
          });

          if (targetId) {
            this.switchToVideo(targetId);
          } else {
            this.stopCurrentVideo();
          }
        })
        .exec();
    },
    switchToVideo(id) {
      if (this.playingId && this.playingId !== id) {
        const oldContext = uni.createVideoContext(
          "video-" + this.playingId,
          this
        );
        oldContext.pause();
        this.updatePlayIcon(this.playingId, true);
      }

      if (this.playingId !== id) {
        const newContext = uni.createVideoContext("video-" + id, this);
        newContext.play();
        this.updatePlayIcon(id, false);
        this.playingId = id;
      }
    },
    stopCurrentVideo() {
      if (this.playingId) {
        const context = uni.createVideoContext("video-" + this.playingId, this);
        context.pause();
        this.updatePlayIcon(this.playingId, true);
        this.playingId = null;
      }
    },
    onPlay(id) {
      this.updatePlayIcon(id, false);
    },
    onPause(id) {
      this.updatePlayIcon(id, true);
    },
    clickPlay(id) {
      const context = uni.createVideoContext("video-" + id, this);
      context.play();
      this.updatePlayIcon(id, false);
      this.playingId = id;
    },
    updatePlayIcon(id, isShow) {
      const idx = this.list.findIndex((item) => item.id === id);
      if (idx !== -1) {
        this.$set(this.list[idx], "isShowPlayIcon", isShow);
      }
    },
    fullScreenPlayVideo(item) {
      this.viewMomentFn(item.id);
      this.clickPlay(item.id);
      const videoContext = uni.createVideoContext(`video-${item.id}`, this);
      videoContext.requestFullScreen();
    },
    viewMomentFn(moment_id) {
      const fields =
        this.session.role === "customer" ? "customer_id" : "student_id";
      viewMoment({
        student_id: this.curStudentInfo[fields],
        moment_id
      });
    },
    handleContentOpen(item) {
      this.viewMomentFn(item.id);
    },
    getDisplaySize(imgW, imgH) {
      // 判断类型
      if (imgH / imgW >= 3) {
        // 超长竖图
        return {
          width: 150,
          height: 454
        };
      } else if (imgW / imgH >= 3) {
        // 超长横图
        return {
          width: 454,
          height: 150
        };
      } else if (imgH > imgW) {
        // 普通竖图
        return {
          width: 256,
          height: 341
        };
      } else {
        // 普通横图
        return {
          width: 341,
          height: 256
        };
      }
    },
    getVideoShowShape(item) {
      const { width, height } = item.image_url[0];
      return {
        width: width > height ? "341rpx" : "256rpx",
        height: width > height ? "256rpx" : "341rpx"
      };
    },
    getImgShowShape(image_url) {
      const { width, height } = image_url[0];
      const imgInfo = this.getDisplaySize(width, height);

      return {
        width: imgInfo.width + "rpx",
        height: imgInfo.height + "rpx"
      };
    },
    handleImageClick(current, urls) {
      uni.previewImage({
        current, // 当前点击的图片索引
        urls: urls.image_url.map((i) => i.url) // 所有图片的 URL 列表
      });
    },
    fullscreenchange(e) {
      if (!e.detail.fullScreen) {
        const videoContext = uni.createVideoContext(e.currentTarget.id, this);
        videoContext.play();
        this.videoMuted = false;
      } else {
        // 解除静音
        this.videoMuted = e.currentTarget.id;
      }
    },
    handleFullScreenMask(index) {
      const videoContext = uni.createVideoContext(`video-${index}`, this);
      // 关闭全屏
      videoContext.exitFullScreen();
    },
    handleOnLoad() {
      uni.getSystemInfo({
        success: (res) => {
          console.log(res, "res");
          this.safeAreaInsets = res.safeAreaInsets;
          this.listHeight = `calc(100vh - 74px - 78px - 50px - ${this.safeAreaInsets.bottom}px)`;
        }
      });
    },
    handlePullDownRefresh() {
      this.curStudentInfo = uni.getStorageSync("curStudentInfo");
      this.session = uni.getStorageSync("session");
    },
    handleBack() {
      uni.switchTab({
        url: "/pages/student/home/<USER>"
      });
    },
    handleShareAppMessage(res) {
      console.log(res, "res");
      if (res.from === "button") {
        // 来自页面内分享按钮
        const item = res.target.dataset.item;
        let imageUrl = "";
        if (item.image_type === 1) {
          if (item.image_url?.length) {
            imageUrl = item.image_url[0].url;
          } else {
            imageUrl =
              "https://tg-prod.oss-cn-beijing.aliyuncs.com/f860ad82-95fd-49c4-a8de-c629f069411b.png";
          }
        } else if (item.image_type === 2) {
          if (item.cover_url) {
            imageUrl = item.cover_url;
          } else {
            imageUrl =
              item.image_url[0].url +
              "?x-oss-process=video/snapshot,t_1000,f_jpg,w_0,h_0,m_fast";
          }
        }

        return {
          title: item.content || item.operator_name + "的聂道圈详情",
          imageUrl,
          path: `/pages/student/subpages/niedaoCircle/detail?id=${item.id}&source=share`
        };
      } else if (res.from === "menu") {
        return {
          title: "聂卫平围棋",
          imageUrl:
            "https://tg-prod.oss-cn-beijing.aliyuncs.com/f860ad82-95fd-49c4-a8de-c629f069411b.png",
          path: "/pages/student/home/<USER>"
        };
      }

      this.$refs.uToast.show({
        type: "default",
        duration: "2000",
        message: "请选择聂道圈进行分享！"
      });
      return false;
    }
  },
  // 页面处理函数--监听用户下拉动作
  onPullDownRefresh() {
    if (this.curStudentInfo === "") {
      this.list = [];
      this.showBindStudent = true;
      return false;
    }
    this.refresh();
  },
  mounted() {
    uni.getSystemInfo({
      success: (res) => {
        console.log(res, "res22");
        this.safeAreaInsets = res.safeAreaInsets;
        this.listHeight = `calc(100vh - 74px - 78px - 50px - ${this.safeAreaInsets.bottom}px)`;
      }
    });
    // this.tabbarKey++;
    this.curStudentInfo = uni.getStorageSync("curStudentInfo");
    this.session = uni.getStorageSync("session");
    // this.$refs.tabbar.setMenuList();
    if (this.session.role === "default") {
      this.list = [];
      this.showBindStudent = true;
      return false;
    }
    const fields =
      this.session.role === "customer" ? "customer_id" : "student_id";
    this.listParams.department_id = this.curStudentInfo.department_id;
    this.listParams.student_id = this.curStudentInfo[fields];
    this.getMyCircle("onShow");
  },
  onLoad() {
    uni.getSystemInfo({
      success: (res) => {
        console.log(res, "res22");
        this.safeAreaInsets = res.safeAreaInsets;
        this.listHeight = `calc(100vh - 74px - 78px - 50px - ${this.safeAreaInsets.bottom}px)`;
      }
    });
  },
  onReady() {},
  onHide() {
    // this.disconnectObservers();
  }
};
</script>

<style lang="scss" scoped>
::v-deep .u-read-more__toggle {
  justify-content: start !important;

  .u-icon__icon,
  .u-text__value {
    font-weight: 500 !important;
  }
}

.niedao-circle-page {
  width: 100%;
  height: 100%;
  background: url(https://tg-prod.oss-cn-beijing.aliyuncs.com/88cd9858-d973-46f7-96c5-a46e7f42fb76.png)
    no-repeat center center;
  background-size: 100% 12%;
  background-position: top;
  // overflow: hidden;
}

.niedao-circle-title {
  color: #333;
  font-size: 34rpx;
  font-weight: 500;
  text-align: center;
  padding-top: 110rpx;
  position: relative;
  .left-icon {
    position: absolute;
    left: 32rpx;
    bottom: 0;
    width: 40rpx;
    height: 40rpx;
    img {
      width: 100%;
      height: 100%;
    }
  }
}

.niedao-circle-content {
  height: 100%;
  overflow: hidden;

  .niedao-circle-school {
    display: flex;
    align-items: center;
    padding: 22rpx 32rpx 10rpx 32rpx;
    .pisition {
      width: 26rpx;
      height: 30rpx;
      margin-right: 12rpx;

      img {
        width: 100%;
        height: 100%;
      }
    }

    .school-name {
      font-size: 30rpx;
      font-weight: 500;
      color: #333;
    }
  }

  .ulist-item {
    .niedao-circle-content-item {
      &:last-child {
        border-bottom: none;
      }
    }
  }

  .niedao-circle-content-item {
    border-bottom: 1rpx solid #eee;
    padding: 34rpx 32rpx 20rpx 32rpx;
    // margin-bottom: 34rpx;
    display: flex;

    .avatar {
      flex-shrink: 0;
      width: 85rpx;
      height: 85rpx;
      border-radius: 50%;

      img {
        width: 100%;
        height: 100%;
        border-radius: 50%;
      }

      background-color: #eee;
    }

    .content {
      margin-left: 20rpx;
      flex: 1;

      .teacher-name {
        font-size: 32rpx;
        color: #333;
        font-weight: 500;
      }

      .value {
        margin-top: 10rpx;
        font-size: 28rpx;
        color: #666;
        margin-bottom: 18rpx;
      }

      .media {
        display: flex;
        flex-wrap: wrap;

        .media-item {
          margin-right: 12rpx;
          // margin-bottom: 12rpx;
          .single-img {
            width: 256rpx;
            max-height: 341rpx;
            border-radius: 16rpx;
          }

          .multiple-img {
            width: 150rpx;
            height: 150rpx;
            border-radius: 16rpx;
          }
        }

        .video-wrap {
          width: 256rpx;
          height: 341rpx;
          position: relative;

          .play-btn {
            width: 50rpx;
            height: 50rpx;
            position: absolute;
            top: 50%;
            left: 50%;
            z-index: 1;
            transform: translate(-50%, -50%);
          }

          .video {
            border-radius: 16rpx;
            width: 100%;
            height: 100%;
          }
        }
      }
      .four-grid {
        display: grid;
        grid-template-columns: repeat(2, 150rpx);
        width: 100%;

        .media-item {
          margin-right: 0;

          .u-image {
            width: 100% !important;
            height: 100% !important;
          }
        }
      }
      .row-bar {
        padding-top: 24rpx;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .is-top,
        .post-time {
          font-size: 26rpx;
          font-weight: 400;
          color: #999;
        }

        .controls {
          display: flex;
          align-items: center;
          // width: 125px;
          justify-content: space-between;
          .text {
            font-size: 26rpx;
            font-weight: 400;
            color: #999;
            padding-left: 12rpx;
          }

          .like {
            margin-right: 50rpx;
            min-width: 50rpx;
            height: 50rpx;

            .img {
              width: 30rpx;
              height: 30rpx;
              flex-shrink: 0;

              img {
                width: 100%;
                height: 100%;
              }
            }
          }

          .share,
          .like {
            display: flex;
            align-items: center;

            .img {
              width: 30rpx;
              height: 30rpx;
              flex-shrink: 0;

              img {
                width: 100%;
                height: 100%;
              }
            }
          }
        }
      }
    }
  }
}

.full-screen-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 150rpx;
}
// 最后一个is-bottom
.is-bottom {
  border: none !important;
}
</style>
