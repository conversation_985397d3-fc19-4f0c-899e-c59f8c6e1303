<template>
  <div class="myHomePage">
    <u-navbar
      :title="operator_name"
      :placeholder="true"
      :autoBack="true"
      :titleStyle="{
        fontWeight: '500'
      }"
    >
    </u-navbar>
    <u-sticky offset-top="44" customNavHeight="44">
      <div class="myHomePage-search-date" @tap="handleSearchDateClick">
        {{ searchDate.year }}年<span class="sanjiao"
          ><image
            src="https://tg-prod.oss-cn-beijing.aliyuncs.com/3f1cc03b-6dba-4213-8879-04679686bf86.webp"
          ></image
        ></span>
      </div>
    </u-sticky>
    <div class="myHomePage-content">
      <u-list
        v-if="formattedData.length > 0"
        @scrolltolower="handleScrollToLower"
        @scrolltoupper="handleScrollToUpper"
        @scroll="handleScroll"
        :lowerThreshold="100"
        :upperThreshold="5"
        preLoadScreen="2"
        :scrollable="true"
        ref="scrollWrap"
        :scrollTop="scrollTop"
      >
        <!-- 顶部加载更多，只在筛选时且还有数据时显示 -->
        <u-loadmore
          v-if="
            listParams.date &&
            topLoadStatus !== 'nomore' &&
            topLoadStatus !== 'loadmore'
          "
          :status="topLoadStatus"
          :load-text="loadText"
          loadmoreText="继续上滑加载更早内容"
          margin-top="20"
          margin-bottom="20"
          :showLoadingMore="true"
          lineColor="#DADADA"
          line
          color="#999999"
          :fontSize="'26rpx'"
        />
        <u-list-item
          v-for="(item, index) in formattedData"
          :key="index"
          :anchor="item.id || item.publish_time"
        >
          <template v-if="today != item.publish_time">
            <div
              v-if="item.type === 'year'"
              class="year-line"
              :data-year="item.publish_time"
              :id="item.id || item.publish_time"
            >
              {{ item.publish_time }}
            </div>
            <template v-else>
              <div
                :class="'myHomePage-content-item ' + item.class"
                :data-year="item.publish_time.year"
                :id="item.id || item.publish_time"
              >
                <div class="time">
                  <template v-if="item.publish_time">
                    <span v-if="item.timeType === 'precision'" class="date">
                      <span class="day">{{ item.publish_time.day }}</span>
                      <span class="month">{{ item.publish_time.month }}月</span>
                    </span>
                    <span v-else class="lately">{{ item.publish_time }}</span>
                  </template>
                </div>
                <div
                  class="content"
                  v-if="item.image_url && item.image_url.length"
                >
                  <div
                    v-if="item.image_type === 1"
                    :class="'album ' + layoutClass(item.image_url.length)"
                    @tap="handleImageClick(0, item)"
                  >
                    <image
                      mode="aspectFill"
                      v-for="(image, index) in item.image_url"
                      :src="image.url"
                      :key="index"
                    />
                  </div>

                  <div
                    class="video-wrap"
                    @tap="fullScreenPlayVideo(index)"
                    v-else-if="item.image_type === 2"
                  >
                    <img :src="playIcon" alt="" class="play-btn" />
                    <video
                      :id="'video-' + index"
                      direction="0"
                      :muted="videoMuted !== 'video-' + index"
                      :autoplay="false"
                      :showCenterPlayBtn="false"
                      :loop="true"
                      @fullscreenchange="fullscreenchange"
                      object-fit="contain"
                      class="video"
                      :src="item.image_url[0].url"
                      :poster="item.cover_url"
                      :controls="videoMuted === 'video-' + index"
                      :custom-cache="false"
                    >
                      <view
                        class="full-screen-mask"
                        @tap="handleFullScreenMask(index)"
                      ></view>
                    </video>
                  </div>
                </div>
                <div
                  @tap="handleContentClick(item)"
                  :class="[
                    'value',
                    !item.image_url || item.image_url.length === 0 ? 'text' : ''
                  ]"
                >
                  <div class="content-txt">{{ item.content }}</div>
                </div>
              </div>
            </template>
          </template>
        </u-list-item>
        <u-loadmore
          :status="loadStatus"
          :load-text="loadText"
          margin-top="20"
          margin-bottom="20"
          loadmoreText="点击加载更多"
          :showLoadingMore="true"
          lineColor="#DADADA"
          line
          color="#999999"
          :fontSize="'26rpx'"
          @loadmore="loadMore"
        />
      </u-list>
    </div>
    <loading-animation
      :width="300"
      :characterSize="260"
      :textSize="32"
      :style="{
        height: 'calc(100vh - 88px - 32px - 40rpx)',
        marginTop: '-45rpx',
        display: 'grid',
        placeItems: 'center'
      }"
      v-if="isLoading && formattedData.length === 0"
    />
    <Empty
      :style="{
        height: 'calc(100vh - 88px - 32px - 40rpx)',
        marginTop: '-45rpx',
        display: 'grid',
        placeItems: 'center'
      }"
      :text="loadText.nomore"
      v-if="formattedData.length === 0 && !isLoading"
    />
    <view @touchmove.stop.prevent="">
      <u-picker
        :show="isShowDatePicker"
        ref="uPicker"
        :columns="columns"
        title="请选择发表的日期"
        @confirm="handleDateConfirm"
        @cancel="handleDateCancel"
        @change="changeHandler"
        confirmColor="#333"
      ></u-picker>
    </view>
  </div>
</template>

<script>
import LoadingAnimation from "@/components/common/LoadingAnimation.vue";
import { momentsList, momentsDate } from "@/services/student/niedaoCircle";
import Empty from "@/components/empty/index.vue";
export default {
  name: "studentNiedaoCircleMyHomePage",
  components: {
    Empty,
    LoadingAnimation
  },
  data() {
    return {
      fields: "",
      session: {},
      isShowDatePicker: false,
      columns: [],
      monthColumns: [],
      playIcon:
        "https://tg-prod.oss-cn-beijing.aliyuncs.com/7c8ff79b-619f-4842-b640-87504abe4ed1.png",
      operator_name: "",
      formattedData: [],
      allPosts: [],
      value1: "",
      searchDate: {
        year: "",
        month: ""
      },
      curStudentInfo: {},
      loadStatus: "loadmore",
      topLoadStatus: "loadmore",
      loadText: {
        loadmore: "上拉加载更多",
        loading: "正在加载...",
        nomore: "没有更多了"
      },
      listParams: {
        page: 1,
        page_size: 10,
        employee_id: "",
        department_id: "",
        student_id: "",
        view_type: 2
      },
      pageInfo: {
        upPage: 1,
        downPage: 2
      },
      today: "",
      videoMuted: "",
      showSticky: false,
      scrollTop: 0,
      isRefreshing: false,
      lastVisibleItemId: "",
      isLoading: true
    };
  },
  methods: {
    // 获取聂道圈日期
    async getMomentsDate() {
      const res = await momentsDate({
        student_id: this.curStudentInfo[this.fields],
        employee_id: this.listParams.employee_id,
        department_id: this.listParams.department_id
      });
      if (res.data?.length) {
        this.searchDate = {
          year: res.data[0].year,
          month: res.data[0].month[0]
        };
        this.columns = [res.data.map((i) => i.year), res.data[0].month];
        this.monthColumns = res.data.map((i) => i.month);
      }
      console.log(this.columns, this.monthColumns, res, "res");
    },
    changeHandler(e) {
      const {
        columnIndex,
        index,
        // 微信小程序无法将picker实例传出来，只能通过ref操作
        picker = this.$refs.uPicker
      } = e;
      // 当第一列值发生变化时，变化第二列(后一列)对应的选项
      if (columnIndex === 0) {
        // picker为选择器this实例，变化第二列对应的选项
        picker.setColumnValues(1, this.monthColumns[index]);
      }
    },
    layoutClass(count) {
      const classes = [
        "one",
        "two",
        "three",
        "four",
        "five",
        "six",
        "seven",
        "eight",
        "nine"
      ];
      if (count >= 1 && count <= 9) {
        return classes[count - 1];
      } else {
        return "default-layout";
      }
    },
    // Optimized scroll handling with throttle
    handleScrol(e) {
      uni.$u.throttle(function (e) {
        // this.scrollTop = e;
      }, 500);
    },
    handleScrollToLower() {
      console.log("下滑加载");
      uni.$u.throttle(() => {
        this.listParams.operate_type = "down";
        this.loadMore("down");
      }, 500);
    },
    // Enhanced loadMore function
    async loadMore(type) {
      // let currentLoadStatus;
      // if (type) {
      const currentLoadStatus =
        this.listParams.operate_type === "up" ? "topLoadStatus" : "loadStatus";
      console.log(this[currentLoadStatus], "this[currentLoadStatus]");
      if (this[currentLoadStatus] === "nomore") return;
      this[currentLoadStatus] = "loading";
      // } else {
      //   currentLoadStatus = "loadStatus";
      //   this.listParams.operate_type = "down";
      //   if (this[currentLoadStatus] !== "loadmore") return;
      //   this[currentLoadStatus] = "loading";
      // }

      try {
        this.isLoading = true;

        // 只在筛选和特定滚动方向时设置页码
        if (type === "clear") {
          this.allPosts = [];
          this.formattedData = [];
          this.listParams.page = 1;
          this.pageInfo.upPage = 1;
          this.pageInfo.downPage = 2;
          this.topLoadStatus = "loadmore";
          this.loadStatus = "loadmore";
          this.listParams.operate_type = "";
        } else if (this.listParams.operate_type) {
          // 只有当明确设置了operate_type时才处理滚动方向
          if (this.listParams.operate_type === "up") {
            this.listParams.page = this.pageInfo.upPage;
            this.pageInfo.upPage += 1;
          } else if (this.listParams.operate_type === "down") {
            this.listParams.page = this.pageInfo.downPage;
            this.pageInfo.downPage += 1;
          }
        } else {
          // 默认加载更多的情况（向下加载）
          this.listParams.page = this.pageInfo.downPage;
          this.pageInfo.downPage += 1;
        }

        // 如果不是筛选后的数据，则删除operate_type
        if (type === "clear" || !this.listParams.date) {
          delete this.listParams.operate_type;
        }
        const res = await momentsList(this.listParams);
        const newPosts = res.data.results || [];
        this.total = res.data.count || 0;
        console.log(this.listParams, "this.listParams", res.data.count);
        console.log(newPosts, "newPosts");
        console.log(this.allPosts, "this.allPosts");
        // 合并数据
        this.allPosts = [...this.allPosts, ...newPosts];
        console.log(this.allPosts, "this.allPosts");
        // 如果是向上加载，先设置滚动位置
        // if (this.listParams.operate_type === "up" && this.scrollTop) {
        // const oldPosition = null;
        // 使用 nextTick 确保在数据更新后再设置滚动位置
        //   this.$nextTick(async () => {
        //     if (
        //       this.listParams.operate_type === "up" &&
        //       this.lastVisibleItemId
        //     ) {
        //       const query = uni.createSelectorQuery().in(this);
        //       await new Promise((resolve) => {
        //         query
        //           .select("#" + this.lastVisibleItemId)
        //           .boundingClientRect((data) => {
        //             console.log(data, "data");
        //             if (data) {
        //               oldPosition = {
        //                 bottom: data.bottom,
        //                 height: data.height,
        //                 top: data.top
        //               };
        //             }
        //             resolve();
        //           })
        //           .exec();
        //       });
        //     }
        //     // requestAnimationFrame(() => {
        //     //   // setTimeout(() => {
        //     //     this.scrollTop = oldPosition.top - 100 - oldPosition.height;
        //     //   // }, 10);
        //     // });
        //   });
        // } else {
        //   if (this.listParams.date) {
        //     this.scrollTop = 20;
        //   }
        // }
        this.$nextTick(() => {
          if (
            this.listParams.date &&
            this.listParams.operate_type === "up" &&
            this.topLoadStatus !== "nomore"
          ) {
            this.scrollTop = 0;
            setTimeout(() => {
              this.scrollTop = 20;
            }, 10);
            this.lastVisibleItemId = res.data.results[0].id;
          }
        });
        // 格式化数据
        this.formattedData = this.formatData(this.allPosts);
        console.log(this.formattedData, "this.formattedData");
        this.isLoading = false;
        // 检查是否全部加载完成
        if (res.data.results.length === 0) {
          this[currentLoadStatus] = "nomore";
        } else {
          this[currentLoadStatus] = "loadmore";
        }
        if (!this.listParams.date && this.total === this.allPosts.length) {
          this[currentLoadStatus] = "nomore";
        }
      } catch (error) {
        console.error("加载数据失败:", error);
      } finally {
        console.log(this.loadStatus, "this.loadStatus");
        this.isRefreshing = false;
      }
    },
    // async loadMore() {
    //   if (this.loadStatus === "loading") return;

    //   // Get current content height before loading
    //   const oldHeight = await this.getContentHeight();

    //   this.loadStatus = "loading";
    //   try {
    //     const res = await momentsList({
    //       ...this.listParams,
    //       page: this.pageInfo.downPage
    //     });

    //     if (res.data?.list?.length) {
    //       // Merge data efficiently
    //       const newPosts = res.data.list.filter(
    //         (post) => !this.allPosts.some((existing) => existing.id === post.id)
    //       );

    //       this.allPosts = [...this.allPosts, ...newPosts];
    //       this.formatData();
    //       this.pageInfo.downPage++;

    //       // Update loading status
    //       if (!this.listParams.date && this.total === this.allPosts.length) {
    //         this.loadStatus = "nomore";
    //       } else {
    //         this.loadStatus = "loadmore";
    //       }

    //       // Maintain scroll position
    //       const newHeight = await this.getContentHeight();
    //       const heightDiff = newHeight - oldHeight;
    //       if (heightDiff > 0) {
    //         uni.pageScrollTo({
    //           scrollTop: this.scrollTop + heightDiff,
    //           duration: 0
    //         });
    //       }
    //     } else {
    //       this.loadStatus = "nomore";
    //     }
    //   } catch (error) {
    //     console.error("Load more error:", error);
    //     this.loadStatus = "loadmore";
    //   }
    // },
    // Helper function to get content height
    getContentHeight() {
      return new Promise((resolve) => {
        const query = uni.createSelectorQuery().in(this);
        query
          .select(".myHomePage-content")
          .boundingClientRect((data) => {
            resolve(data?.height || 0);
          })
          .exec();
      });
    },
    // Enhanced scroll to upper handler
    async handleScrollToUpper() {
      console.log("上滑加载");
      // 找出第一个有效的id
      // const firstItem = this.formattedData.find((item) => item.id);
      // if (firstItem) {
      //   this.lastVisibleItemId =
      //     firstItem.id || firstItem.year || firstItem.publish_time;
      // }
      // console.log(firstItem, "this.lastVisibleItemId");
      if (this.listParams.date && this.topLoadStatus !== "nomore") {
        this.listParams.operate_type = "up";
        this.topLoadStatus = "loadmore";
        uni.$u.throttle(() => {
          this.loadMore("up");
        }, 500);
      }
    },
    // 添加日期格式化方法
    formatDateString(dateStr) {
      if (!dateStr) return "";
      // 将 "yyyy-MM-dd HH:mm:ss" 转换为 "yyyy-MM-ddTHH:mm:ss"
      return dateStr.replace(" ", "T");
    },
    isSameDay(date1, date2) {
      return (
        date1.getFullYear() === date2.getFullYear() &&
        date1.getMonth() === date2.getMonth() &&
        date1.getDate() === date2.getDate()
      );
    },
    formatData(data) {
      const today = new Date();
      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);

      const groupedData = [];
      if (this.isOneself) {
        groupedData.push({
          id: "",
          timeType: "semanticization",
          date: today,
          publish_time: "今天",
          type: "create",
          content: ""
        });
      }
      let lastYear = null;
      let lastDate = null;
      data
        .map((post) => {
          // 确保日期格式兼容 iOS
          const formattedDate = this.formatDateString(post.publish_time);
          const date = new Date(formattedDate);
          // 验证日期是否有效
          if (isNaN(date.getTime())) {
            console.error("Invalid date:", post.publish_time);
            return null;
          }
          return {
            ...post,
            date,
            year: date.getFullYear()
          };
        })
        .filter((item) => item !== null) // 过滤掉无效的日期
        .sort((a, b) => b.date - a.date) // 按日期倒序排序
        .forEach((post, index) => {
          // 添加年份分隔符
          if (lastYear !== post.year) {
            groupedData.push({
              type: "year",
              publish_time: post.year.toString()
            });
            lastYear = post.year;
          }

          // 处理日期显示逻辑
          const isToday = this.isSameDay(post.date, today);
          const isYesterday = this.isSameDay(post.date, yesterday);

          let publishTime = "";
          let timeType = "";
          if (isToday) {
            publishTime = this.isOneself ? "" : "今天";
            timeType = "semanticization";
          } else if (isYesterday) {
            publishTime = "昨天";
            timeType = "semanticization";
          } else if (!lastDate || !this.isSameDay(post.date, lastDate)) {
            publishTime = {
              day: post.date.getDate(),
              month: post.date.getMonth() + 1,
              year: post.date.getFullYear()
            };
            timeType = "precision";
          }
          groupedData.push({
            id: post.id,
            image_url: post.image_url,
            visibility_scope: post.visibility_scope,
            publish_status: post.publish_status,
            cover_url: post.cover_url,
            image_type: post.image_type,
            content: post.content,
            publish_time:
              lastDate && this.isSameDay(post.date, lastDate)
                ? ""
                : publishTime,
            timeType,
            type: "day",
            class:
              lastDate && this.isSameDay(post.date, lastDate) ? "" : "same-date"
          });

          lastDate = post.date;
        });
      return groupedData;
    },

    handleImageClick(current, urls) {
      uni.previewImage({
        current, // 当前点击的图片索引
        urls: urls.image_url.map((item) => item.url) // 所有图片的 URL 列表
      });
    },
    handleContentClick(item) {
      uni.navigateTo({
        url: `/pages/student/subpages/niedaoCircle/detail?id=${item.id}&source=myHomePage`
      });
    },
    fullScreenPlayVideo(index) {
      const videoContext = uni.createVideoContext(`video-${index}`, this);
      videoContext.requestFullScreen();
    },
    handleSearchDateClick() {
      this.isShowDatePicker = true;
    },
    async handleDateConfirm(data) {
      this.loadStatus = "loadmore";
      this.listParams.date = data.value.join("-");
      // this.directionPage[this.listParams.operate_type || "down"] = 1;
      this.searchDate = {
        year: data.value[0],
        month: data.value[1]
      };
      this.loadStatus = "loadmore";
      this.topLoadStatus = "loadmore";
      this.scrollTop = 40;
      this.loadMore("clear");
      this.isShowDatePicker = false;
    },
    handleDateCancel() {
      this.isShowDatePicker = false;
    },
    fullscreenchange(e) {
      const videoContext = uni.createVideoContext(e.currentTarget.id, this);
      if (!e.detail.fullscreen) {
        this.videoMuted = false;
        // 暂停播放视频
        videoContext.pause();
      } else {
        videoContext.play();
        // 解除静音
        this.videoMuted = e.currentTarget.id;
      }
    },
    handleFullScreenMask(index) {
      const videoContext = uni.createVideoContext(`video-${index}`, this);
      // 关闭全屏
      videoContext.exitFullScreen();
    },
    handleScroll(e) {
      // this.showSticky = e > 100;
      // 获取 myHomePage-search-date 元素的位置
      const searchDateQuery = uni.createSelectorQuery().in(this);
      searchDateQuery
        .select(".myHomePage-search-date")
        .boundingClientRect((searchDateRect) => {
          if (searchDateRect) {
            // 获取所有 myHomePage-content-item 元素
            const query = uni.createSelectorQuery().in(this);
            query
              .selectAll(".myHomePage-content-item")
              .boundingClientRect((data) => {
                if (data && data.length) {
                  // 找到距离最近的 myHomePage-content-item
                  let closestItem = null;
                  let minDistance = Infinity;

                  data.forEach((item) => {
                    const distance = Math.abs(item.top - searchDateRect.bottom);
                    if (distance < minDistance) {
                      minDistance = distance;
                      closestItem = item;
                    }
                  });

                  // 当找到最近的元素时
                  if (closestItem) {
                    // console.log('距离最近的列表项:', closestItem);
                    // 这里可以添加您想要触发的逻辑
                    if (closestItem.dataset && closestItem.dataset.year) {
                      this.$nextTick(() => {
                        this.searchDate.year = closestItem.dataset.year;
                      });
                    }
                  }
                }
              })
              .exec();
          }
        })
        .exec();
    }
  },
  watch: {},

  // 页面周期函数--监听页面加载
  onLoad(options) {
    this.session = uni.getStorageSync("session");
    this.fields =
      this.session.role === "customer" ? "customer_id" : "student_id";
    this.operator_name = options.operator_name;
    this.curStudentInfo = uni.getStorageSync("curStudentInfo");
    this.listParams.department_id = this.curStudentInfo.department_id;
    this.listParams.student_id = this.curStudentInfo[this.fields];
    this.listParams.employee_id = options.operate_id;
    this.getMomentsDate();
    this.today = new Date().getFullYear();
  },
  mounted() {
    this.$forceUpdate();
    setTimeout(() => {
      this.loadMore("clear");
    }, 300);
  },
  // 页面周期函数--监听页面初次渲染完成
  onReady() {},
  // 页面周期函数--监听页面显示(not-nvue)
  onShow() {},
  // 页面周期函数--监听页面隐藏
  onHide() {},
  // 页面周期函数--监听页面卸载
  onUnload() {},
  // 页面处理函数--监听用户下拉动作
  onPullDownRefresh() {
    this.listParams.page = 1;
    this.loadMore("clear");
    uni.stopPullDownRefresh();
  }
  // 页面处理函数--监听用户上拉触底
  // onReachBottom() {},
  // 页面处理函数--监听页面滚动(not-nvue)
  // onPageScroll(event) {},
  // 页面处理函数--用户点击右上角分享
  // onShareAppMessage(options) {},
};
</script>

<style lang="scss" scoped>
.myHomePage {
  .myHomePage-search-date {
    height: 64rpx;
    width: 100%;
    display: flex;
    color: #666;
    font-size: 26rpx;
    align-items: center;
    background-color: #f5f5f5;
    padding: 0 32rpx;
    align-items: center;
    .sanjiao {
      margin-left: 8rpx;
      // width: 0;
      // height: 0;
      // border-left: 8rpx solid transparent;
      // border-right: 8rpx solid transparent;
      // border-top: 10rpx solid #666;
      // border-radius: 6rpx;
      width: 18rpx;
      height: 13rpx;
      image {
        width: 100%;
        height: 100%;
      }
    }
  }
  .myHomePage-content {
    // padding-top: 40rpx;

    .year-line {
      font-size: 34rpx;
      color: #333;
      font-weight: 500;
      padding: 0 32rpx;
      margin: 20px 0 -24rpx 0;
    }

    .myHomePage-content-item {
      display: flex;
      padding: 0 32rpx;
      margin-bottom: 16rpx;
      position: relative;

      &.same-date {
        margin-top: 40rpx !important;
      }

      .time {
        .lately {
          font-size: 36rpx;
          color: #333;
          font-weight: 500;
        }
        .date {
          .day {
            font-size: 38rpx;
            color: #333;
            font-weight: 500;
            margin-right: 5rpx;
          }
          .month {
            font-size: 24rpx;
            color: #666;
            font-weight: 500;
          }
        }
        width: 109rpx;
        flex-shrink: 0;
        margin-right: 16rpx;
      }
      .content {
        .video-wrap {
          width: 175rpx;
          height: 175rpx;
          position: relative;
          .play-btn {
            width: 50rpx;
            height: 50rpx;
            position: absolute;
            top: 50%;
            left: 50%;
            z-index: 1;
            transform: translate(-50%, -50%);
          }
          .video {
            border-radius: 16rpx;
            width: 100%;
            height: 100%;
          }
        }
      }
    }

    .value {
      &.text {
        width: 100%;
        margin-left: 0;
        // padding: 12rpx;
        background-color: #f5f5f5;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
        line-height: 1.5em;
        text-overflow: ellipsis;
        word-break: break-word; /* 优先在单词间断行 */
        line-break: anywhere;
        height: auto !important;
        border-radius: 4rpx;
        height: 132rpx;
        .content-txt {
          margin: 12rpx;
        }
      }
      margin-left: 16rpx;
      flex: 1;
      height: 100%;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 3;
      line-height: 1.5em;
      overflow: hidden;
      text-overflow: ellipsis;
      word-break: break-word; /* 优先在单词间断行 */
      line-break: anywhere;
    }
  }
}
.album {
  margin: 0 auto;
  display: grid;
  grid-template-columns: repeat(6, 15px);
  grid-template-rows: repeat(6, 14px);
  grid-column-gap: 2px;
  grid-row-gap: 2px;
  background-color: #f9f9f9;
  overflow: hidden;
  border-radius: 16rpx;
}
.album image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}
.album.one image {
  grid-column: span 6;
  grid-row: span 6;
}
.album.two image {
  grid-column: span 3;
  grid-row: span 6;
}
.album.three image {
  grid-column: span 3;
  grid-row: span 3;
}
.album.three image:nth-child(1) {
  grid-column: span 3;
  grid-row: span 6;
}
.album.four image {
  grid-column: span 3;
  grid-row: span 3;
}
.album.five image {
  grid-column: span 2;
  grid-row: span 2;
}
.album.five image:nth-child(1) {
  grid-column: span 4;
  grid-row: span 4;
}
.album.five image:nth-child(2) {
  grid-column: span 2;
  grid-row: span 4;
}
.album.six image {
  grid-column: span 2;
  grid-row: span 2;
}
.album.six image:nth-child(1) {
  grid-column: span 4;
  grid-row: span 4;
}
.album.seven image {
  grid-column: span 2;
  grid-row: span 2;
}
.album.seven image:nth-child(1),
.album.seven image:nth-child(2) {
  grid-column: span 2;
  grid-row: span 4;
}
.album.eight image {
  grid-column: span 2;
  grid-row: span 2;
}
.album.eight image:nth-child(1) {
  grid-column: span 2;
  grid-row: span 4;
}
.album.nine image {
  grid-column: span 2;
  grid-row: span 2;
}
.full-screen-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 150rpx;
}
::v-deep .u-popup__content {
  border-radius: 24rpx;
}
::v-deep .u-list {
  height: calc(100vh - 88px - 32px - 40rpx) !important;
  overflow-anchor: auto;
  overflow: scroll;
  // Add transform to create a new stacking context
}
::v-deep .u-toolbar__title {
  font-weight: 500;
}
</style>
