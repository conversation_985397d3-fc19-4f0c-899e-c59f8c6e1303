<template>
  <view class="video-player-container">
    <video
      v-if="videoUrl"
      id="courseVideo"
      :src="videoUrl"
      autoplay
      controls
      show-fullscreen-btn
      show-center-play-btn
      enable-progress-gesture
      :title="courseTitle"
      vslide-gesture-in-fullscreen
      show-bottom-progress
      :picture-in-picture-mode="pictureInPictureMode"
      @enterpictureinpicture="handleEnterPictureInPicture"
      @leavepictureinpicture="handleLeavePictureInPicture"
      @error="handleVideoError"
      @fullscreenchange="handleFullscreenChange"
      @controlstoggle="handleControlsToggle"
    >
      <cover-view
        class="pip-cover"
        @click="handlePipCoverClick"
        v-if="isPipShow"
      >
        <cover-image
          src="https://tg-prod.oss-cn-beijing.aliyuncs.com/7d4744d7-d6cf-4e77-b355-a1839c795ebf.webp"
          mode="widthFix"
        ></cover-image>
      </cover-view>
    </video>
  </view>
</template>

<script>
export default {
  name: "VideoPlay",
  data() {
    return {
      videoUrl: "",
      courseTitle: "",
      courseId: "",
      isFullscreen: true,
      showSpeedControl: false,
      pictureInPictureMode: [], // 小窗模式配置
      isInPictureInPicture: false, // 小窗状态标记
      pipPosition: "right,20", // 小窗位置：右侧，距顶部20%
      videoContext: null,
      isPipShow: true
    };
  },
  onLoad(options) {
    this.courseId = options.id || "";
    this.courseTitle = options.title || "课程视频";
    this.videoUrl = options.videoUrl;
  },
  onReady() {
    // 创建video上下文
    // this.videoContext = uni.createVideoContext("courseVideo", this);
    // setTimeout(() => {
    //   this.videoContext.requestFullScreen();
    // }, 600);
    console.log("视频组件准备完成，支持小窗功能");
  },
  onHide() {
    console.log("页面隐藏");
  },
  onShow() {
    console.log("页面显示");
    this.pictureInPictureMode = [];
    // if (this.isInPictureInPicture) {
    //   setTimeout(() => {
    //     this.videoContext.requestFullScreen();
    //   }, 600);
    // }
    this.videoContext = uni.createVideoContext("courseVideo", this);
    this.isInPictureInPicture = false;
    // 页面重新显示时的处理
    if (this.videoContext) {
      // 延迟处理，确保页面完全显示
      setTimeout(() => {
        // 恢复全屏播放
        this.videoContext.requestFullScreen();
      }, 500);
    }
  },
  onUnload() {
    console.log("页面卸载");
    // 页面卸载时，小窗会根据配置自动处理
  },
  methods: {
    // 小窗相关事件处理
    handleEnterPictureInPicture(event) {
      console.log("进入小窗模式:", event);
    },
    handleLeavePictureInPicture(event) {
      console.log("退出小窗模式:", event);
    },
    handlePipCoverClick(event) {
      console.log("点击小窗封面:", event);
      this.pictureInPictureMode = ["pop"];
      this.videoContext.pause();
      setTimeout(() => {
        this.videoContext.play();
        this.videoContext.exitFullScreen();
      }, 500);
    },
    goBack() {
      // 返回时直接返回，小窗会根据picture-in-picture-mode自动处理
      uni.navigateBack();
    },

    handleFullscreenChange(e) {
      console.log("全屏状态变化:", e.detail);
      // 如果退出全屏且不在小窗模式，返回上一页
      if (!e.detail.fullScreen) {
        // this.videoContext.exitFullScreen();
        setTimeout(() => {
          uni.navigateBack();
          if (!this.isInPictureInPicture) {
            console.log("退出小窗模式");
            // this.videoContext.exitPictureInPicture();
          }
        }, 1500);
      }
    },
    handleVideoError(e) {
      console.error("视频播放出错:", e);
      uni.showToast({
        title: "视频加载失败，请稍后重试",
        icon: "none",
        duration: 2000
      });
    },
    handleControlsToggle(e) {
      console.log("控制按钮状态变化:", e.detail);
      this.isPipShow = e.detail.show;
    }
  }
};
</script>

<style lang="scss" scoped>
.video-player-container {
  position: relative;
  width: 100%;
  height: 100vh;
  background-color: #000;
}

// .video-player {
//   width: 100%;
//   height: 100vh;
// }

// /* 新增：永久显示的顶部导航栏样式 */
// .permanent-top-nav {
//   position: absolute;
//   top: 0;
//   left: 0;
//   width: 100%;
//   z-index: 30;
//   /* 确保在最上层 */
//   display: flex;
//   align-items: center;
//   padding: 0rpx 20rpx 20rpx 30rpx;
//   background: linear-gradient(to bottom, rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0));
//   opacity: 1;
//   transition: opacity 0.3s;
//   .back-button {
//     width: 60rpx;
//     height: 50rpx;
//     display: flex;
//     justify-content: center;
//     align-items: center;
//     margin-right: 20rpx;
//     image {
//       width: 40rpx;
//       height: 40rpx;
//     }
//   }

//   .video-title {
//     margin-left: -20rpx;
//     flex: 1;
//     color: #fff;
//     font-size: 20rpx;
//     font-weight: 500;
//     /* 添加文本阴影，使白色文字在任何背景上都清晰可见 */
//     text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
//   }

//   /* 添加倍速显示样式 */
//   .speed-indicator {
//     color: #fff;
//     font-size: 24rpx;
//     font-weight: bold;
//     background-color: rgba(0, 0, 0, 0.5);
//     padding: 8rpx 16rpx;
//     border-radius: 30rpx;
//     margin-right: 20rpx;
//   }
// }

// .video-controls {
//   position: absolute;
//   top: 0;
//   left: 0;
//   width: 100%;
//   height: 100%;
//   background-color: rgba(0, 0, 0, 0.3);
//   z-index: 10;
//   display: flex;
//   flex-direction: column;
//   justify-content: space-between;
// }

// /* 保留原来的top-controls样式但在这里不再使用 */
// .top-controls {
//   display: flex;
//   align-items: center;
//   padding: 40rpx;

//   .back-button {
//     width: 60rpx;
//     height: 60rpx;
//     display: flex;
//     justify-content: center;
//     align-items: center;

//     image {
//       width: 40rpx;
//       height: 40rpx;
//     }
//   }

//   .video-title {
//     margin-left: 30rpx;
//     color: #fff;
//     font-size: 28rpx;
//     font-weight: 500;
//   }
// }

// .loading-container {
//   position: absolute;
//   top: 0;
//   left: 0;
//   width: 100%;
//   height: 100%;
//   display: flex;
//   justify-content: center;
//   align-items: center;
//   background-color: rgba(0, 0, 0, 0.5);
//   z-index: 20;
// }

// .video-ended {
//   position: absolute;
//   top: 0;
//   left: 0;
//   width: 100%;
//   height: 100%;
//   background-color: rgba(0, 0, 0, 0.8);
//   z-index: 30;
//   display: flex;
//   justify-content: center;
//   align-items: center;

//   .ended-content {
//     text-align: center;

//     .ended-title {
//       color: #fff;
//       font-size: 36rpx;
//       font-weight: 500;
//       margin-bottom: 60rpx;
//     }

//     .action-buttons {
//       display: flex;
//       justify-content: center;

//       .action-btn {
//         width: 240rpx;
//         height: 80rpx;
//         line-height: 80rpx;
//         border-radius: 40rpx;
//         margin: 0 20rpx;
//         font-size: 30rpx;

//         &.replay {
//           background: linear-gradient(15deg, #ffbf0d 18.1%, #ffcb3c 83.29%);
//           color: #fff;
//         }

//         &.back {
//           background: transparent;
//           color: #fff;
//           border: 2rpx solid #fff;
//         }
//       }
//     }
//   }
// }

// /* 底部控制条样式 */
// .bottom-controls {
//   padding: 20rpx;
//   display: flex;
//   justify-content: flex-end;
//   align-items: center;
//   margin-bottom: 30rpx;

//   .speed-button {
//     color: #fff;
//     font-size: 28rpx;
//     background-color: rgba(0, 0, 0, 0.5);
//     padding: 10rpx 20rpx;
//     border-radius: 30rpx;
//     margin-right: 20rpx;
//   }
// }

// /* 倍速选择器样式 */
// .speed-selector {
//   position: absolute;
//   bottom: 100rpx;
//   right: 30rpx;
//   background-color: rgba(0, 0, 0, 0.8);
//   border-radius: 10rpx;
//   padding: 10rpx 0;
//   z-index: 40;

//   .speed-list {
//     display: flex;
//     flex-direction: column;

//     .speed-item {
//       color: #fff;
//       font-size: 28rpx;
//       padding: 15rpx 30rpx;
//       text-align: center;

//       &.active {
//         color: #ffb800;
//         font-weight: bold;
//       }

//       &:active {
//         background-color: rgba(255, 255, 255, 0.1);
//       }
//     }
//   }
// }

// /* 新增：倍速滑动控制器样式 */
// .speed-control-container {
//   position: absolute;
//   bottom: 120rpx;
//   left: 0;
//   width: 100%;
//   display: flex;
//   justify-content: center;
//   z-index: 40;
//   padding: 0 30rpx;
//   box-sizing: border-box;

//   .speed-control-panel {
//     background-color: rgba(0, 0, 0, 0.8);
//     border-radius: 20rpx;
//     padding: 30rpx;
//     width: 100%;
//     position: relative;

//     .speed-slider-container {
//       width: 100%;

//       .speed-value {
//         color: #ffffff;
//         font-size: 32rpx;
//         font-weight: bold;
//         text-align: center;
//         margin-bottom: 20rpx;
//       }

//       .speed-slider {
//         margin: 30rpx 0;
//       }

//       .speed-marks {
//         display: flex;
//         justify-content: space-between;
//         padding: 0 10rpx;

//         .speed-mark {
//           color: #aaaaaa;
//           font-size: 24rpx;

//           &.active {
//             color: #ffb800;
//             font-weight: bold;
//           }
//         }
//       }
//     }

//     .speed-control-close {
//       position: absolute;
//       top: 20rpx;
//       right: 20rpx;
//       width: 40rpx;
//       height: 40rpx;
//       display: flex;
//       justify-content: center;
//       align-items: center;
//     }
//   }
// }
// .show {
//   opacity: 1 !important;
// }

// .hide {
//   opacity: 0 !important;
//   pointer-events: none;
// }
// .pip-controls {
//   position: fixed;
//   right: 30rpx;
//   bottom: 100rpx;
//   display: flex;
//   flex-direction: column;
//   align-items: center;
//   justify-content: center;
//   color: #fff;
//   background: rgba(0, 0, 0, 0.6);
//   border-radius: 20rpx;
//   padding: 16rpx 20rpx;
//   min-height: 80rpx;
//   min-width: 120rpx;
//   z-index: 40;
//   backdrop-filter: blur(10rpx);
//   border: 1rpx solid rgba(255, 255, 255, 0.2);
//   transition: all 0.3s ease;

//   &:active {
//     transform: scale(0.95);
//     background: rgba(0, 0, 0, 0.8);
//     box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.3);
//   }

//   &.pip-active {
//     background: rgba(255, 184, 0, 0.8);
//     border-color: rgba(255, 184, 0, 0.5);

//     .pip-text {
//       color: #fff;
//       font-weight: 600;
//     }
//   }

//   .pip-icon {
//     font-size: 32rpx;
//     margin-bottom: 4rpx;
//   }

//   .pip-text {
//     font-size: 22rpx;
//     font-weight: 500;
//     text-align: center;
//     line-height: 1.2;
//     transition: color 0.3s ease;
//   }
// }

// /* 小窗状态提示样式 */
// .pip-status {
//   position: fixed;
//   top: 20rpx;
//   left: 20rpx;
//   right: 20rpx;
//   background: rgba(0, 0, 0, 0.8);
//   color: white;
//   padding: 20rpx;
//   border-radius: 10rpx;
//   display: flex;
//   justify-content: space-between;
//   align-items: center;
//   z-index: 1000;
// }

// .pip-status-text {
//   font-size: 28rpx;
//   color: #fff;
// }

// .pip-exit-btn {
//   background: #ff4757;
//   color: white;
//   border: none;
//   padding: 10rpx 20rpx;
//   border-radius: 6rpx;
//   font-size: 24rpx;
// }

// /* 小窗功能说明样式 */
// .pip-info {
//   position: absolute;
//   bottom: 50rpx;
//   left: 30rpx;
//   right: 30rpx;
//   background: rgba(255, 255, 255, 0.9);
//   padding: 30rpx;
//   border-radius: 10rpx;
//   display: flex;
//   flex-direction: column;
//   gap: 10rpx;
// }

// .info-title {
//   font-size: 32rpx;
//   font-weight: bold;
//   color: #333;
//   margin-bottom: 10rpx;
// }

// .info-text {
//   font-size: 26rpx;
//   color: #666;
//   line-height: 1.5;
// }

// /* 原有的控制相关样式保持不变 */
// .control-item {
//   display: flex;
//   justify-content: space-between;
//   align-items: center;
//   padding: 20rpx 0;
//   border-bottom: 1px solid #f0f0f0;
// }

// .control-label {
//   font-size: 32rpx;
//   color: #333;
// }

// .control-btn {
//   background: #007aff;
//   color: white;
//   border: none;
//   padding: 15rpx 30rpx;
//   border-radius: 8rpx;
//   font-size: 28rpx;
// }

// .control-btn.active {
//   background: #ff3b30;
// }

// .control-btn:disabled {
//   background: #ccc;
//   color: #999;
// }

// /* 速度控制样式 */
// .speed-selector {
//   position: absolute;
//   bottom: 200rpx;
//   right: 30rpx;
//   background: rgba(0, 0, 0, 0.8);
//   color: white;
//   padding: 20rpx;
//   border-radius: 10rpx;
//   z-index: 1000;
// }

// .speed-options {
//   display: flex;
//   flex-direction: column;
//   gap: 10rpx;
// }

// .speed-option {
//   background: transparent;
//   color: white;
//   border: 1px solid #555;
//   padding: 10rpx 20rpx;
//   border-radius: 6rpx;
//   font-size: 28rpx;
// }

// .speed-option.active {
//   background: #007aff;
//   border-color: #007aff;
// }
.pip-cover {
  bottom: 25rpx;
  color: #fff;
  font-size: 30rpx;
  height: 20rpx;
  position: fixed;
  right: 46rpx;
  width: 20rpx;
  z-index: 1000;
}
</style>
