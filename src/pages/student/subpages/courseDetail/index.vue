<template>
  <view
    class="course-detail-container"
    :class="{ popupShow: showServiceGuarantee }"
  >
    <!-- :class="{ popupShow: showServiceGuarantee }" -->
    <!-- 顶部导航栏 -->
    <!-- <u-navbar
      title="课程详情"
      bgColor="#fff"
      leftIconSize="25px"
      leftIconColor="#FFFFFF"
      :titleStyle="{
        color: '#333333',
        fontSize: '34rpx',
        fontWeight: '500',
        lineHeight: '40rpx'
      }"
      :autoBack="true"
      placeholder
      fixed
    >
      <view class="nav-letf" slot="left">
        <image
          src="https://tg-prod.oss-cn-beijing.aliyuncs.com/e9ac0f4b-6282-4bac-b602-189057c45c90.webp"
        ></image>
      </view>
    </u-navbar> -->

    <!-- 课程封面区域 -->
    <view class="course-banner">
      <image
        class="banner-bg"
        :src="
          courseDetail.header_url + '?x-oss-process=image/resize,h_380,w_422'
        "
      ></image>
    </view>

    <!-- 课程信息区域 -->
    <view class="course-info">
      <view class="course-tag-row">
        <view class="course-name"
          ><view class="tag-item">
            <view class="tag tag-yel">{{
              courseDetail.minpro_course_type === 2 ? "试听课" : "公开课"
            }}</view> </view
          >{{ courseDetail.course_name }}</view
        >
      </view>
      <view class="price-row-container">
        <view class="price-row">
          <view
            v-if="courseDetail.department_price > 0"
            style="display: flex; align-items: center; margin-right: 8rpx"
          >
            <view class="price-tag">¥</view>
            <view class="free-tag">{{ courseDetail.department_price }}</view>
          </view>
          <view class="free-tag" v-else>免费</view>
          <view
            class="price-num"
            v-if="
              courseDetail.is_show_price === 1 &&
              courseDetail.minpro_course_type !== 4
            "
            >¥{{ courseDetail.standard_price }}</view
          >
        </view>
        <view class="class-time"> {{ courseDetail.standard_numb }}课时 </view>
      </view>

      <!-- 服务保障 -->
      <view class="service-guarantee" @tap="showServicePopup">
        <view class="guarantee-item service">
          <text>服务保障</text>
        </view>
        <view class="guarantee-item">
          <u-icon
            name="https://tg-prod.oss-cn-beijing.aliyuncs.com/21911f38-63a5-4f1b-9f7d-24a588939b2a.webp"
            width="30rpx"
            height="30rpx"
          ></u-icon>
          <text>专业师资</text>
        </view>
        <view class="guarantee-item">
          <u-icon
            name="https://tg-prod.oss-cn-beijing.aliyuncs.com/21911f38-63a5-4f1b-9f7d-24a588939b2a.webp"
            width="30rpx"
            height="30rpx"
          ></u-icon>
          <text>精品教学</text>
        </view>
        <view class="guarantee-item">
          <u-icon
            name="https://tg-prod.oss-cn-beijing.aliyuncs.com/21911f38-63a5-4f1b-9f7d-24a588939b2a.webp"
            width="30rpx"
            height="30rpx"
          ></u-icon>
          <text>科学体系</text>
        </view>
        <u-icon
          name="https://tg-prod.oss-cn-beijing.aliyuncs.com/f17646ef-c3e7-47e8-a12a-a567b75de0e3.webp"
          width="30rpx"
          height="30rpx"
        ></u-icon>
      </view>
    </view>

    <!-- 详情内容 -->
    <view class="detail-section" :style="show ? '' : 'padding-bottom:0'">
      <view class="detail-tab">
        <view class="tab-item active">详情</view>
      </view>

      <!-- 课程体系 -->
      <view class="course-system">
        <image
          v-for="(item, index) in courseDetail.body_url"
          :key="index"
          :src="item"
          mode="widthFix"
        ></image>
      </view>
    </view>

    <view class="bottom-btn u-safe-area-inset-bottom" v-if="show">
      <view
        class="register-btn"
        @click="$u.throttle(handleRegister, 1500)"
        :disabled="
          courseDetail.can_sale_to === 1 &&
          courseDetail.minpro_course_type !== 4
        "
        :class="{
          disabled:
            courseDetail.minpro_course_type !== 4 &&
            courseDetail.can_sale_to !== 1
        }"
      >
        <view v-if="courseDetail.can_sale_to === 1">
          {{ courseDetail.minpro_course_type !== 4 ? "立即购买" : "立即观看" }}
        </view>
        <view v-else>
          {{
            courseDetail.minpro_course_type !== 4
              ? "暂不支持购买"
              : "暂不支持观看"
          }}
        </view>
      </view>
    </view>

    <!-- 服务保障弹窗 -->
    <service-guarantee-popup
      :show="showServiceGuarantee"
      @close="showServiceGuarantee = false"
    />
    <!-- 购买无权限时 -->
    <ContactPopup v-model="showContact" :image="qrCode" :text="qrName" />
    <ConfirmPopup
      v-model="showConfirm"
      @cancel="confirm"
      @confirm="confirmCancel"
      :confirmText="confirmPopup.confirmText"
      :cancelText="confirmPopup.cancelText"
      :title="confirmPopup.title"
      :dectitle="confirmPopup.dectitle"
    />
  </view>
</template>

<script>
import ContactPopup from "../../home/<USER>/ContactPopup.vue";
import ServiceGuaranteePopup from "./components/ServiceGuaranteePopup.vue";
import { getCourseDetail } from "@/services/student/myCourse";
import { watchCollect } from "@/services/student/order";
import ConfirmPopup from "@/components/ConfirmPopup";
export default {
  name: "CourseDetail",
  components: {
    ServiceGuaranteePopup,
    ContactPopup,
    ConfirmPopup
  },
  data() {
    return {
      courseId: "",
      showServiceGuarantee: false,
      courseDetail: null,
      showContact: false,
      session: "",
      curStudentInfo: "",
      showConfirm: false,
      confirmPopup: {
        show: true,
        title: "您手机号已有关联用户",
        description: "",
        confirmText: "去绑定",
        cancelText: "联系客服"
      },
      qrCode: "",
      qrName: "",
      show: false
    };
  },
  onLoad(options) {
    console.log(options);
    this.courseId = options.course_id;
    this.show = !options.show;
    uni.hideShareMenu();
  },
  onShow() {
    this.session = uni.getStorageSync("session");
    this.curStudentInfo = uni.getStorageSync("curStudentInfo");
    this.fetchCourseDetail();
  },
  methods: {
    async fetchCourseDetail() {
      const student_id = uni.getStorageSync("curStudentInfo").student_id || "";
      const department_id = uni.getStorageSync("curStudentInfo").department_id;
      const { data, code, message } = await getCourseDetail({
        minpro_course_id: this.courseId,
        department_id,
        student_id
      });
      if (code === 0) {
        this.courseDetail = data;
      } else {
        uni.showToast({
          title: message,
          icon: "none",
          duration: 2000
        });
        setTimeout(() => {
          uni.navigateBack();
        }, 2000);
      }
    },
    watchCourse() {
      uni.showToast({
        title: "开始观看课程",
        icon: "none"
      });
      // 这里应该跳转到课程播放页面
    },
    showServicePopup() {
      this.showServiceGuarantee = true;
    },
    confirmCancel() {
      uni.navigateTo({
        url: "/pages/student/studentPage/index?prospective=login"
      });
    },
    confirm() {
      this.showConfirm = false;
      this.showContact = true;
    },
    async handleRegister() {
      // 购买失败时 打开
      //   this.showContact = true;
      if (
        this.courseDetail.minpro_course_type !== 4 &&
        this.courseDetail.can_sale_to !== 1
      ) {
        uni.showToast({
          title: "暂不支持购买",
          icon: "none",
          duration: 2000
        });
        return;
      }

      if (this.courseDetail.minpro_course_type !== 4) {
        const student_id =
          uni.getStorageSync("curStudentInfo").student_id || "";
        const department_id =
          uni.getStorageSync("curStudentInfo").department_id;
        const { code, message } = await getCourseDetail({
          minpro_course_id: this.courseId,
          department_id,
          student_id
        });
        if (code === 0) {
          uni.navigateTo({
            url:
              "/pages/student/subpages/orderDetail/index?course_id=" +
              this.courseDetail.course_id +
              "&status=delivered&course_name=" +
              this.courseDetail.course_name +
              "&standard_numb=" +
              this.courseDetail.standard_numb +
              "&minpro_course_id=" +
              this.courseDetail.minpro_course_id
          });
        } else {
          uni.showToast({
            title: message,
            icon: "none",
            duration: 2000
          });
          setTimeout(() => {
            uni.navigateBack();
          }, 2000);
        }
      } else {
        watchCollect({ minpro_course_id: this.courseDetail.minpro_course_id });
        // 跳转到视频播放页面而不是显示"暂未开通"
        uni.navigateTo({
          url: `/pages/student/subpages/videoPlay/index?id=${this.courseDetail.minpro_course_id}&title=${this.courseDetail.course_name}&poster=${this.courseDetail.header_url}&videoUrl=${this.courseDetail.html_url}&title=${this.courseDetail.course_name}`
        });
      }
    },
    moveHandle() {}
  }
};
</script>

<style lang="scss" scoped>
.nav-letf {
  width: 40rpx;
  height: 40rpx;
  image {
    width: 100%;
    height: 100%;
  }
}
.course-detail-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
  width: 100%;
}

/* 课程封面区域 */
.course-banner {
  position: relative;
  width: 100%;
  height: 422rpx;
  overflow: hidden;

  .banner-bg {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
  }
}

/* 课程信息区域 */
.course-info {
  background-color: #ffffff;
  padding: 30rpx 30rpx;
  // border-radius: 24rpx 24rpx 0 0;
  // margin-top: -43rpx;
  padding-top: 24rpx;
  position: relative;
  z-index: 3;

  .course-tag-row {
    display: flex;
    align-items: center;
    margin-bottom: 20rpx;

    .tag-item {
      margin-right: 20rpx;
      display: inline-block;
      .tag {
        // width: 80rpx;
        position: relative;
        bottom: 3rpx;
        height: 38rpx;
        padding: 0 16rpx;
        display: inline-flex;
        justify-content: center;
        align-items: center;
        gap: 10rpx;
        flex-shrink: 0;
        color: #fff;
        font-size: 24rpx;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
        // margin-left: 12rpx;
        border-radius: 16rpx 8rpx 16rpx 8rpx;
        &-yel {
          background: linear-gradient(313deg, #ffb200 35.48%, #ffe32d 127.89%);
        }

        &-bul {
          background: linear-gradient(312deg, #4ab9ff 35.69%, #a9deff 126.33%);
        }
        &-purp {
          background: linear-gradient(300deg, #9f6bff 31.22%, #ece2ff 133.9%);
        }
        &-gre {
          background: linear-gradient(296deg, #2ccba7 44.48%, #94e3e7 168.27%);
        }
        &-pink {
          background: linear-gradient(296deg, #f85395 44.48%, #ffeff9 168.27%);
        }
      }
    }
    .course-name-container {
      display: inline-block;
    }
    .course-name {
      font-size: 32rpx;
      font-weight: 500;
      color: #333333;
      flex: 1;
      // overflow: hidden;
      // text-overflow: ellipsis;
      // white-space: nowrap;
    }
  }
  .price-row-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 25rpx;
    .price-row {
      display: flex;
      // align-items: center;
      .price-tag {
        color: #ff553a;
        font-size: 24rpx;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
        margin-right: 5rpx;
      }
      .free-tag {
        font-size: 36rpx;
        font-weight: 600;
        color: #ff553a;
        display: flex;
        align-items: flex-end;
        line-height: 40rpx;
      }
      .price-num {
        color: #999;
        font-size: 24rpx;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
        text-decoration: line-through;
        // margin-left: 10rpx;
        display: flex;
        align-items: flex-end;
      }
    }
    .class-time {
      color: #999;
      font-size: 28rpx;
      font-style: normal;
      font-weight: 400;
      line-height: 42rpx;
    }
  }

  .service-guarantee {
    display: flex;
    align-items: center;
    padding: 20rpx 20rpx;
    border-radius: 20rpx;
    background: #fefaeb;
    justify-content: space-between;
    .guarantee-item {
      display: flex;
      align-items: center;
      margin-right: 30rpx;

      text {
        font-size: 24rpx;
        color: #333;
        margin-left: 8rpx;
      }
    }
    .service {
      text {
        color: #999;
      }
    }
  }
}

/* 详情区域 */
.detail-section {
  flex: 1;
  background-color: #ffffff;
  //   margin-top: 20rpx;
  padding-bottom: 180rpx;
  .detail-tab {
    display: flex;
    padding: 0 30rpx;

    .tab-item {
      position: relative;
      color: #333;
      font-size: 30rpx;
      font-style: normal;
      font-weight: 500;
      line-height: normal;
    }
  }

  .course-system {
    width: 100%;
    padding: 20rpx 32rpx;
    box-sizing: border-box;

    image {
      width: 100%;
      height: 100%;
      display: block;
      object-fit: contain; // 保持图片比例
      max-width: 100%; // 限制最大宽度
      // border-radius: 24rpx;
    }
    // 第一个
    image:first-child {
      border-top-right-radius: 24rpx;
      border-top-left-radius: 24rpx;
    }
    // 最后一个
    image:last-child {
      border-bottom-right-radius: 24rpx;
      border-bottom-left-radius: 24rpx;
    }
  }
}
.bottom-btn {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  // height: 128rpx;
  background: #ffffff;
  display: flex;
  // align-items: center;
  justify-content: center;
  z-index: 4;
  transition: transform 0.2s ease;
  .disabled {
    opacity: 0.5;
  }
  &:active {
    transform: scale(1.05);
  }
  .register-btn {
    width: 686rpx;
    height: 92rpx;
    border-radius: 71rpx;
    background: linear-gradient(15deg, #ffbf0d 18.1%, #ffcb3c 83.29%);
    box-shadow: 0px -10px 18px 0px #f3b300 inset,
      0px 4px 10px 0px rgba(254, 197, 36, 0.47);
    color: #ffffff;
    font-size: 32rpx;
    font-weight: 500;
    text-align: center;
    // line-height: 92rpx;
    border: none;
    margin-top: 18rpx;
    margin-bottom: 18rpx;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
.popupShow {
  overflow: hidden;
  position: fixed;
}
</style>
