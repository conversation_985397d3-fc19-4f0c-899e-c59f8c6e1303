<template>
  <u-popup
    :show="showPopup"
    mode="bottom"
    width="600rpx"
    height="1200rpx"
    round="22"
    :safeAreaInsetBottom="false"
  >
    <view class="service-popup">
      <!-- 标题栏 -->
      <view class="popup-header">
        <text class="popup-title">服务保障</text>
        <view class="close-icon" @tap="closePopup">
          <u-icon
            name="https://tg-prod.oss-cn-beijing.aliyuncs.com/3bd908dd-5fb5-4ed5-b74a-43ea7a07098f.webp"
            width="36rpx"
            height="36rpx"
            color="#999"
            bold
          ></u-icon>
        </view>
      </view>

      <!-- 内容区域 -->
      <view class="popup-content">
        <view
          class="service-item"
          v-for="(item, index) in serviceList"
          :key="index"
        >
          <view class="item-icon">
            <u-icon
              name="https://tg-prod.oss-cn-beijing.aliyuncs.com/2168c590-1506-4852-92bb-062525183e83.webp"
              width="40rpx"
              height="40rpx"
            ></u-icon>
          </view>
          <view class="item-content">
            <view class="item-title">{{ item.title }}</view>
            <view class="item-desc">{{ item.desc }}</view>
          </view>
        </view>
      </view>

      <!-- 底部按钮 -->
      <view class="popup-footer u-safe-area-inset-bottom">
        <button class="confirm-btn" @tap="closePopup">知道了</button>
      </view>
    </view>
  </u-popup>
</template>

<script>
export default {
  name: "ServiceGuaranteePopup",
  props: {
    show: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      showPopup: false,
      serviceList: [
        {
          icon: "checkmark-circle",
          title: "专业师资，全程护航",
          desc: "经验教练经过严苛筛选与专业培训，提升棋艺，教学借鉴日本顶尖棋院，为学员量身定制课程，确保精准匹配"
        },
        {
          icon: "checkmark-circle",
          title: "精品教学，精准培育",
          desc: "精品教学重实战能力，精心打造全龄儿童围棋基础课程，与身心成长特点的课程，确保精准匹配幼儿学棋需求，让孩子快乐学围棋，轻松登成长"
        },
        {
          icon: "checkmark-circle",
          title: "科学体系，全面成长",
          desc: "课程体系完善，覆盖囊括职业阶段，循序渐进，不仅注重棋艺提升，更融入围棋文化、思维训练与品德培养，助力孩子全面发展"
        },
        {
          icon: "checkmark-circle",
          title: "自研教材，趣味高效",
          desc: "自研教材搭配动画教学，以故事串联知识，激发孩子学习兴趣，在快乐中提升思维与文化素养"
        },
        {
          icon: "checkmark-circle",
          title: "家长课堂，共同成长",
          desc: "定期举办家长课堂，邀请教育专家与资深教练，分享围棋知识、学习方法及孩子教育技巧，促进家长与孩子共同成长"
        },
        {
          icon: "checkmark-circle",
          title: "多端平台，学习闭环",
          desc: "提供电脑端、手机端、平板全平台学习体验，支持下载离线观看，随时随地学习不受限"
        }
      ]
    };
  },
  watch: {
    show(val) {
      this.showPopup = val;
    },
    showPopup(val) {
      if (val !== this.show) {
        this.$emit("update:show", val);
      }
    }
  },
  methods: {
    closePopup() {
      this.showPopup = false;
      this.$emit("close");
    }
  }
};
</script>

<style lang="scss" scoped>
.service-popup {
  padding: 30rpx;
  max-height: 1200rpx;
  display: flex;
  flex-direction: column;
  border-radius: 22rpx 22rpx 0px 0px;
  background: url("https://tg-prod.oss-cn-beijing.aliyuncs.com/b06d33db-ff93-458f-bf99-3ce9a17a4512.webp")
    no-repeat;
  background-size: 100% 100%;
  padding-bottom: 0;
  .popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    // padding-bottom: 20rpx;
    // border-bottom: 1rpx solid #eeeeee;

    .popup-title {
      color: #333;
      text-align: center;
      font-size: 36rpx;
      font-style: normal;
      font-weight: 500;
      line-height: 38rpx; /* 105.556% */
      letter-spacing: 0.5rpx;
    }

    .close-icon {
      padding: 10rpx;
    }
  }

  .popup-content {
    flex: 1;
    overflow-y: auto;
    margin: 36rpx 0;
    background: #fff;
    border-radius: 22rpx 22rpx 0px 0px;
    height: 910rpx;
    overflow: auto;
    margin-bottom: 180rpx;
    .service-item:first-child {
      padding-top: 28rpx;
    }
    .service-item {
      display: flex;
      padding: 0 22rpx 28rpx 22rpx;

      &:not(:last-child) {
        // border-bottom: 1rpx solid #f5f5f5;s
      }

      .item-icon {
        margin-right: 10rpx;
      }

      .item-content {
        flex: 1;

        .item-title {
          color: #333;
          font-size: 28rpx;
          font-style: normal;
          font-weight: 500;
          line-height: 38rpx;
          margin-bottom: 4rpx;
        }

        .item-desc {
          color: #999;
          font-size: 25rpx;
          font-style: normal;
          font-weight: 400;
          line-height: 36rpx;
        }
      }
    }
  }

  .popup-footer {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    // height: 128rpx;
    background: #ffffff;
    display: flex;
    // align-items: center;
    justify-content: center;
    z-index: 4;
    transition: transform 0.2s ease;

    &:active {
      transform: scale(1.05);
    }

    .confirm-btn {
      width: 686rpx;
      height: 92rpx;
      border-radius: 71rpx;
      background: linear-gradient(15deg, #ffbf0d 18.1%, #ffcb3c 83.29%);
      box-shadow: 0px -10px 18px 0px #f3b300 inset,
        0px 4px 10px 0px rgba(254, 197, 36, 0.47);
      color: #ffffff;
      font-size: 32rpx;
      font-weight: 600;
      text-align: center;
      line-height: 100rpx;
      border: none;
      margin-top: 18rpx;
      margin-bottom: 18rpx;
      &::after {
        border: none;
      }
    }
  }
}
</style>
