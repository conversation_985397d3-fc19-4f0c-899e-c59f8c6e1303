<template>
  <view class="profile-container">
    <!-- 返回导航 -->
    <!-- <u-navbar
      title="个人信息"
      bgColor="#fff"
      leftIconSize="25px"
      leftIconColor="#333333"
      :titleStyle="{
        color: '#333333',
        fontSize: '34rpx',
        fontWeight: '500',
        lineHeight: '40rpx'
      }"
      :autoBack="true"
    >
      <view class="nav-letf" slot="left">
        <image
          src="https://tg-prod.oss-cn-beijing.aliyuncs.com/e9ac0f4b-6282-4bac-b602-189057c45c90.webp"
        ></image>
      </view>
    </u-navbar> -->

    <!-- 个人信息列表 -->
    <view class="info-list">
      <!-- 头像项 -->
      <view class="info-item avatar-item" @click="showAvatarPicker">
        <text class="label">头像</text>
        <view class="value-box">
          <image
            class="avatar"
            :src="genders_list[studentInfo.choose_head] || genders_list[1]"
            mode="aspectFill"
          />
          <u-icon size="30rpx" class="arrow" name="arrow-right"></u-icon>
        </view>
      </view>

      <!-- 其他信息项 -->
      <view class="info-item" v-for="(item, index) in infoList" :key="index">
        <text class="label">{{ item.label }}</text>
        <view class="value-box">
          <text class="value">{{
            item.key === "sign_up_time"
              ? formatDate(studentInfo[item.key])
              : item.key === "student_mobile"
              ? desensitizePhone(studentInfo[item.key])
              : studentInfo[item.key] || "--"
          }}</text>
        </view>
      </view>
    </view>

    <!-- 头像选择弹窗 -->
    <view @touchmove.stop.prevent="">
      <u-popup
        :show="showPopup"
        @close="showPopup = false"
        mode="bottom"
        round="22rpx"
      >
        <view class="avatar-picker">
          <view class="picker-header">
            <text class="title">编辑头像</text>
          </view>
          <view class="avatar-list">
            <view
              class="avatar-option"
              v-for="(url, key) in avatarOptions"
              :key="key"
              @click="selectAvatar(key, url)"
            >
              <image :src="url" mode="aspectFill" class="avatar-img" />
              <!-- <u-icon
              v-if="curStudentInfo.student_gender === key"
              name="checkbox-mark"
              color="#2979ff"
              class="selected-icon"
            ></u-icon> -->
            </view>
          </view>
        </view>
      </u-popup>
    </view>
  </view>
</template>

<script>
import {
  setStudentHead,
  setIntentAvatar,
  getStudentDetail,
  getIntentDetail,
  getCustomerDetail,
  setCustomerHead
} from "@/services/student/my";
import { genders_list } from "../../my/config/index";
export default {
  name: "ProfileInfo",
  data() {
    return {
      userInfo: {
        avatar: "", // 头像URL
        name: "喵小喵",
        studentId: "S123456",
        campus: "亦庄校区",
        phone: "133****9876",
        jlptLevel: "N1",
        enrollDate: "2025-11-02"
      },
      infoList: [
        { label: "学生姓名", key: "student_name" },
        { label: "学号", key: "student_number" },
        { label: "APP账号", key: "app_account" },
        { label: "校区", key: "department_name" },
        { label: "手机号码", key: "student_mobile" },
        { label: "聂道棋力", key: "nie_dao_level" },
        { label: "入学时间", key: "sign_up_time" }
      ],
      student_id: "",
      showPopup: false,
      avatarOptions: genders_list, // 使用已有的性别头像配置
      role: "",
      studentInfo: {},
      genders_list
    };
  },
  methods: {
    showAvatarPicker() {
      this.showPopup = true;
    },
    async selectAvatar(gender, url) {
      if (this.role === "student") {
        await setStudentHead({
          student_id: this.student_id,
          choose_head: Number(gender)
        });
      } else if (this.role === "customer") {
        await setIntentAvatar({
          customer_id: this.student_id,
          choose_head: Number(gender)
        });
      } else {
        await setCustomerHead({
          open_id: this.student_id,
          choose_head: Number(gender)
        });
      }
      // 关闭弹窗
      this.showPopup = false;
      this.getStudentDetaill();
    },
    // 获取当前学员信息
    async getStudentDetaill() {
      let res;
      if (this.role === "student") {
        res = await getStudentDetail({
          student_id: this.student_id
        });
        this.studentInfo = res.data;
      } else if (this.role === "customer") {
        res = await getIntentDetail({
          customer_id: this.student_id
        });
        this.studentInfo = res.data;
      } else {
        const respones = await getCustomerDetail({
          open_id: this.student_id
        });
        this.studentInfo = respones.data;
      }
      this.studentInfo.app_account = "S294723"; // 为了演示暂时写死
      uni.setStorageSync("curStudentInfo", res.data || "");
    },
    formatDate(date) {
      if (!date || this.studentInfo.student_category !== "is_student") {
        return "";
      }
      const d = new Date(date);
      const year = d.getFullYear();
      const month = String(d.getMonth() + 1).padStart(2, "0");
      const day = String(d.getDate()).padStart(2, "0");
      // const hours = String(d.getHours()).padStart(2, "0");
      // const minutes = String(d.getMinutes()).padStart(2, "0");
      // const seconds = String(d.getSeconds()).padStart(2, "0");
      return `${year}-${month}-${day}`;
    },
    // 手机号脱敏
    desensitizePhone(phone) {
      if (!phone) return "";
      return phone.replace(/(\d{3})(\d{4})(\d{4})/, "$1****$3");
    }
  },
  mounted() {
    const curStudentInfo = uni.getStorageSync("curStudentInfo");
    const session = uni.getStorageSync("session");
    // console.log(this.curStudentInfo, this.session);
    this.role = session.role;
    this.student_id =
      session.role === "student"
        ? curStudentInfo.student_id
        : session.role === "customer"
        ? curStudentInfo.customer_id
        : session.open_id;
    if (this.role === "default" || this.role === "customer") {
      const newInfoList = ["nie_dao_level", "sign_up_time", "student_number"];
      this.infoList = this.infoList.filter(
        (item) => !newInfoList.includes(item.key)
      );
    }
    this.getStudentDetaill();
    uni.hideShareMenu();
  }
};
</script>

<style lang="scss" scoped>
.profile-container {
  min-height: 100vh;
  background: #f5f5f5;
  padding-top: 30rpx;
  .info-list {
    // margin-top: 200rpx;
    background: #fff;
    width: 686rpx;
    margin: 0 auto;
    border-radius: 24rpx;

    .info-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 34rpx 30rpx;
      border-bottom: 1rpx solid #eee;

      .label {
        color: #333;
        font-size: 32rpx;
        font-style: normal;
        font-weight: 500;
        line-height: 40rpx;
      }

      .value-box {
        display: flex;
        align-items: center;

        .value {
          color: #999;
          text-align: right;
          font-size: 32rpx;
          font-style: normal;
          font-weight: 400;
          line-height: 40rpx;
        }

        .arrow {
          color: #999;
          font-size: 24rpx;
        }
      }

      &.avatar-item {
        .avatar {
          width: 113.05rpx;
          height: 113.05rpx;
          border-radius: 50%;
          margin-right: 20rpx;
        }
      }
    }
  }
}

.avatar-picker {
  padding: 30rpx;

  .picker-header {
    display: flex;
    justify-content: center;
    align-items: center;
    padding-bottom: 38rpx;
    // width: 658rpx;
    border-bottom: 1rpx solid #eee;
    .title {
      color: #333;
      text-align: center;
      font-size: 32rpx;
      font-style: normal;
      font-weight: 500;
      line-height: normal;
    }
  }

  .avatar-list {
    display: flex;
    justify-content: space-around;
    padding: 30rpx 0;
    margin-top: 60rpx;
    .avatar-option {
      position: relative;
      width: 178.5rpx;
      height: 178.5rpx;

      .avatar-img {
        width: 100%;
        height: 100%;
        border-radius: 50%;
        // border: 2rpx solid #eee;
        // box-shadow: 0px 0px 10rpx 0px rgba(0, 0, 0, 0.1);
      }

      .selected-icon {
        position: absolute;
        right: 0;
        bottom: 0;
        background-color: #fff;
        border-radius: 50%;
      }
    }
  }
}
.nav-letf {
  width: 40rpx;
  height: 40rpx;
  image {
    width: 100%;
    height: 100%;
  }
}
</style>
