<template>
  <view class="video-player-container">
    <!-- 新增：永久显示的顶部导航栏 -->
    <view class="permanent-top-nav" :class="[ showControls ? 'show' : 'hide']">
      <view class="back-button" @tap.stop="goBack">
        <u-icon
          name="https://tg-prod.oss-cn-beijing.aliyuncs.com/0b795b53-f202-4201-91e0-67b39535e8d8.webp"
          width="13rpx"
          height="20rpx"
          bold
        ></u-icon>
      </view>
      <view class="video-title">{{ courseTitle }}</view>
    </view>

    <!-- <video
      id="courseVideo"
      class="video-player"
      :src="videoUrl"
      :controls="true"
      :show-fullscreen-btn="false"
      :enable-play-gesture="true"
      :show-loading="true"
      :show-center-play-btn="true"
      :enable-progress-gesture="true"
      :direction="90"
      :object-fit="objectFit"
      @fullscreenchange="handleFullscreenChange"
      @ended="handleVideoEnd"
      @error="handleVideoError"
      @play="handleVideoPlay"
      @pause="handleVideoPause"
      :play-back-rate="playbackRate"
    ></video> -->
    <VideoPlay
      :src="videoUrl"
      :courseTitle="courseTitle"
      :autoplay="true"
      :showRate="true"
      @controls-change="controlsChange"
      ref="videoPlay"
    />
  </view>
</template>

<script>
import VideoPlay from "@/components/videoPlay/index.vue";
export default {
  name: "VideoPlay",
  components: {
    VideoPlay
  },
  data() {
    return {
      videoUrl: "",
      courseTitle: "",
      courseId: "",
      objectFit: "contain",
      screenHeight: 0,
      screenWidth: 0,
      isFullscreen: true,
      showControls: true,
      controlsTimer: null,
      isLoading: true,
      videoEnded: false,
      showSpeedSelector: false,
      // 新增: 倍速控制相关数据
      playbackRate: 1.0,
      speedOptions: [0.5, 0.75, 1.0, 1.25, 1.5, 2.0],
      currentSpeedIndex: 2, // 默认是1.0倍速，对应索引2
      showSpeedControl: false,
      speedControlTimer: null
    };
  },
  onLoad(options) {
    this.courseId = options.id || "";
    this.courseTitle = options.title || "课程视频";

    // 在实际应用中，应该基于courseId从API获取视频URL
    console.log(options.videoUrl);
    this.videoUrl = options.videoUrl;

    // 自动隐藏控制层
    this.autoHideControls();
  },
  onReady() {
    this.videoContext = uni.createVideoContext("courseVideo", this);
    // this.videoContext.requestFullScreen();

    setTimeout(() => {
      this.isLoading = false;
      this.videoContext.play();
      wx.setPageOrientation({
        orientation: "landscape"
      });
    }, 500);
  },
  onHide() {
    // 页面隐藏时暂停视频
    if (this.videoContext) {
      this.videoContext.pause();
    }
  },
  onUnload() {
    clearTimeout(this.controlsTimer);
    clearTimeout(this.speedControlTimer);
  },
  methods: {
    goBack() {
      // 返回上一页
      uni.navigateBack();
    },
    handleFullscreenChange(e) {
      this.isFullscreen = e.detail.fullScreen;
    },
    handleVideoEnd() {
      this.videoEnded = true;
      this.showControls = false;
    },
    handleVideoError(e) {
      console.error("视频播放出错:", e);
      uni.showToast({
        title: "视频加载失败，请稍后重试",
        icon: "none",
        duration: 2000
      });
    },
    handleVideoPlay() {
      this.autoHideControls();
    },
    handleVideoPause() {
      this.showControls = true;
      clearTimeout(this.controlsTimer);
    },
    autoHideControls() {
      clearTimeout(this.controlsTimer);
      this.controlsTimer = setTimeout(() => {
        this.showControls = false;
        this.showSpeedSelector = false;
      }, 3000);
    },
    replayVideo() {
      this.videoEnded = false;
      this.videoContext.seek(0);
      this.videoContext.play();
      this.showControls = true;
      this.autoHideControls();
    },
    // 切换倍速选择器显示状态
    toggleSpeedControl() {
      this.showSpeedControl = !this.showSpeedControl;
      if (this.showSpeedControl) {
        // 打开倍速控制器时清除自动关闭定时器
        clearTimeout(this.speedControlTimer);
        // 设置新的自动关闭定时器
        this.speedControlTimer = setTimeout(() => {
          this.showSpeedControl = false;
        }, 5000);
      }
    },
    closeSpeedControl() {
      this.showSpeedControl = false;
      clearTimeout(this.speedControlTimer);
    },
    keepSpeedControlOpen(e) {
      // 阻止事件冒泡，避免点击控制面板时关闭它
      e.stopPropagation();
      // 重置自动关闭定时器
      clearTimeout(this.speedControlTimer);
      this.speedControlTimer = setTimeout(() => {
        this.showSpeedControl = false;
      }, 5000);
    },
    onSpeedSliderChange(e) {
      const index = e.detail.value;
      this.currentSpeedIndex = index;
      this.playbackRate = this.speedOptions[index];

      // 应用新的播放速度
      if (this.videoContext) {
        this.videoContext.playbackRate(this.playbackRate);
      }

      // 显示倍速变化提示
      uni.showToast({
        title: `播放速度: ${this.playbackRate}x`,
        icon: "none",
        duration: 1500
      });

      // 重置自动关闭定时器
      clearTimeout(this.speedControlTimer);
      this.speedControlTimer = setTimeout(() => {
        this.showSpeedControl = false;
      }, 3000);
    },
    controlsChange(newVal) {
      console.log(newVal);
      this.showControls = newVal;
    }
  }
};
</script>

<style lang="scss" scoped>
.video-player-container {
  position: relative;
  width: 100%;
  height: 100vh;
  background-color: #000;
}

.video-player {
  width: 100%;
  height: 100vh;
}

/* 新增：永久显示的顶部导航栏样式 */
.permanent-top-nav {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 30;
  /* 确保在最上层 */
  display: flex;
  align-items: center;
  padding: 0rpx 20rpx 20rpx 30rpx;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0));
  opacity: 1;
  transition: opacity 0.3s;
  .back-button {
    width: 60rpx;
    height: 50rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 20rpx;
    image {
      width: 40rpx;
      height: 40rpx;
    }
  }

  .video-title {
    margin-left:-20rpx;
    flex: 1;
    color: #fff;
    font-size: 20rpx;
    font-weight: 500;
    /* 添加文本阴影，使白色文字在任何背景上都清晰可见 */
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
  }

  /* 添加倍速显示样式 */
  .speed-indicator {
    color: #fff;
    font-size: 24rpx;
    font-weight: bold;
    background-color: rgba(0, 0, 0, 0.5);
    padding: 8rpx 16rpx;
    border-radius: 30rpx;
    margin-right: 20rpx;
  }
}

.video-controls {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.3);
  z-index: 10;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

/* 保留原来的top-controls样式但在这里不再使用 */
.top-controls {
  display: flex;
  align-items: center;
  padding: 40rpx;

  .back-button {
    width: 60rpx;
    height: 60rpx;
    display: flex;
    justify-content: center;
    align-items: center;

    image {
      width: 40rpx;
      height: 40rpx;
    }
  }

  .video-title {
    margin-left: 30rpx;
    color: #fff;
    font-size: 28rpx;
    font-weight: 500;
  }
}

.loading-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 20;
}

.video-ended {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  z-index: 30;
  display: flex;
  justify-content: center;
  align-items: center;

  .ended-content {
    text-align: center;

    .ended-title {
      color: #fff;
      font-size: 36rpx;
      font-weight: 500;
      margin-bottom: 60rpx;
    }

    .action-buttons {
      display: flex;
      justify-content: center;

      .action-btn {
        width: 240rpx;
        height: 80rpx;
        line-height: 80rpx;
        border-radius: 40rpx;
        margin: 0 20rpx;
        font-size: 30rpx;

        &.replay {
          background: linear-gradient(15deg, #ffbf0d 18.1%, #ffcb3c 83.29%);
          color: #fff;
        }

        &.back {
          background: transparent;
          color: #fff;
          border: 2rpx solid #fff;
        }
      }
    }
  }
}

/* 底部控制条样式 */
.bottom-controls {
  padding: 20rpx;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-bottom: 30rpx;

  .speed-button {
    color: #fff;
    font-size: 28rpx;
    background-color: rgba(0, 0, 0, 0.5);
    padding: 10rpx 20rpx;
    border-radius: 30rpx;
    margin-right: 20rpx;
  }
}

/* 倍速选择器样式 */
.speed-selector {
  position: absolute;
  bottom: 100rpx;
  right: 30rpx;
  background-color: rgba(0, 0, 0, 0.8);
  border-radius: 10rpx;
  padding: 10rpx 0;
  z-index: 40;

  .speed-list {
    display: flex;
    flex-direction: column;

    .speed-item {
      color: #fff;
      font-size: 28rpx;
      padding: 15rpx 30rpx;
      text-align: center;

      &.active {
        color: #ffb800;
        font-weight: bold;
      }

      &:active {
        background-color: rgba(255, 255, 255, 0.1);
      }
    }
  }
}

/* 新增：倍速滑动控制器样式 */
.speed-control-container {
  position: absolute;
  bottom: 120rpx;
  left: 0;
  width: 100%;
  display: flex;
  justify-content: center;
  z-index: 40;
  padding: 0 30rpx;
  box-sizing: border-box;

  .speed-control-panel {
    background-color: rgba(0, 0, 0, 0.8);
    border-radius: 20rpx;
    padding: 30rpx;
    width: 100%;
    position: relative;

    .speed-slider-container {
      width: 100%;

      .speed-value {
        color: #ffffff;
        font-size: 32rpx;
        font-weight: bold;
        text-align: center;
        margin-bottom: 20rpx;
      }

      .speed-slider {
        margin: 30rpx 0;
      }

      .speed-marks {
        display: flex;
        justify-content: space-between;
        padding: 0 10rpx;

        .speed-mark {
          color: #aaaaaa;
          font-size: 24rpx;

          &.active {
            color: #ffb800;
            font-weight: bold;
          }
        }
      }
    }

    .speed-control-close {
      position: absolute;
      top: 20rpx;
      right: 20rpx;
      width: 40rpx;
      height: 40rpx;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}
.show {
  opacity: 1 !important;
}

.hide {
  opacity: 0 !important;
  pointer-events: none;
}
</style>
