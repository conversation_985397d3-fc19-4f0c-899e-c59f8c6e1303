<template>
  <view class="richtext-container">
    <u-navbar
      title="选择查看"
      bgColor="#fff"
      leftIconSize="25px"
      leftIconColor="#333333"
      :titleStyle="{
        color: '#333333',
        fontSize: '34rpx',
        fontWeight: '500',
        lineHeight: '40rpx'
      }"
      :autoBack="true"
      placeholder
    >
      <view class="nav-letf" slot="left" @click="handleBack">
        <image
          src="https://tg-prod.oss-cn-beijing.aliyuncs.com/b644afbe-0f58-4b93-ba2a-e9ad50e77c41.webp"
        ></image>
      </view>
    </u-navbar>
    <view class="richtextList">
      <view
        class="richtextItem"
        v-for="(item, index) in richtextList"
        :key="index"
        @click="handleClick(item, index)"
      >
        <view class="richtextItem-avatar">
          <image :src="genders[item.student_gender]" mode="widthFix"></image>
        </view>
        <view class="richtextItem-content">
          <view class="richtextItem-title">{{ item.student_name }}</view>
          <view class="richtextItem-department">{{
            item.department_name
          }}</view>
        </view>
        <view class="richtextItem-background">
          <image
            src="https://tg-prod.oss-cn-beijing.aliyuncs.com/65f63bc0-9767-4929-ab62-ceb012289add.png"
            mode="widthFix"
          ></image>
        </view>
      </view>
    </view>
    <view @touchmove.stop.prevent="">
      <alert
        :visible="alertVisible"
        :content="alertContent"
        :button-text="alertButtonText"
        @confirm="handleAlertConfirm"
        @close="handleAlertClose"
      />
      <bind-student-popup
        ref="bindStudentPopup"
        v-model="showBindStudent"
        @confirm="handleBindStudent"
      ></bind-student-popup>
    </view>
  </view>
</template>

<script>
import { arouseLogin } from "@/utils/user";
import alert from "@/components/alert/index.vue";
import { bindVisitor, getBindStudent } from "@/services/student/home";
import BindStudentPopup from "../../home/<USER>/BindStudentPopup.vue";
import { studentSendStatus } from "@/services/student/richtext";
import { genders } from "@/pages/student/my/config";
export default {
  name: "richtextIndex",
  components: {
    alert,
    BindStudentPopup
  },
  data() {
    return {
      richtextList: [],
      feedback_id: "",
      student_id: "",
      pageTitle: "",
      alertVisible: false,
      alertContent: "",
      alertButtonText: "",
      open_id: "",
      showBindStudent: false,
      student_name: "",
      genders,
      teacher_name: ""
    };
  },
  methods: {
    handleClick(item) {
      if (!item.student_feedback_id) {
        uni.showToast({
          title: `暂无${item.student_name}的${this.pageTitle}`,
          icon: "none",
          duration: 2000
        });
        return;
      }
      if (item?.type === "student") {
        uni.redirectTo({
          url: `/pages/student/subpages/studyReport/detail?openEdit=1&openPreview=1&isShare=1&feedback_id=${this.feedback_id}&student_id=${this.student_id}&pageTitle=${this.pageTitle}&student_name=${item.student_name}&teacher_name=${this.teacher_name}`
        });
      } else {
        uni.navigateTo({
          url: `/pages/student/subpages/studyReport/detail?openEdit=1&openPreview=1&isShare=1&feedback_id=${item.feedback_id}&student_id=${item.student_id}&pageTitle=${this.pageTitle}&student_name=${item.student_name}&teacher_name=${this.teacher_name}`
        });
      }
    },
    handleBack() {
      uni.switchTab({
        url: "/pages/student/home/<USER>"
      });
    },
    async handleAlertConfirm() {
      if (this.alertButtonText === "去登录") {
        // 登录
        uni.navigateTo({
          url: "/pages/student/login/index"
        });
      } else if (this.alertButtonText === "去绑定") {
        // 绑定学员 去绑定
        const bindRes = await bindVisitor({
          open_id: this.open_id,
          UNAUTHORIZED: true
        });
        if (bindRes.code === 0 && bindRes.data && bindRes.data.length > 0) {
          uni.navigateTo({
            url: "/pages/student/studentPage/index?prospective=login"
          });
        } else {
          this.showBindStudent = true;
        }
      }
    },
    async handleBindStudent(data) {
      try {
        const formData = await this.$refs.bindStudentPopup.validate();
        this.handleBindUser(formData);
      } catch (error) {
        console.error("表单验证失败:", error);
      }
      // this.showBindStudent = false;
      // this.initData(); // 重新加载数据
    },
    async handleBindUser(formData) {
      uni.showLoading({
        title: "查找中..."
      });
      const { code, data, message } = await getBindStudent(formData);
      if (code === 0) {
        uni.hideLoading();
        uni.navigateTo({
          url:
            "/pages/student/studentPage/index?prospective=" +
            JSON.stringify(data)
        });
        this.$refs.bindStudentPopup.handleClose();
      } else {
        uni.hideLoading();
        uni.$u.toast({ message, icon: "none", duration: 2000 });
      }
    },
    async getStudentSendStatus() {
      const { code, data, message } = await studentSendStatus({
        feedback_type: this.feedback_type,
        classroom_id: this.classroom_id,
        scheduling_id: this.scheduling_id
      });
      if (code === 0) {
        this.richtextList = data;
      } else {
        uni.showToast({ title: message, icon: "none", duration: 2000 });
      }
    }
  },
  async onLoad(option) {
    console.log(option, "option");
    this.feedback_id = option.feedback_id;
    this.student_id = option.student_id;
    this.pageTitle = option.pageTitle;
    const roleKey = {
      default: 1,
      student: 3,
      customer: 2
    };
    const visitor = roleKey[uni.getStorageSync("session").role] ?? 1;
    option.visitor = visitor;
    option.token = uni.getStorageSync("token");
    this.student_name = option.student_name;
    this.feedback_type = option.feedback_type;
    this.classroom_id = option.classroom_id;
    this.scheduling_id = option.scheduling_id;
    this.teacher_name = option.teacher_name;
    if (this.student_name) {
      this.handleClick({
        student_name: this.student_name,
        type: "student",
        student_feedback_id: 1
      });
    }
  },
  async onShow() {
    uni.setStorageSync("portType", "STUDENT");
    const { role, is_fist_login, open_id } = await arouseLogin();
    this.open_id = open_id;
    // 是否为第一次登录
    if (is_fist_login) {
      this.alertVisible = true;
      this.alertContent = "请先登录";
      this.alertButtonText = "去登录";
    } else if (role === "default" && !is_fist_login) {
      this.alertVisible = true;
      this.alertContent = "请先绑定学员";
      this.alertButtonText = "去绑定";
    }
    await this.getStudentSendStatus();
  }
};
</script>

<style lang="scss" scoped>
.richtext-container {
  width: 100%;
  height: 100vh;
  background-color: #f5f5f5;
  overflow: auto;
  padding: 0 32rpx;
  .richtextList {
    width: 100%;
    height: 100%;
    padding-bottom: 100rpx;
    .richtextItem {
      width: 100%;
      height: 192rpx;
      background-color: #fff;
      margin: 30rpx 0;
      border-radius: 24rpx;
      display: flex;
      flex-direction: row;
      align-items: center;
      position: relative;
      //   justify-content: space-between;
      //   display: flex;
      //   align-items: center;
      //   justify-content: center;
      .richtextItem-avatar {
        width: 107rpx;
        height: 107rpx;
        border-radius: 50%;
        overflow: hidden;
        margin-right: 29rpx;
        margin-left: 18rpx;
        image {
          width: 100%;
          height: 100%;
        }
      }
      .richtextItem-content {
        // flex: 1;
        // display: flex;
        // flex-direction: column-reverse;
        // justify-content: space-around;
        // justify-content: center;
        // align-items: center;
        .richtextItem-title {
          color: #333;
          font-size: 32rpx;
          font-style: normal;
          font-weight: 500;
          line-height: normal;
        }
        .richtextItem-department {
          color: #666;
          font-size: 28rpx;
          font-style: normal;
          font-weight: 400;
          line-height: normal;
        }
      }
      .richtextItem-background {
        width: 181rpx;
        height: 100%;
        position: absolute;
        right: 0;
        image {
          width: 100%;
          height: 100%;
        }
      }
    }
  }
  .nav-letf {
    width: 40rpx;
    height: 40rpx;

    image {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
