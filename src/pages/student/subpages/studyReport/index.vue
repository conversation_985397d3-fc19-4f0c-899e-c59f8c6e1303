<template>
  <div class="study-report-page">
    <!-- <u-navbar
      :title="title"
      bgColor="transparent"
      leftIconSize="25px"
      leftIconColor="#333333"
      :titleStyle="{
        color: '#333333',
        fontSize: '32rpx',
        fontWeight: '500',
        lineHeight: '40rpx'
      }"
      :autoBack="true"
      :placeholder="true"
    >
    </u-navbar> -->
    <!-- <u-navbar
      :title="title"
      bgColor="#fff"
      leftIconSize="25px"
      leftIconColor="#333333"
      :titleStyle="{
        color: '#333333',
        fontSize: '34rpx',
        fontWeight: '500',
        lineHeight: '40rpx'
      }"
      :autoBack="true"
      placeholder
    >
      <view class="nav-letf" slot="left">
        <image
          src="https://tg-prod.oss-cn-beijing.aliyuncs.com/e9ac0f4b-6282-4bac-b602-189057c45c90.webp"
        ></image>
      </view>
    </u-navbar> -->
    <!-- <div class="bg2"></div> -->

    <div class="bg2"></div>
    <div class="study-report-list">
      <div class="bg3"></div>
      <div class="date-selector-wrap">
        <view class="date-selector" @click="openPicker">
          <text v-if="listParams.start_time"
            >{{ currentYear }}年{{ currentMonth }}月</text
          >
          <text v-else>全部时间</text>
          <u-icon
            name="https://tg-prod.oss-cn-beijing.aliyuncs.com/1df5cbe4-ec23-4d7c-aecf-90b3e2fd66a8.webp"
            width="18rpx"
            height="12rpx"
          ></u-icon>
        </view>

        <view class="date-selector" @click="openReportTypePicker">
          <text>{{ listParams.read_label }}</text>
          <u-icon
            name="https://tg-prod.oss-cn-beijing.aliyuncs.com/1df5cbe4-ec23-4d7c-aecf-90b3e2fd66a8.webp"
            width="18rpx"
            height="12rpx"
          ></u-icon>
        </view>
      </div>
      <!-- <div class="bg3"></div> -->

      <div
        class="study-report-list__wrap"
        :style="{ height: studyReportListHeight }"
      >
        <!-- <div class="scroll"> -->
        <scroll-view
          v-if="list.length"
          scroll-y
          @scrolltolower="scrolltolower"
          refresher-enabled
          :refresher-triggered="isRefreshing"
          @refresherrefresh="onRefresh"
          refresher-background="#f5f5f5"
          :scroll-top="scrollTop"
        >
          <!-- <u-list v-if="list.length" @scrolltolower="scrolltolower"> -->
          <view
            v-for="item in list"
            :key="item.feedback_id"
            class="study-report-item-wrap"
          >
            <div class="study-report-item" @click="handleDetailClick(item)">
              <div class="cover">
                <img :src="cover.ov" alt="" />
              </div>
              <div class="report-info">
                <div class="class-name">
                  <div
                    class="value title"
                    style="color: #333; font-size: 30rpx; font-weight: 500"
                  >
                    {{ item.classroom_alias_name }}
                  </div>
                </div>
                <div class="send-name text-center">
                  <div class="label">发送人：</div>
                  <div class="value">{{ item.teacher_name }}</div>
                </div>
                <div class="date text-center">
                  <div class="label">日期：</div>
                  <div class="value">
                    {{
                      $ljsPublic.date.formatTime(
                        new Date(item.created_date).getTime(),
                        "{y}-{m}-{d} {h}:{i}:{s}"
                      )
                    }}
                  </div>
                </div>
              </div>
              <div class="right-image">
                <image :src="cover.bg"></image>
                <span
                  class="read-status"
                  :class="{ read: item.read_status !== 1 }"
                  >{{ item.read_status === 1 ? "已读" : "未读" }}</span
                >
              </div>
            </div>
          </view>
          <u-loadmore
            :status="status"
            lineColor="#DADADA"
            line
            color="#999999"
            :fontSize="'26rpx'"
            :marginTop="'70rpx'"
          />
          <!-- </u-list> -->
        </scroll-view>
        <view
          style="
            height: 80vh;
            display: flex;
            justify-content: center;
            align-items: center;
          "
          v-else
        >
          <loading-animation
            :width="300"
            :characterSize="260"
            :textSize="32"
            v-if="status === 'loading'"
          />
          <u-empty
            text="暂无数据~"
            :width="'163rpx'"
            :height="'230rpx'"
            textColor="#999999"
            icon="https://tg-prod.oss-cn-beijing.aliyuncs.com/d02814d0-bfd3-48bf-8d9d-80cda362b89a.webp"
            v-else
          >
          </u-empty>
        </view>
        <!-- </div> -->
      </div>
    </div>
    <view @touchmove.stop.prevent="">
      <u-picker
        ref="uPicker"
        :show="showDatePicker"
        :columns="[years, months]"
        @confirm="confirmDate"
        @cancel="showDatePicker = false"
        :defaultIndex="[yearIndex, monthIndex]"
        confirmColor="#333"
        immediateChange
      ></u-picker>
    </view>
    <view @touchmove.stop.prevent="">
      <u-picker
        ref="uPicker"
        :show="showReportTypePicker"
        :columns="reportTypeList"
        @confirm="confirmReportType"
        @cancel="showReportTypePicker = false"
        keyName="label"
        confirmColor="#333"
        immediateChange
      ></u-picker>
    </view>
  </div>
</template>

<script>
import { studentFeedbackList } from "@/services/student/studyReport";
import LoadingAnimation from "@/components/common/LoadingAnimation.vue";
import { getYearList } from "@/utils/date";
export default {
  name: "studyReportIndex",
  components: {
    LoadingAnimation
  },
  data() {
    return {
      title: "",
      status: "loading",
      curStudentInfo: {},
      currentYear: new Date().getFullYear(),
      currentMonth: (new Date().getMonth() + 1).toString().padStart(2, "0"),
      showDatePicker: false,
      years: [...getYearList()],
      months: Array.from({ length: 12 }, (_, i) =>
        (i + 1).toString().padStart(2, "0")
      ),
      count: 0,
      listParams: {
        page: 1,
        page_size: 7,
        year: new Date().getFullYear().toString(),
        month: new Date().getMonth() + 1,
        student_id: "string",
        start_time: "",
        read_status: "",
        read_label: "全部状态"
      },
      list: [],
      options: {},
      cover: {},
      isRefreshing: false,
      yearIndex: 0,
      monthIndex: 0,
      scrollTop: 0,
      session: "",
      studyReportListHeight: 0,
      showReportTypePicker: false,
      reportTypeList: [
        [
          {
            label: "全部状态",
            id: ""
          },
          {
            label: "已读",
            id: 1
          },
          {
            label: "未读",
            id: 2
          }
        ]
      ]
    };
  },
  computed: {},
  methods: {
    scrolltolower() {
      console.log("到底了", this.count, this.list.length);
      if (this.list.length >= this.count) {
        this.status = "nomore";
        return;
      }
      this.listParams.page++;
      this.getStudyReportList();
    },
    initYears(isInit = true) {
      const currentYear = new Date().getFullYear();
      // // 找到当前年份的索引
      this.yearIndex = this.years.indexOf(currentYear);
      // // 找到当前月份的索引
      this.monthIndex = this.months.indexOf(this.currentMonth);
      if (isInit) {
        this.listParams.start_time = `${this.currentYear}-${this.currentMonth}-01`;
      }
      console.log("🚀 ~ initYears ~ years:", this.years);
    },
    confirmDate(e) {
      console.log("🚀 ~ confirmDate ~ e:", e);
      const [year, month] = e.value;
      // let needUpdate = false;

      // if (this.currentYear !== year) {
      this.currentYear = year;
      this.listParams.year = year;
      //   needUpdate = true;
      // }

      // if (this.currentMonth !== month) {
      this.currentMonth = month;
      this.listParams.month = month;
      //   needUpdate = true;
      // }

      this.listParams.start_time = `${this.currentYear}-${this.currentMonth}-01`;
      this.showDatePicker = false;
      // if (needUpdate) {
      this.listParams.page = 1;
      this.list = [];
      this.getStudyReportList();
      // }
    },
    async getStudyReportList() {
      this.status = "loading";
      if (this.session.role !== "student") {
        this.list = [];
        this.status = "nomore";
        return;
      }
      this.listParams.student_id = this.curStudentInfo.student_id;
      this.listParams.type = this.options.type;
      this.listParams.year = this.currentYear;
      this.listParams.month = this.currentMonth;
      const { code, data, message } = await studentFeedbackList(
        this.listParams
      );
      if (code === 0) {
        if (this.listParams.page === 1) {
          this.list = data.results || [];
        } else {
          for (let i = 0; i < data.results.length; i++) {
            this.list.push(data.results[i]);
          }
        }
        this.count = data.count;
        if (this.list.length >= this.count) {
          this.status = "nomore";
        } else {
          this.status = "loadmore";
        }
        // for (let i = 0; i < data.results.length; i++) {
        //   this.list.push(data.results[i]);
        // }
        // this.count = data.count;
      } else {
        this.list = [];
        this.status = "nomore";
        uni.showToast({
          title: message || "获取数据失败",
          icon: "none"
        });
      }
      // this.status = "nomore";
    },
    handleDetailClick(item) {
      uni.navigateTo({
        url: `/pages/student/subpages/studyReport/detail?teacher_name=${item.teacher_name}&feedback_id=${item.feedback_id}&student_id=${this.curStudentInfo.student_id}&pageTitle=${this.title}&student_name=${this.curStudentInfo.student_name}&`
      });
    },
    async onRefresh() {
      this.isRefreshing = true;
      this.listParams.page = 1;
      this.list = [];
      await this.getStudyReportList();
      setTimeout(() => {
        this.isRefreshing = false;
      }, 500);
    },
    openPicker() {
      this.yearIndex = this.years.indexOf(this.currentYear);
      this.monthIndex = this.months.indexOf(this.currentMonth);
      this.showDatePicker = true;
    },
    openReportTypePicker() {
      this.showReportTypePicker = true;
    },
    confirmReportType(e) {
      console.log("🚀 ~ confirmReportType ~ e:", e);
      this.listParams.read_status = e.value[0].id;
      this.listParams.read_label = e.value[0].label;
      this.showReportTypePicker = false;
      this.listParams.page = 1;
      this.list = [];
      this.getStudyReportList();
    }
  },
  watch: {},

  // 页面周期函数--监听页面加载
  onLoad(options) {
    const titleList = {
      course_summary: "课程总结",
      class_notice: "班级通知",
      parent_class: "家长课堂"
    };

    const coverList = {
      course_summary: {
        ov: "https://tg-prod.oss-cn-beijing.aliyuncs.com/5fee296b-7e7a-43c2-b885-0788fe21205b.png",
        bg: "https://tg-prod.oss-cn-beijing.aliyuncs.com/29d3c96f-374c-48db-b9a3-e42af41874e4.png"
      },
      class_notice: {
        ov: "https://tg-prod.oss-cn-beijing.aliyuncs.com/f22c98a5-dff6-47c6-b623-2bcc45baad68.png",
        bg: "https://tg-prod.oss-cn-beijing.aliyuncs.com/af5c93f2-da85-4cb0-b788-6d44b07e93ee.png"
      },
      parent_class: {
        ov: "https://tg-prod.oss-cn-beijing.aliyuncs.com/788214bb-d6f3-427a-9cf5-2ba894800c0f.png",
        bg: "https://tg-prod.oss-cn-beijing.aliyuncs.com/97ca4720-1010-4d22-a25d-fa8b559f7f93.webp"
      }
    };
    const title = titleList[options.type];
    uni.setNavigationBarTitle({
      title
    });
    this.title = title;
    this.cover = coverList[options.type];
    this.options = options;
    this.curStudentInfo = uni.getStorageSync("curStudentInfo");
    this.session = uni.getStorageSync("session");
    this.initYears(false);
  },
  onShow() {
    this.getStudyReportList();
  },
  // 页面周期函数--监听页面初次渲染完成
  onReady() {
    uni
      .createSelectorQuery()
      .select(".date-selector") // 选中元素，传 CSS 选择器
      .fields(
        {
          size: true, // 拿 width 和 height（只包含 content+padding+border）
          rect: true, // 拿 left/top/right/bottom
          computedStyle: [
            "margin-top",
            "margin-bottom",
            "padding-top",
            "padding-bottom"
          ] // 额外拿 margin/padding
        },
        (res) => {
          console.log("节点信息", res);

          // 计算真实高度
          const contentHeight = res.height; // 包括 padding，不包括 margin
          const marginTop = parseFloat(res["margin-top"]) || 0;
          const marginBottom = parseFloat(res["margin-bottom"]) || 0;

          const totalHeight = contentHeight + marginTop + marginBottom;
          uni.getSystemInfo({
            success: (res) => {
              this.studyReportListHeight = `calc(100% - ${totalHeight}px - ${res.safeAreaInsets.bottom}rpx)`;
            }
          });
        }
      )
      .exec();
  },
  // 页面周期函数--监听页面隐藏
  onHide() {},
  // 页面周期函数--监听页面卸载
  onUnload() {}
  // 页面处理函数--监听用户下拉动作
  // onPullDownRefresh() { uni.stopPullDownRefresh(); },
  // 页面处理函数--监听用户上拉触底
  // onReachBottom() {},
  // 页面处理函数--监听页面滚动(not-nvue)
  // onPageScroll(event) {},
  // 页面处理函数--用户点击右上角分享
  // onShareAppMessage(options) {},
};
</script>

<style lang="scss" scoped>
.study-report-page {
  width: 100vw;
  min-height: 100vh;
  height: 100vh;
  overflow: hidden;
  // background: linear-gradient(180deg, #08CEFF 0%, #12D7FE 9.56%, #28EFFF 21.88%, #53F3F9 33.21%, #8FF7F1 45.93%, #D1FAE8 59.28%, #EAFFE5 75.21%);
  position: relative;
  background: #f5f5f5;
  display: flex;
  flex-direction: column;

  ::v-deep .u-navbar {
    // .u-navbar--fixed {
    //   top: 60rpx;
    // }
    .u-navbar__content__title {
      text-align: center;
      font-size: 36rpx;
      color: #fff;
      font-weight: 600;
    }
  }

  // .bg2 {
  //   width: 1699.219rpx;
  //   height: 690rpx;
  //   border-top-left-radius: 43%;
  //   border-top-right-radius: 43%;
  //   background: linear-gradient(180deg, #25E67E 0%, #11CC70 54.57%);
  //   box-shadow: 0px 50px 100px 0px #A0FB64 inset;
  //   position: absolute;
  //   left: 50%;
  //   bottom: 0px;
  //   transform: translateX(-50%);
  //   z-index: 0;
  // }
  .study-report-list {
    flex: 1;
    overflow: hidden;
    padding: 0 32rpx;

    &__wrap {
      height: calc(100% - 56rpx - 54rpx - 18rpx);
      overflow: auto;

      scroll-view {
        height: 100%;
        overflow: auto;

        ::v-deep .u-list {
          min-height: 100%;
        }
      }
    }
    .date-selector-wrap {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .bg3 {
      display: none;
      position: absolute;
      bottom: 0;
      width: 100%;
      height: 86.4rpx;
      left: 0;
      border-radius: 0px 0px 36rpx 36rpx;
      background: #ffe8c8;
    }

    .study-report-item {
      padding: 30rpx;
      display: flex;
      align-items: center;
      border-radius: 24rpx;
      background: #fff;
      margin-bottom: 30rpx;
      min-height: 194rpx;
      position: relative;
      .right-image {
        position: absolute;
        right: 0;
        bottom: 0;
        width: 178rpx;
        height: 194rpx;
        z-index: 1;
        image {
          width: 100%;
          height: 100%;
          border-radius: 24rpx;
        }
        .read-status {
          position: absolute;
          bottom: 33rpx;
          right: 33rpx;
          color: #999;
          font-size: 26rpx;
          font-style: normal;
          font-weight: 400;
          line-height: 34rpx;
        }
        .read {
          color: #fe4f37;
        }
      }

      .cover {
        width: 100rpx;
        height: 100rpx;
        margin-right: 30rpx;

        img {
          width: 100%;
          height: 100%;
        }
      }

      .report-info {
        font-size: 24rpx;
        font-weight: 600;
        width: calc(100% - 148rpx);
        z-index: 2;
        position: relative;
        .text-center {
          color: #999;
          font-size: 26rpx;
          font-weight: 400;
          line-height: 34rpx;
          margin-top: 12rpx;
        }

        .title {
          color: #333;
          font-size: 30rpx;
          font-style: normal;
          font-weight: 500;
          line-height: normal;
        }

        .label {
          display: inline-block;
          vertical-align: middle;
        }

        .value {
          display: inline-block;
          vertical-align: middle;
        }
        .class-name {
          .value {
            width: 100%;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
      }
    }
  }
}

.date-selector {
  display: inline-flex;
  padding: 9rpx 24rpx;
  gap: 10rpx;
  border-radius: 12rpx;
  background: #fff;
  font-size: 26rpx;
  font-weight: 500;
  line-height: normal;
  color: #666;
  height: 56rpx;
  align-items: center;
  margin: 24rpx 0 30rpx;

  text {
    color: #666;
    font-size: 26rpx;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
  }
}

.nav-letf {
  width: 40rpx;
  height: 40rpx;

  image {
    width: 100%;
    height: 100%;
  }
}

::v-deep .u-popup__content {
  border-radius: 24rpx;
}
</style>
