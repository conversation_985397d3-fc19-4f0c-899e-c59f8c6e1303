<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <div class="study-report-detail-page">
    <web-view
      id="webview"
      @load="pageLoad"
      :webview-styles="webviewStyles"
      :src="webViewSrc"
    ></web-view>
  </div>
</template>

<script>
import { getOpenId, login } from "@/services/student/home";
export default {
  components: {},
  data() {
    return {
      webviewStyles: {
        progress: {
          color: "#FF3333"
        }
      },
      webViewSrc: "",
      option: {},
      // baseUrl: "http://localhost:8080",
      baseUrl: process.env.VUE_APP_TG_HOST,
      guideBottom: 0,
      guideLeft: 0
    };
  },
  computed: {},
  onShareAppMessage() {
    const { feedback_id, student_id, pageTitle, teacher_name } = this.option;
    setTimeout(() => {
      this.sendMessageToWebView();
    }, 1000);
    console.log(this.option, this.shareData, !!this.shareData, "this.option");
    if (this.shareData?.teacher_name) {
      return this.shareData;
    } else {
      return {
        title: teacher_name + "发布的" + pageTitle,
        path: `/pages/teacher/subpages/richtext/index?openEdit=1&openPreview=1&isShare=1&feedback_id=${feedback_id}&student_id=${student_id}&pageTitle=${pageTitle}`,
        imageUrl:
          "https://tg-prod.oss-cn-beijing.aliyuncs.com/f860ad82-95fd-49c4-a8de-c629f069411b.png"
      };
    }
  },
  methods: {
    calcPosition() {
      uni.getSystemInfo({
        success: (res) => {
          const menuButton = uni.getMenuButtonBoundingClientRect();
          const systemInfo = uni.getSystemInfoSync();

          const pxPerRpx = systemInfo.windowWidth / 750;
          const imgWidth = 575 * pxPerRpx;
          const popupPadding = 36 * pxPerRpx + 43 * pxPerRpx;
          const popupTitleMarginBottom = 55 * pxPerRpx;
          // 计算图片 bottom = 屏幕高度 - 菜单按钮 bottom - 安全区域底部 - 弹窗padding - 弹窗标题marginBottom
          console.log(res.safeAreaInsets, "res.safeAreaInsets");
          this.guideBottom =
            systemInfo.windowHeight -
            menuButton.bottom -
            res.safeAreaInsets.bottom -
            popupPadding -
            popupTitleMarginBottom;

          // 可调位置：比如右对齐、靠右侧留 16px
          this.guideLeft = systemInfo.windowWidth - imgWidth - 16;
          console.log(this.guideBottom, this.guideLeft, "guideBottom");
        }
      });
    },
    arouseLogin(option) {
      const that = this;
      uni.login({
        provider: "weixin",
        onlyAuthorize: true, // 微信登录仅请求授权认证
        success: async function (event) {
          const { code } = event;
          console.log(code, event);
          const res = await getOpenId({ code: event.code });
          if (res.code === 0) {
            const res2 = await login({ open_id: res.data.openid });
            if (res2.code === 0) {
              const role =
                !res2.data.is_student && !res2.data.is_customer
                  ? "default"
                  : res2.data.is_student
                  ? "student"
                  : "customer";
              const roleKey = {
                default: 1,
                student: 3,
                customer: 2
              };
              const visitor = roleKey[role] ?? 1;
              console.log(uni.getStorageSync("session"), visitor);
              const query = {
                ...option,
                token: res2.data.token,
                visitor
              };
              that.webViewSrc = `${
                that.baseUrl
              }/studyReportPreview${uni.$u.queryParams(query)}&guideBottom=${
                that.guideBottom
              }&guideLeft=${that.guideLeft}`;
              console.log(that.webViewSrc);
            }
          }
        }
      });
    },
    pageLoad(e) {
      uni.hideLoading();
    }
  },
  watch: {},

  // 页面周期函数--监听页面加载
  onLoad(option) {
    // this.getStudyReportInfo(option);
    uni.showLoading({
      title: "加载中..."
    });
    option.token = uni.getStorageSync("token");
    console.log(option);
    this.option = option;
    // const urlParams = uni.$u.queryParams(option);
    // this.webViewSrc = `${this.baseUrl}/studyReportPreview${urlParams}`;
    this.calcPosition();
    this.arouseLogin(option);
    uni.$u.sleep(3000).then(() => {
      uni.hideLoading();
    });
    if (option.type === "studyReport") {
      uni.setNavigationBarTitle({
        title: option.student_name
      });
    }
  },
  // 页面周期函数--监听页面初次渲染完成
  onReady() {},
  // 页面周期函数--监听页面显示(not-nvue)
  onShow() {},
  // 页面周期函数--监听页面隐藏
  onHide() {},
  // 页面周期函数--监听页面卸载
  onUnload() {}
  // 页面处理函数--监听用户下拉动作
  // onPullDownRefresh() { uni.stopPullDownRefresh(); },
  // 页面处理函数--监听用户上拉触底
  // onReachBottom() {},
  // 页面处理函数--监听页面滚动(not-nvue)
  // onPageScroll(event) {},
  // 页面处理函数--用户点击右上角分享
  // onShareAppMessage(options) {},
};
</script>
