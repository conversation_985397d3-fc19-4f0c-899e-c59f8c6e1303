<template>
  <div class="promotion-page">
    <u-navbar
      v-if="options.from === 'menu'"
      bgColor="transparent"
      leftIconSize="25px"
      leftIconColor="#fff"
      :autoBack="true"
    >
    </u-navbar>
    <div class="placeholder">
      <img :src="staticImg.bg" alt="" />
    </div>
    <div class="main">
      <img :src="staticImg.bg2" class="heads" alt="" />
      <perfectInfo
        :query="options"
        :key="reloadComIndex"
        v-if="customer_id === ''"
        @switchCom="handleSwitchCom"
        :departmentList="departmentList"
      ></perfectInfo>
      <surveySheet
        :query="options"
        :key="reloadComIndex"
        :surveyListParams="surveyListParams"
        v-else
        :customer_id="customer_id"
      ></surveySheet>
    </div>
  </div>
</template>
<script>
import { staticImg } from "./config/index";
import perfectInfo from "./components/perfectInfo.vue";
import surveySheet from "./components/surveySheet.vue";
import { getOpenId } from "@/services/login";
import { getDepartmentList } from "@/services/intention";
export default {
  name: "dtbIndex",
  components: { perfectInfo, surveySheet },
  data() {
    return {
      staticImg,
      reloadComIndex: 0,
      from: "menu",
      // cp5jcsqknrgc73bavmh0
      customer_id: "",
      surveyListParams: {},
      survey_student_id: "",
      options: null,
      departmentList: []
    };
  },
  computed: {},
  methods: {
    handleSwitchCom(obj) {
      this.surveyListParams = obj;
      this.customer_id = obj.customer_id;
      console.log(this.surveyListParams);
    },
    async arouseLogin() {
      uni.login({
        provider: "weixin",
        onlyAuthorize: true, // 微信登录仅请求授权认证
        success: async (event) => {
          const { code } = event;
          const res = await getOpenId({ code, UNAUTHORIZED: true });
          this.options.openid = res.data.openid;
          this.reloadComIndex++;
          console.log(
            this.options.openid === res.data.openid,
            this.options.openid,
            this.reloadComIndex
          );
        }
      });
    },
    urlToJson(url = window.location.href) {
      // 箭头函数默认传值为当前页面url
      const obj = {};
      const index = url.indexOf("?"); // 看url有没有参数
      const params = url.substr(index + 1); // 截取url参数部分 name = aaa & age = 20

      if (index !== -1) {
        // 有参数时
        const parr = params.split("&"); // 将参数分割成数组 ["name = aaa", "age = 20"]
        for (const i of parr) {
          // 遍历数组
          const arr = i.split("="); // 1） i name = aaa   arr = [name, aaa]  2）i age = 20  arr = [age, 20]
          obj[arr[0]] = arr[1]; // obj[arr[0]] = name, obj.name = aaa   obj[arr[0]] = age, obj.age = 20
        }
      }
      return obj;
    },
    getDepartmentList() {
      getDepartmentList({
        department_id: this.options.department_id,
        UNAUTHORIZED: true
      }).then((res) => {
        if (res.data) {
          this.departmentList = res.data.map((item) => {
            return {
              name: item.department_name,
              id: item.department_id
            };
          });
          console.log(this.departmentList);
        }
      });
    }
  },
  watch: {},

  // 页面周期函数--监听页面加载
  onLoad(options) {
    // this.options = options;
    // const options = { q: "https%3A%2F%2Ftg-dev.estar-go.com%2Fminiprogram%2Fdtb%2F%3Fopenid%3DoFb-75ODhpYb5X2ZvemiejwL56ek%26employee_id%3Dcnk5gnfta6ec73e04fq0%26organization_id%3D1", scancode_time: "1715146333" };
    this.from = options.from;
    if (options.from === "menu") {
      this.customer_id = options.customerId;
      this.options = options;
      this.surveyListParams = this.options;
    } else {
      uni.setStorageSync("portType", "STUDENT");
      uni.removeStorageSync("curStudentInfo");
      uni.removeStorageSync("session");
      const q = decodeURIComponent(options.q); // 获取到二维码原始链接内容
      const scancode_time = parseInt(options.scancode_time); // 获取用户扫码时间 UNIX 时间戳
      console.log(this.urlToJson(q));
      this.arouseLogin();
      this.options = this.urlToJson(q);
      console.log("q :>> ", q, this.urlToJson(q));
      console.log("scancode_time :>> ", scancode_time);
    }
    console.log("dtb_index_options :>> ", options, this.options);
    this.getDepartmentList();
  },

  // 页面周期函数--监听页面初次渲染完成
  onReady() {},
  // 页面周期函数--监听页面显示(not-nvue)
  onShow() {},
  // 页面周期函数--监听页面隐藏
  onHide() {},
  // 页面周期函数--监听页面卸载
  onUnload() {}
  // 页面处理函数--监听用户下拉动作
  // onPullDownRefresh() { uni.stopPullDownRefresh(); },
  // 页面处理函数--监听用户上拉触底
  // onReachBottom() {},
  // 页面处理函数--监听页面滚动(not-nvue)
  // onPageScroll(event) {},
  // 页面处理函数--用户点击右上角分享
  // onShareAppMessage(options) {},
};
</script>

<style lang="scss" scoped>
.promotion-page {
  width: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  .placeholder {
    width: 100%;
    height: 560rpx;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .main {
    width: 100%;
    height: 100%;
    flex: 1;
    position: relative;
    margin-top: -255rpx;
    background-color: #fff;
    border-top-left-radius: 40rpx;
    border-top-right-radius: 40rpx;
    padding-top: 25rpx;
    .heads {
      width: 240rpx;
      height: 45rpx;
      position: absolute;
      top: -20rpx;
      left: 50%;
      transform: translateX(-50%);
    }
  }
}
</style>

<style lang="scss" scoped>
/**index.wxss**/
.index {
  position: relative;
}
.top_background image {
  width: 750rpx;
  margin-top: -88rpx;
}
.hand image {
  width: 240rpx;
}
.hand {
  position: absolute;
  top: 167rpx;
  left: 255rpx;
  z-index: 1;
}
.content {
  position: absolute;
  top: 186rpx;
  left: 0;
  width: 100%;
  background-color: #fff;
  border-top-left-radius: 40rpx;
  border-top-right-radius: 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
}
.title {
  margin-top: 98rpx;
  line-height: 56rpx;
  height: 56rpx;
  font-size: 50rpx;
  font-weight: bold;
}
.tips {
  color: #7c8fa6;
  font-size: 26rpx;
  line-height: 32rpx;
  height: 32rpx;
  margin-top: 10rpx;
}
.content_write {
  width: 622rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.content_input {
  height: 96rpx;
  width: 100%;
  background-color: #f4f8fc;
  border-radius: 48rpx;
  display: flex;
  flex-direction: row;
  align-items: center;
}
.content_placeholder {
  color: #bac8d9;
  font-size: 32rpx;
}
.content_input text {
  font-size: 32rpx;
  border-right: 2rpx solid rgba(186, 200, 217, 0.25);
  height: 48rpx;
  line-height: 48rpx;
  margin-left: 48rpx;
  padding-right: 15rpx;
  font-weight: bold;
}
.content_input input {
  padding-left: 24rpx;
}
.content_input .picker {
  padding-left: 24rpx;
  width: 300rpx;
}
view.content_input + view.content_input {
  margin-top: 40rpx;
}
.content_write {
  padding-top: 80rpx;
}
.sex {
  display: flex;
  flex-direction: row;
  align-items: center;
  width: 100%;
  justify-content: space-around;
  margin-top: 45rpx;
}
.sex_border {
  width: 170rpx;
  height: 170rpx;
  border: 8rpx solid transparent;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  position: relative;
}
.sex image {
  width: 150rpx;
}
.sex_ac {
  border-color: #00baff;
}
.sex_box {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.sex_box text {
  font-size: 32rpx;
  font-weight: bold;
  margin-top: 10rpx;
}
image.sex_choose {
  width: 64rpx;
  position: absolute;
  bottom: -22rpx;
  right: -6rpx;
}

.btn button {
  width: 622rpx !important;
  border-radius: 48rpx;
  height: 96rpx;
  padding: 0;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
.btn .btn_one {
  background-color: #0bd67d;
  box-shadow: 0 6rpx 30rpx 0 rgba(20, 206, 115, 0.3);
}
.btn .btn_two {
  background-color: #00baff;
  color: #fff;
  box-shadow: 0 6rpx 30rpx 0 rgba(0, 186, 255, 0.3);
}
.btn button image {
  margin-right: 20rpx;
}
button[disabled][type="primary"] {
  background-color: rgba(0, 186, 255, 0.3) !important;
  color: #fff !important;
  box-shadow: none;
}
button + button {
  margin-top: 40rpx;
}

.btn_safe {
  width: 31rpx;
}
.btn {
  padding-top: 100rpx;
}
</style>
