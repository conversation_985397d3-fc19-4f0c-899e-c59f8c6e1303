<template>
  <div class="perfect-info">
    <u-toast ref="uToast"></u-toast>
    <div class="header">
      <div class="title">完善学员信息</div>
      <div class="subtitle">您的信息给予保密，请您放心填写</div>
    </div>

    <u--form
      labelPosition="left"
      class="content_write"
      :model="formModel"
      :rules="rules"
      ref="uForm"
    >
      <u-form-item prop="student_name">
        <div class="content_input">
          <div class="label">姓名</div>
          <u--input
            :customStyle="{ paddingLeft: '15rpx' }"
            placeholderClass="content_placeholder"
            border="none"
            placeholder="请输入姓名"
            v-model="formModel.student_name"
          ></u--input>
        </div>
      </u-form-item>
      <u-form-item prop="student_age" @tap="age_focus">
        <div class="content_input">
          <div class="label">年龄</div>
          <u--input
            :customStyle="{ paddingLeft: '15rpx' }"
            placeholderClass="content_placeholder"
            border="none"
            readonly
            placeholder="请选择年龄"
            v-model="formModel.age_txt"
          ></u--input>
          <u-picker
            v-if="age_show"
            :show="true"
            :columns="age_list"
            :immediateChange="true"
            @confirm="ageConfirm"
            @cancel="age_show = false"
          ></u-picker>
        </div>
      </u-form-item>
      <u-form-item prop="student_mobile">
        <div style="display: flex">
          <div class="content_input" style="width: 70%; margin-right: 5%">
            <div class="label">手机</div>
            <u--input
              :customStyle="{ paddingLeft: '15rpx' }"
              placeholderClass="content_placeholder"
              border="none"
              maxlength="11"
              type="number"
              placeholder="请输入手机号码"
              v-model="formModel.student_mobile"
            ></u--input>
          </div>
          <div class="btn" style="width: 25%">
            <button
              type="primary"
              class="btn_one"
              open-type="getPhoneNumber"
              @getphonenumber="getPhone"
            >
              获取手机号
            </button>
            <!-- <u-button type="primary" text="提交" @click="submit"></u-button> -->
            <!-- <div class="btn" @click="submit">
            <img :src="btnImages.save" alt="" />
          </div> -->
          </div>
        </div>
      </u-form-item>
      <!-- prop="department_name" @tap="department_focus" -->
      <u-form-item @tap="department_focus">
        <div class="content_input">
          <div class="label">校区</div>
          <span style="padding-left: 7px">{{ formModel.department_name }}</span>
          <!-- <u--input
            :customStyle="{ paddingLeft: '15rpx' }"
            placeholderClass="content_placeholder"
            border="none"
            placeholder="请选择校区"
            v-model="formModel.department_name"
          ></u--input> -->
        </div>
      </u-form-item>
      <u-form-item prop="starNum">
        <div class="content_input">
          <div class="label">意向级别</div>
          <div style="display: flex">
            <u-rate
              active-color="#FFBF0D"
              inactive-color="#b2b2b2"
              gutter="10"
              v-model="formModel.starNum"
            ></u-rate>
            <span style="margin-left: 10px">{{
              intentionLevelText[Number(formModel.starNum)]
            }}</span>
          </div>
        </div>
      </u-form-item>
      <u-form-item>
        <div class="sex">
          <div class="sex_box" @click="chooseSex('male')">
            <div
              :class="[
                'sex_border',
                formModel.student_gender == 'male' ? 'sex_ac' : ''
              ]"
            >
              <img class="sex-img" :src="staticImg.male" alt="" />
              <img
                :src="staticImg.checked"
                v-show="formModel.student_gender === 'male'"
                class="sex_choose"
                alt=""
              />
            </div>
            <text>男孩</text>
          </div>
          <div class="sex_box" @click="chooseSex('female')">
            <div
              :class="[
                'sex_border',
                formModel.student_gender == 'female' ? 'sex_ac' : ''
              ]"
            >
              <img class="sex-img" :src="staticImg.female" alt="" />
              <img
                :src="staticImg.checked"
                v-show="formModel.student_gender === 'female'"
                class="sex_choose"
                alt=""
              />
            </div>
            <text>女孩</text>
          </div>
        </div>
      </u-form-item>
      <u-form-item>
        <div class="btn">
          <!-- <button
            type="primary"
            class="btn_one"
            open-type="getPhoneNumber"
            @getphonenumber="getPhone"
          >
            <img
              :src="staticImg.btnIcon"
              class="btn_safe"
              alt=""
            />授权获取手机号
          </button> -->
          <!-- <u-button type="primary" text="提交" @click="submit"></u-button> -->
          <button
            type="primary"
            class="btn_two"
            @tap="$u.throttle(submit, 500)"
            hover-class="none"
          >
            提交
          </button>
          <!-- <div class="btn" @click="submit">
            <img :src="btnImages.save" alt="" />
          </div> -->
        </div>
      </u-form-item>
    </u--form>
    <u-action-sheet
      :show="showAge"
      :actions="ageList"
      title="请选择性别"
      @close="showSex = false"
      @select="sexSelect"
    >
    </u-action-sheet>
    <gq-tree
      ref="gqTree"
      :range="range"
      idKey="id"
      nameKey="name"
      childKey="child"
      pidKey="pid"
      allKey="pid"
      :showSearch="true"
      :showClose="true"
      :multiple="false"
      :cascade="true"
      :selectParent="false"
      :maskClick="false"
      :lessOne="true"
      confirmColor="#007aff"
      cancelColor="#757575"
      title="校区关键字"
      titleColor="#757575"
      @cancel="treeCancel"
      @confirm="treeConfirm"
    >
    </gq-tree>
  </div>
</template>

<script>
import { staticImg } from "../config/index";
import gqTree from "@/uni_modules/gq-tree/gq-tree.vue";
import {
  getSchoolData,
  customerCreate,
  getUserPhoneNumber,
  sendToCustomerSurvey
} from "@/services/student/dtb";
import { getIntentionLevelList } from "@/services/intention";
export default {
  name: "perfectInfo",
  props: {
    query: {
      type: Object,
      default: () => {
        return {};
      }
    },
    departmentList: {
      type: Array,
      default: () => {
        return [];
      }
    }
  },
  components: { gqTree },
  data() {
    return {
      showAge: false,
      age_show: false,
      isSingSchool: false,
      staticImg,
      rules: {
        student_name: {
          type: "string",
          required: true,
          message: "请填写姓名",
          trigger: ["blur", "change"]
        },
        student_age: {
          type: "number",
          required: true,
          message: "请选择年龄",
          trigger: ["blur", "change"]
        },
        student_mobile: [
          { required: true, message: "请输入手机号", trigger: "blur" },
          {
            validator: (rule, value, callback) => {
              return uni.$u.test.mobile(value);
            },
            message: "手机号码不正确",
            // 触发器可以同时用blur和change
            trigger: ["change", "blur"]
          }
        ]
      },
      starList: [],
      intentionLevelText: {
        1: "10%",
        2: "30%",
        3: "50%",
        4: "70%",
        5: "90%以上"
      },
      formModel: {
        UNAUTHORIZED: true,
        department_name: "",
        open_id: "",
        employee_id: "",
        starNum: 1,
        organization_id: "",
        intention_level_id: 1,
        intention_level_name: "1",
        student_mobile: "",
        student_gender: "male",
        channel_id: process.env.VUE_APP_CHANNEL_ID,
        sub_channel_id: process.env.VUE_APP_SUB_CHANNEL_ID,
        know_go: "no",
        department_id: ""
      },
      range: [],
      age_list: [[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]]
    };
  },
  computed: {},
  methods: {
    intentionLevelList() {
      getIntentionLevelList({ UNAUTHORIZED: true }).then(({ code, data }) => {
        if (code === 0) {
          this.starList = data.map((i) => ({ ...i, name: Number(i.name) }));
        }
      });
    },
    getSchool() {
      return new Promise((resolve, reject) => {
        getSchoolData({ UNAUTHORIZED: true })
          .then((res) => {
            resolve(res);
          })
          .catch((err) => {
            reject(err);
          });
      });
    },
    age_focus() {
      this.age_show = true;
    },
    ageConfirm(e) {
      this.formModel.student_age = e.value[0];
      this.formModel.age_txt = e.value[0] + "岁";
      const year = new Date().getFullYear();
      this.formModel.birth_day = year - e.value[0] + "-01-01";
      this.age_show = false;
    },
    chooseSex(sex) {
      this.formModel.student_gender = sex;
    },
    department_focus() {
      if (this.departmentList.length > 0) {
        this.schoolShowHandle();
      }
    },
    treeCancel(e) {
      this.$refs.gqTree._hide();
    },
    treeConfirm(e) {
      if (!e.length) {
        this.$refs.uToast.show({
          type: "info",
          message: "请至少选择一个校区！"
        });
        return;
      }
      console.log(e, "treeConfirm");
      this.formModel.department_id = e[0].id;
      this.formModel.department_name = e[0].name;
    },
    schoolShowHandle() {
      const schoolData = this.departmentList;
      // const checkedSchool = uni.getStorageSync("checkedSchool");
      console.log(schoolData);
      if (schoolData) {
        console.log(this.formModel.department_id);
        const idKeysResult = this.formModel.department_id;
        const options = schoolData;
        options.forEach((parent) => {
          if (parent.child) {
            // eslint-disable-next-line array-callback-return
            parent.child.map((item) => {
              item.pid = parent.id;
            });
          }
        });
        const newOptions = this.deepCheckValue(
          options,
          idKeysResult,
          "id",
          "child"
        );
        this.range = [...newOptions];
        this.$refs.gqTree._initTree();
        this.$refs.gqTree._show();
        console.log(this.range, "range");
        console.log(this.$refs.gqTree, "department_id");
      } else {
        this.$refs.uToast.show({
          type: "error",
          message: "获取缓存校区失败，请退出重新登录！"
        });
      }
    },
    deepCheckValue(options, values, idKey, childKey) {
      return options.map((i) => {
        if (values.indexOf(i[idKey]) > -1) {
          i.isGqAddChecked = true;
        } else {
          i.isGqAddChecked = false;
        }
        if (i[childKey] && i[childKey].length > 0) {
          this.deepCheckValue(i[childKey], values, idKey, childKey);
        }
        return i;
      });
    },
    getPhone(e) {
      getUserPhoneNumber({
        code: e.detail.code,
        openid: this.query.openid,
        UNAUTHORIZED: true
      }).then((res) => {
        console.log(res);
        const { code, data } = res;
        console.log(code, data);
        if (code === 0) {
          this.formModel.student_mobile = data.purePhoneNumber;
          console.log(this.formModel);
        }
      });
    },
    throttle(fn, threshold, scope) {
      threshold || (threshold = 250); // 默认阈值为250ms
      let last;
      let timer;
      return function () {
        const context = scope || this;
        const now = new Date();
        if (last && now - last < threshold) {
          // 如果距离上次执行的时间小于阈值，则清除定时器并重设执行时间
          clearTimeout(timer);
          timer = setTimeout(function () {
            last = now;
            fn.apply(context, arguments);
          }, threshold);
        } else {
          // 如果距离上次执行的时间大于或等于阈值，则直接执行函数
          last = now;
          fn.apply(context, arguments);
        }
      };
    },
    submit() {
      this.$refs.uForm.validate().then((res) => {
        this.createCustomer();
      });
    },
    sendToCustomer(customer_id) {
      sendToCustomerSurvey({
        UNAUTHORIZED: true,
        open_id: this.query.openid,
        customer_id
      }).then((res) => {
        console.log("发送问卷 ===>", res);
        const { code, data } = res;
        if (code === 0) {
          this.$emit("switchCom", data);
        } else {
          this.$refs.uToast.show({
            type: "error",
            message: "问卷已关闭！2秒后自动跳转到学生端"
          });
          setTimeout(() => {
            uni.switchTab({ url: "/pages/student/home/<USER>" });
            uni.setStorageSync("fromDTB", "1");
          }, 2000);
        }
      });
    },
    createCustomer() {
      this.formModel.init_status = "intention"; // 创建意向客户
      this.formModel.open_id = this.query.openid;
      this.formModel.employee_id = this.query.employee_id;
      this.formModel.organization_id = this.query.organization_id;
      const curLevelStart = this.starList.find(
        (i) => i.name === this.formModel.starNum
      );
      this.formModel.intention_level_id = curLevelStart.id;
      this.formModel.intention_level_name = curLevelStart.name;
      this.formModel.channel = this.query.channel;
      console.log(this.query);
      customerCreate(this.formModel)
        .then((res) => {
          console.log("创建意向 ===>", res);
          if (res.code === 2) {
            const { customer_id } = res.data[res.data.length - 1];
            this.sendToCustomer(customer_id);
          } else if (res.code === 0) {
            this.$refs.uToast.show({
              type: "success",
              message: "添加意向客户成功！"
            });

            const { customer_id } = res.data;
            this.sendToCustomer(customer_id);
          } else {
            this.$refs.uToast.show({
              type: "error",
              message: res.message
            });
          }
        })
        .catch((err) => {
          const { code, data, message } = err;
          this.errCode = code;
          if (code === 1) {
            this.$refs.uToast.show({
              type: "error",
              message
            });
          } else if (code === 2) {
            const { customer_id } = data[data.length - 1];
            this.$emit("switchCom", customer_id);
            this.sameMobileStuData = data ?? [];
            // this.$refs.uToast.show({
            //   type: "success",
            //   message: "添加意向客户成功！"
            // });
            this.student_repeat_visible = true;
          } else if (code === 3) {
            this.sameMobileStuData = data ?? [];
            this.$refs.uToast.show({
              type: "error",
              message: "学员已存在"
            });
            this.student_repeat_visible = true;
          }
        });
    }
  },
  watch: {},
  created() {
    this.intentionLevelList();
    // this.getSchool().then((res) => {
    //   this.schoolData = res.data.school_data;
    // });
  },
  // 组件周期函数--监听组件挂载完毕
  mounted() {
    console.log(this.query, "perfectInfo_query");
    this.formModel.department_id = this.query.department_id;
    this.formModel.department_name = this.query.department_name;
    this.formModel.channel_id = this.query.channel_on;
    this.formModel.sub_channel_id = this.query.channel_id;
    this.$refs.uForm.setRules(this.rules);
    console.log(this.formModel, "formModel");
  },
  onReady() {},
  // 组件周期函数--监听组件数据更新之前
  beforeUpdate() {},
  // 组件周期函数--监听组件数据更新之后
  updated() {},
  // 组件周期函数--监听组件激活(显示)
  activated() {},
  // 组件周期函数--监听组件停用(隐藏)
  deactivated() {},
  // 组件周期函数--监听组件销毁之前
  beforeUnmount() {}
};
</script>

<style lang="scss" scoped>
.perfect-info {
  padding: 80rpx 30rpx 30rpx 30rpx;
  .header {
    text-align: center;
    .title {
      line-height: 56rpx;
      height: 56rpx;
      font-size: 50rpx;
      font-weight: bold;
    }
    .subtitle {
      color: #7c8fa6;
      font-size: 26rpx;
      line-height: 32rpx;
      height: 32rpx;
      margin-top: 10rpx;
    }
  }

  .content_write {
    width: 100%;
  }
  .content_input {
    height: 96rpx;
    width: 100%;
    background-color: #f4f8fc;
    border-radius: 48rpx;
    display: flex;
    flex-direction: row;
    align-items: center;
    .label {
      font-size: 32rpx;
      border-right: 2rpx solid rgba(186, 200, 217, 0.25);
      height: 48rpx;
      line-height: 48rpx;
      margin-left: 48rpx;
      padding-right: 15rpx;
      font-weight: bold;
    }
    ::v-deep .u-input__content {
      padding-left: 15rpx;
    }
  }
  .content_placeholder {
    color: #bac8d9;
    font-size: 32rpx;
  }
  .sex {
    display: flex;
    flex-direction: row;
    align-items: center;
    width: 100%;
    justify-content: space-around;
    margin-top: 45rpx;
  }
  .sex_border {
    width: 170rpx;
    height: 170rpx;
    border: 8rpx solid transparent;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    position: relative;
  }
  .sex-img {
    width: 150rpx;
    height: 150rpx;
  }
  .sex_ac {
    border-color: #00baff;
  }
  .sex_box {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  .sex_box text {
    font-size: 32rpx;
    font-weight: bold;
    margin-top: 10rpx;
  }
  image.sex_choose {
    width: 64rpx;
    height: 64rpx;
    position: absolute;
    bottom: -22rpx;
    right: -6rpx;
  }
  .btn button {
    width: 100%;
    border-radius: 48rpx;
    height: 96rpx;
    padding: 0;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
  }
  .btn .btn_one {
    background-color: #0bd67d;
    box-shadow: 0 6rpx 30rpx 0 rgba(20, 206, 115, 0.3);
  }
  .btn .btn_two {
    background-color: #00baff;
    color: #fff;
    box-shadow: 0 6rpx 30rpx 0 rgba(0, 186, 255, 0.3);
  }
  .btn button img {
    margin-right: 20rpx;
  }
  button[disabled][type="primary"] {
    background-color: rgba(0, 186, 255, 0.3) !important;
    color: #fff !important;
    box-shadow: none;
  }
  button + button {
    margin-top: 40rpx;
  }

  .btn_safe {
    width: 31rpx;
    height: 31rpx;
  }
}
</style>
