<template>
  <div class="surveySheet">
    <u-toast ref="uToast"></u-toast>
    <div class="header">
      <div class="title">{{ sheetInfo.title }}</div>
    </div>
    <div class="content">
      <div class="topic-item" v-for="(item, index) in list" :key="item.field">
        <!--  v-if="![8, 9].includes(index)" -->
        <template>
          <div style="display: flex">
            <span>{{ index + 1 }}.</span>
            <div class="topic-name" v-html="item.title"></div>
          </div>
          <u-radio-group
            v-if="item.type === 'radio'"
            class="raido-hh"
            placement="column"
            v-model="item.value"
          >
            <u-radio
              :customStyle="{ marginBottom: '8px', marginRight: '40rpx' }"
              v-for="(i, k) in item.options"
              :key="k"
              :disabled="isDisabled"
              :label="i.text"
              :name="i.hash"
            >
            </u-radio>
          </u-radio-group>
          <u-checkbox-group
            v-else-if="item.type === 'checkbox'"
            v-model="item.value"
            placement="column"
          >
            <u-checkbox
              :customStyle="{ marginBottom: '8px', marginRight: '40rpx' }"
              v-for="(i, k) in item.options"
              :key="k"
              :label="i.text"
              :name="i.hash"
              :disabled="isDisabled"
            >
            </u-checkbox>
          </u-checkbox-group>
          <div v-else-if="item.type === 'date-str'">
            <div class="footer-item" style="margin-right: 10rpx">
              <div @click="openCalendar(index)" class="bb consult-date">
                {{ item.value }}
              </div>
            </div>
          </div>
          <div
            v-else-if="item.type === 'text'"
            class="bb consult-date"
            style="padding-bottom: 10rpx"
          >
            <u--input
              :disabled="isDisabled"
              v-if="query.from === 'menu'"
              @change="(val) => handleInput(val, index, item)"
              border="none"
              v-model="item.value"
            ></u--input>
            <span v-else>{{ value }}</span>
          </div>
        </template>
      </div>
      <div class="remark">
        非常感谢您信任聂卫平围棋道场，填写客户需求调研表，对于您登记信息我们将严格保密，保护您和孩子的信息安全！
      </div>
    </div>
    <!-- <div class="footer">
      <div class="footer-item" style="margin-right: 10rpx;">
        <label>咨询日期：</label>
        <div @click="openCalendar" class="bb consult-date">{{ consultDate }}</div>
      </div>
      <div class="footer-item">
        <label>课程顾问：</label>
        <div class="bb consult-date" style="padding-bottom: 10rpx;">
          <span>{{ value }}</span>
          <u--input
            @change="handleInput"
            border="none"
            v-model="value"
          ></u--input>
        </div>
      </div>
    </div> -->
    <u-button
      type="primary"
      text="提交"
      class="btn_one"
      v-if="!isDisabled"
      :throttleTime="500"
      @click="submit"
    ></u-button>
    <u-calendar
      :show="isShowCalendar"
      @confirm="confirm"
      @close="close"
    ></u-calendar>
    <u-popup
      :show="isShowPopup"
      :closeOnClickOverlay="false"
      :customStyle="styleObj"
      :safeAreaInsetBottom="false"
      :round="10"
      mode="center"
    >
      <div style="display: flex; align-items: center">
        <u-icon color="#19be6b" size="28" name="checkmark-circle"></u-icon>
        <span style="margin-left: 15rpx">调研表提交成功！</span>
      </div>
    </u-popup>
  </div>
</template>

<script>
import {
  surveyInfo,
  createSurveyInfo,
  customerSurveyInfo,
  customerCreateSurvey
} from "@/services/student/dtb";
export default {
  name: "surveySheet",
  props: {
    customer_id: {
      type: String,
      default: ""
    },
    surveyListParams: {
      type: Object
    },
    query: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      list: [],
      calendarIndex: 0,
      styleObj: {
        width: "200px",
        height: "100px",
        display: "flex",
        alignItems: "center",
        justifyContent: "center"
      },
      consultDate: "",
      value: "",
      isShowPopup: false,
      saveParams: {
        UNAUTHORIZED: true,
        surveyPath: "",
        open_id: "",
        questionData: [],
        customer_id: ""
      },
      sheetInfo: {},
      isShowCalendar: false
    };
  },
  computed: {
    isDisabled() {
      return this.query.fillStutas === "1";
    }
  },
  methods: {
    async getSurveyInfo() {
      console.log(this.query);
      const { code, data } =
        this.query?.from === "menu"
          ? await customerSurveyInfo(this.surveyListParams)
          : await surveyInfo({
              UNAUTHORIZED: true,
              ...this.surveyListParams
            });
      // const { code, data } = await surveyInfo({ UNAUTHORIZED: true, ...{ customer_id: "cp5ji5qknrgc73bavmj0", open_id: "oFb-75Hj6j196Cy8Wz-ROi3nZRJw", survey_id: "24052002", survey_student_id: 2670 } });
      console.log("data===>", data);
      this.getList(code, data);
    },
    getList(code, data) {
      const extractTextFromHTML = (htmlString) => {
        // 正则表达式匹配非标签字符
        const textMatches = htmlString.match(/(?<=>)[^<>]+/g);
        return textMatches ? textMatches.join("") : "";
      };
      if (code === 0) {
        if (this.query.from === "menu") {
          this.saveParams = { ...this.surveyListParams };
          this.saveParams.surveyStudentId = Number(
            this.saveParams.surveyStudentId
          );
          // this.saveParams.surveyPath = Number(this.saveParams.surveyPath);
        } else {
          this.saveParams.surveyPath = data.surveyPath;
          this.saveParams.survey_student_id =
            this.surveyListParams.survey_student_id;
          this.saveParams.customer_id = this.customer_id;
          this.saveParams.open_id = this.query.openid;
        }
        this.sheetInfo = data;
        const field = {
          checkbox: "checkboxResult",
          radio: "optionHash",
          textarea: "optionHash",
          text: "optionHash",
          "radio-star": "starSort",
          "date-str": "optionHash"
        };
        const getValue = (target, origin) => {
          const obj = target.map((i) => {
            return {
              ...i,
              ...origin.find((j) => i.field === j.field)
            };
          });
          return obj;
        };
        console.log(this.saveParams);
        const valueFill = (type, index) => {
          let result = "";
          if (type === "radio") {
            result = "";
          } else {
            if (type === "date-str") {
              result = uni.$u.timeFormat(new Date().getTime(), "yyyy-mm-dd");
            } else if (type === "text") {
              result = this.query.name;
            } else {
              result = [];
            }
          }
          return result;
        };

        if (data.isFill) {
          this.list = getValue(
            data.code.dataConf.dataList,
            data.questionFill
          ).map((i) => {
            return {
              ...i,
              value: i[field[i.questionType]],
              options: i.options
                ? i.options.map((j) => {
                    return {
                      ...j,
                      text: extractTextFromHTML(j.text)
                    };
                  })
                : undefined
            };
          });
        } else {
          this.list = data.code.dataConf.dataList.map((i, k) => {
            return {
              ...i,
              value: valueFill(i.type, k),
              options: i.options
                ? i.options.map((j) => {
                    return {
                      ...j,
                      text: extractTextFromHTML(j.text)
                    };
                  })
                : undefined
            };
          });
        }
        console.log(this.list);
      }
    },
    openCalendar(index) {
      if (this.query.from === "menu" && !this.isDisabled) {
        this.isShowCalendar = true;
        this.calendarIndex = index;
      }
    },
    confirm(e) {
      console.log(e);
      this.consultDate = e[0];
      for (let i = 0; i < this.list.length; i++) {
        console.log(i, this.list[i], this.list[i].field);
        if (i === 8) {
          this.list[this.calendarIndex].value = e[0];
        }
      }
      this.isShowCalendar = false;
    },
    handleInput(val, index, item) {
      this.list[index].value = val;
    },
    close() {
      this.isShowCalendar = false;
    },
    async submit() {
      const questionData = this.list.map((i) => {
        return {
          field: i.field,
          questionType: i.type,
          starSort: i.type === "radio-star" ? Number(i.value) : undefined,
          hash: [
            "radio",
            "textarea",
            "text",
            "binary-choice",
            "date-str"
          ].includes(i.type)
            ? String(i.value)
            : undefined,
          checkboxHash: ["checkbox"].includes(i.type)
            ? i.value.map((i) => String(i))
            : undefined
        };
      });
      this.saveParams.questionData = questionData;
      const { code, message } =
        this.query.from === "menu"
          ? await customerCreateSurvey(this.saveParams)
          : await createSurveyInfo(this.saveParams);
      // const { code } = await createSurveyInfo({ ...this.saveParams, ...{ customer_id: "cp5ji5qknrgc73bavmj0", open_id: "oFb-75Hj6j196Cy8Wz-ROi3nZRJw", survey_id: "24052002", survey_student_id: 2670 } });
      if (code === 0) {
        this.$refs.uToast.show({
          type: "success",
          message: "提交成功！"
        });
        // this.isShowPopup = true;
        setTimeout(() => {
          uni.switchTab({ url: "/pages/student/home/<USER>" });
          uni.setStorageSync("fromDTB", "1");
        }, 2000);
      } else {
        this.$refs.uToast.show({
          type: "error",
          message
        });
      }
      console.log(questionData, this.saveParams);
    }
  },
  watch: {},
  created() {
    this.getSurveyInfo();
  },
  // 组件周期函数--监听组件挂载完毕
  mounted() {
    console.log(this.query, "surverSheet_query");
    this.value = this.query.name;
    this.consultDate = uni.$u.timeFormat(new Date().getTime(), "yyyy-mm-dd");
  },
  // 组件周期函数--监听组件数据更新之前
  beforeUpdate() {},
  // 组件周期函数--监听组件数据更新之后
  updated() {},
  // 组件周期函数--监听组件激活(显示)
  activated() {},
  // 组件周期函数--监听组件停用(隐藏)
  deactivated() {},
  // 组件周期函数--监听组件销毁之前
  beforeUnmount() {}
};
</script>

<style lang="scss" scoped>
.surveySheet {
  padding: 15rpx 20rpx 40rpx 20rpx;
  .header {
    padding: 25rpx 0;
    .title {
      font-size: 48rpx;
      font-weight: 600;
      text-align: center;
      text-shadow: #999 3px 3px 9px;
      color: #333;
    }
  }
  .content {
    // padding: 0 25rpx;
    ::v-deep .u-radio-group {
      flex-wrap: wrap !important;
    }
    ::v-deep .u-radio {
      margin-right: 40rpx !important;
    }
    .topic-item {
      margin: 15rpx 0;
      .topic-name {
        margin-bottom: 15rpx;
      }
    }
  }
  .bb {
    width: 185rpx;
    height: 40rpx;
    border-bottom: 1px solid #333;
    font-size: 30rpx;
  }
  .footer {
    display: flex;
    justify-content: center;
    margin: 35rpx 0;
    .footer-item {
      display: flex;
    }
  }
}
</style>
