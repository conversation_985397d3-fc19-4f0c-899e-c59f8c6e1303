<template>
  <view class="family-group">
    <!-- 顶部导航栏 -->
    <u-navbar
      :title="modifyVisible ? '修改绑定关系' : '家庭组'"
      bgColor="#fff"
      leftIconSize="25px"
      leftIconColor="#333333"
      :titleStyle="{
        color: '#333333',
        fontSize: '34rpx',
        fontWeight: '500',
        lineHeight: '40rpx'
      }"
      placeholder
      @leftClick="handleBack"
      :autoBack="!modifyVisible"
    >
    </u-navbar>

    <!-- 家庭成员列表 -->
    <view class="member-list">
      <!-- 主账号(妈妈) -->
      <!-- <view class="member-card primary">
        <view class="member-info">
          <text class="member-name">妈妈</text>
          <text class="member-phone">{{
            desensitizePhone("18835744963")
          }}</text>
        </view>
        <view class="member-tag">主账号</view>
      </view> -->

      <!-- 其他成员 -->
      <view
        class="member-card"
        v-for="item in familyMembers"
        :key="item.open_id"
        :style="{
          backgroundImage:
            item.main_number === 1
              ? `url(https://tg-prod.oss-cn-beijing.aliyuncs.com/24caee44-5e33-4c94-95e0-b1704f7477a8.webp)`
              : `url(https://tg-prod.oss-cn-beijing.aliyuncs.com/98c663ff-3395-4d0e-be9d-8a4e2536e740.webp)`
        }"
      >
        <view
          class="member-info"
          :class="{
            primaryItem: item.main_number !== 1
          }"
        >
          <view class="member-name"
            ><text>{{ item.role_chn }} </text>
            <image
              class="icon"
              v-if="session.open_id === item.open_id && item.main_number === 1"
              src="https://tg-prod.oss-cn-beijing.aliyuncs.com/3681a140-d398-465b-99ca-685ac7ac0f74.webp"
            ></image>
            <image
              class="icon"
              v-if="session.open_id === item.open_id && item.main_number !== 1"
              src="https://tg-prod.oss-cn-beijing.aliyuncs.com/1e630600-ea66-422c-9522-0de72a71b5b7.webp"
            ></image>
          </view>
          <text class="member-phone">{{ desensitizePhone(item.mobile) }}</text>
        </view>
        <view class="member-tag" v-if="item.main_number === 1 && !modifyVisible"
          >主账号</view
        >
        <view class="confirm-btn" v-if="modifyVisible">
          <view
            class="confirm-btn-text"
            @tap="editMember(item)"
            :style="
              item.main_number === 1 ? 'color: #795A00' : 'color: #3459AA;'
            "
            ><u-icon
              size="22"
              width="24rpx"
              height="24rpx"
              :name="
                item.main_number !== 1
                  ? 'https://tg-prod.oss-cn-beijing.aliyuncs.com/fb703bd7-57fa-476f-a264-40a504011f5e.webp'
                  : 'https://tg-prod.oss-cn-beijing.aliyuncs.com/9e0c2d76-aa14-4776-8e47-8e8f7f36ae54.webp'
              "
              :color="item.main_number === 1 ? '#795A00' : '#3459AA'"
            ></u-icon>
            <view> 修改 </view>
          </view>
          <view
            class="confirm-btn-text"
            v-if="item.main_number !== 1"
            @tap="deleteMember(item)"
            :style="
              item.main_number === 1 ? 'color: #795A00' : 'color: #3459AA;'
            "
          >
            <u-icon
              size="22"
              width="24rpx"
              height="24rpx"
              name="https://tg-prod.oss-cn-beijing.aliyuncs.com/18e31adf-dbed-4169-abf6-2b40acbcb16b.webp"
              :color="item.main_number === 1 ? '#795A00' : '#3459AA'"
            ></u-icon>
            <view>解绑</view>
          </view>
        </view>
      </view>
      <!-- 底部按钮 -->
    </view>
    <view class="bottom-buttons">
      <view class="u-safe-area-inset-bottom">
        <button
          v-if="!modifyVisible && mainRole"
          class="invite-btn"
          @tap="showInviteModal"
          :class="{ disabled: session.role === 'customer' || disabled }"
        >
          邀请亲属绑定
        </button>
        <button class="modify-btn" @tap="goToModify">
          {{ modifyVisible ? "取消修改" : "修改绑定关系" }}
        </button>
      </view>
    </view>
    <!-- 邀请弹窗 -->
    <invite-modal
      :visible="inviteModalVisible"
      @close="closeInviteModal"
      @confirm="confirmInvite"
      :relationList="relationList"
      :relationOptions="relationOptions"
      :modifyVisible="modifyVisible"
      :modifyMember="modifyMember"
      ref="inviteModal"
    />
    <sub-role
      v-model="subRoleVisible"
      @close="closeSubRole"
      @confirm="confirmSubRole"
      :roleData="roleData"
      :roleName="roleName"
    />
  </view>
</template>

<script>
import {
  getFamilyList,
  updateBindRole,
  malnUumunBinduser
} from "@/services/student/family";
import InviteModal from "./components/InviteModal.vue";
import { bindRole } from "@/services/student/home";
import subRole from "./components/subRole.vue";
import { getStudentDetail } from "@/services/student/my";
export default {
  name: "ModifyRelation",
  components: {
    InviteModal,
    subRole
  },
  data() {
    return {
      height: 180,
      inviteModalVisible: false,
      familyMembers: [],
      relationList: [],
      relationOptions: [],
      subRoleVisible: false,
      curStudentInfo: {},
      session: {},
      // 切换的类型
      modifyVisible: false,
      modifyMember: {},
      // 删除的id
      deleteMemberId: "",
      id: {},
      mainRole: false,
      roleData: {},
      roleName: "",
      disabled: false
    };
  },
  methods: {
    handleBack() {
      console.log("返回", this.modifyVisible);
      if (this.modifyVisible) {
        // 滚动到顶部
        uni.pageScrollTo({
          scrollTop: 0,
          duration: 300
        });
        this.modifyVisible = false;
      } else {
        uni.navigateBack();
      }
    },
    showInviteModal() {
      if (this.session.role === "customer" || this.disabled) {
        return;
      }
      this.inviteModalVisible = true;
    },
    closeInviteModal() {
      this.inviteModalVisible = false;
    },
    async confirmInvite(relation) {
      console.log("确认邀请:", relation);

      if (this.modifyVisible) {
        let id;
        if (this.session.role === "student") {
          id = {
            student_id: this.curStudentInfo.student_id
          };
        } else if (this.session.role === "customer") {
          id = {
            customer_id: this.curStudentInfo.customer_id
          };
        }
        const res = await updateBindRole({
          role_id: relation.role,
          save_open_id: this.modifyMember.open_id,
          ...id
        });
        if (res.code === 0) {
          uni.$u.toast("修改成功");
          await this.getFamilyList();
          this.modifyVisible = false;
        } else {
          uni.$u.toast(res.message, {
            duration: 2000
          });
        }
      } else {
        console.log("邀请");
      }
      this.inviteModalVisible = false;
    },
    getRelationLabel(value) {
      // 和学员关系 1本人 2妈妈 3爸爸 4爷爷 5奶奶 6姥爷 7姥姥 8家长
      const relationMap = {
        1: "本人",
        2: "妈妈",
        3: "爸爸",
        4: "爷爷",
        5: "奶奶",
        6: "姥爷",
        7: "姥姥",
        8: "家长"
      };
      return relationMap[value] || "";
    },
    goToModify(member) {
      this.modifyVisible = !this.modifyVisible;
    },
    async getFamilyList() {
      this.id = {};
      if (this.session.role === "student") {
        this.id = {
          student_id: this.curStudentInfo.student_id
        };
      } else if (this.session.role === "customer") {
        this.id = {
          customer_id: this.curStudentInfo.customer_id
        };
      }
      const { code, data, message } = await getFamilyList({
        ...this.id
      });
      if (code === 0) {
        this.familyMembers = Array.isArray(data)
          ? [...data].sort(
              (a, b) => (b.main_number === 1) - (a.main_number === 1)
            )
          : data;
        // 当前账号是否为主账号
        this.mainRole = this.familyMembers.find((item) => {
          if (item.main_number === 1) {
            return item.open_id === this.session.open_id;
          } else {
            return false;
          }
        });
        // 获取当前列表中有什么关系
        this.relationList = this.familyMembers.map((item) => item.role);
      } else {
        uni.$u.toast(message);
      }
    },
    // 手机号脱敏
    desensitizePhone(phone) {
      if (!phone) return "";
      return phone.replace(/(\d{3})(\d{4})(\d{4})/, "$1****$3");
    },
    async getRoleList() {
      this.relationOptions = [
        {
          role_id: 1,
          role_name: "本人"
        },
        {
          role_id: 2,
          role_name: "妈妈"
        },
        {
          role_id: 3,
          role_name: "爸爸"
        },
        {
          role_id: 4,
          role_name: "爷爷"
        }
      ];
      const { code, data, message } = await bindRole();
      if (code === 0) {
        this.relationOptions = data || [
          {
            role_id: 1,
            role_name: "本人"
          },
          {
            role_id: 2,
            role_name: "妈妈"
          },
          {
            role_id: 3,
            role_name: "爸爸"
          },
          {
            role_id: 4,
            role_name: "爷爷"
          }
        ];
      } else {
        this.$refs.uToast.show({ type: "error", message });
      }
    },
    closeSubRole() {
      this.subRoleVisible = false;
      this.deleteMemberId = "";
    },
    async confirmSubRole(item) {
      console.log("确认绑定关系：", item);

      const res = await malnUumunBinduser({
        ...this.id,
        un_open_id: item.open_id
      });
      if (res.code === 0) {
        this.subRoleVisible = false;
        await this.getFamilyList();
      } else {
        uni.$u.toast(res.message, {
          duration: 2000
        });
      }
    },
    editMember(member) {
      console.log("修改成员:", member);
      this.inviteModalVisible = true;
      this.modifyMember = member;
    },
    async deleteMember(roleData) {
      this.subRoleVisible = true;
      this.roleData = roleData;
      this.roleName = this.getRelationLabel(roleData.role);
      this.deleteMemberId = roleData.open_id;
    },
    async getStudentInfo() {
      if (this.session.role === "student") {
        const { data } = await getStudentDetail({
          student_id: this.curStudentInfo.student_id
        });
        this.disabled = data.student_type === "drop_school";
      }
    }
  },
  mounted() {
    this.curStudentInfo = uni.getStorageSync("curStudentInfo");
    this.session = uni.getStorageSync("session");
    this.getStudentInfo();
    this.getFamilyList();
    this.getRoleList();
    uni.hideShareMenu();
  },
  onShareAppMessage(res) {
    console.log("onShareAppMessage", res);
    // 添加当前时间戳作为分享参数
    const shareTime = Date.now();

    return {
      title: "你的好友邀请你和他绑定，绑定后可查看孩子学习状态哦",
      path: `/pages/student/subpages/familyShare/index?roleId=${
        res.target.dataset.role
      }&&id=${JSON.stringify(this.id)}&&studentName=${
        this.curStudentInfo.student_name
      }&&openId=${this.session.open_id}&&shareTime=${shareTime}`,
      imageUrl:
        "https://tg-prod.oss-cn-beijing.aliyuncs.com/f860ad82-95fd-49c4-a8de-c629f069411b.png", // 分享图标
      success: function (res) {
        // 用户确认分享后执行的回调函数
        console.log("分享成功", res);
      },
      fail: function (res) {
        // 用户取消分享后执行的回调函数
        console.log("分享失败", res);
      }
    };
  }
};
</script>

<style lang="scss" scoped>
.family-group {
  min-height: 100vh;
  background: #fff;
  padding: 0 32rpx;
}
.member-list {
  // margin-top: 20rpx;
  // height: calc(100vh + 250rpx);
  margin-bottom: 300rpx;
}

.member-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  // background: #fff;
  border-radius: 16rpx;
  margin-top: 30rpx;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  height: 192rpx;
  &.primary {
    background: #ffe4b5;
  }
  .primaryItem {
    color: #3459aa !important;
  }
  .member-info {
    display: flex;
    flex-direction: column;
    // justify-content: space-between;
    align-content: space-around;
    height: 100rpx;
    color: #795a00;
    .member-name {
      font-size: 32rpx;
      font-style: normal;
      font-weight: 500;
      line-height: normal;
      display: flex;
      .icon {
        width: 80rpx;
        height: 40rpx;
        margin-left: 14rpx;
      }
    }

    .member-phone {
      font-size: 28rpx;
      font-style: normal;
      font-weight: 400;
      margin-top: 4rpx;
      // line-height: 36px;
    }
  }

  .member-tag {
    display: inline-flex;
    padding: 12rpx 25rpx;
    justify-content: center;
    align-items: center;
    border-radius: 43rpx;
    background: #fff;
    color: #fb0;
    text-align: center;
    font-size: 26rpx;
    font-style: normal;
    font-weight: 500;
    line-height: 28rpx;
  }
  .confirm-btn {
    display: flex;
    // gap: 20rpx;
    .confirm-btn-text {
      display: flex;
      align-items: center;
      gap: 10rpx;
      color: #795a00;
      font-size: 24rpx;
      font-style: normal;
      font-weight: 400;
      line-height: 28rpx;
      margin-left: 46rpx;
    }
  }
}

.bottom-buttons {
  // margin-top: 160rpx;
  position: fixed;
  bottom: 30rpx;
  left: 0;
  width: 100%;
  padding: 18rpx 32rpx;
  background: #fff;
  .disabled {
    opacity: 0.5;
  }
  .invite-btn {
    width: 100%;
    height: 92rpx;
    line-height: 92rpx;
    border-radius: 80px;
    background: linear-gradient(15deg, #ffbf0d 18.1%, #ffcb3c 83.29%);
    box-shadow: 0px -10px 18px 0px #f3b300 inset,
      0px 4px 20px 0px rgba(254, 197, 36, 0.47);
    color: #fff;
    text-align: center;
    font-size: 32rpx;
    font-style: normal;
    font-weight: 500;
    margin-bottom: 48rpx;
  }

  .modify-btn {
    width: 100%;
    height: 92rpx;
    line-height: 92rpx;
    border-radius: 80px;
    border: 1rpx solid #fb0;
    background: #fff;
    box-shadow: 0px 4rpx 20rpx 0px rgba(254, 197, 36, 0.12);
    color: #fb0;
    text-align: center;
    font-size: 32rpx;
    font-style: normal;
    font-weight: 500;
  }
}
::v-deep .u-safe-area-inset-bottom {
  padding: 0 !important;
}
.nav-letf {
  width: 40rpx;
  height: 40rpx;
  image {
    width: 100%;
    height: 100%;
  }
}
</style>
