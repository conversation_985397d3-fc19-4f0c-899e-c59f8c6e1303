<template>
  <u-popup
    :show="show"
    @close="handleClose"
    mode="center"
    :round="10"
    :customStyle="{
      borderRadius: '32rpx',
      background: '#FFFFFF',
      fontSize: '34rpx',
      width: '582rpx'
    }"
  >
    <view class="bind-popup">
      <view class="content">
        <text class="title"> {{ `是否确认解绑${roleName}学员？` }}</text>
        <view class="btn-group">
          <button class="cancel-btn" @tap="handleClose">取消</button>
          <button class="confirm-btn" @tap="$u.throttle(handleConfirm, 500)">
            确定
          </button>
        </view>
      </view>
    </view>
  </u-popup>
</template>

<script>
export default {
  name: "unbindSubmit",
  props: {
    value: {
      type: Boolean,
      default: false
    },
    studentInfo: {
      type: Object,
      default: () => {}
    },
    roleName: {
      type: String,
      default: ""
    },
    roleData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      curStudentInfo: {},
      session: {}
    };
  },
  computed: {
    show: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit("input", val);
      }
    }
  },
  methods: {
    handleClose() {
      this.show = false;
      this.$emit("cancel");
    },
    handleConfirm() {
      this.$emit("confirm", this.roleData);
      // this.show = false;
    }
  },
  async mounted() {
    this.curStudentInfo = uni.getStorageSync("curStudentInfo");
    this.session = uni.getStorageSync("session");
  }
};
</script>

<style lang="scss" scoped>
.bind-popup {
  padding: 48rpx 40rpx 0;

  .content {
    display: flex;
    flex-direction: column;
    align-items: center;

    .title {
      color: #333;
      text-align: center;
      font-family: PingFang SC;
      font-size: 36rpx;
      font-weight: 500;
      line-height: 56rpx;
      margin-bottom: 70rpx;
    }

    .btn-group {
      display: flex;
      gap: 20rpx;
      width: 100%;
      margin-bottom: 60rpx;
      .cancel-btn,
      .confirm-btn {
        flex: 1;
        height: 88rpx;
        border-radius: 44rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 32rpx;
        font-weight: 500;
        transition: transform 0.2s ease;

        &:active {
          transform: scale(0.98);
        }
      }

      .cancel-btn {
        background: #fff;
        color: #ffbb00;
        border: 3rpx solid #ffbb00;
      }

      .confirm-btn {
        background: linear-gradient(15deg, #ffbf0d 18.1%, #ffcb3c 83.29%);
        box-shadow: 0px -5rpx 12rpx 0px #fc0 inset,
          0px 9rpx 20rpx 0px #fff7e1 inset;
        filter: drop-shadow(0px 4rpx 4rpx rgba(255, 192, 18, 0.11));
        color: #fff;
      }
    }
  }
}
</style>
