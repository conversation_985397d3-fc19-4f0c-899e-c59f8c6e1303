<template>
  <view @touchmove.stop.prevent="">
  <u-popup
    :show="visible"
    mode="center"
    width="600rpx"
    border-radius="20"
    :mask-close-able="true"
    @close="handleClose"
    round="35rpx"
  >
    <view class="invite-modal">
      <!-- 标题和关闭按钮 -->
      <view class="modal-header close">
        <view class="close-icon" @tap="handleClose">
          <image
            class="close-x"
            src="https://tg-prod.oss-cn-beijing.aliyuncs.com/6896ad4d-0cb3-4970-9016-24f611a2a67a.webp"
          />
        </view>
      </view>
      <view class="modal-header title">
        <text class="modal-title">
          {{ modifyVisible ? "请选择绑定关系" : "请选择邀请对象" }}
        </text>
      </view>

      <!-- 关系选择网格 -->
      <view class="relation-grid">
        <view
          v-for="(item, index) in relationOptions"
          :key="index"
          class="relation-item"
          :class="{
            disabled: relationList.includes(item.role_id) && item.role_id !== 8,
            active: selectedRelation === item.role_id
          }"
          @tap="selectRelation(item.role_id)"
        >
          {{ item.role_name }}
        </view>
      </view>

      <!-- 确定按钮 -->
      <view
        class="confirm-btn-wrapper"
        :class="{ disabled: !selectedRelation }"
      >
        <button class="confirm-btn" @tap="handleConfirm" v-if="modifyVisible">
          确定
        </button>
        <button
          class="confirm-btn"
          @tap="handleConfirm"
          open-type="share"
          :data-role="selectedRelation"
          v-else
        >
          确定
        </button>
      </view>
    </view>
  </u-popup>
  </view>
</template>

<script>
export default {
  name: "InviteModal",
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    relationList: {
      type: Array,
      default: () => []
    },
    relationOptions: {
      type: Array,
      default: () => []
    },
    modifyVisible: {
      type: Boolean,
      default: false
    },
    modifyMember: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      selectedRelation: ""
    };
  },
  methods: {
    handleClose() {
      this.$emit("close");
      this.selectedRelation = "";
    },
    selectRelation(value) {
      if (
        this.relationList.length > 0 &&
        this.relationList.includes(value) &&
        value !== 8
      ) {
        return;
      }
      this.selectedRelation = value;
    },
    handleConfirm() {
      if (!this.selectedRelation) {
        uni.showToast({
          title: "请选择邀请对象",
          icon: "none",
          duration: 2000
        });
        return;
      }
      if (this.modifyVisible) {
        console.log("修改", this.modifyMember, this.selectedRelation);
        this.$emit("confirm", {
          ...this.modifyMember,
          role: this.selectedRelation
        });
      } else {
        // this.modifyMember.role = this.selectedRelation;
        this.$emit("confirm", this.selectedRelation);
      }
      this.selectedRelation = "";
    }
  },
  mounted() {}
};
</script>

<style lang="scss" scoped>
.invite-modal {
  padding: 60rpx 30rpx 50rpx 30rpx;

  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 40rpx;

    .modal-title {
      color: #333;
      text-align: center;
      font-size: 36rpx;
      font-style: normal;
      font-weight: 500;
      line-height: 56rpx;
    }

    .close-icon {
      width: 100rpx;
      height: 100rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      position: absolute;
      right: 0;
      .close-x {
        font-size: 40rpx;
        width: 30rpx;
        height: 30rpx;
        color: #cccccc;
        line-height: 1;
        font-weight: 500;
      }
    }
  }

  .relation-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20rpx;

    .relation-item {
      width: 156rpx;
      height: 82rpx;
      background-color: #f6f6f6;
      border: 2rpx solid #d4d4d4;
      border-radius: 10rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #666;
      text-align: center;
      font-size: 30rpx;
      font-style: normal;
      font-weight: 400;
      line-height: 56rpx;
      &.active {
        background-color: #fff8e0;
        color: #fb0;
        border: 1px solid #fb0;
      }
    }
  }

  .confirm-btn-wrapper {
    margin-top: 60rpx;

    .confirm-btn {
      width: 238rpx;
      height: 88rpx;
      line-height: 88rpx;
      background: linear-gradient(15deg, #ffbf0d 18.1%, #ffcb3c 83.29%);
      box-shadow: 0px -5rpx 12rpx 0rpx #fc0 inset,
        0px 9px 20px 0px #fff7e1 inset;
      filter: drop-shadow(0px 4rpx 4rpx rgba(255, 192, 18, 0.11));
      border-radius: 44rpx;
      color: #fff;
      text-align: center;
      font-size: 34rpx;
      font-style: normal;
      font-weight: 500;
      margin: 0 auto;
    }
  }
  .close {
    justify-content: flex-end;
    margin: 0;
  }
  .title {
    justify-content: center;
  }
  .disabled {
    opacity: 0.5;
  }
}
</style>
