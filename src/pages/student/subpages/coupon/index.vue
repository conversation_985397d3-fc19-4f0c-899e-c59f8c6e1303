<template>
  <div class="coupon-page">
    <custom-tabs
      :list="tabs"
      :current="current"
      @change="handleTabChange"
      :activeColor="'#FFC525'"
    ></custom-tabs>
    <div class="coupon-list-wrap">
      <div class="coupon-list">
        <!-- <u-list v-if="couponList.length">
          <u-list-item v-for="(item, index) in couponList" :key="index"> -->
        <view
          v-for="(item, index) in couponList"
          :key="index"
          class="coupon-item"
        >
          <!-- 左侧金额/折扣区域 -->
          <view class="coupon-left" :class="tabs[current].status">
            <view class="coupon-value">
              <text v-if="item.coupon_type !== 2" class="symbol">¥</text>
              <view class="number"
                >{{
                  item.coupon_type === 1
                    ? item.discount_price / 100
                    : item.discount_price / 10
                }}
                <view class="text-num">
                  {{ item.coupon_type === 2 ? "折" : "" }}
                </view>
              </view>
            </view>
            <view class="coupon-condition" v-if="item.used_threshold > 0">
              满{{ item.used_threshold / 100 }}元可用</view
            >
            <view class="coupon-condition" v-else> 无使用门槛</view>
          </view>

          <!-- 右侧详情区域 -->
          <view class="coupon-right" :class="tabs[current].status">
            <view>
              <view class="coupon-name">{{ item.name }}</view>
              <view class="coupon-validity"
                >有效期：{{ formatTime(item.end_time) }}前使用</view
              >
            </view>
          </view>
        </view>
        <!-- </u-list-item>
        </u-list> -->
        <!-- 空组件 -->
        <view v-if="showEmpty && couponList.length === 0" class="empty-wrap">
          <empty text="暂无数据~" />
        </view>
      </div>
    </div>
  </div>
</template>

<script>
import { getCouponList } from "@/services/student/coupon";
import CustomTabs from "@/components/tabs/index.vue";
import Empty from "@/components/empty/index.vue";
export default {
  name: "CouponIndex",
  components: { CustomTabs, Empty },
  data() {
    return {
      showEmpty: false,
      curStudentInfo: {},
      current: 0,
      listParams: {
        sort: "created_at",
        page: 1,
        page_size: 50,
        status: 1,
        student_id: "",
        customer_id: ""
      },
      couponList: [],
      tabs: [
        {
          name: "未使用",
          value: 1,
          status: "unused"
        },
        {
          name: "已使用",
          value: 2,
          status: "used"
        },
        {
          name: "已过期",
          value: 3,
          status: "expired"
        }
      ]
    };
  },
  computed: {},
  methods: {
    async getList() {
      uni.showLoading({
        title: "查询中"
      });
      // this.listParams.student_id = this.curStudentInfo.student_id;
      if (this.session.role === "student") {
        this.listParams.student_id = this.curStudentInfo.student_id;
      } else if (this.session.role === "customer") {
        this.listParams.customer_id = this.curStudentInfo.customer_id;
      }
      try {
        const { code, data } = await getCouponList(this.listParams);
        if (code === 0) {
          this.couponList = data.results || [];
        } else {
          this.couponList = [];
        }
      } catch (error) {
        console.log("获取优惠券列表失败", error);
        this.couponList = [];
      } finally {
        uni.hideLoading();
        this.showEmpty = this.couponList.length === 0;
      }
    },
    // scrolltolower() {
    //   console.log("到底了", this.couponList.length, this.count);
    //   if (this.couponList.length >= this.count) {
    //     this.status = "nomore";
    //     return;
    //   }
    //   this.listParams.page++;
    //   this.getList();
    // },

    handleTabChange(index) {
      this.current = index;
      this.listParams.page = 1;
      this.listParams.status = this.tabs[index].value;
      this.couponList = [];
      this.getList();
    },
    formatTime(time) {
      if (!time) return "";
      const date = new Date(time);
      const year = date.getFullYear();
      const month = date.getMonth() + 1;
      const day = date.getDate();
      return `${year}-${month}-${day}`;
    }
  },
  watch: {},

  // 页面周期函数--监听页面加载
  onLoad() {
    this.curStudentInfo = uni.getStorageSync("curStudentInfo");
    this.session = uni.getStorageSync("session");
    this.getList();
    uni.hideShareMenu();
  },
  // 页面周期函数--监听页面初次渲染完成
  onReady() {},
  // 页面周期函数--监听页面显示(not-nvue)
  onShow() {},
  // 页面周期函数--监听页面隐藏
  onHide() {},
  // 页面周期函数--监听页面卸载
  onUnload() {}
};
</script>

<style lang="scss" scoped>
.coupon-page {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  background: #f5f5f5;
  .coupon-tabs {
    box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.05);
  }

  .coupon-list-wrap {
    .coupon-list {
      height: calc(100vh - 140rpx);
      overflow-y: auto;
      padding: 0 30rpx;
      padding-bottom: 100rpx;
    }
    .coupon-item {
      display: flex;
      margin-top: 30rpx;
      border-radius: 12rpx;
      overflow: hidden;
      position: relative;
      height: 174rpx;
      .coupon-left {
        width: 210rpx;
        // background: linear-gradient(to right, #ffc107, #ff9800);
        padding: 20rpx;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        // background: url("https://tg-prod.oss-cn-beijing.aliyuncs.com/77c32a7a-b1d3-4e5c-a21f-4e829645f679.webp")
        //   no-repeat;
        background-size: 100% 100%;
        background-repeat: no-repeat;
        &.unused {
          background-image: url("https://tg-prod.oss-cn-beijing.aliyuncs.com/77c32a7a-b1d3-4e5c-a21f-4e829645f679.webp");
        }
        &.used {
          background-image: url("https://tg-prod.oss-cn-beijing.aliyuncs.com/cff862a1-5055-4616-8ea3-af4497b0aba0.png");
        }
        &.expired {
          background-image: url("https://tg-prod.oss-cn-beijing.aliyuncs.com/cff862a1-5055-4616-8ea3-af4497b0aba0.png");
        }
        .coupon-value {
          display: flex;
          align-items: baseline;

          .symbol {
            color: #fff;
            font-size: 36rpx;
            font-style: normal;
            font-weight: 400;
            line-height: normal;
          }

          .number {
            color: #fff;
            font-size: 56rpx;
            font-style: normal;
            font-weight: 500;
            line-height: normal;
            display: flex;
            align-items: center;
            justify-content: center;
          }
          .text-num {
            color: #fff;
            font-size: 36rpx;
            font-style: normal;
            font-weight: 500;
            line-height: normal;
            margin-left: 4rpx;
          }
        }

        .coupon-condition {
          color: #fff;
          text-align: center;
          font-size: 26rpx;
          font-style: normal;
          font-weight: 400;
          line-height: normal;
        }
      }

      .coupon-right {
        flex: 1;
        padding: 24rpx;
        position: relative;
        width: 476rpx;

        background-size: 100% 100%;
        background-repeat: no-repeat;
        justify-content: space-evenly;
        display: flex;
        flex-direction: column;
        &.unused {
          background-image: url("https://tg-prod.oss-cn-beijing.aliyuncs.com/e20db7bb-8a7f-4e55-bb83-373ef53fe0ad.webp");
        }
        &.used {
          background-image: url("https://tg-prod.oss-cn-beijing.aliyuncs.com/2ddcf037-0bcf-4da2-a528-218faa71f2a9.png");
        }
        &.expired {
          background-image: url("https://tg-prod.oss-cn-beijing.aliyuncs.com/ce034a40-141e-4baf-8bb4-f08d24e8f59e.png");
        }
        .coupon-name {
          color: #333;
          font-size: 30rpx;
          font-style: normal;
          font-weight: 500;
          line-height: 1.3;
          width: 100%;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          overflow: hidden;
          text-overflow: ellipsis;
          word-break: break-word;
        }

        .coupon-validity {
          color: #666;
          font-size: 26rpx;
          font-style: normal;
          font-weight: 400;
          line-height: normal;
          margin-top: 12rpx;
        }

        .select-mark {
          position: absolute;
          right: 24rpx;
          bottom: 0;
          transform: translateY(-50%);
        }
      }
    }
  }
  .empty-wrap {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: -45rpx;
  }
}
</style>
<style lang="scss">
.coupon-page {
  .coupon-tabs {
    box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.05);
  }
}
</style>
