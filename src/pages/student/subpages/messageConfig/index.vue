<template>
  <view class="notification-page">
    <view class="notification-header">课程通知</view>

    <view class="notification-card">
      <view
        class="notification-item"
        v-for="(item, index) in notificationList"
        :key="index"
      >
        <view class="item-content">
          <view class="item-label">{{ item.label }}</view>
          <u-switch
            v-model="item.value"
            size="25"
            activeColor="#FFC525"
            inactiveColor="#E3E3E3"
            :activeValue="1"
            :inactiveValue="2"
            @change="handleSwitchChange(index)"
          ></u-switch>
        </view>
        <view
          class="item-border"
          v-if="index !== notificationList.length - 1"
        ></view>
      </view>
    </view>
    <view class="notification-header">活动通知</view>

    <view class="notification-card">
      <view
        class="notification-item"
        v-for="(item, index) in activityNotificationList"
        :key="index"
      >
        <view class="item-content">
          <view class="item-label">{{ item.label }}</view>
          <u-switch
            v-model="item.value"
            size="25"
            activeColor="#FFC525"
            inactiveColor="#E3E3E3"
            :activeValue="1"
            :inactiveValue="2"
            @change="handleSwitchActivityChange(index)"
          ></u-switch>
        </view>
        <view
          class="item-border"
          v-if="index !== activityNotificationList.length - 1"
        ></view>
      </view>
    </view>
  </view>
</template>

<script>
import { getMessageInfo, setMessageNotice } from "@/services/student/message";
export default {
  name: "MessageConfig",
  data() {
    return {
      notificationList: [
        { label: "课程总结通知", value: 1, type: "course_summary_status" },
        { label: "家长课堂通知", value: 1, type: "parent_class_status" },
        { label: "班级通知", value: 1, type: "class_notice_status" },
        { label: "课消提醒通知", value: 1, type: "course_deduct_status" },
        { label: "上课通知", value: 1, type: "go_to_class_notice_status" }
      ],
      activityNotificationList: [
        { label: "电子合同签署通知", value: 1, type: "electron_tag_status" },
        { label: "问卷通知", value: 1, type: "survey_status" }
      ]
    };
  },
  methods: {
    handleSwitchChange(index) {
      // 处理开关变化逻辑
      uni.vibrateShort();
      const item = this.notificationList[index];

      // 这里可以添加保存设置到服务器的逻辑
      this.saveNotificationSettings(item);
    },
    handleSwitchActivityChange(index) {
      // 处理开关变化逻辑
      uni.vibrateShort();
      const item = this.activityNotificationList[index];

      // 这里可以添加保存设置到服务器的逻辑
      this.saveNotificationSettings(item);
    },
    saveNotificationSettings(item) {
      setMessageNotice({ [item.type]: item.value })
        .then((res) => {
          if (res.code !== 0) {
            uni.showToast({
              title: res.message,
              icon: "none"
            });
            this.getMessageInfo();
          }
        })
        .catch((err) => {
          console.log(err, "err");
          uni.showToast({
            title: "设置失败",
            icon: "none"
          });
          this.getMessageInfo();
        });
    },
    getMessageInfo() {
      getMessageInfo().then((res) => {
        this.notificationList.forEach((item) => {
          item.value = res.data[item.type];
        });
        this.activityNotificationList.forEach((item) => {
          item.value = res.data[item.type];
        });
      });
    }
  },
  onShow() {
    this.getMessageInfo();
    uni.hideShareMenu();
  }
};
</script>

<style lang="scss" scoped>
.notification-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 0 32rpx;
}

.notification-header {
  font-weight: 400;
  font-size: 26rpx;
  // line-height: 100%;
  letter-spacing: 0%;
  color: #666;
  padding-top: 30rpx;
  padding-bottom: 16rpx;
  padding-left: 24rpx;
}

.notification-card {
  border-radius: 22rpx;
  background-color: #ffffff;
  // padding: 20rpx 0;
  // box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.notification-item {
  padding: 0 24rpx;
  // border-bottom: 1rpx solid #f5f5f5;

  &:last-child {
    border-bottom: none;
  }

  .item-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    // padding: 27rpx 0;
    height: 100rpx;
  }

  .item-label {
    font-weight: 400;
    font-size: 30rpx;
    line-height: 40rpx;
    letter-spacing: 0px;
    color: #333;
  }

  .item-border {
    border-bottom: 1rpx solid #eeeeee;
  }
}
</style>
