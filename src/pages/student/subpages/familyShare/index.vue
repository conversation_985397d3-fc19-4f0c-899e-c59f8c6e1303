<template>
  <view class="share-page">
    <u-navbar
      title="绑定关系"
      bgColor="#fff"
      leftIconSize="25px"
      leftIconColor="#333333"
      :titleStyle="{
        color: '#333333',
        fontSize: '34rpx',
        fontWeight: '500',
        lineHeight: '40rpx'
      }"
      placeholder
      @leftClick="handleBack"
      :autoBack="!modifyVisible"
    >
      <!-- <view class="nav-letf" slot="left">
        <image
          src="https://tg-prod.oss-cn-beijing.aliyuncs.com/e9ac0f4b-6282-4bac-b602-189057c45c90.webp"
        ></image>
      </view> -->
    </u-navbar>
    <ConfirmPopup
      ref="confirmPopup"
      @confirm="handleConfirm"
      @cancel="handleCancel"
      :title="title"
      :closeOnClickOverlay="false"
      :isFistLogin="is_fist_login"
      @onNoPermission="onNoPermission"
      @onGoPhone="onGoPhone"
      v-model="show"
    />
    <BindStudentPopup
      ref="bindStudentPopup"
      v-model="showBindStudent"
      :btnText="'登录'"
      :title="'登录'"
      @confirm="handleBindStudent"
    />
    <alertPup
      :visible="alertVisible"
      ref="alert"
      :content="content"
      @confirm="handleAlerConfirm"
    />
  </view>
</template>

<script>
import ConfirmPopup from "./UpopupCom/index.vue";
import {
  bindRole,
  getOpenId,
  login,
  loginByCode
} from "@/services/student/home";
import BindStudentPopup from "../../home/<USER>/BindStudentPopup.vue";
import alertPup from "./alertPuop/index.vue";
import {
  arouseLogin,
  getCheckedStudentInfo,
  getDataByRole,
  processUserList,
  updateCheckedUserInfo
} from "@/utils/user";
import { shareBindUser } from "@/services/student/family";
export default {
  name: "familyShare",
  components: {
    ConfirmPopup,
    BindStudentPopup,
    alertPup
  },
  data() {
    return {
      title: "是否绑定",
      show: false,
      roleId: "",
      studentId: "",
      studentName: "",
      role: "",
      is_fist_login: false,
      showBindStudent: false,
      loading: false,
      openId: "",
      alertVisible: false,
      content: "自身不需要绑定！"
    };
  },
  methods: {
    handleBack() {
      uni.navigateBack();
    },
    handleConfirm() {
      this.bindRole();
    },
    handleAlerConfirm() {
      // uni.navigateBack();
      console.log("退出");
    },
    handleCancel() {
      // uni.navigateBack();
    },
    async getRoleList() {
      const { code, data, message } = await bindRole();
      if (code === 0) {
        this.title = `是否绑定为${this.studentName}${
          data.find((item) => item.role_id === this.roleId).role_name
        }`;
        this.show = true;
      } else {
        this.$refs.uToast.show({ type: "error", message });
      }
    },
    // 用户拒绝授权
    onNoPermission() {
      // uni.exitMiniProgram();
      this.showBindStudent = true;
    },
    // 用户授权手机号
    onGoPhone() {
      this.bindRole();
    },
    getAlogin() {
      uni.login({
        provider: "weixin",
        onlyAuthorize: true, // 微信登录仅请求授权认证
        success: async (event) => {
          const { code } = event;
          const res = await getOpenId({ code, UNAUTHORIZED: true });
          if (res.code === 0) {
            // is_fist_login 是否为第一次进入小程序
            const res2 = await login({
              open_id: res.data.openid,
              UNAUTHORIZED: true
            });
            if (res2.code === 0) {
              uni.hideLoading();
              const role =
                !res2.data.is_student && !res2.data.is_customer
                  ? "default"
                  : res2.data.is_student
                  ? "student"
                  : "customer";
              this.role = role;
              uni.setStorageSync("session", { ...res2.data, role });
              this.is_fist_login = res.data.is_fist_login;
            }
          }
        }
      });
    },
    async handleBindStudent() {
      const formData = await this.$refs.bindStudentPopup.validate();
      uni.showLoading({ title: "登录中..." });
      uni.login({
        provider: "weixin",
        success: async (loginRes) => {
          if (!loginRes.code) {
            // this.phoneType = "input";
            this.$refs.uToast.show({ type: "none", message: "登录失败" });
            return;
          }
          const { code } = loginRes;
          const res = await getOpenId({
            code,
            UNAUTHORIZED: true
          });
          if (res.code === 0) {
            const res2 = await loginByCode({
              open_id: res.data.openid,
              template_code: formData.code,
              mobile: formData.mobile,
              UNAUTHORIZED: true
            });
            if (res2.code === 0) {
              const role =
                !res2.data.is_student && !res2.data.is_customer
                  ? "default"
                  : res2.data.is_student
                  ? "student"
                  : "customer";
              this.role = role;
              const session = { ...res2.data, role };
              uni.setStorageSync("session", session);
              this.session = session;
              // this.role = this.session.role;
              // this.rowIdField = this.rowIds[this.role];
              this.is_fist_login = false;
              this.showBindStudent = false;
              this.bindRole();
            } else {
              uni.showToast({
                title: res2.message,
                icon: "none"
              });
              uni.hideLoading();
            }
          } else {
            uni.showToast({
              title: res.message,
              icon: "none"
            });
            uni.hideLoading();
          }
        },
        fail: () => {
          uni.showToast({
            title: "登录失败，请重试",
            icon: "none"
          });
          uni.hideLoading();
        }
      });
    },
    // 绑定学员
    async bindRole() {
      const selectedStudent = {
        ...this.studentId,
        role_id: this.roleId,
        share_open_id: this.openId
      };
      const { session, role, openId } = await arouseLogin();
      const { code, data, message } = await shareBindUser(selectedStudent);
      if (code === 0) {
        uni.hideLoading();
        uni.showToast({
          title: `绑定成功，欢迎${data}同学`,
          icon: "none",
          duration: 3000
        });
        this.session = session;
        this.role = role;
        let roles;
        let id;
        if (this.studentId.student_id) {
          roles = "student";
          id = this.studentId.student_id;
        } else {
          roles = "customer";
          id = this.studentId.customer_id;
        }
        await updateCheckedUserInfo(roles, id, openId);
        // this.rowIdField = this.rowIds[role];

        // 获取选中的学生/客户信息
        await getCheckedStudentInfo(openId, role, session.operation_id);

        // 获取用户列表并处理
        await getDataByRole(this, role, openId, (code, data, message) => {
          processUserList(code, data, message, this, openId);
        });
        console.log(uni.getStorageSync("curStudentInfo"), "绑定");
        setTimeout(() => {
          uni.switchTab({
            url: "/pages/student/home/<USER>"
          });
        }, 1000);
      } else {
        setTimeout(() => {
          uni.switchTab({
            url: "/pages/student/home/<USER>"
          });
        }, 3000);
        uni.hideLoading();
        // this.$refs.uToast.show({ type: "error", message, duration: 3000 });
        uni.showToast({
          title: message,
          icon: "none",
          duration: 3000
        });
      }
    }
  },
  async onLoad(options) {
    console.log("onLoad", options);
    await this.getAlogin();

    // 检查分享链接是否过期（7天 = 604800000毫秒）
    if (options.shareTime) {
      const shareTime = parseInt(options.shareTime);
      const currentTime = Date.now();
      const sevenDaysInMs = 7 * 24 * 60 * 60 * 1000; // 7天的毫秒数

      if (currentTime - shareTime > sevenDaysInMs) {
        // 链接已过期
        this.content = "邀请链接已过期！";
        this.alertVisible = true;
        return;
      }
    }

    // if (options.openId === uni.getStorageSync("session").open_id) {
    //   this.content = "自身不需要绑定！";
    //   this.alertVisible = true;
    //   return;
    // }
    // 绑定的身份
    this.roleId = Number(options.roleId);
    this.studentId = JSON.parse(options.id);
    this.studentName = options.studentName;
    this.openId = options.openId;
    this.getRoleList();
  }
};
</script>
<style lang="scss" scoped>
.share-page {
  width: 100%;
  height: 100%;
  background-color: #f5f5f5;
  .nav-letf {
    width: 40rpx;
    height: 40rpx;
    image {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
