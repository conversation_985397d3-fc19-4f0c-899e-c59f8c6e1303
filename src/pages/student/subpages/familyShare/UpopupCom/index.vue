<template>
  <u-popup :show="show" @close="handleClose" mode="center" :round="10" :customStyle="{
    borderRadius: '32rpx',
    background: '#FFFFFF',
    fontSize: '32rpx',
    width: '582rpx',
    ...customStyle
  }" :safeAreaInsetBottom="false" :closeOnClickOverlay="closeOnClickOverlay">
    <view class="confirm-popup">
      <view class="content">
        <text class="dectitle" v-if="dectitle">{{ dectitle }}</text>
        <text class="title">{{ title }}</text>
        <view class="btn-group">
          <navigator target="miniProgram" open-type="exit" class="cancel-btn" @tap="handleClose"
            :style="cancelBtnStyle">
            {{ cancelText }}
          </navigator>
          <button open-type="getPhoneNumber" @getphonenumber="onGetPhoneNumber" class="confirm-btn" v-if="isFistLogin">
            {{ confirmText }}
          </button>
          <button v-else class="confirm-btn" @tap="$u.throttle(handleConfirm, 1000)" :style="confirmBtnStyle">
            {{ confirmText }}
          </button>
        </view>
      </view>
    </view>
  </u-popup>
</template>

<script>
import { getOpenId } from "@/services/login";
import { getPhoneByCode, loginByPhone } from "@/services/student/home";
export default {
  name: "ConfirmPopup",
  props: {
    value: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: "确认操作"
    },
    cancelText: {
      type: String,
      default: "取消"
    },
    confirmText: {
      type: String,
      default: "确定"
    },
    customStyle: {
      type: Object,
      default: () => ({})
    },
    cancelBtnStyle: {
      type: Object,
      default: () => ({})
    },
    confirmBtnStyle: {
      type: Object,
      default: () => ({})
    },
    dectitle: {
      type: String,
      default: ""
    },
    closeOnClickOverlay: {
      type: Boolean,
      default: true
    },
    isFistLogin: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    show: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit("input", val);
      }
    }
  },
  methods: {
    handleClose() {
      //   this.show = false;
      this.$emit("cancel");
    },
    handleConfirm() {
      this.$emit("confirm");
      //   this.show = false;
    },
    async onGetPhoneNumber(e) {
      console.log("🚀 ~ onGetPhoneNumber ~ e:", e);

      // 用户拒绝授权
      if (e.detail.errMsg !== "getPhoneNumber:ok") {
        this.$emit("onNoPermission");
        return;
      }
      getPhoneByCode({
        code: e.detail.code
      }).then((response) => {
        console.log("🚀 ~ getPhoneByCode ~ res:", response);
        if (response.code === 0) {
          uni.login({
            provider: "weixin",
            success: async (loginRes) => {
              if (!loginRes.code) {
                this.loading = false;
                // this.phoneType = "input";
                return;
              }
              const { code } = loginRes;
              const res = await getOpenId({
                code,
                UNAUTHORIZED: true
              });
              if (res.code === 0) {
                const res2 = await loginByPhone({
                  open_id: res.data.openid,
                  mobile: response.data.phoneNumber,
                  UNAUTHORIZED: true
                });
                if (res2.code === 0) {
                  uni.hideLoading();
                  console.log(
                    "🚀 ~ res2:",
                    !res2.data.is_student && !res2.data.is_customer
                  );
                  const role =
                    !res2.data.is_student && !res2.data.is_customer
                      ? "default"
                      : res2.data.is_student
                        ? "student"
                        : "customer";
                  console.log("🚀 ~ role:", role);
                  this.role = role;
                  const session = { ...res2.data, role };
                  uni.setStorageSync("session", session);
                  uni.setStorageSync("portType", "STUDENT");
                  this.session = session;
                  //   this.role = this.session.role;
                  //   this.rowIdField = this.rowIds[this.role];
                  console.log(this.session, "session");
                  this.$emit("onGoPhone");
                  // this.curCheckedStudent = uni.getStorageSync("curStudentInfo");
                  //   uni.switchTab({ url: "/pages/student/home/<USER>" });
                } else {
                  uni.hideLoading();
                  uni.showToast({
                    title: res2.message,
                    icon: "none"
                  });
                  this.loading = false;
                }
              }
            },
            fail: () => {
              uni.hideLoading();
              uni.showToast({
                title: "网络错误，请重试",
                icon: "none"
              });
              this.loading = false;
            }
          });
        } else {
          uni.hideLoading();
          uni.showToast({
            title: "网络错误，请重试",
            icon: "none"
          });
          this.loading = false;
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.confirm-popup {
  padding: 48rpx 40rpx 0;

  .content {
    display: flex;
    flex-direction: column;
    align-items: center;

    .dectitle {
      color: #333;

      text-align: center;
      font-size: 34rpx;
      font-style: normal;
      font-weight: 500;
      line-height: 48rpx;
      margin-bottom: 40rpx;
    }

    .title {
      color: #333;
      text-align: center;
      font-family: PingFang SC;
      font-size: 32rpx;
      font-weight: 400;
      line-height: 48rpx;
      margin-bottom: 48rpx;
    }

    .btn-group {
      display: flex;
      gap: 20rpx;
      width: 100%;
      margin-bottom: 60rpx;

      .cancel-btn,
      .confirm-btn {
        flex: 1;
        height: 88rpx;
        border-radius: 44rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 32rpx;
        font-weight: 500;
        transition: transform 0.2s ease;

        &:active {
          transform: scale(0.98);
        }
      }

      .cancel-btn {
        background: #fff;
        color: #ffbb00;
        border: 3rpx solid #ffbb00;
      }

      .confirm-btn {
        background: linear-gradient(15deg, #ffbf0d 18.1%, #ffcb3c 83.29%);
        box-shadow: 0px -5rpx 12rpx 0px #fc0 inset,
          0px 9rpx 20rpx 0px #fff7e1 inset;
        filter: drop-shadow(0px 4rpx 4rpx rgba(255, 192, 18, 0.11));
        color: #fff;
      }
    }
  }
}
</style>
