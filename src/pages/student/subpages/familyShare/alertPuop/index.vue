<template>
  <view :style="{ left: visible ? '0' : '-200%' }" class="alert-container">
    <view class="alert-mask" @click.stop="handleCancel"></view>
    <view class="alert-content" :class="{ 'alert-show': visible }">
      <view
        :style="{ color: color, fontSize: fontSize }"
        class="alert-message"
        >{{ content }}</view
      >
      <navigator
        target="miniProgram"
        open-type="exit"
        class="alert-button"
        @click="handleConfirm"
        >{{ buttonText }}</navigator
      >
    </view>
  </view>
</template>

<script>
export default {
  name: "MyAlert",
  props: {
    content: {
      type: String,
      default: ""
    },
    buttonText: {
      type: String,
      default: "确定"
    },
    showCancel: {
      type: Boolean,
      default: false
    },
    visible: {
      type: Boolean,
      default: false
    },
    color: {
      type: String,
      default: "#333"
    },
    fontSize: {
      type: String,
      default: "36rpx"
    }
  },
  data() {
    return {
      // 我们不再需要内部 visible 状态，使用 prop
    };
  },
  watch: {
    // 监听可见性变化，确保动画效果正常
    visible(val) {
      console.log("Alert组件可见性变化:", val);
    }
  },
  methods: {
    handleConfirm() {
      console.log("Alert组件确认按钮被点击");
      this.$emit("confirm");
      this.$emit("close");
    },
    handleCancel() {
      this.$emit("close");
    }
  },
  // 组件销毁时确保恢复页面滚动
  beforeUnmount() {}
};
</script>

<style lang="scss" scoped>
.alert-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.alert-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}

.alert-content {
  position: relative;
  width: 582rpx;
  height: 334rpx;
  padding: 60rpx 40rpx;
  background-color: #fff;
  border-radius: 35rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  transform: scale(0.8);
  opacity: 0;
  transition: all 0.3s;
  z-index: 10000;

  &.alert-show {
    transform: scale(1);
    opacity: 1;
  }
}

.alert-message {
  color: #333;
  text-align: center;
  font-size: 36rpx;
  font-style: normal;
  font-weight: 500;
  line-height: 1.5;
}

.alert-button {
  width: 238rpx;
  height: 88rpx;
  border-radius: 44rpx;
  border: 2rpx solid #ffc525;
  color: #ffc525;
  font-size: 34rpx;
  font-weight: bold;
  text-align: center;
  line-height: 88rpx;
  margin-top: 40rpx;
  &:active {
    background-color: #ffc525;
    color: #fff;
    opacity: 0.8;
  }
}
</style>
