<template>
  <view class="success-page">
    <!-- 成功状态内容 -->
    <view class="success-content">
      <u-navbar
        title="领取详情"
        bgColor="transparent"
        leftIconSize="25px"
        leftIconColor="#333333"
        :titleStyle="{
          color: '#fff',
          fontSize: '34rpx',
          fontWeight: '500',
          lineHeight: '40rpx'
        }"
        :autoBack="true"
        placeholder
      >
        <view class="nav-letf" slot="left" @click="goBack">
          <image
            src="https://tg-prod.oss-cn-beijing.aliyuncs.com/68798755-2327-4bac-8d65-3de4ed10a6e3.webp"
          ></image>
        </view>
      </u-navbar>
      <!-- 成功图标 -->
      <view class="success-icon-container">
        <view class="success-icon">
          <view class="check-mark">
            <image
              src="https://tg-prod.oss-cn-beijing.aliyuncs.com/2706d9fa-d369-4f3e-95d3-dee950a74777.png"
            ></image>
          </view>
        </view>
      </view>

      <!-- 成功文字 -->
      <view class="success-text">领取成功</view>

      <!-- 说明文字 -->
      <view class="success-desc"> 校区会有老师联系确认到店体验，敬请留意 </view>
    </view>

    <!-- 分割线 -->
    <!-- <view class="divider"></view> -->

    <!-- 领取信息标题 -->
    <view class="info-container">
      <!-- 领取信息详情 -->
      <view class="info-content">
        <view class="info-title">领取信息</view>
        <view class="info-item">
          <view class="info-label">姓名</view>
          <view class="info-value">{{
            formData.student_name || "未填写"
          }}</view>
        </view>
        <view class="info-item">
          <view class="info-label">性别</view>
          <view class="info-value">{{
            formData.student_gender === "male" ? "男" : "女" || "未选择"
          }}</view>
        </view>
        <view class="info-item">
          <view class="info-label">手机号</view>
          <view class="info-value">{{
            formData.student_mobile || "未填写"
          }}</view>
        </view>
        <view class="info-item">
          <view class="info-label">出生日期</view>
          <view class="info-value">{{
            formData.student_birth_day || "未选择"
          }}</view>
        </view>
        <view class="info-item">
          <view class="info-label">选择校区</view>
          <view class="info-value">{{ formData.campus || "未选择" }}</view>
        </view>
      </view>
    </view>
    <view class="bottom-btn" @click="goBack">
      <view class="btn-text">完成</view>
    </view>
  </view>
</template>

<script>
export default {
  name: "ExperienceSuccess",
  data() {
    return {
      formData: {
        student_name: "",
        student_gender: "",
        student_mobile: "",
        student_birth_day: "",
        campus: ""
      }
    };
  },
  onLoad(options) {
    // 接收传递过来的表单数据
    if (options.formData) {
      try {
        this.formData = JSON.parse(decodeURIComponent(options.formData));
      } catch (error) {
        console.error("解析表单数据失败:", error);
      }
    }
  },
  methods: {
    goBack() {
      uni.navigateBack({
        delta: 2 // 返回到表单页面的上一页
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.success-page {
  width: 100%;
  min-height: 100vh;
  background: #fafafa;
  display: flex;
  flex-direction: column;
}

.success-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 70rpx 128rpx;
  // margin: 0 128rpx;
  background: url("https://tg-prod.oss-cn-beijing.aliyuncs.com/85850d9e-1457-40e5-b620-873d7149c1c6.webp")
    no-repeat center center;
  background-size: 100% 100%;
  height: 666rpx;
}

.success-icon-container {
  width: 144rpx;
  height: 134rpx;
  // margin-bottom: 24rpx;
}

.success-icon {
  width: 144rpx;
  height: 134rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.check-mark {
  width: 100%;
  height: 100%;
  image {
    width: 100%;
    height: 100%;
  }
}

.success-text {
  font-family: PingFang SC;
  font-weight: 500;
  font-size: 32rpx;
  line-height: 48rpx;
  color: #fff;
  margin-bottom: 8rpx;
}

.success-desc {
  font-family: PingFang SC;
  font-weight: 400;
  font-size: 26rpx;
  line-height: 42rpx;
  color: #fff;
  text-align: center;
  margin-bottom: 90rpx;
}

.divider {
  width: calc(100% - 82rpx);
  height: 2rpx;
  background: #eeeeee;
  margin: 0 41rpx;
}
.info-container {
  width: 650rpx;
  height: 100%;
  background: #ffffff;
  border-radius: 24rpx 24rpx 0 0;
  padding: 0 32rpx;
  margin: 0 auto;
  margin-top: -170rpx;
  box-shadow: 0px 4rpx 6.6rpx 0px rgba(0, 0, 0, 0.03);
}
.info-title {
  font-family: PingFang SC;
  font-weight: 500;
  font-size: 32rpx;
  line-height: 45rpx;
  color: #333333;
  margin: 36rpx 0 26rpx 0;
}

.info-content {
  padding: 0 40rpx;
  padding-bottom: 10rpx;
  flex: 1;
}

.info-item {
  display: flex;
  //   justify-content: space-between;
  align-items: center;
  padding: 26rpx 0;
  //   border-bottom: 1rpx solid #f5f5f5;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-family: PingFang SC;
  font-weight: 400;
  font-size: 28rpx;
  color: #666666;
  width: 150rpx;
}

.info-value {
  font-family: PingFang SC;
  font-weight: 400;
  font-size: 28rpx;
  color: #333333;
  margin-left: 60rpx;
}
.nav-letf {
  width: 40rpx;
  height: 40rpx;
  image {
    width: 100%;
    height: 100%;
  }
}
.bottom-btn {
  padding: 18rpx 32rpx;
  padding-bottom: env(safe-area-inset-bottom);
  position: fixed;
  bottom: 0;
  background: #fff;
  .btn-text {
    width: 686rpx;
    height: 92rpx;
    flex-shrink: 0;
    border-radius: 80rpx;
    background: linear-gradient(15deg, #ffbf0d 18.1%, #ffcb3c 83.29%);
    box-shadow: 0px -10rpx 18rpx 0px #f3b300 inset,
      0px 4rpx 20rpx 0px rgba(254, 197, 36, 0.47);
    color: #fff;

    text-align: center;
    font-size: 32rpx;
    font-style: normal;
    font-weight: 500;
    line-height: 40rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
</style>
