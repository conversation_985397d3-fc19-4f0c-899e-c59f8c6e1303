<template>
  <view class="receive-experience-page">
    <!-- 背景图片 -->
    <!-- <view class="bg-container">
      <image
        class="bg-image"
        src="https://tg-prod.oss-cn-beijing.aliyuncs.com/experience-course-bg.jpg"
        mode="aspectFill"
      />
    </view> -->
    <image
      class="bg-image"
      src="https://tg-prod.oss-cn-beijing.aliyuncs.com/7c251d5a-6744-4871-8fcf-b47fc8473ed2.png"
      mode="aspectFill"
    />
    <!-- 表单内容 -->
    <view class="form-container">
      <!-- 姓名 -->
      <view class="form-item">
        <view class="form-label">
          <text>*</text>
          姓名
        </view>
        <view class="form-input-container">
          <input
            placeholder-class="placeholderStyle"
            class="form-input"
            type="text"
            v-model="formData.student_name"
            placeholder="请输入姓名"
            maxlength="20"
          />
        </view>
      </view>

      <!-- 性别 -->
      <view class="form-item">
        <view class="form-label">
          <text>*</text>
          性别
        </view>
        <view class="form-input-container" @click="showGenderPicker = true">
          <view class="form-input readonly">
            {{ formData.student_gender_name || "请选择" }}
          </view>
          <u-icon student_name="arrow-right" color="#999" size="24rpx" />
        </view>
      </view>

      <!-- 手机号 -->
      <view class="form-item">
        <view class="form-label">
          <text>*</text>
          手机号
        </view>
        <view class="form-input-container student_mobile-container">
          <input
            class="form-input student_mobile-input"
            type="number"
            v-model="formData.student_mobile"
            placeholder="请输入手机号"
            placeholder-class="placeholderStyle"
            maxlength="11"
          />
          <button
            class="get-student_mobile-btn"
            open-type="getPhoneNumber"
            @getphonenumber="getPhoneNumber"
          >
            获取手机号
          </button>
        </view>
      </view>

      <!-- 出生日期 -->
      <view class="form-item">
        <view class="form-label">
          <text>*</text>
          出生日期
        </view>
        <view class="form-input-container" @click="showDatePicker = true">
          <view class="form-input readonly">
            {{ formData.student_birth_day || "请选择出生日期" }}
          </view>
          <u-icon student_name="arrow-right" color="#999" size="24rpx" />
        </view>
      </view>

      <!-- 选择校区 -->
      <view class="form-item">
        <view class="form-label">
          <text>*</text>
          选择校区
        </view>
        <view class="form-input-container" @click="showCampusPicker = true">
          <view class="form-input readonly">
            {{ selectedCampus || "请选择校区" }}
          </view>
          <u-icon student_name="arrow-right" color="#999" size="24rpx" />
        </view>
      </view>
    </view>
    <!-- <view class="form-container-bottom"> -->
    <image
      src="https://tg-prod.oss-cn-beijing.aliyuncs.com/bc3eba8a-b0bd-4673-ad3f-84267a209559.png"
      mode="widthFix"
      class="form-container-bottom"
    />
    <!-- </view> -->
    <!-- 底部按钮 -->
    <view class="bottom-container">
      <view class="submit-btn" @click="submitForm"> 提交预约申请 </view>
    </view>

    <!-- 性别选择器 -->
    <u-picker
      :show="showGenderPicker"
      :columns="genderColumns"
      @confirm="onGenderConfirm"
      @cancel="showGenderPicker = false"
      :keyName="'name'"
    />

    <!-- 日期选择器 -->
    <u-datetime-picker
      :show="showDatePicker"
      v-model="student_birth_day"
      mode="date"
      :maxDate="maxDate"
      :minDate="minDate"
      @confirm="onDateConfirm"
      @cancel="showDatePicker = false"
    />

    <!-- 校区选择器 -->
    <u-picker
      :show="showCampusPicker"
      :columns="[campusColumns]"
      @confirm="onCampusConfirm"
      @cancel="showCampusPicker = false"
      :keyName="'department_name'"
    />
  </view>
</template>

<script>
import { getPhoneByCode } from "@/services/student/home";
import {
  getCampusList,
  addCustomer
} from "@/services/student/receiveExperienceCourse";
export default {
  name: "ReceiveExperienceCourse",
  data() {
    return {
      formData: {
        student_name: "",
        student_gender: "",
        student_mobile: "",
        student_birth_day: "",
        department_id: "",
        student_gender_name: ""
      },
      selectedCampus: "",
      showGenderPicker: false,
      showDatePicker: false,
      showCampusPicker: false,
      student_birth_day: Date.now(),
      maxDate: Date.now(), // 当前时间为最大可选日期
      minDate: 0, // 70年前为最小可选日期，在 onLoad 中初始化
      genderColumns: [
        [
          { name: "男", value: "male" },
          { name: "女", value: "female" }
        ]
      ],
      campusColumns: []
    };
  },
  onLoad() {
    this.getCampusList();
    this.minDate = this.getMinDate(); // 初始化最小日期
  },
  methods: {
    // 计算100年前的日期
    getMinDate() {
      const now = new Date();
      const minYear = now.getFullYear() - 100;
      return new Date(minYear, now.getMonth(), now.getDate()).getTime();
    },

    // 获取手机号
    getPhoneNumber(e) {
      console.log("🚀 ~ getPhoneNumber ~ e:", e);
      if (e.detail.errMsg !== "getPhoneNumber:ok") {
        // uni.showToast({
        //   title: "请先授权手机号",
        //   icon: "none"
        // });
        return;
      }
      getPhoneByCode({
        code: e.detail.code
      }).then((response) => {
        if (response.code === 0) {
          this.formData.student_mobile = response.data.purePhoneNumber;
        } else {
          uni.showToast({
            title: response.message,
            icon: "none"
          });
        }
      });
    },

    // 性别选择确认
    onGenderConfirm(e) {
      this.formData.student_gender_name = e.value[0].name;
      this.formData.student_gender = e.value[0].value;
      this.showGenderPicker = false;
    },

    // 日期选择确认
    onDateConfirm(e) {
      const date = new Date(e.value);
      const year = date.getFullYear();
      const month = date.getMonth() + 1;
      const day = date.getDate();
      // console.log("🚀 ~ onDateConfirm ~ e:", e);
      const monthStr = month < 10 ? `0${month}` : month;
      const dayStr = day < 10 ? `0${day}` : day;
      this.formData.student_birth_day = `${year}-${monthStr}-${dayStr}`;
      this.showDatePicker = false;
    },

    // 校区选择确认
    onCampusConfirm(e) {
      this.selectedCampus = e.value[0].department_name;
      this.formData.department_id = e.value[0].department_id;
      this.showCampusPicker = false;
    },
    async getCampusList() {
      const res = await getCampusList({
        ...(uni.getStorageSync("location") || {})
      });
      console.log("🚀 ~ getCampusList ~ res:", res);
      if (res.code === 0) {
        this.campusColumns = res.data;
      } else {
        uni.showToast({
          title: res.message,
          icon: "none"
        });
      }
    },
    // 表单验证
    validateForm() {
      if (!this.formData.student_name.trim()) {
        uni.showToast({
          title: "请输入姓名",
          icon: "none"
        });
        return false;
      }

      if (!this.formData.student_gender) {
        uni.showToast({
          title: "请选择性别",
          icon: "none"
        });
        return false;
      }

      if (!this.formData.student_mobile) {
        uni.showToast({
          title: "请输入手机号",
          icon: "none"
        });
        return false;
      }

      if (!/^1[3-9]\d{9}$/.test(this.formData.student_mobile)) {
        uni.showToast({
          title: "请输入正确的手机号",
          icon: "none"
        });
        return false;
      }

      if (!this.formData.student_birth_day) {
        uni.showToast({
          title: "请选择出生日期",
          icon: "none"
        });
        return false;
      }

      if (!this.selectedCampus) {
        uni.showToast({
          title: "请选择校区",
          icon: "none"
        });
        return false;
      }

      return true;
    },

    // 提交表单
    async submitForm() {
      if (!this.validateForm()) {
        return;
      }

      try {
        uni.showLoading({
          title: "提交中..."
        });

        // 这里调用提交接口
        console.log("提交表单数据:", this.formData);

        // 模拟接口调用
        const res = await addCustomer(this.formData);
        console.log("🚀 ~ submitForm ~ res:", res);
        if (res.code === 0) {
          uni.showToast({
            title: "提交成功",
            icon: "none"
          });
        } else {
          uni.showToast({
            title: res.message,
            icon: "none"
          });
          return;
        }
        uni.hideLoading();

        // 构造要传递的表单数据
        const submitData = {
          student_name: this.formData.student_name,
          student_gender: this.formData.student_gender,
          student_mobile: this.formData.student_mobile,
          student_birth_day: this.formData.student_birth_day,
          campus: this.selectedCampus
        };

        // 跳转到成功页面并传递表单数据
        uni.navigateTo({
          url: `/pages/student/subpages/receiveExperienceCourse/scuss?formData=${encodeURIComponent(
            JSON.stringify(submitData)
          )}`
        });
      } catch (error) {
        uni.hideLoading();
        uni.showToast({
          title: "提交失败，请重试",
          icon: "none"
        });
        console.error("提交失败:", error);
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.receive-experience-page {
  width: 100%;
  min-height: 100vh;
  position: relative;
  background: #fff3e8;
}

.bg-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  z-index: 0;
}

.bg-image {
  width: 100%;
  height: 806rpx;
  object-fit: cover;
}

.form-container {
  position: relative;
  z-index: 1;
  padding: 136rpx 73rpx 0;
  height: 650rpx;
  display: flex;
  flex-direction: column;
  gap: 30rpx;
  background: url("https://tg-prod.oss-cn-beijing.aliyuncs.com/89fbeebc-66c2-4fc7-925b-3f5ea07880a3.png")
    no-repeat center center;
  background-size: 100% 100%;
}

.form-item {
  width: 100%;
  height: 66rpx;
  position: relative;
}

.form-label {
  position: absolute;
  left: 0;
  top: 13rpx;
  width: 129rpx;
  height: 40rpx;
  font-weight: 400;
  font-size: 26rpx;
  line-height: 40rpx;
  color: #6d3c31;
  text {
    color: #fe4f37;
  }
}

.form-input-container {
  position: absolute;
  left: 145rpx;
  top: 0;
  width: 458rpx;
  height: 66rpx;
  background: #fffbea;
  border-radius: 6rpx;
  display: flex;
  align-items: center;
  padding: 0 20rpx;

  &.student_mobile-container {
    padding: 0;
  }
}

.form-input {
  flex: 1;
  height: 40rpx;
  font-family: PingFang SC;
  font-weight: 400;
  font-size: 26rpx;
  line-height: 40rpx;
  color: #6d3c31;
  background: transparent;

  &.readonly {
    color: #6d3c31;
  }

  &.student_mobile-input {
    padding-left: 20rpx;
    width: 292rpx;
  }
}

.get-student_mobile-btn {
  width: 148rpx;
  height: 66rpx;
  background: #ffc525;
  border-radius: 6rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: PingFang SC;
  font-weight: 400;
  font-size: 24rpx;
  line-height: 40rpx;
  color: #ffffff;
  margin-left: 17rpx;
}
.form-container-bottom {
  width: 100%;
  margin-bottom: 50rpx;
}
.bottom-container {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 180rpx;
  background: #ffffff;
  padding: 18rpx 32rpx 0;
  z-index: 2;
  padding-bottom: calc(env(safe-area-inset-bottom) + 18rpx);
}

.submit-btn {
  width: 686rpx;
  height: 92rpx;
  background: linear-gradient(135deg, #ffbf0d 0%, #ffcb3c 100%);
  box-shadow: 0px 4px 20px 0px rgba(254, 197, 36, 0.47),
    inset 0px -10px 18px 0px rgba(243, 179, 0, 1);
  border-radius: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: PingFang SC;
  font-weight: 500;
  font-size: 32rpx;
  line-height: 40rpx;
  color: #ffffff;
  text-align: center;
}
</style>
<style lang="scss">
.placeholderStyle {
  color: #6d3c31;
}
</style>
