<template>
  <view class="pay-success-container">
    <!-- 顶部导航栏 -->
    <u-navbar
      title="支付结果"
      bgColor="#FFF"
      leftIconSize="25px"
      leftIconColor="#333333"
      :titleStyle="{
        color: '#333333',
        fontSize: '34rpx',
        fontWeight: '500',
        lineHeight: '40rpx'
      }"
      :autoBack="true"
      placeholder
    >
    </u-navbar>

    <!-- 成功内容 -->
    <view class="success-content">
      <!-- 成功图标 -->
      <view class="success-icon">
        <u-loading-icon
          v-if="status === 'loading'"
          size="66rpx"
          style="margin-bottom: 40rpx"
        ></u-loading-icon>
        <u-icon
          name="https://tg-prod.oss-cn-beijing.aliyuncs.com/6ff30fa4-ff8a-4f7f-9b10-1fcfedd12dc8.webp"
          color="#FFFFFF"
          width="170rpx"
          height="170rpx"
          size="140rpx"
          v-else-if="status === 'success'"
        ></u-icon>
        <u-icon
          name="https://tg-prod.oss-cn-beijing.aliyuncs.com/6ff30fa4-ff8a-4f7f-9b10-1fcfedd12dc8.webp"
          color="#FFFFFF"
          width="170rpx"
          height="170rpx"
          size="140rpx"
          v-else-if="status === 'error'"
        ></u-icon>
      </view>

      <!-- 成功文字 -->
      <view class="success-text">{{ statusText[status] }}</view>
    </view>

    <!-- 底部按钮 -->
    <view class="bottom-actions">
      <button class="home-btn" @tap="goToHome">回到首页</button>
      <button class="order-btn" @tap="goToOrder">查看订单</button>
    </view>
  </view>
</template>

<script>
import { getPayResult } from "@/services/student/order";
export default {
  name: "PaySuccess",
  data() {
    return {
      orderId: "",
      timer: null,
      status: "loading",
      checkCount: 0,
      statusText: {
        error: "支付失败",
        success: "支付成功",
        loading: "订单状态确认中"
      },
      isFree: false
    };
  },
  onLoad(options) {
    if (options.orderId) {
      const decodedParams = JSON.parse(decodeURIComponent(options.orderId));
      this.orderId = decodedParams;
      this.isFree = options.isFree || false;
      this.startCheckPayResult();
    }
  },
  onUnload() {
    // 页面卸载时清除定时器
    this.clearCheckTimer();
  },
  methods: {
    startCheckPayResult() {
      if (this.isFree) {
        this.status = "success";
        this.clearCheckTimer();
        return;
      }
      this.checkPayResult();
      this.timer = setInterval(() => {
        this.checkPayResult();
      }, 3000); // 每3秒检查一次
    },
    clearCheckTimer() {
      if (this.timer) {
        clearInterval(this.timer);
        this.timer = null;
      }
    },
    checkPayResult() {
      this.checkCount++;
      getPayResult({
        order_id: this.orderId
      }).then(({ code, data, message }) => {
        if (code === 0) {
          if (data.pay_status === "paid") {
            this.status = "success";
            this.clearCheckTimer();
          } else if (data.pay_status === "failed") {
            this.status = "error";
            this.clearCheckTimer();
          } else {
            this.status = "loading";
          }
        } else {
          uni.showToast({
            title: message,
            icon: "none"
          });
          this.clearCheckTimer();
        }
      });
    },
    goToHome() {
      // 跳转到首页
      uni.switchTab({
        url: "/pages/student/home/<USER>"
      });
    },
    goToOrder() {
      // 跳转到订单详情页面
      // uni.redirectTo({
      //   url: `/pages/student/subpages/orderDetail/index?status=${this.status}&orderId=${this.orderId}`
      // });
      uni.redirectTo({
        url: `/pages/student/subpages/order/index`
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.pay-success-container {
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  //   min-height: 100vh;
}

.success-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 80rpx;

  .success-icon {
    width: 140rpx;
    height: 140rpx;
    // background-color: #6dd400;
    // border-radius: 60rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 40rpx;
  }

  .success-text {
    font-size: 36rpx;
    font-weight: 500;
    color: #333333;
  }
}

.bottom-actions {
  display: flex;
  justify-content: center;
  padding: 80rpx 32rpx 80rpx;
  gap: 30rpx;

  button {
    width: 300rpx;
    height: 80rpx;
    border-radius: 44rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 30rpx;
    font-weight: 500;

    &::after {
      border: none;
    }
  }

  .home-btn {
    background-color: #ffffff;
    border: 2rpx solid #ffc525;
    color: #fb0;
    text-align: center;
    font-size: 34rpx;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    width: 238rpx;
    height: 88rpx;
  }

  .order-btn {
    width: 238rpx;
    height: 88rpx;
    background: linear-gradient(15deg, #ffbf0d 18.1%, #ffcb3c 83.29%);
    box-shadow: 0px -5rpx 12rpx 0px #fc0 inset, 0px 9rpx 20rpx 0px #fff7e1 inset;
    filter: drop-shadow(0px 4rpx 4rpx rgba(255, 192, 18, 0.11));
    color: #ffffff;
  }
}
.nav-left {
  image {
    width: 40rpx;
    height: 40rpx;
  }
}
</style>
