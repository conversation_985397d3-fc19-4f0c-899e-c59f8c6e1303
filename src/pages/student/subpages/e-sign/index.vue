<template>
  <view class="e-sign">
    <view class="e-sign-header">
      <custom-tabs
        :list="tabList"
        :current="current"
        @change="handleTabChange"
        :activeColor="'#fb0'"
      ></custom-tabs>
    </view>

    <view class="e-sign-content">
      <view class="contract-list">
        <template>
          <view
            v-for="(item, index) in contractList"
            :key="index"
            class="contract-item"
          >
            <view v-if="item.status === 'signing'" class="contract-action">
              <view class="countdown" v-if="item.status === 'signing'">
                <view class="countdown-num-wrap">
                  <text class="countdown-num">{{
                    getCountdown(countdowns[item.id] || 0).days
                  }}</text
                  ><text class="countdown-unit">天</text>
                  <text class="countdown-num">{{
                    getCountdown(countdowns[item.id] || 0).hours
                  }}</text
                  ><text class="countdown-unit">时</text>
                  <text class="countdown-num">{{
                    getCountdown(countdowns[item.id] || 0).minutes
                  }}</text
                  ><text class="countdown-unit">分</text>
                </view>

                <view class="sign-time">剩余处理时间</view>
              </view>
              <view class="action-btn" @click="handleSign(item)">
                <text class="go-sign">去签署</text>
                <text class="arrow">></text>
              </view>
            </view>
            <view @click="handlePreview(item)" class="contract-info">
              <view
                v-if="item.status == 'signing'"
                class="contract-title text-ellipsis"
                >{{ item.contract_title }}
              </view>
              <view v-else-if="item.status === 'done'" class="download-wrap">
                <view class="contract-title text-ellipsis">{{
                  item.contract_title
                }}</view>
                <view class="download-btn"
                  >预览<text class="arrow">></text></view
                >
              </view>
              <view v-else class="download-wrap">
                <view class="contract-title text-ellipsis">{{
                  item.contract_title
                }}</view>
              </view>
              <view class="contract-detail">
                <view class="detail-row">
                  <text class="label">发起时间</text>
                  <text class="value">{{ formatDate(item.apply_time) }}</text>
                </view>
                <view class="detail-row">
                  <text class="label">签署方(甲方)</text>
                  <text class="value"
                    >{{ item.organization_name }}({{ item.manager_name }})</text
                  >
                </view>
                <view class="detail-row" style="margin: 0">
                  <text class="label">签署方(乙方)</text>
                  <text class="value">{{ item.user_name }}</text>
                </view>
              </view>
            </view>
            <view class="status-img">
              <u-image
                v-if="statusImgMap[item.callback_status]"
                :src="statusImgMap[item.callback_status]"
                mode="widthFix"
                width="185rpx"
                height="200rpx"
                :showMenuByLongpress="false"
              />
            </view>
          </view>
        </template>
      </view>
      <!-- 空组件 -->
      <view v-if="showEmpty && contractList.length === 0" class="empty-wrap">
        <empty text="暂无相关合同~" />
      </view>
    </view>
  </view>
</template>

<script>
import {
  getContractList,
  getContractSchemaUrl
} from "@/services/student/contract.js";
import CustomTabs from "@/components/tabs/index.vue";
import Empty from "@/components/empty/index.vue";
export default {
  name: "eSignIndex",
  components: {
    CustomTabs,
    Empty
  },
  data() {
    return {
      showEmpty: false,
      current: 0,
      tabList: [{ name: "待签订" }, { name: "已签订" }, { name: "已失效" }],
      statusMap: {
        signing: "待签订",
        done: "已签订",
        failed: "已失效"
      },
      contractList: [],
      statusImgMap: {
        客户拒签:
          "https://tg-prod.oss-cn-beijing.aliyuncs.com/fc424923-0d68-4290-bf0f-b25789ac0c53.webp",
        合同过期:
          "https://tg-prod.oss-cn-beijing.aliyuncs.com/adaa871c-a5be-4f07-9979-77f4d5b6c586.webp",
        已解除:
          "https://tg-prod.oss-cn-beijing.aliyuncs.com/b179166a-537b-4b0b-979f-d43ed122e210.webp"
      },
      timer: null,
      countdowns: {},
      page_from: "notification",
      student_name: "",
      student_id: "",
      open_id: "",
      contract_no: ""
    };
  },
  computed: {
    // 计算倒计时
    getCountdown() {
      return (leftTime) => {
        if (!leftTime) return { days: 0, hours: 0, minutes: 0 };

        const days = Math.floor(leftTime / (24 * 60 * 60));
        const hours = Math.floor((leftTime % (24 * 60 * 60)) / (60 * 60));
        const minutes = Math.floor((leftTime % (60 * 60)) / 60);

        return { days, hours, minutes };
      };
    }
  },
  created() {
    this.loadContractList();
  },
  onLoad(options) {
    console.log("options", options);
    if (options.open_from === "notification") {
      this.page_from = "notification";
      this.student_name = options.student_name;
      this.student_id = options.student_id;
      this.open_id = options.open_id;
      this.contract_no = options.contract_no;
      uni.setNavigationBarTitle({
        title: `${this.student_name}的电子合同`
      });
      this.handleSign(options.contract_no);
    }
  },
  onReady() {},
  methods: {
    // 格式化日期
    formatDate(date) {
      if (!date) return "";
      const d = new Date(date);
      const year = d.getFullYear();
      const month = String(d.getMonth() + 1).padStart(2, "0");
      const day = String(d.getDate()).padStart(2, "0");
      const hours = String(d.getHours()).padStart(2, "0");
      const minutes = String(d.getMinutes()).padStart(2, "0");
      const seconds = String(d.getSeconds()).padStart(2, "0");
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },
    // 更新所有待签署合同的倒计时
    updateCountdowns() {
      this.contractList.forEach((item) => {
        if (item.status === "signing" && item.left_time > 0) {
          this.$set(this.countdowns, item.id, item.left_time);
        }
      });
    },

    // 启动定时器
    startTimer() {
      this.clearTimer();

      if (this.current === 0) {
        this.updateCountdowns();

        this.timer = setInterval(() => {
          let hasValidContract = false;

          Object.keys(this.countdowns).forEach((id) => {
            if (this.countdowns[id] > 0) {
              this.countdowns[id]--;
              hasValidContract = true;
              // console.log("倒计时中", this.countdowns[id]);
            } else {
              // 倒计时结束时的处理
              // 从countdowns中移除
              this.$delete(this.countdowns, id);
              // 从contractList中移除对应的合同
              const index = this.contractList.findIndex(
                (item) => item.id === id
              );
              if (index > -1) {
                this.contractList.splice(index, 1);
              }
              // 如果列表为空,显示空提示
              if (this.contractList.length === 0) {
                this.showEmpty = true;
              }
            }
          });

          if (!hasValidContract) {
            this.clearTimer();
          }
        }, 1000);
      }
    },

    // 清除定时器
    clearTimer() {
      if (this.timer) {
        clearInterval(this.timer);
        this.timer = null;
      }
    },

    handleTabChange(index) {
      this.current = index;
      this.clearTimer();
      this.loadContractList();
    },

    handlePreview(item) {
      if (item.document_url) {
        // 加载中
        uni.showToast({
          title: "加载中...",
          icon: "loading"
        });
        uni.downloadFile({
          url: item.document_url,
          success: (res) => {
            uni.openDocument({
              filePath: res.tempFilePath,
              fileType: "pdf",
              showMenu: true,
              success: () => {
                uni.hideToast();
              }
            });
          },
          fail: () => {
            uni.hideToast();
            uni.showToast({
              title: "预览失败",
              icon: "none"
            });
          }
        });
      } else {
        uni.showToast({
          title: "合同不存在",
          icon: "none"
        });
      }
    },

    async loadContractList() {
      const statusMap = ["signing", "done", "failed"];
      const status = statusMap[this.current];

      this.contractList = [];
      this.countdowns = {};

      const studentId =
        this.page_from === "notification"
          ? this.student_id
          : uni.getStorageSync("curStudentInfo").student_id;
      try {
        // 加载中
        uni.showToast({
          title: "加载中...",
          icon: "loading"
        });
        this.showEmpty = false;
        const res = await getContractList({
          student_id: studentId,
          status
        });
        uni.hideToast();
        if (res.code === 0) {
          if (res.data.results) {
            // this.contractList = [];
            this.contractList = res.data.results.map((item) => {
              return {
                contract_no: item.contract_no,
                id: item.id,
                document_url: item.document_url,
                organization_name: item.organization_name,
                manager_name: item.manager_name,
                user_name: item.user_name,
                status: item.status,
                contract_title: item.contract_title,
                apply_time: item.apply_time,
                left_time: item.left_time,
                callback_status: item.callback_status
              };
            });
            if (this.contractList.length > 0) {
              this.startTimer();
            } else {
              this.showEmpty = true;
            }
          } else {
            this.contractList = [];
            this.showEmpty = true;
          }
        }
      } catch (e) {
        uni.hideToast();
        this.showEmpty = true;
        uni.showToast({
          title: "获取数据失败",
          icon: "none"
        });
      }
    },

    async handleSign(contract) {
      // 处理签署事件
      uni.showToast({
        title: "加载中...",
        icon: "loading"
      });
      const res = await getContractSchemaUrl({
        contract_no: contract.contract_no
      });
      uni.hideToast();
      if (res.code === 0) {
        const path = res.data;

        console.log("res", res);
        uni.showToast({
          title: "即将跳转到腾讯电子签",
          icon: "none"
        });
        uni.navigateToMiniProgram({
          appId: "wxa023b292fd19d41d", // 电子签appId; 联调时, 请使用demo小程序appId: 'wx371151823f6f3edf'
          path, // 跳转的页面路径，可选，默认跳转到目标小程序首页; 签署时，需使用后台API返回的完整链接（类似pages/guide?id=xxx&foo=bar）
          extraData: {
            // 需要传递给目标小程序的数据，可选；签署时，腾讯电子签小程序未使用到该参数
            // foo: "bar"
          },
          envVersion: "release", // 跳转正式或demo小程序，都需要传 'release'
          success(res) {
            // 成功跳转到目标小程序后的回调函数
          },
          fail(res) {
            // 跳转失败的回调函数
            uni.showToast({
              title: "跳转失败",
              icon: "none"
            });
          }
        });
      } else {
        uni.showToast({
          title: "获取腾讯电子签链接失败",
          icon: "none"
        });
      }
    }
  },

  beforeUnmount() {
    this.clearTimer();
  }
};
</script>

<style lang="scss" scoped>
.e-sign {
  min-height: 100vh;
  background: #f5f5f5;

  &-header {
    background: #fff;
    position: sticky;
    top: 0;
    z-index: 100;
  }

  &-content {
    padding: 30rpx 32rpx;
  }
}

.contract-list {
  .contract-item {
    position: relative;
    background: #fff;
    padding: 30rpx 24rpx;
    margin-bottom: 20rpx;
    // display: flex;
    // justify-content: space-between;
    border-radius: 24rpx;
    overflow: hidden;

    .arrow {
      color: #ffc525;
      font-size: 24rpx;
      margin-left: 4rpx;
    }

    .contract-info {
      flex: 1;
      // margin-right: 20rpx;

      .contract-title {
        font-size: 30rpx;
        color: #333;
        margin-bottom: 30rpx;
        font-weight: 500;
        line-height: 1.4;
      }

      .contract-detail {
        position: relative;

        .detail-row {
          display: flex;
          font-size: 28rpx;
          color: #999;
          margin-bottom: 12rpx;
          // line-height: 34rpx;

          .label {
            width: 160rpx;
            color: #999;
          }

          .value {
            flex: 1;
            color: #333;
            padding-left: 20rpx;
          }
        }
      }

      .download-wrap {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .contract-title {
          width: 450rpx;
        }

        .download-btn {
          font-size: 26rpx;
          color: #fb0;
          margin-left: 12rpx;
          margin-bottom: 16rpx;
          font-weight: 500;
        }
      }
    }

    .contract-action {
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-bottom: 1rpx solid #f5f2f2;
      padding-bottom: 24rpx;
      margin-bottom: 24rpx;

      .countdown-num-wrap {
        display: flex;
        align-items: center;
        margin-right: 18rpx;
      }

      .countdown {
        font-size: 24rpx;
        color: #666;
        display: flex;
        align-items: center;

        .countdown-num {
          color: #fff;
          // margin: 0 4rpx;
          background: #fb0;
          width: 36rpx;
          height: 36rpx;
          border-radius: 4rpx;
          text-align: center;
          line-height: 36rpx;
        }

        .countdown-unit {
          margin: 0 8rpx;
        }
      }

      .action-btn {
        display: flex;
        align-items: center;
        font-weight: 500;
        width: 100rpx;
        height: 100%;
        justify-content: flex-end;

        .go-sign {
          font-size: 26rpx;
          color: #fb0;
        }
      }
    }

    .status-img {
      position: absolute;
      right: -25rpx;
      bottom: -28rpx;
      width: 185rpx;
      height: 200rpx;
    }
  }
}

.empty-wrap {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 80vh;
  margin-top: -54rpx;
}

.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
