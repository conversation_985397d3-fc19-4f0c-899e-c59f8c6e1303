<template>
  <div class="questionFillPage">
    <!-- <web-view :webview-styles="webviewStyles" src="https://tg-dev.estar-go.com/question?surveyId=24032201"></web-view> -->
    <web-view
      :webview-styles="webviewStyles"
      @message="handleMessage"
      :src="webViewSrc"
    ></web-view>
  </div>
</template>

<script>
import { getOpenId, login } from "@/services/student/home";
export default {
  components: {},
  data() {
    return {
      surveyId: "",
      webViewSrc: "",
      baseUrl: process.env.VUE_APP_TG_HOST,
      // baseUrl: "http://localhost:8080",
      // baseUrl: "http://*************:8080",
      curStudentInfo: {},
      session: {},
      webviewStyles: {
        progress: {
          color: "#FF3333"
        }
      }
    };
  },
  computed: {},
  methods: {
    arouseLogin(option) {
      console.log(111);
      const that = this;
      uni.login({
        provider: "weixin",
        onlyAuthorize: true, // 微信登录仅请求授权认证
        success: async function (event) {
          const { code } = event;
          console.log(code, event);
          const res = await getOpenId({ code: event.code });
          if (res.code === 0) {
            const res2 = await login({ open_id: res.data.openid });
            if (res2.code === 0) {
              const role =
                !res2.data.is_student && !res2.data.is_customer
                  ? "default"
                  : res2.data.is_student
                  ? "student"
                  : "customer";
              const roleKey = {
                default: 1,
                student: 3,
                customer: 2
              };
              const visitor = roleKey[role] ?? 1;
              console.log(visitor);
              that.webViewSrc = `${that.baseUrl}/question?survey_id=${option.survey_id}&student_id=${option.student_id}&from=mini&token=${res2.data.token}&survey_student_id=${option.survey_student_id}&visitor=${visitor}`;
              console.log(that.webViewSrc);
            }
          }
          // uni.request({
          //   url: "https://tg-api-dev.estar-go.com/web/questionnaire-web-service/public/getOpenId",
          //   data: {
          //     code: event.code
          //   },
          //   success: (res) => {
          //     // 获得token完成登录
          //     const { code, data } = res.data;
          //     if (code === 0) {
          //       uni.request({
          //         url: "https://tg-api-dev.estar-go.com/web/questionnaire-web-service/public/login",
          //         method: "post",
          //         data: {
          //           open_id: data.openid
          //         },
          //         success: (res) => {
          //           // 获得token完成登录
          //           const { code, data } = res.data;
          //           if (code === 0) {
          //           }
          //         }
          //       });
          //     }
          //   }
          // });
        }
      });
    },
    handleMessage(e) {
      const data = e.detail.data[0];
      console.log(data, "data");
      // if (data.action === "navigateBack") {
      //   uni.navigateBack(); // 执行返回
      // }
      // const { data } = e.detail;
      // console.log(e, "data");
      // if (data && data[0]?.action === "navigateBack") {
      //   uni.navigateBack();
      // }
    }
  },
  watch: {},

  // 页面周期函数--监听页面加载
  onLoad(option) {
    uni.onAppRoute((e) => {
      console.log(e, "e");
      if (e.path === "uniwebview://navigateBack") {
        uni.navigateBack();
      }
    });
    this.curStudentInfo = uni.getStorageSync("curStudentInfo");
    this.session = uni.getStorageSync("session");
    this.surveyId = option.surveyId;
    if (option.from === "push") {
      this.arouseLogin(option);
    } else {
      this.webViewSrc = `${this.baseUrl}/question?visitor=${option.visitor}&survey_id=${option.surveyId}&student_id=${this.curStudentInfo.student_id}&from=mini&token=${this.session.token}&survey_student_id=${option.survey_student_id}&write_status=${option.write_status}`;
      console.log(this.webViewSrc);
      console.log(
        `survey_id=${option.surveyId}&student_id=${this.curStudentInfo.student_id}&from=mini&token=${this.session.token}&survey_student_id=${option.survey_student_id}&write_status=${option.write_status}`
      );
    }
  },
  // 页面周期函数--监听页面初次渲染完成
  onReady() {},
  // 页面周期函数--监听页面显示(not-nvue)
  onShow() {},
  // 页面周期函数--监听页面隐藏
  onHide() {},
  // 页面周期函数--监听页面卸载
  onUnload() {}
  // 页面处理函数--监听用户下拉动作
  // onPullDownRefresh() { uni.stopPullDownRefresh(); },
  // 页面处理函数--监听用户上拉触底
  // onReachBottom() {},
  // 页面处理函数--监听页面滚动(not-nvue)
  // onPageScroll(event) {},
  // 页面处理函数--用户点击右上角分享
  // onShareAppMessage(options) {},
};
</script>

<style lang="scss" scoped></style>
