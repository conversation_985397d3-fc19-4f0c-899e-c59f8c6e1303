<template>
  <div class="questionnaire-survey-page">
    <!-- <div class="tab">
      <div class="tab-list">
        <div
          :class="[
            'tab-item',
            listParams.submitState === item.value ? 'active' : ''
          ]"
          @click="handleTabChange(item)"
          v-for="item in tabList"
          :key="item.value"
        >
          <span class="tab-item__text">{{ item.label }}</span>
        </div>
      </div>
    </div> -->
    <!-- <u-navbar
      title="调查问卷"
      bgColor="#fff"
      leftIconSize="25px"
      leftIconColor="#333333"
      :titleStyle="{
        color: '#333333',
        fontSize: '34rpx',
        fontWeight: '500',
        lineHeight: '40rpx'
      }"
      :autoBack="true"
      placeholder
    >
      <view class="nav-letf" slot="left">
        <image
          src="https://tg-prod.oss-cn-beijing.aliyuncs.com/e9ac0f4b-6282-4bac-b602-189057c45c90.webp"
        ></image>
      </view>
    </u-navbar> -->
    <custom-tabs
      :list="tabList"
      :current="curActive"
      @change="handleTabChange"
    ></custom-tabs>
    <div class="survey-list">
      <div v-if="surveys.length">
        <div class="survey-item" v-for="item in surveys" :key="item.surveyId">
          <div class="survey-item-left">
            <div style="display: flex; align-items: center">
              <div
                :class="[
                  'write-status',
                  item.write_status === 1 ? 'yes' : 'no'
                ]"
              >
                {{ item.write_status === 1 ? "已填写" : "未填写" }}
              </div>
              <div class="survey-title">{{ item.title }}</div>
            </div>
            <div class="survey-time">
              {{
                $ljsPublic.date.formatTime(
                  new Date(item.send_time).getTime(),
                  "{y}-{m}-{d} {h}:{i}"
                )
              }}
            </div>
          </div>
          <div class="survey-item-right">
            <view
              type="primary"
              shape="circle"
              @click="toSurvey(item)"
              :class="
                (curActive === 0 && item.write_status === 2) ||
                (curActive === 1 && item.write_status === 2)
                  ? 'survey-item-right-button'
                  : 'survey-item-right-button-go'
              "
            >
              {{ item.write_status === 1 ? "查看问卷" : "点击进入" }}
            </view>
          </div>
        </div>
      </div>
      <div
        v-else
        style="
          height: 80vh;
          display: flex;
          align-items: center;
          justify-content: center;
        "
      >
        <u-empty
          text="暂无问卷～"
          :width="'163rpx'"
          :height="'230rpx'"
          textColor="#999999"
          marginTop="-45rpx"
          icon="https://tg-prod.oss-cn-beijing.aliyuncs.com/d02814d0-bfd3-48bf-8d9d-80cda362b89a.webp"
        >
        </u-empty>
        <!-- <u-empty text="暂无问卷~" style="height: 100%"> </u-empty> -->
      </div>
    </div>
  </div>
</template>

<script>
import {
  surveyList,
  customerList
} from "@/services/student/questionnaireSurvey";
import CustomTabs from "@/components/tabs/index.vue";
export default {
  name: "QuestionnaireSurvey",
  components: {
    CustomTabs
  },
  data() {
    return {
      role: "",
      tabList: [
        {
          name: "进行中",
          value: 1
        },
        {
          name: "未开始",
          value: 3
        },
        {
          name: "已结束",
          value: 2
        }
      ],
      curActive: 0,
      curStudentInfo: {},
      session: {},
      surveys: [],
      listParams: {
        submitState: 1
      },
      student_id: ""
    };
  },
  computed: {},
  methods: {
    handleTabChange(item) {
      console.log(item, "item");
      this.curActive = item;
      this.listParams.submitState = this.tabList[item].value;
      if (this.role === "student") {
        this.getSurveyList();
      } else {
        this.getCustomerList();
      }
    },
    async getSurveyList() {
      this.listParams.studentId =
        this.student_id || this.curStudentInfo.student_id;
      if (!this.listParams.studentId) {
        return;
      }
      const { code, data } = await surveyList(this.listParams);
      this.getList(code, data);
    },
    async getCustomerList() {
      this.listParams.customerId = this.curStudentInfo.customer_id;
      if (!this.listParams.customerId) {
        return;
      }
      const { code, data } = await customerList(this.listParams);
      this.getList(code, data);
    },
    getList(code, data) {
      if (code === 0) {
        this.surveys = data.results || [];
      }
    },
    toSurvey(item) {
      console.log(item);
      const roleKey = {
        default: 1,
        student: 3,
        customer: 2
      };
      const visitor = roleKey[uni.getStorageSync("session").role] ?? 1;
      console.log(visitor);
      if (item.surveyType === "satisfaction") {
        uni.navigateTo({
          url: `/pages/student/subpages/dtb/index?from=menu&surveyPath=${item.surveyId}&surveyStudentId=${item.survey_student_id}&customerId=${this.curStudentInfo.customer_id}&fillStutas=${this.listParams.submitState}`
        });
      } else {
        uni.navigateTo({
          url: `/pages/student/questionnaireSurvey/questionFillPage?visitor=${visitor}&surveyId=${item.surveyId}&survey_student_id=${item.survey_student_id}&write_status=${this.curActive}`
        });
      }
    }
  },
  watch: {},

  // 页面周期函数--监听页面加载
  onLoad(option) {
    if (option.student_id) {
      this.student_id = option.student_id;
      uni.setNavigationBarTitle({
        title: option.student_name + "的问卷调查"
      });
    }
  },
  // 页面周期函数--监听页面初次渲染完成
  onReady() {},
  // 页面周期函数--监听页面显示(not-nvue)
  onShow() {
    this.curStudentInfo = uni.getStorageSync("curStudentInfo");
    this.session = uni.getStorageSync("session");
    this.role = this.session.role;
    if (this.session.role === "student") {
      this.getSurveyList();
    } else {
      this.getCustomerList();
    }
  },
  // 页面周期函数--监听页面隐藏
  onHide() {},
  // 页面周期函数--监听页面卸载
  onUnload() {}
  // 页面处理函数--监听用户下拉动作
  // onPullDownRefresh() { uni.stopPullDownRefresh(); },
  // 页面处理函数--监听用户上拉触底
  // onReachBottom() {},
  // 页面处理函数--监听页面滚动(not-nvue)
  // onPageScroll(event) {},
  // 页面处理函数--用户点击右上角分享
  // onShareAppMessage(options) {},
};
</script>

<style lang="scss" scoped>
.questionnaire-survey-page {
  background: #f5f5f5;
  height: 100vh;

  .nav-letf {
    width: 40rpx;
    height: 40rpx;

    image {
      width: 100%;
      height: 100%;
    }
  }

  .tab {
    width: 100%;

    .tab-list {
      background: #fff;
      box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.05);
      display: flex;
      color: #999;

      .tab-item {
        flex: 1;
        text-align: center;
        position: relative;
        transition: color 0.3s ease-in-out;

        &.active {
          color: #ffbf0d;

          .tab-item__text {
            position: relative;

            &::after {
              content: "";
              position: absolute;
              bottom: -6rpx;
              left: 50%;
              transform: translateX(-50%) scaleX(1);
              width: 40rpx;
              height: 6rpx;
              background: #ffbf0d;
              border-radius: 3rpx;
              transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
              opacity: 1;
            }
          }
        }

        .tab-item__text {
          padding: 28rpx 0;
          display: inline-block;
          font-size: 30rpx;
          font-style: normal;
          font-weight: 400;
          position: relative;

          &::after {
            content: "";
            position: absolute;
            bottom: -6rpx;
            left: 50%;
            transform: translateX(-50%) scaleX(0);
            width: 40rpx;
            height: 6rpx;
            background: #ffbf0d;
            border-radius: 3rpx;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            opacity: 0;
          }
        }
      }
    }
  }

  .survey-list {
    margin-top: 16px;

    .survey-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      // border-bottom: 1px solid #e5e5e5;
      padding: 0 24rpx;
      background: #fff;
      border-radius: 22rpx;
      // box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.05);
      margin: 30rpx 32rpx;
      min-height: 148rpx;
      // padding-top: 34rpx;

      &:last-child {
        border-bottom: none;
      }

      .survey-item-left {
        display: flex;
        flex-direction: column;
        align-content: center;
        min-width: 0;
        padding-right: 30rpx;
        .write-status {
          padding: 3rpx 8rpx;
          border-radius: 6rpx;
          color: #999;
          font-size: 22rpx;
          margin-right: 12rpx;
          font-weight: 400;
          &.yes {
            border: 1rpx solid #ff7300;
            color: #ff7300;
          }
          &.no {
            border: 1rpx solid #2d80ed;
            color: #2d80ed;
          }
        }
        .survey-title {
          min-width: 0;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
          font-size: 30rpx;
          font-style: normal;
          flex: 1;
          font-weight: 500;
          line-height: normal;
        }

        .survey-time {
          color: #999;
          font-size: 26rpx;
          font-style: normal;
          font-weight: 400;
          line-height: 34rpx;
          padding-top: 12rpx;
          /* 130.769% */
        }
      }

      .survey-item-right {
        ::v-deep .survey-item-right-button {
          width: 160rpx;
          height: 64rpx;
          border-radius: 32rpx;
          background: linear-gradient(15deg, #ffbf0d 18.1%, #ffcb3c 83.29%);
          box-shadow: 0px -4px 8px 0px #eaac00 inset;
          border: none;
          color: #fff;
          font-size: 24rpx;
          font-style: normal;
          line-height: 32rpx;
          font-weight: 500;
          display: flex;
          justify-content: center;
          align-items: center;
          /* 133.333% */
        }
        .survey-item-right-button-go {
          width: 160rpx;
          height: 64rpx;
          border-radius: 32rpx;
          background: rgba(255, 244, 213, 0.5);
          // box-shadow: 0px -4px 8px 0px #eaac00 inset;
          border: none;
          color: #ffc525;
          font-size: 24rpx;
          font-style: normal;
          font-weight: 600;
          line-height: 32rpx;
          font-weight: 500;
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }
    }
  }
}
</style>
