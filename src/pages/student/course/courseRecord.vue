<template>
  <div class="course-record">
    <view class="header-row">
      <view class="date-selector" @click="showDatePicker = true">
        <text v-if="search_time">{{ currentYear }}年{{ currentMonth }}月</text>
        <text v-else>全部时间</text>
        <u-icon
          name="https://tg-prod.oss-cn-beijing.aliyuncs.com/1df5cbe4-ec23-4d7c-aecf-90b3e2fd66a8.webp"
          width="18rpx"
          height="12rpx"
        ></u-icon>
      </view>

      <view class="header-notice">
        <u-icon
          color="#ff9500"
          size="24rpx"
          name="https://tg-prod.oss-cn-beijing.aliyuncs.com/5b5f969d-ad40-49cd-9a43-e98d82cf0117.png"
        ></u-icon>
        <text class="notice-text">仅可查询2025年8月30日之后课消数据</text>
      </view>
    </view>
    <view @touchmove.stop.prevent="">
      <u-picker
        :show="showDatePicker"
        :columns="[years, months]"
        @confirm="confirmDate"
        @cancel="showDatePicker = false"
        :defaultIndex="[yearIndex, monthIndex]"
        confirmColor="#333"
      ></u-picker>
    </view>

    <div class="course-record-header"></div>
    <div
      :class="['course-record-list', courseRecord.length === 0 ? 'empty' : '']"
    >
      <div
        class="course-record-item"
        v-for="(item, index) in courseRecord"
        :key="index"
      >
        <div class="course-info">
          <div class="course-info-header">
            <div class="class-info">
              <div class="class-name">
                <div class="class-num">{{ item.SchoolroomName }}</div>
                {{ item.courseName }}
              </div>
            </div>
            <div class="another-name">{{ item.classroom_alias }}</div>
          </div>
          <div class="teacher-info">
            <div class="avatar">
              <u-image
                :src="teacherIcon"
                width="54rpx"
                height="54rpx"
                :showMenuByLongpress="false"
              ></u-image>
            </div>
            <div class="teacher-name">任课老师：{{ item.teacherName }}</div>
          </div>
          <div class="course-consumption-info">
            <div class="info" style="font-size: 26rpx">
              <div>扣费课程：</div>
              <div style="flex: 1">{{ item.deduct_course_name }}</div>
            </div>
            <div class="info" v-if="item.deduct_number_is_show === 1">
              扣课数量：{{ item.deduct_number }}课时
            </div>
            <div class="info" v-if="item.deduct_price_is_show === 1">
              课消金额：¥{{ item.deduct_price.toFixed(2) }}
            </div>
          </div>
        </div>
        <div class="course-time">
          上课时间：{{ $u.timeFormat(item.startTime, "yyyy-mm-dd hh:MM:ss") }}
        </div>
      </div>
      <!-- <div class="course-record-item">
        <div class="course-info">
          <div class="course-info-header">
            <div class="class-info">
              <div class="class-num">201教师</div>
              <div class="class-name">N1-系统课系统课系统课系统课</div>
            </div>
            <div class="another-name">这是班级别名这是班级别名</div>
          </div>
          <div class="teacher-info">
            <div class="avatar">
              <u-image
                :src="teacherIcon"
                width="54rpx"
                height="54rpx"
              ></u-image>
            </div>
            <div class="teacher-name">任课老师：赵老是</div>
          </div>
        </div>
        <div class="course-time">上课时间：2025-02-10 09:00:00</div>
      </div> -->
      <empty
        text="暂无课消数据~"
        v-if="showEmpty && courseRecord.length === 0"
      />
    </div>
  </div>
</template>

<script>
import { getCourseRecord } from "@/services/student/course.js";
import Empty from "@/components/empty";
import { getYearList } from "@/utils/date";
export default {
  name: "CourseRecord",
  components: { Empty },
  data() {
    return {
      teacherIcon:
        "https://tg-prod.oss-cn-beijing.aliyuncs.com/2b2711d3-8efd-49a5-9744-b7e8ff7970d1.png",
      showDatePicker: false,
      currentYear: new Date().getFullYear().toString(),
      currentMonth: (new Date().getMonth() + 1).toString().padStart(2, "0"),
      years: [...getYearList()],
      months: Array.from({ length: 12 }, (_, i) =>
        (i + 1).toString().padStart(2, "0")
      ),
      courseRecord: [],
      showEmpty: false,
      curStudentInfo: {},
      yearIndex: 0,
      monthIndex: 0,
      search_time: "",
      student_id: ""
    };
  },
  computed: {},
  created() {
    this.curStudentInfo = uni.getStorageSync("curStudentInfo");
    this.initYears(false);
    this.getCourseRecord();
  },
  methods: {
    initYears(isInit = true) {
      // const currentYear = new Date().getFullYear();
      // const years = [];
      // for (let year = 2025; year <= currentYear + 10; year++) {
      //   years.push(year);
      // }
      // this.years = years;
      const currentYear = new Date().getFullYear();
      // const currentMonth = new Date().getMonth() + 1;

      // // 找到当前年份的索引
      this.yearIndex = this.years.indexOf(currentYear);
      // // 找到当前月份的索引
      this.monthIndex = this.months.indexOf(this.currentMonth);
      if (isInit) {
        this.search_time = `${this.currentYear}-${this.currentMonth}-01`;
      }
    },
    confirmDate(e) {
      const [year, month] = e.value;
      this.currentYear = year;
      this.currentMonth = month;
      this.page = 1;
      this.courseRecord = [];
      this.showDatePicker = false;
      this.search_time = `${this.currentYear}-${this.currentMonth}-01`;
      this.getCourseRecord();
    },
    async getCourseRecord() {
      this.courseRecord = [];
      this.showEmpty = false;
      try {
        // const month = this.currentMonth.toString().padStart(2, "0");
        const { code, data } = await getCourseRecord({
          page: 1,
          page_size: 999,
          student_id: this.student_id || this.curStudentInfo.student_id,
          search_time: this.search_time
        });
        if (code === 0) {
          this.courseRecord = data?.results || [];
          this.showEmpty = this.courseRecord.length === 0;
        } else {
          this.courseRecord = [];
          this.showEmpty = true;
        }
      } catch (error) {
        this.showEmpty = true;
      }
    }
  },
  watch: {},

  // 页面周期函数--监听页面加载
  onLoad(option) {
    if (option.student_id) {
      this.student_id = option.student_id;
      uni.setNavigationBarTitle({
        title: option.student_name + "的课消记录"
      });
    }
  },
  // 页面周期函数--监听页面初次渲染完成
  onReady() {},
  // 页面周期函数--监听页面显示(not-nvue)
  onShow() {
    this.getCourseRecord();
  },
  // 页面周期函数--监听页面隐藏
  onHide() {},
  // 页面周期函数--监听页面卸载
  onUnload() {}
  // 页面处理函数--监听用户下拉动作
  // onPullDownRefresh() { uni.stopPullDownRefresh(); },
  // 页面处理函数--监听用户上拉触底
  // onReachBottom() {},
  // 页面处理函数--监听页面滚动(not-nvue)
  // onPageScroll(event) {},
  // 页面处理函数--用户点击右上角分享
  // onShareAppMessage(options) {},
};
</script>

<style lang="scss" scoped>
.course-record {
  padding: 24rpx 32rpx;
  background: #f5f5f5;
  min-height: 100vh;
  height: auto;
  .header-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 30rpx;
    gap: 16rpx;
  }

  .date-selector {
    display: inline-flex;
    padding: 9rpx 24rpx;
    gap: 10rpx;
    border-radius: 12rpx;
    background: #fff;
    font-size: 26rpx;
    font-weight: 400;
    line-height: normal;
    color: #666;
    height: 56rpx;
    align-items: center;
    flex-shrink: 0;
  }

  .header-notice {
    display: flex;
    align-items: center;
    gap: 6rpx;
    padding: 10rpx 10rpx;
    background: #fff6e6;
    border-radius: 12rpx;
    // border-left: 3rpx solid #ff9500;
    flex: 1;
    min-width: 0;
    margin-left: 30rpx;
    .notice-text {
      font-size: 26rpx;
      color: #ff9500;
      font-weight: 400;
      line-height: 1.2;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
  .course-record-list {
    display: flex;
    flex-direction: column;
    min-height: calc(100vh - 160rpx);
    &.empty {
      justify-content: center;
    }
    .course-record-item {
      padding: 34rpx 24rpx 24rpx 24rpx;
      border-radius: 24rpx;
      background: #fff;
      box-shadow: 0px 0px 50.0025px 0px rgba(124, 143, 166, 0.1);
      margin-bottom: 30rpx;
      &:last-child {
        margin-bottom: 0;
      }
      .course-info {
        padding-bottom: 24rpx;
        .course-info-header {
          padding-bottom: 22rpx;
          .class-info {
            display: flex;
            align-items: flex-start;
            margin-bottom: 8rpx;
            .class-num {
              padding: 3rpx 8rpx;
              border-radius: 6rpx;
              border: 0.9975px solid #ff7300;
              color: #ff7300;
              font-size: 22rpx;
              font-weight: 400;
              margin-right: 16rpx;
              line-height: 28rpx; /* 127.273% */
              flex-shrink: 0;
              display: inline-block;
              vertical-align: top;
            }
            .class-name {
              font-weight: 500;
              font-size: 30rpx;
              color: #333;
            }
          }
          .another-name {
            font-size: 24rpx;
            color: #999;
            font-weight: 400;
            // -webkit-line-clamp: 2; /* 默认两行 */
            overflow: hidden;
            // text-overflow: ellipsis;
            // display: -webkit-box;
            // -webkit-box-orient: vertical;
            // word-break: break-all;
            line-height: 1.5em;
          }
        }
        .teacher-info {
          padding-bottom: 24rpx;
          display: flex;
          align-items: center;
          .avatar {
            margin-right: 12rpx;
          }
          .teacher-name {
            color: #666;
            font-size: 26rpx;
            font-weight: 400;
          }
        }
        .course-consumption-info {
          border-radius: 12rpx 6rpx 12rpx 12rpx;
          padding: 20rpx 16rpx;
          border: 0.9975px solid #ffdf6c;
          background: #fffbea;
          padding-bottom: 16rpx;
          .info {
            margin-bottom: 6rpx;
            color: #dea50a;
            font-size: 24rpx;
            font-weight: 400;
            display: flex;
            // align-items: center;
            &:last-child {
              margin-bottom: 0;
            }
          }
        }
      }
      .course-time {
        // padding-top: 24rpx;
        font-size: 24rpx;
        font-weight: 400;
        color: #666;
      }
    }
  }
}
::v-deep .u-popup__content {
  border-radius: 24rpx;
}
::v-deep .u-empty {
  margin-top: -45rpx !important;
}
</style>
