<template>
  <div class="course-page">
    <!-- <div class="tab">
      <div class="tab-list">
        <div
          :class="['tab-item', curActive === item.value ? 'active' : '']"
          @click="handleTabChange(item)"
          v-for="item in tabList"
          :key="item.value"
        >
          <span class="tab-item__text">{{ item.label }}</span>
        </div>
      </div>
    </div> -->
    <custom-tabs
      :list="tabList"
      :current="curActive"
      @change="handleTabChange"
    ></custom-tabs>
    <div :class="['course-list', courseList.length === 0 ? 'empty' : '']">
      <div
        class="course-item"
        v-for="item in courseList"
        :key="item.course_id"
        :class="item.last_status ? 'active-course' : ''"
      >
        <div v-if="item.last_status" class="course-status">
          <u-image
            :src="courseStatus[item.last_status]"
            width="160rpx"
            height="200rpx"
            style="top: 15px"
            :showMenuByLongpress="false"
          ></u-image>
        </div>
        <div
          :style="{
            backgroundImage: `url(${item.cover_url})`
          }"
          class="cover"
        ></div>
        <div class="course-info">
          <div class="course-name">
            {{ item.good_name }}
          </div>
          <div v-if="item.number_left_is_show === 1" class="surplus-quantity">
            <span>剩余数量：</span>
            <span>{{ item.number_left }}</span>
          </div>
          <div
            v-if="item.price_left_is_show === 1"
            class="surplus-quantity"
            style="margin-bottom: 0"
          >
            <span>剩余金额：</span>
            <span>{{ item.price_left.toFixed(2) }}元</span>
          </div>
        </div>
      </div>
      <!-- 空组件 -->
      <empty text="暂无数据~" v-if="showEmpty && courseList.length === 0" />
    </div>
    <div class="course-footer">
      <div class="to-course-record" @click="toCourseRecord">课消记录</div>
    </div>
  </div>
</template>

<script>
import { getCourseList } from "@/services/student/course";
import Empty from "@/components/empty";
import CustomTabs from "@/components/tabs/index.vue";
export default {
  name: "courseIndex",
  components: { Empty, CustomTabs },
  data() {
    return {
      curActive: 0,

      courseList: [],
      showEmpty: false,
      tabList: [
        {
          name: "剩余课程",
          value: 2
        },
        {
          name: "全部课程",
          value: 1
        }
      ],
      curStudentInfo: {},
      courseStatus: {
        refund:
          "https://tg-prod.oss-cn-beijing.aliyuncs.com/1ec62f5b-c2e9-4825-9f8d-9d1399a7fd40.png",
        deduct:
          "https://tg-prod.oss-cn-beijing.aliyuncs.com/f24a81bc-f8bf-48a2-909d-d67c9d63476b.png",
        transfer:
          "https://tg-prod.oss-cn-beijing.aliyuncs.com/a3efc483-bd79-458e-a4f1-473acea7ef98.png"
      },
      session: {},
      safe_bottom: 0
    };
  },
  computed: {},
  methods: {
    toCourseRecord() {
      uni.navigateTo({
        url: "/pages/student/course/courseRecord"
      });
    },
    handleTabChange(item) {
      this.curActive = item;
      this.getCourseList();
    },
    async getCourseList() {
      this.courseList = [];
      const params = {
        student_id: this.curStudentInfo.student_id,
        page: 1,
        page_size: 999,
        has_left: this.curActive === 0
      };
      try {
        const { code, data } = await getCourseList(params);
        if (code === 0) {
          this.courseList = data.results.map((item) => {
            return {
              ...item,
              last_status: item.last_status,
              cover_url:
                item.cover_url ||
                "https://tg-prod.oss-cn-beijing.aliyuncs.com/52da6c6e-f408-431e-a613-ce7fc8bbd2e3.webp"
            };
          });
          this.showEmpty = this.courseList.length === 0;
        } else {
          this.showEmpty = true;
        }
      } catch (error) {
        this.showEmpty = true;
      }
    }
  },
  watch: {},

  // 页面周期函数--监听页面加载
  onLoad() {
    const windowInfo = uni.getWindowInfo();
    console.log("windowInfo :>> ", windowInfo);
    const safe_bottom = windowInfo.safeAreaInsets.bottom;
    this.safe_bottom = safe_bottom;
  },
  // 页面周期函数--监听页面初次渲染完成
  onReady() {},
  // 页面周期函数--监听页面显示(not-nvue)
  onShow() {
    // uni.hideTabBar();
    this.curStudentInfo = uni.getStorageSync("curStudentInfo");
    this.session = uni.getStorageSync("session");
    this.getCourseList();
  },
  onLaunch: function () {
    uni.hideTabBar();
  },
  // 页面周期函数--监听页面隐藏
  onHide() {},
  // 页面周期函数--监听页面卸载
  onUnload() {}
  // 页面处理函数--监听用户下拉动作
  // onPullDownRefresh() { uni.stopPullDownRefresh(); },
  // 页面处理函数--监听用户上拉触底
  // onReachBottom() {},
  // 页面处理函数--监听页面滚动(not-nvue)
  // onPageScroll(event) {},
  // 页面处理函数--用户点击右上角分享
  // onShareAppMessage(options) {},
};
</script>

<style lang="scss" scoped>
::v-deep .u-popup__content {
  border-radius: 22rpx;
  overflow: hidden;
}
.course-page {
  .course-tab {
    width: 100%;
    height: 92rpx;
  }

  .tab {
    width: 100%;
    .tab-list {
      background: #fff;
      box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.05);
      display: flex;
      padding-bottom: 30rpx;
      color: #999;
      .tab-item {
        flex: 1;
        text-align: center;
        position: relative;
        transition: color 0.3s ease-in-out;
        &.active {
          color: #ffbf0d;
          .tab-item__text {
            position: relative;
            &::after {
              content: "";
              position: absolute;
              bottom: -6rpx;
              left: 50%;
              transform: translateX(-50%) scaleX(1);
              width: 40rpx;
              height: 6rpx;
              background: #ffbf0d;
              border-radius: 3rpx;
              transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
              opacity: 1;
            }
          }
        }
        .tab-item__text {
          padding: 28rpx 0;
          padding-bottom: 8rpx;
          display: inline-block;
          font-size: 30rpx;
          font-style: normal;
          font-weight: 400;
          position: relative;
          &::after {
            content: "";
            position: absolute;
            bottom: -6rpx;
            left: 50%;
            transform: translateX(-50%) scaleX(0);
            width: 40rpx;
            height: 6rpx;
            background: #ffbf0d;
            border-radius: 3rpx;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            opacity: 0;
          }
        }
      }
    }
  }
  .course-list {
    width: 100%;
    background: #f5f5f5;
    padding: 30rpx 32rpx;
    height: calc(100vh - 92rpx - 180rpx);
    overflow: auto;
    display: flex;
    flex-direction: column;
    &.empty {
      justify-content: center;
    }
    .active-course {
      opacity: 0.5;
    }
    .course-item {
      padding: 20rpx 18rpx;
      border-radius: 24rpx;
      background: #fff;
      box-shadow: 0px 0px 50px 0px rgba(124, 143, 166, 0.1);
      margin-bottom: 30rpx;
      display: flex;
      align-items: center;
      position: relative;
      &:last-child {
        margin-bottom: 0;
      }
      .course-status {
        width: 160rpx;
        height: 100%;
        position: absolute;
        right: 0;
        bottom: 0;
        img {
          width: 100%;
          height: 100%;
        }
      }
      .cover {
        width: 160rpx;
        height: 160rpx;
        border-radius: 18rpx;
        margin-right: 24rpx;
        flex-shrink: 0;
        background: #fff;
        background-size: cover;
        background-position: center;
        overflow: hidden;
      }
      .course-info {
        flex: 1;
        width: calc(100% - 160rpx - 24rpx);
        .course-name {
          font-size: 30rpx;
          color: #333;
          font-weight: 500;
          width: 100%;
          // white-space: nowrap;
          // overflow: hidden;
          margin-bottom: 12rpx;
          // text-overflow: ellipsis;
        }
        .surplus-quantity {
          font-size: 26rpx;
          font-weight: 400;
          color: #666;
          margin-bottom: 12rpx;
        }
      }
    }
  }
  .course-footer {
    width: 100%;
    height: 180rpx;
    padding: 18rpx 32rpx;
    .to-course-record {
      width: 100%;
      height: 92rpx;
      text-align: center;
      line-height: 92rpx;
      color: #fff;
      font-size: 32rpx;
      font-weight: 500;
      border-radius: 80rpx;
      background: linear-gradient(15deg, #ffbf0d 18.1%, #ffcb3c 83.29%);
      box-shadow: 0px -10px 18px 0px #f3b300 inset,
        0px 4px 20px 0px rgba(254, 197, 36, 0.47);
    }
  }
}
::v-deep .u-empty {
  margin-top: -45rpx !important;
}
</style>
