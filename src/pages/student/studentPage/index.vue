<template>
  <view class="student-page">
    <!-- 顶部标题 -->
    <u-navbar
      title="绑定学员信息"
      bgColor="#fff"
      leftIconSize="25px"
      leftIconColor="#333333"
      :titleStyle="{
        color: '#333333',
        fontSize: '34rpx',
        fontWeight: '500',
        lineHeight: '40rpx'
      }"
      :autoBack="false"
      @leftClick="handleLeftClick"
      placeholder
    >
      <!-- <view class="nav-letf" slot="left">
        <image
          src="https://tg-prod.oss-cn-beijing.aliyuncs.com/e9ac0f4b-6282-4bac-b602-189057c45c90.webp"
        ></image>
      </view> -->
    </u-navbar>
    <u-toast ref="uToast" style="z-index: 999999 !important"></u-toast>
    <!-- 学员列表 -->
    <view class="student-list">
      <view class="visitor-wrap">
        <view class="visitor-label">绑定关系</view>
        <view
          class="visitor-selector"
          @click="getUserRelation()"
          :class="{ borderCode: borderRed && !currentVisitor }"
        >
          <text
            class="visitor-selector-text visitor-selector-text-active"
            v-if="currentVisitor"
          >
            {{ currentVisitor }}
          </text>
          <text class="visitor-selector-text" v-else>请选择绑定关系</text>
          <u-icon
            :width="'22rpx'"
            :height="'14rpx'"
            :name="'https://tg-prod.oss-cn-beijing.aliyuncs.com/1df5cbe4-ec23-4d7c-aecf-90b3e2fd66a8.webp'"
          ></u-icon>
        </view>
      </view>
      <view
        class="student-list-wrap"
        v-for="(student, index) in studentListArr"
        :key="index"
      >
        <view
          :class="[
            'student-item',
            `student-item-${studentBj[student.student_type_int]}`
          ]"
          :style="{
            border:
              studentBorder[student.student_type_int] && selectedIndex === index
                ? `2rpx solid ${studentBorder[student.student_type_int]}`
                : '1px solid transparent;'
          }"
          v-if="student.department_name"
          @tap="selectStudent(index)"
        >
          <image
            class="avatar"
            src="https://tg-prod.oss-cn-beijing.aliyuncs.com/e5bd3c33-0f34-4b77-954a-6aa67dc64c30.webp"
            mode="aspectFill"
          />
          <view class="info">
            <view class="name"
              >{{ student.student_name
              }}<view
                class="tag"
                :class="'tag-' + studentTag[student.student_type_int]"
                >{{ studentType[student.student_type_int] }}</view
              >
            </view>
            <view class="student-id" v-if="student.student_type_int != 0"
              >学号：{{ student.student_number }}</view
            >
            <view class="region">校区：{{ student.department_name }}</view>
          </view>
          <image
            :src="studentSelected[student.student_type_int]"
            class="student-selected"
            :style="
              student.student_type_int === 0
                ? 'width: 131rpx; '
                : student.student_type_int === 2 ||
                  student.student_type_int === 3
                ? 'width: 151rpx; '
                : ''
            "
          />
        </view>
      </view>
    </view>

    <!-- 底部确认按钮 -->
    <view
      :class="['confirm-btn', { 'confirm-btn-disabled': selectedIndex === -1 }]"
      @tap="$u.throttle(handleConfirm, 1000)"
    >
      确定
    </view>

    <!-- 将 u-picker 替换为 u-popup -->
    <view @touchmove.stop.prevent="">
      <u-popup
        :show="showVisitorPicker"
        @close="showVisitorPicker = false"
        mode="center"
        :round="'30rpx'"
        :closeOnClickOverlay="true"
        :safeAreaInsetBottom="false"
      >
        <view class="bind-popup">
          <view class="modal-header close">
            <view class="close-icon" @tap="handleClose">
              <image
                class="close-x"
                src="https://tg-prod.oss-cn-beijing.aliyuncs.com/6896ad4d-0cb3-4970-9016-24f611a2a67a.webp"
              />
            </view>
          </view>
          <view class="bind-popup-header">
            <text>请选择绑定关系</text>
          </view>
          <view class="bind-popup-content">
            <view
              class="bind-item"
              v-for="item in roleList"
              :key="item.role_id"
              @click="handleSelectRole(item)"
              :class="{
                disabled:
                  relationList.includes(item.role_id) && item.role_id !== 8,
                active: roleId === item.role_id
              }"
            >
              {{ item.role_name }}
            </view>
          </view>
          <view class="confirm-btn-wrapper" :class="{ disabled: !popVisitor }">
            <button class="confirm-btn" @tap="handlePopupConfirm">确定</button>
          </view>
        </view>
      </u-popup>
    </view>
  </view>
</template>

<script>
import { bindVisitor, bindRole } from "@/services/student/home";
import { bindUser, getUserRelation } from "@/services/student/my";
import {
  arouseLogin,
  getDataByRole,
  processUserList,
  getCheckedStudentInfo
} from "@/utils/user";
import { studentType, studentTag } from "../my/config";
export default {
  name: "StudentPage",
  data() {
    return {
      selectedIndex: -1,
      currentVisitor: "",
      studentType,
      studentTag,
      studentBj: {
        1: "selected",
        2: "xiu",
        3: "active",
        4: "lin",
        0: "linactive"
      },
      studentSelected: {
        0: "https://tg-prod.oss-cn-beijing.aliyuncs.com/cf6d778b-e11f-4df1-a337-56a5442e8833.webp",
        2: "https://tg-prod.oss-cn-beijing.aliyuncs.com/a6b4551d-ad49-48e7-b46e-293612ca6a1f.webp",
        3: "https://tg-prod.oss-cn-beijing.aliyuncs.com/c28667de-68f6-4822-a49c-c513bdcb3abc.webp",
        4: "https://tg-prod.oss-cn-beijing.aliyuncs.com/6b779338-785f-45df-9f9c-dd9932aa7d82.webp",
        1: "https://tg-prod.oss-cn-beijing.aliyuncs.com/f6e7a255-1f15-4cb2-9979-6b2ee3779262.webp"
      },
      studentBorder: {
        1: "#f2b400",
        2: "#0083ff",
        3: "#610bff",
        4: "#2cb899",
        0: "#F75395"
      },
      studentListArr: [],
      showVisitorPicker: false,
      roleList: [],
      roleId: 0,
      prospective: "",
      borderRed: false,
      relationList: [],
      popVisitor: ""
    };
  },
  methods: {
    selectStudent(index) {
      this.selectedIndex = index;
    },
    handleSelectRole(item) {
      if (this.relationList.includes(item.role_id) && item.role_id !== 8) {
        return;
      }

      this.popVisitor = item.role_name;
      this.roleId = item.role_id;
    },
    handlePopupConfirm() {
      if (!this.popVisitor) {
        uni.showToast({
          title: "请选择绑定关系",
          icon: "none"
        });
        return;
      }
      this.currentVisitor = this.popVisitor;
      this.showVisitorPicker = false;

      // this.getUserRelation();
    },
    async handleConfirm() {
      uni.showLoading({
        title: "绑定中..."
      });
      if (this.selectedIndex === -1) {
        uni.showToast({
          title: "请选择学员",
          icon: "none"
        });
        return;
      }
      if (!this.currentVisitor) {
        uni.showToast({
          title: "请选择绑定关系",
          icon: "none"
        });
        this.borderRed = true;
        return;
      }
      let selectedStudent;
      if (this.studentListArr[this.selectedIndex].student_id) {
        selectedStudent = {
          student_id: this.studentListArr[this.selectedIndex].student_id,
          role_id: this.roleId
        };
      } else {
        selectedStudent = {
          customer_id: this.studentListArr[this.selectedIndex].customer_id,
          role_id: this.roleId
        };
      }

      const { code, data, message } = await bindUser(selectedStudent);
      if (code === 0) {
        uni.hideLoading();
        uni.showToast({
          title: `绑定成功，欢迎${data}同学`,
          icon: "none",
          duration: 3000
        });
        // this.cancel();
        // this.getStudent();
        // this.handleOpen();
        const { session, role, openId } = await arouseLogin();
        this.session = session;
        this.role = role;
        // this.rowIdField = this.rowIds[role];

        // 获取选中的学生/客户信息
        await getCheckedStudentInfo(openId, role, session.operation_id);

        // 获取用户列表并处理
        await getDataByRole(this, role, openId, (code, data, message) => {
          processUserList(code, data, message, this, openId);
        });
        console.log(uni.getStorageSync("curStudentInfo"), "绑定");
        // const role = this.studentListArr[this.selectedIndex].student_id
        //   ? "student"
        //   : "customer";
        // const id =
        //   this.studentListArr[this.selectedIndex].student_id ||
        //   this.studentListArr[this.selectedIndex].customer_id;
        // await updateCheckedUserInfo(role, id);
        setTimeout(() => {
          // 是否是从登录进入的
          if (this.prospective === "login") {
            uni.switchTab({
              url: "/pages/student/home/<USER>"
            });
          } else {
            uni.navigateBack();
          }
        }, 1000);
      } else {
        uni.hideLoading();
        // this.$refs.uToast.show({ type: "error", message, duration: 3000 });
        uni.showToast({
          title: message,
          icon: "none",
          duration: 3000
        });
      }
    },
    handleLeftClick() {
      uni.switchTab({
        url: "/pages/student/home/<USER>"
      });
    },
    async getRoleList() {
      const { code, data, message } = await bindRole();
      if (code === 0) {
        this.roleList = data;
      } else {
        this.$refs.uToast.show({ type: "error", message });
      }
    },
    async getUserRelation() {
      if (this.selectedIndex === -1) {
        uni.showToast({
          title: "请选择学员",
          icon: "none"
        });
        return;
      }
      let id;
      if (this.studentListArr[this.selectedIndex].student_id) {
        id = { student_id: this.studentListArr[this.selectedIndex].student_id };
      } else {
        id = {
          customer_id: this.studentListArr[this.selectedIndex].customer_id
        };
      }
      const { code, data, message } = await getUserRelation(id);
      if (code === 0) {
        this.relationList = data.role_list || [];
      } else {
        uni.showToast({
          title: message,
          icon: "none",
          duration: 3000
        });
      }
      this.showVisitorPicker = true;
    },
    handleClose() {
      this.showVisitorPicker = false;
    }
  },
  async onLoad(options) {
    this.prospective = options.prospective;
    if (options.prospective === "login") {
      const res = await bindVisitor({
        open_id: uni.getStorageSync("session").open_id,
        UNAUTHORIZED: true
      });
      this.studentListArr = res.data;
    } else {
      this.studentListArr = JSON.parse(options.prospective);
      // this.getUserRelation();
    }
    this.getRoleList();
  }
};
</script>

<style lang="scss" scoped>
.student-page {
  min-height: 100vh;
  background: #fff;
}

.header {
  padding: 40rpx 0;

  .title {
    font-size: 36rpx;
    font-weight: 600;
    color: #333;
  }
}

.student-list {
  // margin-top: 186rpx;
  .visitor-wrap {
    margin: 30rpx 32rpx 40rpx 32rpx;
    display: flex;
    align-items: center;

    // justify-content: space-between;
    // width: 100%;
    .visitor-label {
      color: #333;
      font-size: 26rpx;
      font-style: normal;
      font-weight: 500;
      line-height: normal;
      margin-right: 20rpx;
    }

    .visitor-selector {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      border-radius: 20rpx;
      // opacity: 0.6;
      background: #f6f6f6;
      width: 562rpx;
      height: 88rpx;
      padding: 24rpx;

      .visitor-selector-text {
        color: #999;
        font-size: 28rpx;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
      }

      .visitor-selector-text-active {
        color: #333 !important;
      }
    }
  }

  .student-item {
    background: #ffffff;
    width: 686rpx;
    height: 194rpx;
    border-radius: 24rpx;
    display: flex;
    align-items: center;
    margin-left: 32rpx;
    margin-bottom: 34rpx;
    position: relative;
    box-sizing: border-box;

    &-selected {
      background: #fff7e1;
      position: relative;
    }

    &-active {
      background: #f7f3ff;
    }

    &-lin {
      background: #e3fcf7;
    }

    &-linactive {
      background: #ffeff9;
    }

    &-xiu {
      background: #eaf5ff;
    }
  }

  .student-item-selecteds {
    inset: 0;
    border: 2rpx solid #ffb800;
    border-radius: 24rpx;
  }

  .avatar {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    margin: 0 30rpx 0 24rpx;
  }

  .info {
    flex: 1;

    .name {
      font-size: 30rpx;
      font-style: normal;
      font-weight: 500;
      line-height: normal;
      margin-bottom: 12rpx;
      display: flex;
      align-items: center;
      gap: 10rpx;
    }

    .student-id,
    .region {
      color: #999;
      font-size: 28rpx;
      font-style: normal;
      font-weight: 400;
      line-height: 42rpx;
      /* 150% */
    }
  }

  .tag {
    width: 80rpx;
    height: 38rpx;
    padding: 3rpx 16rpx;
    justify-content: center;
    align-items: center;
    gap: 10rpx;
    flex-shrink: 0;
    color: #fff;
    font-size: 24rpx;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    margin-left: 12rpx;
    border-radius: 16rpx 8rpx 16rpx 8rpx;

    &-yel {
      background: linear-gradient(313deg, #ffb200 35.48%, #ffe32d 127.89%);
    }

    &-bul {
      background: linear-gradient(312deg, #4ab9ff 35.69%, #a9deff 126.33%);
    }

    &-purp {
      background: linear-gradient(300deg, #9f6bff 31.22%, #ece2ff 133.9%);
    }

    &-gre {
      background: linear-gradient(296deg, #2ccba7 44.48%, #94e3e7 168.27%);
    }

    &-pink {
      background: linear-gradient(296deg, #f85395 44.48%, #ffeff9 168.27%);
    }
  }

  .student-selected {
    position: absolute;
    right: 0;
    bottom: 0;
    width: 164rpx;
    height: 164rpx;
  }
}

.confirm-btn {
  //   position: fixed;
  //   bottom: 48rpx;
  //   left: 32rpx;
  //   right: 32rpx;
  margin: 80rpx 32rpx 68rpx 32rpx;
  height: 100rpx;
  background: linear-gradient(15deg, #ffb800 18.1%, #ffcb3c 83.29%);
  box-shadow: 0px -10rpx 18rpx 0px #f3b300 inset,
    0px 4rpx 20rpx 0px rgba(254, 197, 36, 0.47);
  border-radius: 71rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  font-size: 36rpx;
  font-weight: 600;

  &-disabled {
    opacity: 0.5;
  }
}

.bind-popup {
  background: #fff;
  padding-bottom: 50rpx;
  border-radius: 35rpx;
  width: 582rpx;
  position: relative;

  .close {
    display: flex;
    justify-content: flex-end;
    position: absolute;
    right: 0;
  }

  .close-icon {
    width: 100rpx;
    height: 100rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    right: 0;

    .close-x {
      font-size: 40rpx;
      width: 30rpx;
      height: 30rpx;
      color: #cccccc;
      line-height: 1;
      font-weight: 500;
    }
  }

  .title {
    justify-content: center;
  }

  .disabled {
    opacity: 0.5;
  }

  .bind-popup-header {
    // height: 108rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 40rpx;
    margin-top: 60rpx;

    text {
      color: #333;
      text-align: center;
      font-size: 32rpx;
      font-style: normal;
      font-weight: 500;
      line-height: normal;
      width: 100%;
    }
  }

  .bind-popup-content {
    padding: 32rpx 32rpx 0 32rpx;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 24rpx;

    .bind-item {
      height: 82rpx;
      background-color: #f6f6f6;
      border: 2rpx solid #d4d4d4;
      border-radius: 10rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #666;
      text-align: center;
      font-size: 30rpx;
      font-style: normal;
      font-weight: 400;
      line-height: 56rpx;
    }

    .active {
      background-color: #fff8e0;
      color: #fb0;
      border: 1px solid #fb0;
    }
  }
}

.borderCode {
  border: 2rpx solid #fe4f37;
}

::v-deep .u-popup {
  border-radius: 24rpx 24rpx 0 0;
}

.nav-letf {
  width: 40rpx;
  height: 40rpx;

  image {
    width: 100%;
    height: 100%;
  }
}

.confirm-btn-wrapper {
  margin-top: 60rpx;
  padding-bottom: env(safe-area-inset-bottom);
  .confirm-btn {
    width: 238rpx;
    height: 88rpx;
    line-height: 88rpx;
    background: linear-gradient(15deg, #ffbf0d 18.1%, #ffcb3c 83.29%);
    box-shadow: 0px -5rpx 12rpx 0rpx #fc0 inset, 0px 9px 20px 0px #fff7e1 inset;
    filter: drop-shadow(0px 4rpx 4rpx rgba(255, 192, 18, 0.11));
    border-radius: 44rpx;
    color: #fff;
    text-align: center;
    font-size: 34rpx;
    font-style: normal;
    font-weight: 500;
    margin: 0 auto;
  }
}
</style>
