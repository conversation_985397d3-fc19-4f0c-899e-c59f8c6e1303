<template>
  <view class="buy-course-container" :class="{ 'grid-container': isGrid }">
    <u-toast ref="uToast"></u-toast>
    <!-- 导航栏组件 -->
    <nav-bar @leftClick="handleLeftClick" :isGrid="isGrid"></nav-bar>

    <!-- 主体区域 -->
    <view class="main-content">
      <!-- 左侧分类 + 右侧课程列表 -->
      <view class="content-wrapper">
        <!-- 左侧分类组件 -->
        <category-sidebar
          :currentCategory="currentLeftCategory"
          @change="selectLeftCategory"
          :courseCategoryType="courseCategoryType"
        ></category-sidebar>

        <!-- 右侧课程列表组件 -->
        <course-list
          :courses="courseList"
          :currentTopFilter="currentTopFilter"
          :priceAsc="priceAsc"
          :isLoading="isLoading"
          :isRefreshing="isRefreshing"
          @filterChange="selectTopFilter"
          @loadMore="loadMore"
          @refresh="onRefresh"
          @viewDetail="viewCourseDetail"
          style="flex: 1; height: 100%"
        ></course-list>
      </view>
    </view>
  </view>
</template>

<script>
import NavBar from "./components/NavBar.vue";
import CategorySidebar from "./components/CategorySidebar.vue";
import CourseList from "./components/CourseList.vue";
import { courseCategory, courseList } from "@/services/student/myCourse";
export default {
  name: "BuyCourse",
  components: {
    NavBar,
    CategorySidebar,
    CourseList
  },
  props: {
    isGrid: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      tabbarKey: 0,
      searchKeyword: "",
      showSearch: false,
      currentTopFilter: "", // 顶部筛选： "", sales_volume, standard_price - sales_volume asc \ sales_volume desc
      priceAsc: "sales_volume|asc", // 价格筛选方向
      currentLeftCategory: 0, // 左侧分类
      courses: [],
      page: 1,
      pageSize: 10,
      total: 0,
      isLoading: "loading",
      isRefreshing: false,
      studentInfo: {},
      courseCategoryType: null,
      courseList: []
    };
  },
  computed: {},
  methods: {
    handleLeftClick() {
      // 处理导航栏左侧点击
      uni.navigateBack();
    },
    handleChangeDot(index) {
      this.tabbarKey = index;
    },
    selectLeftCategory(category) {
      this.currentTopFilter = "";
      this.currentLeftCategory = category;
      this.page = 1;
      this.courseList = [];
      this.fetchCourses(false);
    },
    selectTopFilter(filter) {
      this.courseList = [];
      this.page = 1;
      this.currentTopFilter = filter;
      if (filter === this.currentTopFilter && filter === "standard_price") {
        // 如果再次点击价格筛选，则切换排序方向
        this.priceAsc =
          this.priceAsc === "standard_price|asc"
            ? "standard_price|desc"
            : "standard_price|asc";
      } else {
        this.currentTopFilter = filter;
        this.priceAsc = "sales_volume|asc";
      }
      this.fetchCourses(false);
    },
    async loadMore() {
      if (this.isLoading !== "loadmore") return;

      this.page++;
      await this.fetchCourses(false);
    },
    async onRefresh() {
      this.isRefreshing = true;
      this.page = 1;
      await this.fetchCourses(false);
      this.isRefreshing = false;
    },
    viewCourseDetail(course) {
      // 跳转到课程详情页
      uni.navigateTo({
        url: `/pages/student/subpages/courseDetail/index?course_id=${course.minpro_course_id}`
      });
    },
    // 获取课程数据
    async fetchCourses(tyep = true) {
      if (this.isLoading !== "loadmore" && tyep) return;
      this.isLoading = "loading";

      const student_id = uni.getStorageSync("curStudentInfo").student_id;
      const department_id = uni.getStorageSync("curStudentInfo").department_id;
      const { code, data, message } = await courseList({
        student_id,
        department_id,
        page: this.page,
        page_size: this.pageSize,
        minpro_course_type: Number(this.currentLeftCategory),
        sort:
          this.currentTopFilter === "standard_price"
            ? this.priceAsc
            : this.currentTopFilter
      });
      if (code === 0) {
        if (this.page === 1) {
          this.courseList = data.results || [];
        } else {
          for (let i = 0; i < data.results.length; i++) {
            this.courseList.push(data.results[i]);
          }
        }
        this.total = data.count;
        if (this.courseList.length >= this.total) {
          this.isLoading = "nomore";
        } else {
          this.isLoading = "loadmore";
        }
      } else {
        this.isLoading = "loadmore";
        uni.showToast({
          title: message,
          icon: "none"
        });
      }
    },
    async initData() {
      try {
        // 获取登录信息
        // const loginInfo = await arouseLogin();
        // if (loginInfo.session) {
        //   this.studentInfo = uni.getStorageSync("curStudentInfo") || {};
        // }
        // 获取课程分类
        await this.getCourseCategory();
        // 获取课程数据
        await this.fetchCourses(false);
      } catch (error) {
        console.error("初始化数据失败:", error);
        uni.showToast({
          title: "获取数据失败",
          icon: "none"
        });
      }
    },
    // 获取课程分类
    async getCourseCategory() {
      const { data, code, message } = await courseCategory();
      if (code === 0) {
        this.courseCategoryType = data;
      } else {
        uni.showToast({
          title: message,
          icon: "none"
        });
      }
    }
  },
  onLoad() {},
  mounted() {
    this.initData();
  }
};
</script>

<style lang="scss" scoped>
.buy-course-container {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 120rpx - 40px);
  background-color: #fff;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.content-wrapper {
  flex: 1;
  display: flex;
  overflow: hidden;
}
.grid-container {
  height: 100vh !important;
}
</style>
