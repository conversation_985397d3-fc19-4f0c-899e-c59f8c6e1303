<template>
  <u-navbar
    title=""
    bgColor="#FFF"
    leftIconSize="25px"
    leftIconColor="#FFF"
    :titleStyle="{
      color: '#FFF',
      fontSize: '34rpx',
      fontWeight: '500',
      lineHeight: '40rpx'
    }"
    :autoBack="false"
    placeholder
  >
    <view class="u-nav-slot" slot="left">
      <u-icon
        name="https://tg-prod.oss-cn-beijing.aliyuncs.com/e9ac0f4b-6282-4bac-b602-189057c45c90.webp"
        width="40rpx"
        height="40rpx"
        @click="handleClick(true)"
        v-if="isGrid"
      ></u-icon>
      <!-- <u-line
        direction="column"
        :hairline="false"
        length="16"
        margin="0 21rpx"
      ></u-line>
      <u-icon
        name="https://tg-prod.oss-cn-beijing.aliyuncs.com/b46c4fa1-00c9-4cd6-810d-adbaf84e0255.webp"
        width="40rpx"
        height="40rpx"
        @click="handleClick(false)"
      ></u-icon> -->
      <view class="search-left" @tap="handleClick(false)">
        <u-icon
          name="https://tg-prod.oss-cn-beijing.aliyuncs.com/0def198b-5912-4bdc-be08-ef981047e719.webp"
          width="40rpx"
          height="40rpx"
          color="#999"
          class="search-icon"
          slot="prefix"
        ></u-icon>
        <view class="title">
          <text>搜索商品名称</text>
        </view>
      </view>
    </view>
  </u-navbar>
</template>

<script>
export default {
  name: "CourseNavBar",
  props: {
    isGrid: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    handleLeftClick() {
      this.$emit("leftClick");
    },
    handleClick(bool) {
      if (bool) {
        uni.switchTab({ url: "/pages/student/home/<USER>" });
      } else {
        uni.navigateTo({ url: "/pages/student/subpages/search/index" });
      }
    }
  },
  created() {}
};
</script>

<style lang="scss" scoped>
.u-nav-slot {
  @include flex;
  align-items: center;
  justify-content: space-between;
  // border-radius: 33rpx;
  // border: 1rpx solid #e6e6e6;
  // padding: 12rpx 24rpx;
}
.search-left {
  width: 456rpx;
  height: 66rpx;
  background: #f7f7f7;
  border-radius: 70rpx;
  display: flex;
  align-items: center;
  padding-left: 26rpx;
  .title {
    color: #bababa;
    font-size: 26rpx;
    font-weight: 400;
    line-height: normal;
    margin-left: 14rpx;
  }
}
</style>
