<template>
  <view class="left-category">
    <view
      class="category-item"
      v-for="(item, index) in courseCategoryType"
      :key="item"
      @tap="selectCategory(index)"
      :class="[
        currentCategory == index ? 'active' : '',
        currentCategory === 4 && index == 2 ? 'border-bottom' : '',
        currentCategory === 0 && index == 2 ? 'border-top' : '',
        currentCategory === 2 && index == 0 ? 'border-bottom' : '',
        currentCategory === 2 && index == 4 ? 'border-top' : ''
      ]"
      :style="index == 0 ? 'border-top-right-radius: 4rpx;' : ''"
    >
      {{ courseCategoryType[index] }}
    </view>
    <view class="bottom"> </view>
  </view>
</template>

<script>
export default {
  name: "CategorySidebar",
  props: {
    // 后台会返回 0 索引 注意
    currentCategory: {
      type: Number,
      default: 0
    },
    courseCategoryType: {
      type: Object,
      default: () => {}
    }
  },
  methods: {
    selectCategory(category) {
      this.$emit("change", category);
    }
  }
};
</script>

<style lang="scss" scoped>
.left-category {
  width: 170rpx;
  // background-color: #f4f4f4;
  flex: 1;
  height: 100%;
  // .category-item:first-child {
  //   padding-top: 11rpx;
  // }
  position: relative;
  .bottom {
    background-color: #f4f4f4;
    height: 100%;
  }
  .border-top {
    border-top-right-radius: 20rpx;
    border-bottom-right-radius: 0;
  }
  .border-center {
    border-top-right-radius: 20rpx;
    border-bottom-right-radius: 20rpx;
  }
  .border-bottom {
    border-top-right-radius: 0rpx;
    border-bottom-right-radius: 20rpx;
  }
  .category-item {
    height: 94rpx;
    display: flex;
    align-items: center;
    // justify-content: center;
    font-size: 26rpx;
    color: #333;
    position: relative;
    padding-left: 36rpx;
    background-color: #f4f4f4;

    &.active {
      background-color: #fff;
      color: #fb0;
      font-weight: 500;
      font-size: 28rpx;

      &::before {
        content: "";
        position: absolute;
        left: 20rpx;
        top: 50%;
        transform: translateY(-50%);
        width: 6rpx;
        height: 16rpx;
        border-radius: 10rpx;
        background-color: #fb0;
      }
    }
  }
}
</style>
