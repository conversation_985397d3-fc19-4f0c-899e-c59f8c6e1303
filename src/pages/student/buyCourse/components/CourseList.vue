<template>
  <view class="course-right">
    <!-- 顶部分类筛选 -->
    <view class="top-filter">
      <view
        class="filter-tab"
        :class="{ active: currentTopFilter === '' }"
        @tap="selectTopFilter('')"
      >
        默认
      </view>
      <view
        class="filter-tab"
        :class="{ active: currentTopFilter === 'sales_volume|desc' }"
        @tap="selectTopFilter('sales_volume|desc')"
      >
        销量
      </view>
      <view
        class="filter-tab"
        :class="{ active: currentTopFilter === 'standard_price' }"
        @tap="selectTopFilter('standard_price')"
      >
        价格
        <view class="price-arrow" :class="{ 'arrow-up': priceAsc }">
          <!-- {{ priceAsc ? "↑" : "↓" }} -->
          <u-icon
            name="arrow-up"
            size="10"
            bold
            :color="
              priceAsc === 'standard_price|asc' &&
              currentTopFilter === 'standard_price'
                ? '#fb0'
                : '#999'
            "
          ></u-icon>
          <u-icon
            name="arrow-down"
            size="10"
            bold
            :color="
              priceAsc === 'standard_price|desc' &&
              currentTopFilter === 'standard_price'
                ? '#fb0'
                : '#999'
            "
          ></u-icon>
        </view>
      </view>
    </view>
    <!-- 右侧课程列表 -->
    <scroll-view
      v-if="courses.length"
      scroll-y
      class="course-list-scroll"
      @scrolltolower="handleLoadMore"
      show-scrollbar
      refresher-enabled
      :refresher-triggered="isRefreshing"
      @refresherrefresh="handleRefresh"
    >
      <view class="course-list">
        <view
          class="course-item"
          v-for="(course, index) in courses"
          :key="index"
          @tap="viewCourseDetail(course)"
        >
          <image
            class="course-image"
            :src="course.cover_url + '?x-oss-process=image/resize,h_160,w_160'"
            mode="aspectFill"
          ></image>
          <view class="course-info">
            <view style="display: flex; flex-direction: column">
              <text class="course-name">{{ course.course_name }}</text>
              <text class="course-duration"
                >{{ course.standard_numb }}课时</text
              >
            </view>
            <view class="price-area">
              <view class="price-wrapper">
                <view v-if="course.department_price > 0" class="priceInfo">
                  <text class="price-symbol">¥</text>
                  <text class="course-price">{{
                    course.department_price
                  }}</text>
                </view>
                <view class="free-tag" v-else>免费</view>
                <text
                  v-if="
                    course.is_show_price === 1 &&
                    course.minpro_course_type !== 4
                  "
                  class="original-price"
                >
                  ¥{{ course.standard_price }}
                </text>
              </view>
            </view>
          </view>
        </view>
        <u-loadmore
          :status="isLoading"
          lineColor="#DADADA"
          line
          color="#999999"
          :fontSize="'26rpx'"
          :loadmoreText="loadmoreText"
          :marginTop="'70rpx'"
          :paddingBottom="'40rpx'"
        />
      </view>

      <!-- <view class="empty-state" v-if="courses.length === 0">
        <image
          class="empty-image"
          src="https://tg-prod.oss-cn-beijing.aliyuncs.com/d02814d0-bfd3-48bf-8d9d-80cda362b89a.webp"
        ></image>
        <text class="empty-text">暂无相关课程</text>
      </view>

      <view class="loading-more" v-if="isLoading && courses.length > 0">
        <u-loading-icon></u-loading-icon>
        <text>加载中...</text>
      </view>

      <view class="no-more" v-if="!hasMore && courses.length > 0">
        <text>没有更多课程了</text>
      </view> -->
    </scroll-view>
    <view
      style="
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
      "
      v-else
    >
      <loading-animation
        :width="300"
        :characterSize="260"
        :textSize="32"
        v-if="isLoading === 'loading'"
      />
      <u-empty
        text="暂无相关课程~"
        :width="'163rpx'"
        :height="'230rpx'"
        textColor="#999999"
        icon="https://tg-prod.oss-cn-beijing.aliyuncs.com/d02814d0-bfd3-48bf-8d9d-80cda362b89a.webp"
        v-else
      >
      </u-empty>
    </view>
  </view>
</template>

<script>
import LoadingAnimation from "@/components/common/LoadingAnimation.vue";
export default {
  name: "CourseList",
  components: {
    LoadingAnimation
  },
  data() {
    return {
      isEmpty: false,
      loadmoreText: "加载更多"
    };
  },
  props: {
    courses: {
      type: Array,
      default: () => []
    },
    currentTopFilter: {
      type: String,
      default: ""
    },
    priceAsc: {
      type: String,
      default: "standard_price|asc"
    },
    isLoading: {
      type: String,
      default: "loading"
    },
    isRefreshing: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    selectTopFilter(filter) {
      this.$emit("filterChange", filter);
    },
    handleLoadMore() {
      this.$emit("loadMore");
    },
    handleRefresh() {
      this.$emit("refresh");
    },
    viewCourseDetail(course) {
      this.$emit("viewDetail", course);
    }
  }
};
</script>

<style lang="scss" scoped>
.course-right {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

.top-filter {
  display: flex;
  background-color: #fff;
  padding-top: 11rpx;
  flex-shrink: 0;
  .filter-tab {
    margin-left: 103rpx;
    height: 80rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #666;
    position: relative;
    text-align: center;
    font-size: 26rpx;
    font-style: normal;
    font-weight: 400;
    line-height: 28rpx;
    &.active {
      color: #fb0;
    }

    .price-arrow {
      display: flex;
      flex-direction: column;
      align-content: stretch;
      margin-left: 4rpx;
      width: 24rpx;
      margin-left: 5rpx;
      :first-child {
        margin-bottom: -1.7rpx;
      }
    }
  }
}

.course-list-scroll {
  flex: 1;
  height: calc(100% - 91rpx);
  position: relative;
  overflow-y: scroll;
}

.course-list {
  //   padding: 20rpx;
  height: 100%;
  .course-item:first-child {
    padding-top: 0;
  }
  .course-item {
    display: flex;
    background-color: #fff;
    padding: 20rpx;
    // margin-bottom: 20rpx;
    border-radius: 8rpx;

    .course-image {
      width: 160rpx;
      height: 160rpx;
      border-radius: 20rpx;
      flex-shrink: 0;
    }

    .course-info {
      flex: 1;
      margin-left: 24rpx;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .course-name {
        font-size: 32rpx;
        font-weight: 500;
        color: #333;
        margin-bottom: 4rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        width: 354rpx;
      }

      .course-duration {
        font-size: 26rpx;
        color: #999;
        // margin-bottom: 34rpx;
        font-weight: 400;
      }

      .price-area {
        .free-tag {
          font-size: 30rpx;
          font-weight: 500;
          color: #ff553a;
        }

        .price-wrapper {
          display: flex;
          // align-items: baseline;
          .priceInfo {
            display: flex;
            justify-content: center;
            align-items: center;
          }
          .price-symbol {
            font-size: 24rpx;
            color: #ff553a;
            font-weight: 400;
            margin-right: 5rpx;
          }

          .course-price {
            font-size: 36rpx;
            font-weight: 500;
            color: #ff553a;
            display: flex;
            align-items: flex-end;
            line-height: 40rpx;
          }

          .original-price {
            font-size: 24rpx;
            color: #999;
            text-decoration: line-through;
            margin-left: 10rpx;
            display: flex;
            align-items: flex-end;
          }
        }
      }
    }
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;

  .empty-image {
    width: 153rpx;
    height: 230rpx;
    margin-bottom: 20rpx;
  }

  .empty-text {
    font-size: 28rpx;
    color: #999;
  }
}

.loading-more,
.no-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 30rpx 0;
  color: #999;
  font-size: 24rpx;
}
</style>
