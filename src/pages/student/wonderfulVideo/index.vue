<template>
  <div
    class="wonderful-video-page"
    :style="{
      backgroundColor: showBg ? 'transparent' : '#fff',
      minHeight: getTop + 'px'
    }"
    :class="{
      pt: !showBg
    }"
  >
    <view
      style="position: fixed; top: 0; left: 0; z-index: 9"
      class="zhanwei"
      :class="{
        bg: showBg
      }"
    >
      <navbar :navBarHeight="81" v-if="showBg">
        <view class="logo-wrapper">
          <image
            src="https://tg-prod.oss-cn-beijing.aliyuncs.com/2c919ed4-dacd-42af-8531-b054ac6ac945.png"
            alt=""
          />
        </view>
      </navbar>
      <view
        class="wonderful-video-player-container"
        :style="{ 'margin-top': showBg ? '20rpx' : '0rpx' }"
      >
        <video
          ref="video"
          v-if="videoUrl"
          id="courseVideo"
          :src="videoUrl"
          controls
          show-fullscreen-btn
          show-center-play-btn
          enable-progress-gesture
          :title="courseTitle"
          :autoplay="videoAutoPlay"
          vslide-gesture-in-fullscreen
          show-bottom-progress
          :picture-in-picture-mode="pictureInPictureMode"
          @enterpictureinpicture="handleEnterPictureInPicture"
          @leavepictureinpicture="handleLeavePictureInPicture"
          @error="handleVideoError"
          @fullscreenchange="handleFullscreenChange"
          @controlstoggle="handleControlsToggle"
        >
          <cover-image
            v-if="!videoUrl"
            src="https://tg-prod.oss-cn-beijing.aliyuncs.com/b74b3cf4-1342-4358-bc64-d304984b80cd.png"
            mode="widthFix"
          ></cover-image>
        </video>
      </view>
      <u-tabs
        :list="parentsList"
        @click="clickParent"
        :lineColor="'#ffbf0d'"
        :lineWidth="23"
        :lineHeight="3"
        :inactiveStyle="{
          color: '#666666',
          fontSize: '28rpx',
          fontWeight: 400
        }"
        :activeStyle="{
          color: '#333333',
          fontSize: '30rpx',
          fontWeight: 700
        }"
      ></u-tabs>
    </view>
    <!-- 占位元素 -->
    <view :style="{ height: topAreaHeight + 'px' }"></view>
    <view class="scroll_content">
      <view class="grid_wrap" v-if="showCity" v-show="apiDone">
        <u-grid :border="false" col="5">
          <u-grid-item
            v-for="(listItem, listIndex) in address"
            :key="listIndex"
            @click="
              getCity(listItem);
              checkAddressInbdex = listIndex;
            "
          >
            <view
              class="square"
              :class="{ active: checkAddressInbdex === listIndex }"
            >
              {{ listItem.city }}
            </view>
          </u-grid-item>
        </u-grid>
      </view>

      <view class="video_list" v-if="apiDone">
        <view
          class="video_item"
          v-for="(videoItem, index) in videoList"
          :key="index"
          @click="play(videoItem)"
          :class="{ activeVideo: watchVideoId === videoList[index].id }"
        >
          <view class="video_name">
            <view class="name">{{ videoItem.name }}</view>
            <view class="video_address"> {{ videoItem.department_name }}</view>
          </view>
          <view class="play_wrap">
            <image
              class="playing"
              src="https://cdn.elf-go.com/image/default/17719E6AEB314003BF4FB6AB68201E16-6-2.gif"
              alt=""
              v-if="watchVideoId === videoList[index].id"
            />
            <view class="status">{{
              watchVideoId === videoList[index].id ? "播放中" : "立即观看"
            }}</view>
          </view>
        </view>
        <u-loadmore
          color="#999999"
          lineColor="#EAEAEA"
          :line="true"
          :status="loadStatus"
          v-if="videoList.length > 0"
          @loadmore="loadMore"
        />
        <empty text="暂无数据" v-show="videoList.length === 0"></empty>
      </view>
    </view>
  </div>
</template>

<script>
import {
  parentList,
  departmentCity,
  videoList
} from "@/services/student/wonderfulVideo";
import Empty from "@/components/empty";
import navbar from "../home/<USER>/navbar.vue";

export default {
  name: "wonderfulVideoPlay",
  data() {
    return {
      topAreaHeight: 0,
      height: 0,
      showBg: false,
      statusBarHeight: 0,
      apiDone: false,
      videoAutoPlay: false,
      loadStatus: "loading",
      videoUrl: "",
      isFullscreen: true,
      showSpeedControl: false,
      pictureInPictureMode: "", // 小窗模式配置
      isInPictureInPicture: false, // 小窗状态标记
      pipPosition: "right,20", // 小窗位置：右侧，距顶部20%
      videoContext: null,
      isPipShow: true,
      address: [],
      parentsList: [],
      videoList: [],
      checkAddressInbdex: 0,
      showCity: false,
      page: 1,
      pageSize: 5,
      city: "",
      parent_id: "",
      count: 0,
      watchVideoId: ""
    };
  },
  computed: {
    getTop() {
      return this.showBg
        ? uni.getSystemInfoSync().windowHeight - this.height - 476 - 162 - 52
        : uni.getSystemInfoSync().windowHeight - this.statusBarHeight - 470;
    }
  },
  components: { Empty, navbar },
  onReachBottom() {
    if (this.loadStatus !== "nomore") {
      this.getParentInfo();
    }
  },
  async onLoad(options) {
    const statusBarHeight = uni.getSystemInfoSync().statusBarHeight;
    this.statusBarHeight = statusBarHeight;
    this.getHeight();
    this.videoUrl = "";
    uni.showLoading({ title: "加载中" });
    this.videoContext = uni.createVideoContext("courseVideo", this);
    await this.getParentList();
    await this.getParentInfo();
    if (this.videoList.length > 0) {
      this.watchVideoId = this.videoList[0].id;
      this.videoUrl = this.videoList[0].video_url;
    }
    this.$nextTick(() => {
      this.getTopAreaHeight();
    });
  },
  onHide() {
    console.log("页面隐藏");
    this.videoContext.pause();
  },
  onShow() {
    this.videoContext = uni.createVideoContext("courseVideo", this);
    if (this.videoContext) {
      this.videoContext.play();
    }
  },
  onUnload() {
    console.log("页面卸载");
    // 页面卸载时，小窗会根据配置自动处理
  },
  methods: {
    getTopAreaHeight() {
      const query = uni.createSelectorQuery().in(this); // 在当前组件内查询
      query
        .select(".zhanwei") // 选择类名为top-area的元素
        .boundingClientRect((data) => {
          // data包含元素的位置和尺寸信息
          console.log("data", data);
          const height = data.height; // 元素高度（单位：px）
          console.log("top-area高度：", height);

          // 可在此处更新组件数据
          this.topAreaHeight = height + 8;
        })
        .exec(); // 执行查询
    },
    getReachBottom() {
      console.log("123");
      if (this.loadStatus !== "nomore") {
        this.getParentInfo();
      }
    },
    getHeight() {
      // #ifdef APP-PLUS || H5
      // 获取状态栏高度(电量时间通知等信息-单位px)
      const sysInfo = uni.getSystemInfoSync();
      this.statusBarHeight = sysInfo.statusBarHeight;
      this.height = this.statusBarHeight + this.navBarHeight;
      // #endif

      // 判断获取微信小程序胶囊API是否可用
      if (uni.canIUse("getMenuButtonBoundingClientRect")) {
        // 获取微信小程序胶囊布局位置信息
        // #ifndef H5 || APP-PLUS || MP-ALIPAY
        const sysInfo = uni.getSystemInfoSync();
        const rect = uni.getMenuButtonBoundingClientRect();
        this.menuButtonRect = JSON.parse(JSON.stringify(rect));
        // (胶囊上部高度-状态栏高度)*2 + 胶囊高度 = 导航栏高度（不包含状态栏）
        // 以此保证胶囊位于中间位置，多机型适配
        this.navBarHeight =
          (rect.top - sysInfo.statusBarHeight) * 2 + rect.height;
        this.statusBarHeight = sysInfo.statusBarHeight;
        // 状态栏高度 + 导航栏高度 = 自定义导航栏高度总和
        this.height = sysInfo.statusBarHeight + this.navBarHeight;
        // #endif
      }
    },
    play(item) {
      this.videoAutoPlay = false;
      this.videoUrl = item.video_url;
      this.watchVideoId = item.id;
    },
    async getParentList() {
      try {
        const { code, data } = await parentList();
        if (code === 0) {
          console.log(code, data);
          this.parentsList = data;
          this.parent_id = this.parentsList[0]?.id;
          if (this.parentsList[0].display === 1) {
            this.showCity = true;
            await this.getDepartmentCity();
          } else {
            this.showCity = false;
          }
          // this.showEmpty = this.parentsList.length === 0;
        } else {
          // this.showEmpty = true;
        }
      } catch (error) {
        // this.showEmpty = true;
      }
    },
    // 小窗相关事件处理
    handleEnterPictureInPicture(event) {
      console.log("进入小窗模式:", event);
    },
    handleLeavePictureInPicture(event) {
      console.log("退出小窗模式:", event);
    },
    goBack() {
      // 返回时直接返回，小窗会根据picture-in-picture-mode自动处理
      uni.navigateBack();
    },

    handleFullscreenChange(e) {},
    handleVideoError(e) {
      console.error("视频播放出错:", e);
      // uni.showToast({
      //   title: "视频加载失败，请稍后重试",
      //   icon: "none",
      //   duration: 2000
      // });
    },
    handleControlsToggle(e) {
      console.log("控制按钮状态变化:", e.detail);
      this.isPipShow = e.detail.show;
    },
    handleTabChange(item) {
      this.curActive = item;
      this.getCourseList();
    },
    reset() {
      this.videoList = [];
      this.city = "";
      this.page = 1;
      this.loadStatus = "loading";
      //  this.apiDone = false;
      uni.showLoading({ title: "加载中" });
    },
    async clickParent(item) {
      this.reset();
      this.parent_id = item.id;
      this.checkAddressInbdex = 0;
      if (item.display === 1) {
        this.showCity = true;
        await this.getDepartmentCity();
      } else {
        this.showCity = false;
      }
      await this.getParentInfo();
    },
    async getParentInfo() {
      if (this.loadStatus === "nomore") return;
      this.loadStatus = "loading";
      const param = {
        parent_id: this.parent_id,
        city: this.city === "全国" ? "" : this.city,
        page: this.page,
        page_size: this.pageSize
      };
      const { code, data } = await videoList(param);
      if (code === 0) {
        this.count = data.count;
        if (data.results && data.results.length > 0) {
          this.videoList = [...this.videoList, ...data.results];
          if (this.videoList.length >= this.count) {
            this.loadStatus = "nomore";
          } else {
            this.loadStatus = "loading";
          }
          this.page++;
        } else {
          this.videoList = [];
        }
        setTimeout(() => {
          this.apiDone = true;
          uni.hideLoading();
        }, 200);
      }
    },
    async getDepartmentCity() {
      try {
        const { code, data } = await departmentCity({
          parent_id: this.parent_id
        });
        if (code === 0) {
          console.log(code, data);
          this.address = data;
          this.address.unshift({ city: "全国" });
          this.city = this.address[0]?.city;
        }
      } catch (error) {
        //
      }
    },
    getCity(item) {
      // this.videoList = [];
      // this.page = 1;
      this.reset();
      this.city = item.city;
      this.getParentInfo();
    },
    async handleOnLoad() {
      this.showBg = true;
      const statusBarHeight = uni.getSystemInfoSync().statusBarHeight;
      this.statusBarHeight = statusBarHeight;
      this.videoUrl = "";
      uni.showLoading({ title: "加载中" });
      this.videoContext = uni.createVideoContext("courseVideo", this);
      await this.getParentList();
      await this.getParentInfo();
      if (this.videoList.length > 0) {
        this.watchVideoId = this.videoList[0].id;
        this.videoUrl = this.videoList[0].video_url;
      }
      //  this.videoContext = uni.createVideoContext("courseVideo", this);
      // if (this.videoContext) {
      //   this.videoContext.play();
      // }
      this.$nextTick(() => {
        this.getTopAreaHeight();
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.bg {
  // padding: 0 30rpx;
  background: url("https://tg-prod.oss-cn-beijing.aliyuncs.com/5c52e3a3-5990-48e7-ab2e-46a0d6052791.png");
  background-size: 100% auto; /* 保持图片原始尺寸，不变形 */
  background-position: top; /* 背景图顶部与容器顶部对齐 */
  background-repeat: no-repeat;
  .logo-wrapper {
    display: flex;
    align-items: center;
    height: 81rpx;
    width: 300rpx;
    margin-top: -4rpx;
    margin-left: 30rpx;
    image {
      width: 100%;
      height: 100%;
    }
  }
}
.pt {
  padding-bottom: env(safe-area-inset-bottom);
}

.wonderful-video-page {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  .wonderful-video-player-container {
    background-image: url("https://tg-prod.oss-cn-beijing.aliyuncs.com/b74b3cf4-1342-4358-bc64-d304984b80cd.png");
    background-repeat: no-repeat; /* 禁止背景图重复（避免多余显示） */
    background-size: 100% auto; /* 保持图片原始尺寸，不变形 */
    background-position: top; /* 背景图顶部与容器顶部对齐 */
    width: 100%;
    height: 422rpx;
    video {
      width: 100%;
      height: 100%;
    }
  }
  .tab_wrap {
    height: 104rpx;
    min-width: 100vw;
  }
  .grid_wrap {
    // margin-top: 25rpx;
    padding: 0 19rpx;
  }
  .square {
    width: 120rpx;
    height: 54rpx;
    line-height: 54rpx;
    border-radius: 8rpx;
    font-size: 28rpx;
    text-align: center;
    background: #f6f6f6;
    color: "#999999";
    margin-bottom: 22rpx;
  }
  .video_list {
    min-height: 200rpx;
    padding: 5rpx 30rpx 0;
    .video_item {
      width: 100%;
      height: 150rpx;
      border-radius: 20rpx;
      background-color: #f7f7f7;
      margin: 0 auto 28rpx;
      display: flex;
      align-items: center;
      padding: 0 18rpx 0 24rpx;
      box-sizing: border-box;
      border: 2rpx solid transparent;
      .video_name {
        width: calc(100vw - 60rpx - 194rpx);
        // background-color: pink;
        .name {
          font-size: 30rpx;
          font-weight: 500;
          color: #333333;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .video_address {
          font-size: 28rpx;
          font-weight: 400;
          color: #666666;
          margin-top: 10rpx;
        }
      }
      .play_wrap {
        // background-color: red;
        // background-color: pink;
        width: 122rpx;
        display: flex;
        // justify-content: end;
        align-items: end;
        margin-left: 30rpx;
      }

      .status {
        font-size: 28rpx;
        font-weight: 400;
        color: #ffaa00;
        width: 150rpx;
        text-align: center;
      }
      .playing {
        width: 34rpx;
        height: 34rpx;
        margin-top: 3rpx;
      }
    }
  }
}
.scroll_content {
  background: #fff;
  box-sizing: border-box;
}
.activeVideo {
  border: 2rpx solid rgba(255, 209, 76, 1) !important;
  background-color: rgba(255, 197, 37, 0.08) !important;
  box-sizing: border-box;
}
.pip-cover {
  bottom: 25rpx;
  color: #fff;
  font-size: 30rpx;
  height: 20rpx;
  position: fixed;
  right: 46rpx;
  width: 20rpx;
  z-index: 1000;
}
.u-tabs {
  height: 104rpx;
}

::v-deep .u-tabs__wrapper__scroll-view-wrapper {
  // margin-bottom: 25rpx !important;
  background-color: #fff;
}
::v-deep .empty-icon {
  margin-top: 160rpx;
}
::v-deep .square {
  font-weight: 400 !important;
  color: #999 !important;
  transition: none;
}
::v-deep .active {
  background-color: rgba(255, 197, 37, 0.05) !important;
  color: #ffbb00 !important; /* 或者任何其他样式 */
  transition: none;
}
::v-deep .u-grid-item--hover-class {
  opacity: 1 !important;
}
::v-deep .u-loadmore {
  margin-top: 50rpx !important;
}
::v-deep .empty-icon {
  height: 345rpx;
}
:v-deep .u-empty {
  height: 164rpx;
  height: 320rpx;
}
</style>
