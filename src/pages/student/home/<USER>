<template>
  <div class="home">
    <!-- <keep-alive> -->
    <home
      v-if="tab === 0 && show"
      ref="homeComponent"
      :dotList="dotList"
      @handleChangeTab="handleChangeTab"
    >
    </home>
    <my v-if="tab === 3 && show" ref="myComponent"></my>
    <buyCourse
      v-if="tab === 1 && show && !showTab"
      :isGrid="false"
      ref="buyCourseComponent"
    ></buyCourse>
    <wonderfulVideo
      v-if="tab === 1 && show && showTab"
      ref="wonderfulVideoComponent"
    ></wonderfulVideo>
    <niedaoCircle
      v-if="tab === 2 && show"
      ref="niedaoCircleComponent"
    ></niedaoCircle>
    <!-- </keep-alive> -->
    <TabBar
      ref="tabbar"
      :key="tabbarKey"
      @changeDot="handleChangeDot"
      @changeTab="handleChangeTab"
    ></TabBar>
  </div>
</template>
<script>
import { arouseLogin } from "@/utils/user";
import home from "./home.vue";
import my from "../my/index.vue";
import buyCourse from "../buyCourse/index.vue";
import niedaoCircle from "../subpages/niedaoCircle/index.vue";
import TabBar from "../components/TabBar.vue";
import wonderfulVideo from "../wonderfulVideo/index.vue";
export default {
  name: "homeIndex",
  components: { TabBar, home, my, buyCourse, niedaoCircle, wonderfulVideo },
  data() {
    return {
      tabbarKey: 0,
      tab: 0,
      dotList: [],
      show: false,
      showTab: true
    };
  },
  methods: {
    handleChangeDot(val, myHas) {
      // this.isShowDot = val;
      // this.menuList.forEach((item) => {
      //   if (val[item.value]) {
      //     item.isShowDot = val[item.value].has_new;
      //   } else {
      //     item.isShowDot = "";
      //   }
      // });
      this.dotList = val;
      console.log(this.tab, "this.tab", val, "val", myHas, "myHas");
      if (this.tab === 0) {
        this.$refs.homeComponent?.handleChangeDot?.(val, myHas);
      } else if (this.tab === 3) {
        this.$refs.myComponent?.handleChangeDot?.(val, myHas);
      }
    },
    handleChangeTab(val) {
      this.tab = val;
      this.tabbarKey++;
      this.$refs.tabbar.setMenuList();
      // 回到顶部 取消过渡动画
      uni.pageScrollTo({
        scrollTop: 0,
        duration: 0
      });
      if (this.tab === 0) {
        setTimeout(() => {
          this.$refs.homeComponent?.handleOnLoad?.();
        }, 100);
      }
      if (this.tab === 1) {
        if (this.showTab) {
          setTimeout(() => {
            this.$refs.wonderfulVideoComponent?.handleOnLoad?.();
          }, 100);
        } else {
          setTimeout(() => {
            this.$refs.buyCourseComponent?.handleOnLoad?.();
          }, 100);
        }
      }
    }
  },

  onReachBottom() {
    if (this.tab === 1) {
      this.$nextTick(() => {
        if (this.$refs.wonderfulVideoComponent) {
          this.$refs.wonderfulVideoComponent.getReachBottom();
        } else {
          console.log("子组件未渲染，ref无效");
        }
      });
    }
  },
  async onShow() {
    if (uni.getStorageSync("fromDTB")) {
      // eslint-disable-next-line no-undef
      getApp().globalData.tabIndex = 0;
      uni.setStorageSync("portType", "STUDENT");
      uni.removeStorageSync("curStudentInfo");
      uni.removeStorageSync("session");
      uni.removeStorageSync("fromDTB");
      await arouseLogin();
    }
    this.$nextTick(() => {
      this.$refs.tabbar.setMenuList();
    });
    // eslint-disable-next-line no-undef
    this.tab = getApp().globalData.tabIndex;
    this.show = true;
    if (this.tab === 3) {
      this.$refs.myComponent?.handleOnLoad?.();
    } else if (this.tab === 1) {
      this.$refs.homeComponent?.handleOnLoad?.();
    }
    const session = uni.getStorageSync("session");
    if (session.role !== "student") {
      this.showTab = false;
    } else {
      this.showTab = true;
    }
  },
  onPageScroll(e) {
    if (this.tab === 0) {
      this.$refs.homeComponent?.handlePageScroll?.(e);
    }
  },
  onLoad(options) {
    if (options.tab) {
      this.tab = Number(options.tab);
      // eslint-disable-next-line no-undef
      getApp().globalData.tabIndex = this.tab;
    }
    if (this.tab === 0) {
      setTimeout(() => {
        this.$refs.homeComponent?.handleOnLoad?.();
      }, 100);
    }
    if (this.tab === 2) {
      this.$refs.niedaoCircleComponent?.handleOnLoad?.();
    }
  },
  onPullDownRefresh() {
    if (this.tab === 2) {
      this.$refs.niedaoCircleComponent?.handlePullDownRefresh?.();
    }
  },
  onShareAppMessage(res) {
    if (this.tab === 2) {
      return this.$refs.niedaoCircleComponent?.handleShareAppMessage?.(res);
    } else {
      return {
        title: "聂卫平围棋",
        imageUrl:
          "https://tg-prod.oss-cn-beijing.aliyuncs.com/f860ad82-95fd-49c4-a8de-c629f069411b.png",
        path: `/pages/student/home/<USER>
      };
    }
    // return false;
  }
};
</script>
<style lang="scss" scoped>
.home {
  height: 100vh;
  display: flex;
  flex-direction: column;
  // overflow: hidden;
}
</style>
