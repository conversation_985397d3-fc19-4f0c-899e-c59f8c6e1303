<template>
  <div class="home">
    <!-- <u-notify ref="uNotify" message="Hi uView"></u-notify> -->
    <u-toast ref="uToast" style="z-index: 999999 !important"></u-toast>
    <!-- <u-navbar
        title="首页"
        bgColor="transparent"
        leftIconSize="25px"
        leftIcon="home-fill"
        leftIconColor="#333"
        @leftClick="leftClick"
      >
      </u-navbar> -->
    <navbar
      :navBarHeight="changeSearch ? 174 : 81"
      :fixed="stickyNavbar"
      :class="{ 'sticky-navbar': changeSearch }"
      :bgColor="stickyNavbar ? '#fff' : 'transparent'"
    >
      <view class="sticky-wrapper" :style="!stickyNavbar ? 'padding:0' : ''">
        <view class="logo-wrapper">
          <image
            src="https://tg-prod.oss-cn-beijing.aliyuncs.com/2c919ed4-dacd-42af-8531-b054ac6ac945.png"
            alt=""
          />
        </view>
        <view class="section-title" v-if="changeSearch">
          <text class="section-title-text">课程推荐</text>
          <view class="section-title-more" @click="toCourseList">
            <view class="til">查看更多</view>
            <u-icon
              style="margin-left: 8.17rpx"
              width="9.803rpx"
              height="17.511rpx"
              name="https://tg-prod.oss-cn-beijing.aliyuncs.com/ff88152c-9b03-4b35-bedb-aca3724580a9.webp"
            ></u-icon>
          </view>
        </view>
      </view>
    </navbar>
    <!-- 轮播图区域 -->
    <view class="swiper-section">
      <u-swiper
        :list="bannerList"
        :height="'286rpx'"
        :autoplay="true"
        :interval="3000"
        :circular="true"
        :indicator="false"
        imgMode="aspectFill"
        indicatorMode="dot"
        keyName="image_url"
        radius="30rpx"
        @change="(e) => (current = e.current)"
        @click.stop="handleBannerClick"
      >
      </u-swiper>
      <view slot="indicator" class="indicator">
        <view
          class="indicator__dot"
          v-for="(item, index) in bannerList"
          :key="index"
          :class="[index === current && 'indicator__dot--active']"
        >
        </view>
      </view>
    </view>

    <!-- 九宫格菜单区域 -->
    <menu-grid
      :menu-list="menuList"
      :is-show-dot="isShowDot"
      @menu-click="handleMenuClick"
    ></menu-grid>

    <!-- 课程推荐区域 -->
    <view class="course-section">
      <view class="section-title sticky" v-if="!changeSearch">
        <text class="section-title-text">课程推荐</text>
        <view class="section-title-more" @click="toCourseList">
          <view class="til">查看更多</view>
          <u-icon
            style="margin-left: 8.17rpx"
            width="9.803rpx"
            height="17.511rpx"
            name="https://tg-prod.oss-cn-beijing.aliyuncs.com/ff88152c-9b03-4b35-bedb-aca3724580a9.webp"
          ></u-icon>
        </view>
      </view>

      <!-- 添加占位元素，当标题吸顶时显示 -->
      <view
        class="title-placeholder"
        v-if="changeSearch"
        style="height: 94rpx"
      ></view>

      <view class="course-list">
        <view
          class="course-item"
          v-for="(item, index) in courseList"
          :key="index"
          @tap.stop="handleBuyCourse(item)"
        >
          <image
            v-if="index === 0"
            class="course-tag"
            src="https://tg-prod.oss-cn-beijing.aliyuncs.com/674ef10e-ca4b-4a63-ab21-b8887daf8844.png"
          >
          </image>
          <u--image
            :src="item.cover_url + '?x-oss-process=image/resize,h_160,w_160'"
            :fade="true"
            duration="450"
            width="160rpx"
            height="160rpx"
            radius="20rpx"
            :lazy-load="true"
            style="position: relative; z-index: 1"
            :showMenuByLongpress="false"
          ></u--image>
          <view class="course-info">
            <view>
              <text class="course-name">{{ item.course_name }}</text>
              <text class="course-duration">{{ item.standard_numb }}课时</text>
            </view>
            <view class="course-bottom">
              <view class="price-box">
                <view style="display: flex; align-items: flex-end">
                  <view class="course-price" v-if="item.department_price > 0">
                    <text class="price-symbol">¥</text>
                    <text style="display: flex; align-items: flex-end">{{
                      item.department_price
                    }}</text>
                  </view>
                  <text class="course-price" style="font-size: 32rpx" v-else
                    >免费</text
                  >
                  <text
                    v-if="
                      item.is_show_price === 1 && item.minpro_course_type !== 4
                    "
                    class="original-price"
                  >
                    ¥{{ item.standard_price }}
                  </text>
                </view>

                <view class="buy-btn">
                  {{ item.minpro_course_type !== 4 ? "立即购买" : "立即观看" }}
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <image
      :class="['bottom-image', { 'bottom-image-hidden': isScrolling }]"
      src="https://tg-prod.oss-cn-beijing.aliyuncs.com/efb844d4-88d2-4771-aa5d-04adb4d6226c.webp"
      mode="widthFix"
      @tap.stop="goToPage"
    ></image>
    <u-modal
      :show="showUpdateUserInfo"
      :showCancelButton="true"
      :title="title"
      :content="content"
      @cancel="handleCancel"
      @confirm="handleConfirm"
    ></u-modal>
    <!-- <TabBar ref="tabbar" :key="tabbarKey" @changeDot="handleChangeDot"></TabBar> -->
    <view @touchmove.stop.prevent="">
      <contact-popup
        v-model="showContact"
        :image="qrCodeInfo.qr_code_url"
        :text="`长按识别二维码联系${qrCodeInfo.contact_name}`"
      ></contact-popup>
      <bind-student-popup
        ref="bindStudentPopup"
        v-model="showBindStudent"
        @confirm="handleBindStudent"
      ></bind-student-popup>
      <copac-popup :show="showCopac" @close="showCopac = false"></copac-popup>
    </view>
  </div>
</template>

<script>
import { clearRead } from "@/services/student/my";
import {
  getBannerList,
  clickBanner,
  getBindStudent,
  recordLocation,
  getqRCodeService
} from "@/services/student/home";
import { rowIds } from "../my/config/index";
import ContactPopup from "./components/ContactPopup.vue";
import BindStudentPopup from "./components/BindStudentPopup.vue";
import MenuGrid from "./components/MenuGrid.vue";
import navbar from "./components/navbar.vue";
import {
  getDataByRole,
  processUserList,
  getCheckedStudentInfo,
  arouseLogin
} from "@/utils/user";
import { courseList } from "@/services/student/myCourse";
import CopacPopup from "./components/CopacPopup.vue";
import { getStudentAppointmentStatus } from "@/services/student/receiveExperienceCourse";
import { studentGrid, visitorIntentGrid } from "./config";
export default {
  // eslint-disable-next-line vue/multi-word-component-names
  name: "homeIndex",
  components: { ContactPopup, BindStudentPopup, MenuGrid, navbar, CopacPopup },
  data() {
    return {
      showCopac: false,
      showUpdateUserInfo: false,
      tabbarKey: 0,
      title: "获取信息",
      content: "尚未获取学员信息，请到我的页面获取！",
      rowIdField: "",
      rowIds,
      role: "",
      current: 0,
      bannerList: [],
      menuList: [],
      courseList: [],
      isScrolling: false,
      showContact: false,
      showBindStudent: false,
      isShowDot: "",
      curStudentInfo: "",
      session: "",
      studentList: [],
      haveStudent: false,
      flag: false,
      scrollTimer: null,
      scrollTop: 0,
      changeSearch: false,
      sectionTitleOffsetTop: 0,
      stickyNavbarTop: 0,
      stickyNavbar: false,
      qrCodeInfo: ""
    };
  },
  computed: {},
  watch: {
    role: {
      handler(newVal) {
        this.menuList = newVal === "student" ? studentGrid : visitorIntentGrid;
      }
    }
  },
  methods: {
    async initData() {
      try {
        this.session = uni.getStorageSync("session");
        this.role = this.session.role;
        this.rowIdField = this.rowIds[this.role];
        // 获取选中的学生/客户信息
        await getCheckedStudentInfo(
          this.session.open_id,
          this.role,
          this.session.operation_id
        );
        // 根据角色获取用户列表
        await getDataByRole(
          this,
          this.role,
          this.session.open_id,
          (code, data, message) => {
            processUserList(code, data, message, this, this.session.open_id);
          }
        );
        // 获取轮播图数据
        this.getBannerData();
        // 获取首页默认课程
        this.getCourseList();
        // 获取位置信息
        this.getLocation();
        uni.hideLoading();
      } catch (error) {
        console.error("初始化数据失败:", error);
        uni.$u.toast("初始化数据失败,请重新打开小程序");
        uni.hideLoading();
      }
    },

    async getBannerData() {
      try {
        const res = await getBannerList({
          department_id: uni.getStorageSync("curStudentInfo").department_id
        });
        if (res.code === 0) {
          this.bannerList = res.data || [];
        }
      } catch (error) {
        console.error("获取轮播图失败:", error);
      }
    },

    leftClick() {
      uni.redirectTo({ url: "/pages/index/index" });
      this.order();
    },

    handleOrderMessage() {
      const _this = this;
      uni.getSetting({
        withSubscriptions: true,
        success: (res) => {
          if (res.subscriptionsSetting.mainSwitch) {
            if (
              res.subscriptionsSetting.itemSettings &&
              ["reject", "accept"].includes(
                res.subscriptionsSetting.itemSettings[
                  "wIXqKYLaKlbJh30dDpW5OgvrCw6XXC9QS-bKmfQMuDE"
                ]
              )
            ) {
              return;
            }
            _this.order();
            uni.showModal({
              title: "温馨提示",
              content: "您尚未订阅一次性消息，是否立即订阅？",
              confirmText: "订阅",
              success: (res) => {
                if (res.confirm) {
                  _this.order();
                }
              }
            });
          }
        }
      });
    },

    order() {
      uni.requestSubscribeMessage({
        tmplIds: ["vB2CRkazhgWRKudJC8z-533owRJDHIwEvTIazFYl5cA"],
        success(res) {
          uni.login({
            provider: "weixin",
            success: (res) => {}
          });
        },
        fail(err) {
          console.log(err, "err");
        }
      });
    },

    toPage() {
      this.order();
      uni.navigateTo({
        url: "/pages/student/questionnaireSurvey/index?role=" + this.role
      });
    },

    async handleMenuClick(item) {
      if (
        item.isNeedBindStudent &&
        uni.getStorageSync("session").role === "default"
      ) {
        this.showBindStudent = true;
        return;
      }
      const hasNew = [
        "class_notice",
        "course_summary",
        "parent_class",
        "survey"
      ];
      if (hasNew.includes(item.value)) {
        // if (this.session.role !== "default") {
        const student_status =
          this.session.role === "student"
            ? 3
            : this.session.role === "customer"
            ? 2
            : 1;
        const student_id =
          this.session.role === "student"
            ? this.curCheckedStudent.student_id
            : this.session.role === "customer"
            ? this.curCheckedStudent.customer_id
            : this.session.open_id;
        await clearRead({
          student_id,
          type: item.value,
          student_status
        });
        // }
        if (item.value === "survey") {
          this.toPage();
          return;
        }
      }
      // if (item.value === "buyCourse") {
      //   uni.switchTab({
      //     url: "/pages/student/buyCourse/index"
      //   });
      //   return;
      // }
      if (item.url) {
        console.log(item.url, "item.url");
        uni.navigateTo({
          url: item.url,
          success: () => {
            this.flag = false;
            uni.hideLoading();
          },
          fail: () => {
            this.flag = false;
            uni.hideLoading();
          }
        });
      }
    },

    toCourseDetail(course) {
      uni.navigateTo({
        url: `/pages/student/course/detail?id=${course.id}`
      });
    },

    handleScrolling() {
      this.isScrolling = true;
      // 使用防抖处理滚动停止
      if (this.scrollTimer) {
        clearTimeout(this.scrollTimer);
      }
      this.scrollTimer = setTimeout(() => {
        this.isScrolling = false;
      }, 1000); // 滚动停止1秒后恢复显示
    },

    goToPage() {
      this.showContact = true;
    },

    handleBuyCourse(course) {
      uni.navigateTo({
        url: `/pages/student/subpages/courseDetail/index?course_id=${course.minpro_course_id}`
      });
    },

    handleCancel() {
      this.showUpdateUserInfo = false;
      this.order();
    },

    handleConfirm() {
      uni.switchTab({ url: "/pages/student/my/index" });
      this.showUpdateUserInfo = false;
      this.order();
    },

    handleChangeDot(val) {
      this.isShowDot = val;
      this.menuList.forEach((item) => {
        if (val[item.value]) {
          item.isShowDot = val[item.value].has_new;
        } else {
          item.isShowDot = "";
        }
      });
    },

    handleBannerClick(index) {
      if (!this.bannerList[index].jump_url) return;
      clickBanner({
        id: this.bannerList[index].id,
        user_id:
          uni.getStorageSync("session").role === "student"
            ? uni.getStorageSync("curStudentInfo").student_id
            : uni.getStorageSync("session").role === "customer"
            ? uni.getStorageSync("curStudentInfo").customer_id
            : uni.getStorageSync("session").open_id
      });
      const tabBarPages = [
        "/pages/student/home/<USER>",
        "/pages/student/my/index"
      ];

      if (this.bannerList[index].jump_type === "h5") {
        uni.navigateTo({
          url:
            "/pages/student/home/<USER>/index?url=" +
            encodeURIComponent(this.bannerList[index].jump_url)
        });
      } else {
        // 检查是否是 tabBar 页面
        const isTabBarPage = tabBarPages.includes(
          this.bannerList[index].jump_url
        );

        if (isTabBarPage) {
          uni.switchTab({
            url: this.bannerList[index].jump_url
          });
        } else {
          uni.navigateTo({
            url: this.bannerList[index].jump_url
          });
        }
      }
    },

    async handleBindStudent(data) {
      try {
        const formData = await this.$refs.bindStudentPopup.validate();
        this.handleBindUser(formData);
      } catch (error) {
        console.error("表单验证失败:", error);
      }
      // this.showBindStudent = false;
      // this.initData(); // 重新加载数据
    },
    async handleBindUser(formData) {
      uni.showLoading({
        title: "查找中..."
      });
      const { code, data, message } = await getBindStudent(formData);
      if (code === 0) {
        uni.hideLoading();
        uni.navigateTo({
          url:
            "/pages/student/studentPage/index?prospective=" +
            JSON.stringify(data)
        });
        this.$refs.bindStudentPopup.handleClose();
      } else {
        uni.hideLoading();
        uni.$u.toast({ message, icon: "none", duration: 2000 });
      }
    },
    getLocation() {
      uni.getSetting({
        success(res) {
          // 如果没有授权
          if (!res.authSetting["scope.userLocation"]) {
            // 则拉起授权窗口
            uni.authorize({
              scope: "scope.userLocation",
              success(res) {
                console.log("授权成功", res);
                uni.getLocation({
                  type: "wgs84",
                  success: async (res) => {
                    const { data } = await recordLocation({
                      latitude: res.latitude,
                      longitude: res.longitude
                    });
                    uni.setStorageSync("location", {
                      ...res,
                      city: data?.city || ""
                    });
                  },
                  fail(err) {
                    console.log("获取位置信息失败", err);
                  }
                });
              },
              fail() {
                console.log("授权失败");
              }
            });
          } else {
            uni.getLocation({
              type: "wgs84",
              success: async (res) => {
                const { data } = await recordLocation({
                  latitude: res.latitude,
                  longitude: res.longitude
                });
                uni.setStorageSync("location", {
                  ...res,
                  city: data?.city || ""
                });
              }
            });
          }
        },
        fail() {
          console.log("获取位置信息失败");
        }
      });
    },
    async getCourseList() {
      // 获取首页默认课程
      const student_id = uni.getStorageSync("curStudentInfo").student_id;
      const department_id = uni.getStorageSync("curStudentInfo").department_id;
      const { code, data, message } = await courseList({
        student_id,
        department_id,
        is_front: true,
        page: 1,
        page_size: 10
      });
      if (code === 0) {
        this.courseList = data.results || [];
      } else {
        this.$refs.uToast.show({
          message
        });
      }
    },
    toCourseList() {
      // uni.switchTab({
      //     url: "/pages/student/buyCourse/index"
      // });
      if (uni.getStorageSync("session").role === "student") {
        uni.navigateTo({
          url: "/pages/student/buyCourse/index"
        });
        return;
      }
      // eslint-disable-next-line no-undef
      getApp().globalData.tabIndex = 1;
      this.$emit("handleChangeTab", 1);
    },
    // 获取客服信息
    async getCustomerInfo() {
      const res = await getqRCodeService();
      if (res.code === 0) {
        this.qrCodeInfo = res.data;
      }
    },
    handlePageScroll(e) {
      // 记录滚动位置
      this.scrollTop = e.scrollTop;
      // 判断是否需要吸顶
      if (
        e.scrollTop >= this.sectionTitleOffsetTop &&
        this.sectionTitleOffsetTop > 0
      ) {
        this.changeSearch = true;
      } else {
        this.changeSearch = false;
      }
      if (e.scrollTop >= this.stickyNavbarTop && this.stickyNavbarTop > 0) {
        this.stickyNavbar = true;
      } else {
        this.stickyNavbar = false;
      }
      // 触发滚动处理
      this.handleScrolling();
    },
    handleOnLoad() {
      this.$nextTick(() => {
        this.scrollTop = 0;
        this.sectionTitleOffsetTop = 0;
        this.stickyNavbarTop = 0;
        const query = uni.createSelectorQuery().in(this);
        query
          .select(".sticky")
          .boundingClientRect((data) => {
            if (data) {
              this.sectionTitleOffsetTop = data.top - 80; // 减去导航栏高度
            }
          })
          .exec();
        query
          .select(".sticky-wrapper")
          .boundingClientRect((data) => {
            if (data) {
              this.stickyNavbarTop = data.top + 40;
            }
          })
          .exec();
        getStudentAppointmentStatus({
          // student_id: uni.getStorageSync("curStudentInfo").student_id
        }).then((res) => {
          if (
            res.data.status !== 1 &&
            uni.getStorageSync("session").role === "default"
          ) {
            this.showCopac = true;
          }
        });
      });
    }
  },
  // 页面周期函数--监听页面初次渲染完成
  onReady() {},
  // 页面周期函数--监听页面显示(not-nvue)
  async mounted() {
    // this.$refs.tabbar.setMenuList()
    if (uni.getStorageSync("fromDTB")) {
      uni.setStorageSync("portType", "STUDENT");
      uni.removeStorageSync("curStudentInfo");
      uni.removeStorageSync("session");
      uni.removeStorageSync("fromDTB");
      await arouseLogin();
      this.initData();
      this.getCustomerInfo();
    } else {
      // this.getCheckedStudentInfo();
      uni.hideHomeButton();
      // this.getBannerData();
      this.initData();
      this.getCustomerInfo();
    }
    this.tabbarKey++;
  },
  onLaunch: function (options) {
    uni.hideTabBar();
  },
  // 页面周期函数--监听页面隐藏
  onHide() {},
  // 页面周期函数--监听页面卸载
  onUnload() {}
};
</script>

<style lang="scss" scoped>
.home {
  padding: 0 30rpx;
  background: url("https://tg-prod.oss-cn-beijing.aliyuncs.com/5c52e3a3-5990-48e7-ab2e-46a0d6052791.png");
  background-repeat: no-repeat; /* 禁止背景图重复（避免多余显示） */
  background-size: 100% auto; /* 保持图片原始尺寸，不变形 */
  background-position: top; /* 背景图顶部与容器顶部对齐 */
  position: relative;
  min-height: 100vh;

  .logo-wrapper {
    display: flex;
    align-items: center;
    height: 81rpx;
    width: 300rpx;
    margin-top: -4rpx;

    image {
      width: 100%;
      height: 100%;
      // flex-shrink: 0;
      // aspect-ratio: 100/27;
      // position: absolute;
      // top: 79rpx;
      // left: 26rpx;
      // z-index: 10;
    }
  }

  .to-index {
    position: absolute;
    left: 10rpx;
    top: 10rpx;
  }

  .swiper-section {
    // margin: 33rpx 0;
    margin-top: 24rpx;
    border-radius: 10rpx;
    overflow: hidden;

    .indicator {
      @include flex(row);
      justify-content: center;
      margin-top: 12rpx;

      &__dot {
        width: 10rpx;
        height: 10rpx;
        border-radius: 100rpx;
        opacity: 0.4;
        background: #ffc525;
        transition: background-color 0.3s;
        margin-right: 10rpx;

        &--active {
          width: 24.193rpx;
          height: 10rpx;
          background-color: #ffc525;
          opacity: 1;
          border-radius: 20rpx;
        }
      }
    }
  }

  .course-section {
    // margin: 20rpx;
    // background: #fff;
    border-radius: 10rpx;
    // padding: 20rpx;
    margin-top: 26.35rpx;

    .section-title {
      // font-size: 32rpx;
      // font-weight: bold;
      // margin-bottom: 20rpx;
      // padding-left: 20rpx;
      // border-left: 4rpx solid #3c9cff;
      display: flex;
      width: 100%;
      height: 94rpx;
      justify-content: space-between;
      align-items: center;

      .section-title-text {
        color: #333;
        font-size: 34rpx;
        font-style: normal;
        font-weight: 500;
        line-height: normal;
      }

      .section-title-more {
        color: #999;
        font-size: 25rpx;
        font-style: normal;
        font-weight: 400;
        line-height: 35rpx;
        display: flex;
        align-items: center;

        .til {
          margin-right: 8.17rpx;
          color: #999;
          font-size: 25rpx;
          font-style: normal;
          font-weight: 400;
        }
      }
    }

    .course-list {
      .course-item {
        position: relative;
        background: #ffffff;
        border-radius: 16rpx;
        margin-bottom: 20rpx;
        overflow: hidden;
        display: flex;
        padding: 20rpx;

        .course-tag {
          width: 128rpx;
          height: 45.39rpx;
          position: absolute;
          top: 25rpx;
          left: 10rpx;
          z-index: 3;
          color: #fff;
          border-radius: 0;
        }

        image {
          width: 160rpx;
          height: 160rpx;
          flex-shrink: 0;
          border-radius: 20rpx;
          object-fit: cover;
        }

        .course-info {
          padding: 0 24rpx;
          width: 100%;
          display: flex;
          flex-direction: column;
          justify-content: space-between;

          .course-name {
            font-size: 32rpx;
            color: #333;
            font-weight: 500;
            display: block;
            margin-bottom: 4rpx;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            width: 465rpx;
          }

          .course-duration {
            color: #999;
            font-size: 26rpx;
            font-style: normal;
            font-weight: 400;
            line-height: normal;
          }

          .course-bottom {
            display: flex;
            justify-content: space-between;
            // margin-top: 30rpx;
            width: 100%;

            .price-box {
              width: 100%;
              display: flex;
              justify-content: space-between;

              // gap: 16rpx;
              .course-price {
                color: #ff553a;
                font-size: 36rpx;
                font-style: normal;
                font-weight: 600;
                line-height: 40rpx;
                display: flex;
                justify-content: center;
                align-items: center;

                .price-symbol {
                  font-size: 24rpx;
                  color: #ff553a;
                  font-weight: 400;
                  line-height: normal;
                  margin-right: 5rpx;
                }
              }

              .original-price {
                font-size: 24rpx;
                color: #999;
                text-decoration: line-through;
                margin-left: 10rpx;
                // line-height: 40rpx;
                display: flex;
                align-items: center;
              }

              .buy-btn {
                width: 160rpx;
                height: 64rpx;
                border-radius: 32px;
                background: linear-gradient(
                  15deg,
                  #ffbf0d 18.1%,
                  #ffcb3c 83.29%
                );
                box-shadow: 0px -4px 8px 0px #eaac00 inset;
                color: #fff;
                font-size: 24rpx;
                font-style: normal;
                font-weight: 600;
                line-height: 32px;
                text-align: center;
                line-height: 64rpx;
              }
            }
          }
        }
      }
    }
  }

  .bottom-image {
    position: fixed;
    right: 0;
    bottom: 260rpx;
    width: 132rpx;
    height: 142rpx;
    transition: all 1s ease;
    z-index: 2;

    &-hidden {
      transform: translateX(50%);
      opacity: 0.5;
    }
  }
}

.sticky-navbar {
  background-color: #fff !important;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.sticky-title {
  position: fixed;
  top: 81rpx;
  /* 导航栏高度 */
  left: 0;
  right: 0;
  z-index: 998;
  background-color: #fff;
  padding: 30rpx;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.title-placeholder {
  width: 100%;
}

.section-title {
  // font-size: 32rpx;
  // font-weight: bold;
  // margin-bottom: 20rpx;
  // padding-left: 20rpx;
  // border-left: 4rpx solid #3c9cff;
  display: flex;
  width: 100%;
  height: 94rpx;
  justify-content: space-between;
  align-items: center;

  .section-title-text {
    color: #333;
    font-size: 34rpx;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
  }

  .section-title-more {
    color: #999;
    font-size: 25rpx;
    font-style: normal;
    font-weight: 400;
    line-height: 35rpx;
    display: flex;
    align-items: center;

    .til {
      margin-right: 8.17rpx;
      color: #999;
      font-size: 25rpx;
      font-style: normal;
      font-weight: 400;
    }
  }
}

.sticky-wrapper {
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 0 32rpx;
}
</style>
