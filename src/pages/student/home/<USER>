// 游客 意向的金刚区
const visitorIntentGrid = [
    {
        name: "精选视频",
        icon: "https://tg-prod.oss-cn-beijing.aliyuncs.com/621af27f-e1dd-4c15-8445-628f5b9cdf14.webp",
        color: "#3c9cff",
        url: "/pages/student/wonderfulVideo/index",
        isShow: true,
        width: "69rpx",
        height: "63rpx",
        value: "video",
        isHasDot: false,
        isNeedBindStudent: false
      },
      {
        name: "聂道名师",
        icon: "https://tg-prod.oss-cn-beijing.aliyuncs.com/a05f4435-cd4e-46f1-9304-1857d3c65a21.webp",
        color: "#3c9cff",
        url: "/pages/student/subpages/niedaoTeacher/index",
        isShow: true,
        width: "69rpx",
        height: "63rpx",
        value: "teacher",
        isHasDot: false,
        isNeedBindStudent: false
      },
      {
        name: "校区介绍",
        icon: "https://tg-prod.oss-cn-beijing.aliyuncs.com/3bcee89b-d1aa-4a56-9177-db38ca51ae4e.webp",
        color: "#3c9cff",
        url: "/pages/student/subpages/campus/index",
        isShow: true,
        width: "69rpx",
        height: "63rpx",
        value: "campus",
        isHasDot: false,
        isNeedBindStudent: false
      },
    {
      name: "我的课程",
      icon: "https://tg-prod.oss-cn-beijing.aliyuncs.com/8ad75c64-424c-4481-9f1d-2ff07b1242c6.png",
      color: "#3c9cff",
      url: "/pages/student/course/index",
      isShow: true,
      width: "74rpx",
      height: "68rpx",
      value: "course",
      isHasDot: false,
      isNeedBindStudent: true
    },
    {
      name: "报读班级",
      icon: "https://tg-prod.oss-cn-beijing.aliyuncs.com/deba9fd7-9d2b-4780-adbb-801b7d40852e.png",
      color: "#f56c6c",
      url: "/pages/student/appliedClass/index",
      isShow: true,
      width: "77rpx",
      height: "74rpx",
      value: "class",
      isHasDot: false,
      isNeedBindStudent: true
    },
    {
      name: "我的课表",
      icon: "https://tg-prod.oss-cn-beijing.aliyuncs.com/2998258e-7547-4f4d-ab3d-bded1d52197a.png",
      color: "#3c9cff",
      url: "/pages/student/classSchedule/index",
      isShow: true,
      width: "66rpx",
      height: "74rpx",
      value: "classSchedule",
      isHasDot: false,
      isNeedBindStudent: true
    },
    {
      name: "调查问卷",
      icon: "https://tg-prod.oss-cn-beijing.aliyuncs.com/128a850b-77d0-46f8-b363-a71769012027.png",
      color: "#5ac725",
      url: "/pages/student/questionnaireSurvey/index",
      isShow: true,
      width: "68rpx",
      height: "66rpx",
      value: "survey",
      isHasDot: false,
      isNeedBindStudent: true
    },
    {
      name: "课程总结",
      icon: "https://tg-prod.oss-cn-beijing.aliyuncs.com/814972ef-cd4a-46fe-a6cd-ffcdca7300ae.png",
      color: "#3c9cff",
      url: "/pages/student/subpages/studyReport/index?type=course_summary",
      isShow: true,
      width: "84rpx",
      height: "72rpx",
      value: "course_summary",
      isHasDot: false,
      isNeedBindStudent: true
    },
    {
      name: "家长课堂",
      icon: "https://tg-prod.oss-cn-beijing.aliyuncs.com/e24ff86f-1410-48f0-b973-a85c1eddf219.png",
      color: "#3c9cff",
      url: "/pages/student/subpages/studyReport/index?type=parent_class",
      isShow: true,
      width: "73rpx",
      height: "77rpx",
      value: "parent_class",
      isHasDot: false,
      isNeedBindStudent: true
    },
    {
      name: "班级通知",
      icon: "https://tg-prod.oss-cn-beijing.aliyuncs.com/a8be2a7b-d570-4127-80c0-ae3583d00230.png",
      color: "#3c9cff",
      url: "/pages/student/subpages/studyReport/index?type=class_notice",
      isShow: true,
      width: "70rpx",
      height: "69rpx",
      value: "class_notice",
      isHasDot: false,
      isNeedBindStudent: true
    },
    {
      name: "赛事报名",
      icon: "https://tg-prod.oss-cn-beijing.aliyuncs.com/19675ce4-56fd-426c-bb21-afdcd64abfb0.png",
      color: "#3c9cff",
      url: "/pages/student/subpages/competition/index",
      isShow: true,
      width: "69rpx",
      height: "63rpx",
      value: "competition",
      isHasDot: false,
      isNeedBindStudent: false
    }
  ];
// 学员的金刚区
const studentGrid = [
    {
      name: "我的课程",
      icon: "https://tg-prod.oss-cn-beijing.aliyuncs.com/8ad75c64-424c-4481-9f1d-2ff07b1242c6.png",
      color: "#3c9cff",
      url: "/pages/student/course/index",
      isShow: true,
      width: "74rpx",
      height: "68rpx",
      value: "course",
      isHasDot: false,
      isNeedBindStudent: true
    },
    {
      name: "报读班级",
      icon: "https://tg-prod.oss-cn-beijing.aliyuncs.com/deba9fd7-9d2b-4780-adbb-801b7d40852e.png",
      color: "#f56c6c",
      url: "/pages/student/appliedClass/index",
      isShow: true,
      width: "77rpx",
      height: "74rpx",
      value: "class",
      isHasDot: false,
      isNeedBindStudent: true
    },
    {
      name: "我的课表",
      icon: "https://tg-prod.oss-cn-beijing.aliyuncs.com/2998258e-7547-4f4d-ab3d-bded1d52197a.png",
      color: "#3c9cff",
      url: "/pages/student/classSchedule/index",
      isShow: true,
      width: "66rpx",
      height: "74rpx",
      value: "classSchedule",
      isHasDot: false,
      isNeedBindStudent: true
    },
    {
        name: "课程总结",
        icon: "https://tg-prod.oss-cn-beijing.aliyuncs.com/814972ef-cd4a-46fe-a6cd-ffcdca7300ae.png",
        color: "#3c9cff",
        url: "/pages/student/subpages/studyReport/index?type=course_summary",
        isShow: true,
        width: "84rpx",
        height: "72rpx",
        value: "course_summary",
        isHasDot: false,
        isNeedBindStudent: true
      },
      {
        name: "家长课堂",
        icon: "https://tg-prod.oss-cn-beijing.aliyuncs.com/e24ff86f-1410-48f0-b973-a85c1eddf219.png",
        color: "#3c9cff",
        url: "/pages/student/subpages/studyReport/index?type=parent_class",
        isShow: true,
        width: "73rpx",
        height: "77rpx",
        value: "parent_class",
        isHasDot: false,
        isNeedBindStudent: true
      },
      {
        name: "班级通知",
        icon: "https://tg-prod.oss-cn-beijing.aliyuncs.com/a8be2a7b-d570-4127-80c0-ae3583d00230.png",
        color: "#3c9cff",
        url: "/pages/student/subpages/studyReport/index?type=class_notice",
        isShow: true,
        width: "70rpx",
        height: "69rpx",
        value: "class_notice",
        isHasDot: false,
        isNeedBindStudent: true
      },
      {
        name: "聂道名师",
        icon: "https://tg-prod.oss-cn-beijing.aliyuncs.com/a05f4435-cd4e-46f1-9304-1857d3c65a21.webp",
        color: "#3c9cff",
        url: "/pages/student/subpages/niedaoTeacher/index",
        isShow: true,
        width: "69rpx",
        height: "63rpx",
        value: "teacher",
        isHasDot: false,
        isNeedBindStudent: false
      },
      {
        name: "校区介绍",
        icon: "https://tg-prod.oss-cn-beijing.aliyuncs.com/3bcee89b-d1aa-4a56-9177-db38ca51ae4e.webp",
        color: "#3c9cff",
        url: "/pages/student/subpages/campus/index",
        isShow: true,
        width: "69rpx",
        height: "63rpx",
        value: "campus",
        isHasDot: false,
        isNeedBindStudent: false
      },
      {
        name: "赛事报名",
        icon: "https://tg-prod.oss-cn-beijing.aliyuncs.com/19675ce4-56fd-426c-bb21-afdcd64abfb0.png",
        color: "#3c9cff",
        url: "/pages/student/subpages/competition/index",
        isShow: true,
        width: "69rpx",
        height: "63rpx",
        value: "competition",
        isHasDot: false,
        isNeedBindStudent: false
      },
    {
      name: "调查问卷",
      icon: "https://tg-prod.oss-cn-beijing.aliyuncs.com/128a850b-77d0-46f8-b363-a71769012027.png",
      color: "#5ac725",
      url: "/pages/student/questionnaireSurvey/index",
      isShow: true,
      width: "68rpx",
      height: "66rpx",
      value: "survey",
      isHasDot: false,
      isNeedBindStudent: true
    },
    {
      name: "课程购买",
      icon: "https://tg-prod.oss-cn-beijing.aliyuncs.com/53a861e5-f457-476a-855a-4f9ea0bf183d.png",
      color: "#3c9cff",
      url: "/pages/student/buyCourse/index",
      isShow: true,
      width: "69rpx",
      height: "63rpx",
      value: "buyCourse",
      isHasDot: false,
      isNeedBindStudent: false
    }
  ];

export { visitorIntentGrid, studentGrid };
