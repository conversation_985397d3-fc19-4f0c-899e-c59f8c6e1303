<template>
  <u-popup
    :show="show"
    mode="center"
    @close="close"
    bgColor="transparent"
    :safeAreaInsetBottom="false"
    :closeOnClickOverlay="false"
  >
    <view class="copac-popup">
      <image
        src="https://tg-prod.oss-cn-beijing.aliyuncs.com/c9ff7c70-b1da-4df6-aeea-6b377b58a62b.png"
        mode="widthFix"
        @click="handleClick"
      ></image>
    </view>
    <view class="close" @click="close">
      <image
        src="https://tg-prod.oss-cn-beijing.aliyuncs.com/d8e69ea1-3c7b-425a-87cb-7da31b458ec5.webp"
        mode="widthFix"
      ></image>
    </view>
  </u-popup>
</template>
<script>
export default {
  name: "CopacPopup",
  props: {
    show: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    close() {
      this.$emit("close");
      uni.setStorageSync("copac_popup_show", "1");
    },
    handleClick() {
      uni.navigateTo({
        url: "/pages/student/subpages/receiveExperienceCourse/index"
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.copac-popup {
  background: url("https://tg-prod.oss-cn-beijing.aliyuncs.com/37de34eb-2f49-4040-a00a-6957c5ee946a.png")
    no-repeat center center;
  background-size: 100% 100%;
  width: 582rpx;
  height: 854rpx;
  position: relative;
  image {
    width: 450rpx;
    height: 94rpx;
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}
.close {
  width: 100%;
  display: flex;
  justify-content: center;
  image {
    width: 66rpx;
    height: 66rpx;
  }
}
</style>
