<template>
  <u-popup
    :show="show"
    @close="handleClose"
    mode="center"
    round="34rpx"
    :customStyle="{ backgroundColor: '#FFFFFF' }"
    :safeAreaInsetBottom="false"
  >
    <view class="contact-popup">
      <view class="popup-header">
        <image
          class="title"
          src="https://tg-prod.oss-cn-beijing.aliyuncs.com/40a46a2f-40a3-4050-8f7a-e5a15f063357.png"
        />
        <view @tap="handleClose" class="close-icon-box">
          <image
            class="close-icon"
            src="https://tg-prod.oss-cn-beijing.aliyuncs.com/00da00aa-8dad-47b0-a791-6da585b133a9.webp"
          />
        </view>
      </view>
      <view class="popup-content">
        <view class="qr-code">
          <u-image
            :show-menu-by-longpress="true"
            :src="image + '?x-oss-process=image/resize,h_380,w_380'"
            mode="aspectFit"
            width="380rpx"
            height="380rpx"
          />
          <!-- <uv-qrcode
            :value="qrcode_opt.data"
            v-show="false"
            ref="qrcode"
            :foregroundImageBorderRadius="20"
            :backgroundImageBorderRadius="20"
            :size="size"
            :options="qrcode_opt"
            :show-menu-by-longpress="true"
          ></uv-qrcode> -->
          <text>{{ text }}</text>
        </view>
      </view>
    </view>
  </u-popup>
</template>

<script>
// import QRCodeStyleRound from "@/uqrcode-plugin/round/uqrcode.plugin.round.es.js";
export default {
  name: "ContactPopup",
  props: {
    value: {
      type: Boolean,
      default: false
    },
    image: {
      type: String,
      default: ""
    },
    text: {
      type: String,
      default: ""
    }
  },
  computed: {
    show: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit("input", val);
      }
    }
  },
  data() {
    return {
      size: "380rpx",
      qrcode_opt: {
        // data: "",
        backgroundImageBorderRadius: 20,
        // size: 261,
        // useDynamicSize: false,
        // errorCorrectLevel: "Q",
        margin: 10,
        areaColor: "transparent",
        // backgroundColor: "#ffffff",
        borderRadius: 50
        // style: "round"
        // areaColor: "#F95F6B",
        // backgroundColor: "#3c9cff"
        // 指定二维码前景，一般可在中间放logo
        // foregroundImageSrc: "https://www.uvui.cn/common/logo.png"
      }
    };
  },
  methods: {
    handleClose() {
      this.show = false;
    }
  },
  mounted() {
    // this.qrcode_opt.data = "123";
    // // console.log("qrcode_url :>> ", this.qrcode_opt.data);
    // console.log(this.$refs.qrcodeBx);
    // this.$refs.qrcode.registerStyle(QRCodeStyleRound);
  }
};
</script>

<style lang="scss" scoped>
.contact-popup {
  width: 582rpx;
  height: 582rpx;
  //   padding: 40rpx;

  .popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 90rpx;
    position: relative;
    z-index: 2;

    .title {
      width: 308rpx;
      height: 326rpx;
      flex-shrink: 0;
      aspect-ratio: 154/163;
      position: absolute;
      top: -67px;
      left: 60px;
      z-index: 3;
    }
    .close-btn {
      width: 100rpx;
      height: 100rpx;
      position: absolute;
      top: 0;
      right: 0;
      z-index: 3;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .close-icon {
      width: 36rpx;
      height: 36rpx;
    }
    .close-icon-box {
      width: 100rpx;
      height: 100rpx;
      position: absolute;
      top: 0rpx;
      right: 0rpx;
      z-index: 3;
      display: flex;
      align-items: center;
      justify-content: center;
      .close-icon {
        width: 36rpx;
        height: 36rpx;
        // position: absolute;
        // top: 30rpx;
        // right: 32rpx;
        // z-index: 3;
      }
    }
  }

  .popup-content {
    position: relative;

    .qr-code {
      display: flex;
      flex-direction: column;
      align-items: center;
      position: relative;
      z-index: 3;
      background-color: #fff;

      image {
        width: 380rpx;
        height: 380rpx;
        margin-bottom: 24rpx;
      }

      text {
        color: #666;
        font-size: 28rpx;
        font-style: normal;
        font-weight: 400;
        line-height: 39rpx;
        margin-top: 23rpx;
      }
    }
  }
}
</style>
