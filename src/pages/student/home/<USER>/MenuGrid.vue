<template>
  <view class="menu-section">
    <swiper
      :indicator-dots="false"
      indicator-color="#f2f2f2"
      indicator-active-color="#3c9cff"
      :autoplay="false"
      :circular="false"
      :duration="300"
      :style="{ height: '100%' }"
      @change="(e) => (current = e.detail.current)"
    >
      <swiper-item
        v-for="(menuGroup, groupIndex) in groupedMenuList"
        :key="groupIndex"
      >
        <view class="scroll-list">
          <!-- 上面一行 -->
          <view class="scroll-list__line">
            <view
              class="scroll-list__line__item"
              v-for="(item, index) in menuGroup.slice(0, 4)"
              :key="index"
              @touchstart="handleTouchStart(index, groupIndex)"
              @touchend="handleTouchEnd(index, groupIndex)"
              @click.stop="
                $u.throttle(handleMenuClick(index, groupIndex), 1500)
              "
              v-show="item.isShow"
              :class="[
                index === 3 && 'scroll-list__line__item--no-margin-right',
                {
                  'scroll-list__line__item--active':
                    activeItem === `${groupIndex}-${index}`
                }
              ]"
            >
              <div class="menu-icon-wrapper">
                <div class="dot" v-if="item.isShowDot"></div>
                <u-icon :name="item.icon" :color="item.color" size="94rpx" />
              </div>
              <text class="scroll-list__line__item__text">{{ item.name }}</text>
            </view>
          </view>
          <!-- 下面一行 -->
          <view class="scroll-list__line">
            <view
              class="scroll-list__line__item"
              v-for="(item, index) in menuGroup.slice(4, 8)"
              :key="index"
              @touchstart="handleTouchStart(index + 4, groupIndex)"
              @touchend="handleTouchEnd(index + 4, groupIndex)"
              @click.stop="
                $u.throttle(handleMenuClick(index + 4, groupIndex), 1500)
              "
              v-show="item.isShow"
              :class="[
                index === 3 && 'scroll-list__line__item--no-margin-right',
                {
                  'scroll-list__line__item--active':
                    activeItem === `${groupIndex}-${index + 4}`
                }
              ]"
            >
              <div class="menu-icon-wrapper">
                <div class="dot" v-if="item.isShowDot"></div>
                <u-icon :name="item.icon" :color="item.color" size="94rpx" />
              </div>
              <text class="scroll-list__line__item__text">{{ item.name }}</text>
            </view>
          </view>
        </view>
      </swiper-item>
    </swiper>
    <view class="indicator">
      <view
        v-for="(item, index) in 2"
        :key="index"
        class="indicator__dot"
        :class="[index === current && 'indicator__dot--active']"
      >
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: "MenuGrid",
  props: {
    menuList: {
      type: Array,
      required: true
    },
    isShowDot: {
      type: String,
      default: ""
    }
  },
  computed: {
    groupedMenuList() {
      // 每页显示8个菜单项（2行，每行4个）
      const groups = [];
      for (let i = 0; i < this.menuList.length; i += 8) {
        const group = this.menuList.slice(i, i + 8);
        if (group.length > 0) {
          // 只添加有内容的组
          groups.push(group);
        }
      }
      return groups;
    }
  },
  data() {
    return {
      activeItem: null, // 用于跟踪当前激活的菜单项
      current: 0
    };
  },
  methods: {
    handleTouchStart(index, groupIndex) {
      this.activeItem = `${groupIndex}-${index}`;
    },
    handleTouchEnd(index, groupIndex) {
      setTimeout(() => {
        if (this.activeItem === `${groupIndex}-${index}`) {
          this.activeItem = null;
        }
      }, 150); // 延迟重置，让动画效果更明显
    },
    handleMenuClick(index, groupIndex) {
      this.$emit("menu-click", this.groupedMenuList[groupIndex][index]);
    }
  }
};
</script>

<style lang="scss" scoped>
.menu-section {
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0px 0px 50px 0px rgba(124, 143, 166, 0.1);
  margin-top: 25rpx;
  padding: 30rpx 40rpx;
  height: 348.6499938964844rpx;

  .scroll-list {
    @include flex(column);
    width: 100%;
    height: 100%;

    &__line {
      @include flex;
      margin-top: 24rpx;
      justify-content: flex-start;
      width: 100%;

      &:first-child {
        margin-top: 0;
      }

      &__item {
        margin-right: 74rpx;
        display: flex;
        flex-direction: column;
        position: relative;
        flex: inherit;

        .menu-icon-wrapper {
          position: relative;

          .dot {
            position: absolute;
            top: 10px;
            right: 0px;
            width: 16rpx;
            height: 16rpx;
            border-radius: 50%;
            background: #fe4f37;
          }

          ::v-deep .u-icon__img {
            width: 75.959rpx;
            height: 63rpx;
            flex-shrink: 0;
          }
        }

        &__text {
          font-size: 24rpx;
          color: #333;
          font-weight: 500;
          white-space: nowrap;
          line-height: 34rpx;
        }

        &--no-margin-right {
          margin-right: 0;
        }
      }
    }
  }
}
.indicator {
  @include flex(row);
  justify-content: center;
  margin-top: 50rpx;

  &__dot {
    width: 10rpx;
    height: 10rpx;
    border-radius: 100rpx;
    opacity: 0.4;
    background: #ffc525;
    transition: background-color 0.3s;
    margin-right: 10rpx;

    &--active {
      width: 24.193rpx;
      height: 10rpx;
      background-color: #ffc525;
      opacity: 1;
      border-radius: 20rpx;
    }
  }
}
</style>
