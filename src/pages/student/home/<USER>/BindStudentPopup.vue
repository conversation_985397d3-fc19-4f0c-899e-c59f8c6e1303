<template>
  <u-popup :show="show" @close="handleClose" mode="center" :round="10" :customStyle="{
    borderRadius: '32rpx',
    background:
      'linear-gradient(180deg, #FFE9AC 19.26%, #FFF7E2 46.6%, #FFF8EB 105.11%)',
    boxShadow: '0px 8px 40px 0px rgba(0, 0, 0, 0.10)',
    width: '582rpx'
  }" :safeAreaInsetBottom="false" :closeOnClickOverlay="false">
    <u-toast ref="uToast" style="z-index: 999999 !important"></u-toast>
    <view class="bind-popup">
      <view class="popup-header">
        <image class="title"
          src="https://tg-prod.oss-cn-beijing.aliyuncs.com/5ac4b35e-64d1-4191-9f97-b60c74b3ada1.webp" />
        <view class="close-icon" @tap="handleClose">
          <image src="https://tg-prod.oss-cn-beijing.aliyuncs.com/adb128bf-a2ac-4e7a-a48e-8419d88de3fd.webp" />
        </view>
        <image class="bgj"
          src="https://tg-prod.oss-cn-beijing.aliyuncs.com/02d1bc2b-086e-41bb-8b79-fd9d96ae6372.webp" />
      </view>
      <view class="popup-content">
        <text class="tip-text">{{ title }}</text>
        <div class="modal-content">
          <u--form labelPosition="left" :model="formModel" :rules="rules" ref="uForm">
            <u-form-item prop="mobile" class="form-item">
              <u-input v-model="formModel.mobile" placeholder="请输入报名手机号码"
                placeholder-style="color: #BDBEC2; font-size: 28rpx;" maxlength="11" :customStyle="{
                  borderRadius: '60rpx',
                  background: '#fff',
                  border: 'none',
                  width: '502rpx',
                  height: '100rpx',
                  padding: '31rpx 0rpx 30rpx 40rpx',
                  fontSize: '28rpx'
                }" />
            </u-form-item>
            <u-form-item prop="code" class="form-item" style="position: relative">
              <u-input v-model="formModel.code" placeholder="请输入验证码"
                placeholder-style="color: #BDBEC2; font-size: 28rpx;" maxlength="6" :customStyle="{
                  borderRadius: '60rpx',
                  background: '#fff',
                  border: 'none',
                  width: '502rpx',
                  height: '100rpx',
                  padding: '31rpx 0rpx 30rpx 40rpx',
                  fontSize: '28rpx',
                  position: 'relative'
                }">
                <view class="code-block" slot="suffix">
                  <u-code ref="uCode2" @change="codeChange2" keep-running change-text="Xs后重发" seconds="60"
                    start-text="获取验证码"></u-code>
                  <text @tap="getCode2" :text="tips2" :style="{ color: codeTextColor }" class="u-page__code-text">{{
                    tips2 }}</text>
                </view>
              </u-input>
            </u-form-item>
          </u--form>
        </div>
        <view class="btn-group">
          <view class="confirm-btn" @tap="$u.throttle(handleConfirm, 1000)">{{ btnText }}</view>
          <image class="click-icon"
            src="https://tg-prod.oss-cn-beijing.aliyuncs.com/acc99838-bcbd-4efe-a077-31d12b38a8ff.webp" />
        </view>
      </view>
      <image class="bg-x" src="https://tg-prod.oss-cn-beijing.aliyuncs.com/99ff43f8-0cec-47eb-9491-26e72ad6cf79.webp" />
    </view>
  </u-popup>
</template>

<script>
import { sendSms } from "@/services/student/my";
export default {
  options: {
    styleIsolation: "shared"
  },
  name: "BindStudentPopup",
  props: {
    value: {
      type: Boolean,
      default: false
    },
    btnText: {
      type: String,
      default: "去绑定"
    },
    title: {
      type: String,
      default: "绑定学员"
    }
  },
  data() {
    return {
      formModel: {
        mobile: "",
        code: ""
      },
      rules: {
        mobile: [
          { required: true, message: "请输入手机号", trigger: "blur" },
          {
            required: true,
            len: 11,
            message: "请输入正确的手机号",
            trigger: "blur"
          }
        ],
        code: [
          { required: true, message: "请输入验证码", trigger: "blur" },
          {
            required: true,
            len: 6,
            message: "请输入正确的验证码",
            trigger: "blur"
          }
        ]
      },
      tips2: "获取验证码"
    };
  },
  computed: {
    show: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit("input", val);
      }
    }
  },
  methods: {
    validate() {
      return new Promise((resolve, reject) => {
        this.$refs.uForm.validate().then((valid) => {
          if (valid) {
            resolve(this.formModel);
          } else {
            reject(new Error("表单验证失败"));
          }
        });
      });
    },
    handleClose() {
      // 重置表单
      this.formModel = {
        mobile: "",
        code: ""
      };
      this.show = false;
    },
    handleConfirm() {
      this.$emit("confirm");
    },
    codeChange2(text) {
      if (text === "重新获取") {
        this.codeTextColor = "#00BDFF";
      }
      this.tips2 = text;
    },
    async getCode2() {
      if (this.$refs.uCode2.canGetCode) {
        this.$refs.uForm.validateField("mobile", async (err) => {
          if (err?.length === 0) {
            const params = {
              template_code: "SMS_175051151",
              sign_name: "聂卫平围棋课堂",
              mobile: this.formModel.mobile
            };
            const { code, message } = await sendSms(params);
            if (code === 0) {
              uni.hideLoading();
              // uni.$u.toast("验证码已发送");
              this.$refs.uToast.show({
                type: "default",
                message: "验证码已发送",
                iconUrl:
                  "https://tg-prod.oss-cn-beijing.aliyuncs.com/eca3f788-8874-4991-9e0f-10bec48e6233.webp",
                position: "top",
                duration: "1000"
              });
              this.codeTextColor = "#BFC1C5";
              // 通知验证码组件内部开始倒计时
              this.$refs.uCode2.start();
            } else {
              uni.hideLoading();
              this.$refs.uToast.show({
                type: "default",
                message,
                position: "top",
                iconUrl:
                  "https://tg-prod.oss-cn-beijing.aliyuncs.com/eca3f788-8874-4991-9e0f-10bec48e6233.webp",
                duration: "1000"
              });
            }
          }
        });
      } else {
        // uni.$u.toast("倒计时结束后再发送");
        this.$refs.uToast.show({
          type: "default",
          message: "倒计时结束后再发送",
          position: "top",
          duration: "1000"
        });
      }
    }
  }
};
</script>

<style lang="scss" scoped>
::v-deep .u-form-item__body {
  // padding: 15rpx 0;
}

.bind-popup {
  width: 100%;
  height: 100%;
  // padding: 40rpx;
  position: relative;

  .popup-header {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin-bottom: 44rpx;
    position: relative;

    .title {
      width: 254rpx;
      height: 299rpx;
      flex-shrink: 0;
      position: absolute;
      bottom: -100rpx;
      left: 162rpx;
      z-index: 3;
    }

    .close-icon {
      position: absolute;
      top: 0;
      right: 0;
      z-index: 3;
      width: 100rpx;
      height: 100rpx;
      display: flex;
      justify-content: center;
      align-items: center;

      image {
        width: 36rpx;
        height: 36rpx;
      }
    }

    .bgj {
      position: absolute;
      top: 0rpx;
      left: 0rpx;
      width: 100%;
      height: 260rpx;
      z-index: 2;
    }
  }

  .popup-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 130rpx;

    .modal-content {
      position: relative;
      z-index: 4;
    }

    .tip-text {
      color: #333;
      text-align: center;
      font-family: "PingFang SC";
      font-size: 36rpx;
      font-style: normal;
      font-weight: 500;
      line-height: 56rpx;
      margin-bottom: 14rpx;
    }

    .click-icon {
      position: absolute;
      bottom: -11rpx;
      left: 363rpx;
      width: 86rpx;
      height: 74rpx;
      transition: transform 0.2s ease;
      pointer-events: none;
    }

    .btn-group {
      display: flex;
      gap: 20rpx;
      width: 100%;
      justify-content: center;
      margin-top: 20rpx;
      position: relative;
      margin-bottom: 44rpx;

      .cancel-btn,
      .confirm-btn {
        width: 238rpx;
        height: 88rpx;
        fill: linear-gradient(15deg, #ffbf0d 18.1%, #ffcb3c 83.29%);
        box-shadow: 0px -5px 12px 0px #fc0 inset, 0px 9px 20px 0px #fff7e1 inset;
        filter: drop-shadow(0px 4px 4px rgba(255, 192, 18, 0.11));
        border-radius: 44px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 28rpx;
        color: #fff;
        font-weight: 500;
        transition: transform 0.2s ease;
        position: relative;

        &:active {
          transform: scale(1.05);

          &+.click-icon {
            transform: scale(1.05);
          }
        }
      }

      .cancel-btn {
        background: #f5f5f5;
        color: #666;
      }

      .confirm-btn {
        width: 238rpx;
        height: 88rpx;
        background: linear-gradient(15deg, #ffbf0d 18.1%, #ffcb3c 83.29%);
        box-shadow: 0px -5rpx 12rpx 0px #fc0 inset,
          0px 9rpx 20rpx 0px #fff7e1 inset;
        filter: drop-shadow(0px 4rpx 4rpx rgba(255, 192, 18, 0.11));
        color: #fff;

        font-size: 34rpx;
        font-weight: 500;
      }
    }
  }
}

.code-block {
  color: #ffc422;
  font-size: 26rpx;
  font-style: normal;
  font-weight: 400;
  line-height: 39rpx;
  border-left: 1rpx solid #e3e3e3;
  padding-left: 20rpx;
  margin-right: 20rpx;
  transition: transform 0.2s ease;

  &:active {
    transform: scale(1.05);
  }
}

.bg-x {
  position: absolute;
  bottom: -35rpx;
  left: -29rpx;
  width: 100%;
  height: 100%;
  width: 112rpx;
  height: 112rpx;
  z-index: 3;
}

::v-deep .u-form-item__body__right__message {
  margin-left: 40rpx !important;
  color: #fe4f37;
  font-size: 24rpx;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}
</style>
