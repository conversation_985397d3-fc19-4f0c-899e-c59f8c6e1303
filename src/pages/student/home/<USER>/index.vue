<template>
  <view class="web-view-container">
    <web-view :src="url" @message="handleMessage"></web-view>
  </view>
</template>

<script>
export default {
  name: "web_view",
  data() {
    return {
      url: ""
    };
  },

  onLoad(options) {
    // 获取传入的url参数
    if (options.url) {
      this.url = decodeURIComponent(options.url);
    }
  },

  methods: {
    handleMessage(event) {
      console.log("收到web-view消息：", event.detail);
    }
  }
};
</script>

<style lang="scss" scoped>
.web-view-container {
  width: 100%;
  height: 100vh;
}
</style>
