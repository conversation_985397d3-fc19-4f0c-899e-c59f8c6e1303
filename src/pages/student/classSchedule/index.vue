<!-- 我的课表 -->
<template>
  <view class="class-schedule-page">
    <u-navbar
      :placeholder="true"
      bgColor="transparent"
      :title="open_from === 'notification' ? student_name : '我的课表'"
      titleStyle="color: #fff;font-size:17px;font-weight:500"
      leftIconSize="20px"
      leftIconColor="#fff"
      @leftClick="handleLeftClick"
      :autoBack="false"
    >
    </u-navbar>
    <view class="schedule-header">
      <view class="header-top">
        <view class="location-info">
          <u-image
            src="https://tg-prod.oss-cn-beijing.aliyuncs.com/27f62ffb-ea9c-42aa-a0a6-81112819cdb1.png"
            class="location-icon"
            width="26rpx"
            height="30rpx"
            :showMenuByLongpress="false"
          />
          <view class="location-text">{{ campus }}</view>
        </view>
        <view class="view-switch">
          <view
            class="switch-item"
            :class="{ active: viewType === 'day' }"
            @click="switchView('day')"
            >日视图</view
          >
          <view
            class="switch-item"
            :class="{ active: viewType === 'week' }"
            @click="switchView('week')"
            >周视图</view
          >
        </view>
      </view>
      <view class="date-picker">
        <view class="arrow-btn" @click="prevDate">
          <span class="arrow-icon arrow-left"> </span>
          <text>上一周</text>
        </view>
        <view class="date-text">
          <text v-if="viewType === 'day' && selectedDay"
            >{{ selectedDay.year }}-{{ selectedDay.month }}</text
          >
          <text v-if="viewType === 'week' && weekDays.length > 0">{{
            dateText
          }}</text>
        </view>
        <view class="arrow-btn" @click="nextDate">
          <text>下一周</text>
          <span class="arrow-icon arrow-right"> </span>
        </view>
      </view>
    </view>
    <view class="schedule-content">
      <!-- 周视图模式 -->
      <week-view
        v-if="viewType === 'week'"
        :schedule-header-height="scheduleHeaderHeight"
        :date="currentDate"
        :bill-show="billShow"
        :open_from="open_from"
        :student_id="student_id"
      ></week-view>

      <!-- 日视图模式 -->
      <day-view
        v-if="viewType === 'day'"
        @day-selected="onDaySelected"
        :schedule-header-height="scheduleHeaderHeight"
        :date="currentDate"
        :bill-show="billShow"
        :open_from="open_from"
        :student_id="student_id"
      ></day-view>
    </view>
  </view>
</template>

<script>
import WeekView from "./week-view.vue";
import DayView from "./day-view.vue";
import { getWeekDays, getOffsetWeek, getWeekRangeText } from "@/utils/date";
import { menuShow } from "@/services/student/my";
import { login } from "@/services/student/home";
export default {
  name: "ClassSchedule",
  components: {
    WeekView,
    DayView
  },
  data() {
    return {
      viewType: "day",
      scheduleHeaderHeight: 0,
      currentDate: new Date(),
      weekDays: [],
      selectedDay: null,
      // 校区
      campus: "",
      billShow: false,
      open_from: "",
      student_name: "",
      department_id: "",
      student_id: "",
      department_name: "",
      is_first_show: false
    };
  },
  onLoad(options) {
    console.log("options", options);

    if (options.open_from === "notification") {
      uni.setStorageSync("is_first_show", false);
      this.currentDate = new Date(options.date);
      this.open_from = options.open_from;
      this.student_name = options.student_name;
      this.department_id = options.department_id;
      this.department_name = options.department_name;
      this.student_id = options.student_id;
      this.open_id = options.open_id;
    }
  },
  onUnload() {
    uni.removeStorageSync("is_first_show");
  },
  computed: {
    dateText() {
      if (this.viewType === "day" && this.selectedDay) {
        return `${this.selectedDay.year}-${this.selectedDay.month}`;
      } else if (this.viewType === "week" && this.weekDays.length > 0) {
        return getWeekRangeText(this.weekDays);
      }
      return "";
    }
  },
  async mounted() {
    // 获取.schedule-header高度
    const scheduleHeader = uni
      .createSelectorQuery()
      .in(this)
      .select(".schedule-header");
    scheduleHeader
      .boundingClientRect((data) => {
        this.scheduleHeaderHeight = data.height;
      })
      .exec();
    // if (this.open_from === "notification") {
    //   await this.loginByOpenId();
    // }
    // 初始化周数据
    await this.initWeekData();
    // 本地缓存获取校区
    this.campus =
      this.open_from === "notification"
        ? this.department_name
        : uni.getStorageSync("curStudentInfo")?.department_name ||
          "未获取到校区";
    this.getMenuShow();
  },
  methods: {
    async loginByOpenId() {
      const res2 = await login({
        open_id: this.open_id,
        UNAUTHORIZED: true
      });
      if (res2.code === 0) {
        const role =
          !res2.data.is_student && !res2.data.is_customer
            ? "default"
            : res2.data.is_student
            ? "student"
            : "customer";

        const session = { ...res2.data, role };
        uni.setStorageSync("session", session);
      }
    },
    // 获取计费展示权限
    async getMenuShow() {
      const department_id =
        this.open_from === "notification"
          ? this.department_id
          : uni.getStorageSync("curStudentInfo").department_id;
      const { code, data } = await menuShow({
        department_id
      });
      if (code === 0) {
        this.billShow = data.mini_is_billable === 1;
      }
    },
    // 初始化周数据
    initWeekData() {
      this.weekDays = getWeekDays(this.currentDate);
      // 默认选中当天
      const todayIndex = this.weekDays.findIndex((day) => day.isToday);
      if (todayIndex !== -1) {
        this.selectedDay = this.weekDays[todayIndex];
      } else {
        this.selectedDay = this.weekDays[0];
      }
    },

    switchView(type) {
      this.viewType = type;
    },

    prevDate() {
      // 切换到上一周
      this.currentDate = getOffsetWeek(this.currentDate, -1);
      this.weekDays = getWeekDays(this.currentDate);

      // 更新选中的日期
      if (this.viewType === "day") {
        this.selectedDay = this.weekDays[0];
      } else if (this.selectedDay) {
        // 在周视图模式下，保持选中的是同一天，但更新年月信息
        const dayIndex = this.weekDays.findIndex(
          (day) => day.date === this.selectedDay.date
        );
        if (dayIndex !== -1) {
          this.selectedDay = this.weekDays[dayIndex];
        } else {
          this.selectedDay = this.weekDays[0];
        }
      }
    },

    nextDate() {
      // 切换到下一周
      this.currentDate = getOffsetWeek(this.currentDate, 1);
      this.weekDays = getWeekDays(this.currentDate);

      // 更新选中的日期
      if (this.viewType === "day") {
        this.selectedDay = this.weekDays[0];
      } else if (this.selectedDay) {
        // 在周视图模式下，保持选中的是同一天，但更新年月信息
        const dayIndex = this.weekDays.findIndex(
          (day) => day.date === this.selectedDay.date
        );
        if (dayIndex !== -1) {
          this.selectedDay = this.weekDays[dayIndex];
        } else {
          this.selectedDay = this.weekDays[0];
        }
      }
    },

    onDaySelected(day) {
      this.selectedDay = day;
    },
    handleLeftClick() {
      if (this.open_from === "notification") {
        uni.switchTab({
          url: "/pages/student/home/<USER>"
        });
      } else {
        uni.navigateBack();
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.class-schedule-page {
  height: 100vh;
  overflow: hidden;
  background: #fb0;
  background-image: url("https://tg-prod.oss-cn-beijing.aliyuncs.com/310ca404-1a85-44ff-8f89-35702d101686.png");
  background-size: 100% auto;
  background-repeat: no-repeat;
}

.schedule-header {
  // background: #FB0;
  padding: 20rpx 30rpx 30rpx;

  .header-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 54rpx;
    margin-top: 30rpx;

    .location-info {
      display: flex;
      align-items: center;
      .location-text {
        color: #fff;
        font-size: 28rpx;
        margin-left: 10rpx;
      }
    }

    .view-switch {
      display: flex;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 8rpx;
      // padding: 4rpx;
      border: 1px solid #fff;
      border-radius: 30px;
      overflow: hidden;
      .switch-item {
        padding: 8rpx 32rpx;
        color: #fff;
        font-size: 28rpx;
        position: relative;
        opacity: 0.6;
        // border-radius: 6rpx;

        &.active {
          background: #fff;
          color: #fb0;
          font-weight: 500;
          opacity: 1;
        }
      }
    }
  }

  .date-picker {
    display: flex;
    align-items: center;
    justify-content: center;

    .arrow-btn {
      display: flex;
      align-items: center;
      padding: 4rpx 20rpx;
      // background: rgba(255, 255, 255, 0.2);
      border-radius: 24rpx;
      margin: 0 30rpx;
      .arrow-icon {
        display: inline-block;
        width: 30rpx;
        height: 30rpx;
        background-size: cover;
        background-repeat: no-repeat;
        &.arrow-left {
          background-image: url("https://tg-prod.oss-cn-beijing.aliyuncs.com/9bff469f-5c01-4ba7-be9f-9b2f280b66c7.png");
        }
        &.arrow-right {
          background-image: url("https://tg-prod.oss-cn-beijing.aliyuncs.com/5d2e2762-e088-4197-a415-a6dd21537d37.png");
        }
      }
      text {
        color: #fff;
        font-size: 28rpx;
      }
    }

    .date-text {
      color: #fb0;
      font-size: 28rpx;
      height: 48rpx;
      // width: 136rpx;
      background-color: #fff;
      border-radius: 12rpx;
      line-height: 48rpx;
      text-align: center;
      padding: 0 16rpx;
      font-weight: 500;
    }
  }
}

.schedule-content {
  // padding: 20rpx;
  background: #ffc107;
  overflow: hidden;
}
</style>
