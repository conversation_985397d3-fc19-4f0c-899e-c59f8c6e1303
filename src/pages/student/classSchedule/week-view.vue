<template>
  <view class="week-view" @tap="hidePopover">
    <view class="schedule-header-week">
      <view class="month">
        <span class="month-text">{{
          weekDays.length > 0 ? weekDays[0].month : ""
        }}</span>
        月
      </view>
      <view class="weekdays">
        <view class="weekday-item" v-for="day in weekDays" :key="day.date">
          <text class="weekday">{{ day.weekDay }}</text>
          <text class="date">{{ day.date }}</text>
        </view>
      </view>
    </view>

    <!-- 添加课程状态图例 -->
    <view class="status-legend">
      <view class="legend-item">
        <view class="legend-dot waiting"></view>
        <text class="legend-text">已出勤</text>
      </view>
      <view class="legend-item">
        <view class="legend-dot in-progress"></view>
        <text class="legend-text">未出勤</text>
      </view>
      <view v-if="billShow" class="legend-item">
        <view class="legend-icon billable"></view>
        <text class="legend-text">已计费</text>
      </view>
      <view v-if="billShow" class="legend-item">
        <view class="legend-icon unbillable"></view>
        <text class="legend-text">未计费</text>
      </view>
    </view>

    <!-- 课程详情气泡框 -->
    <view
      v-if="showPopover"
      class="course-popover"
      :class="[
        popoverPosition.placement,
        { leaving: isLeaving },
        selectedCourse.status
      ]"
      :style="{
        left: popoverPosition.left + 'px',
        top: popoverPosition.top + 'px'
      }"
    >
      <view class="popover-content">
        <view class="course-title">{{ selectedCourse.name }}</view>
        <view class="course-info">
          <text class="info-label">教室：</text>
          <text class="info-value">{{ selectedCourse.classroom }}</text>
        </view>
        <view class="course-info">
          <view class="info-label">老师：</view>
          <view class="info-value"
            ><span>{{ selectedCourse.teacher }}</span
            ><span class="info-tag" :class="[selectedCourse.status]">{{
              selectedCourse.is_attendance === "YES" ? "已出勤" : "未出勤"
            }}</span></view
          >
        </view>
        <view class="course-info">
          <view class="info-label">时间：</view>
          <view class="info-value"
            ><span>{{ selectedCourse.time }}</span
            ><span
              v-if="billShow"
              class="info-tag"
              :class="[selectedCourse.status]"
              >{{
                selectedCourse.is_billable === "YES" ? "已计费" : "未计费"
              }}</span
            ></view
          >
        </view>
        <!-- <view class="course-info">
          <text class="info-label">状态：</text>
          <text class="info-value">{{ selectedCourse.statusText }}</text>
        </view> -->
      </view>
      <view
        class="popover-arrow"
        :class="[selectedCourse.status]"
        :style="{
          left: popoverPosition.arrowPosition
        }"
      >
      </view>
    </view>

    <scroll-view
      :style="{ height: scrollViewHeight + 'px' }"
      class="schedule-body"
      scroll-y
      scroll-with-animation
    >
      <view class="scroll-content">
        <!-- 上午 -->
        <view class="time-row">
          <view class="time-label morning">
            <text class="period">上午</text>
          </view>
          <view class="day-cells">
            <view class="day-cell" v-for="day in weekDays" :key="day.date">
              <view
                class="course-card"
                v-for="course in getMorningCourses(day.date)"
                :key="course.id"
                :id="course.id"
                :class="[course.status]"
                @tap.stop="showCourseDetail($event, course)"
              >
                <image
                  v-if="billShow"
                  width="26rpx"
                  height="26rpx"
                  :src="
                    course.is_billable === 'YES'
                      ? 'https://tg-prod.oss-cn-beijing.aliyuncs.com/32c9ec6c-98fa-44e7-8285-00c910f5e105.png'
                      : 'https://tg-prod.oss-cn-beijing.aliyuncs.com/22aed7ec-9202-46ed-904a-9a9db94748a5.png'
                  "
                  mode="widthFix"
                  class="course-card-icon"
                ></image>
                <text class="time-label">{{ course.startTime }}</text>
                <text class="course-name">{{ course.name }}</text>
              </view>
            </view>
          </view>
        </view>
        <view class="divider"></view>
        <!-- 下午 -->
        <view class="time-row">
          <view class="time-label afternoon">
            <text class="period">下午</text>
          </view>
          <view class="day-cells">
            <view class="day-cell" v-for="day in weekDays" :key="day.date">
              <view
                class="course-card"
                v-for="course in getAfternoonCourses(day.date)"
                :key="course.id"
                :id="course.id"
                :class="[course.status]"
                @tap.stop="showCourseDetail($event, course)"
              >
                <image
                  v-if="billShow"
                  width="26rpx"
                  height="26rpx"
                  :src="
                    course.is_billable === 'YES'
                      ? 'https://tg-prod.oss-cn-beijing.aliyuncs.com/32c9ec6c-98fa-44e7-8285-00c910f5e105.png'
                      : 'https://tg-prod.oss-cn-beijing.aliyuncs.com/22aed7ec-9202-46ed-904a-9a9db94748a5.png'
                  "
                  mode="widthFix"
                  class="course-card-icon"
                ></image>
                <text class="time-label">{{ course.startTime }}</text>
                <text class="course-name">{{ course.name }}</text>
              </view>
            </view>
          </view>
        </view>
        <view class="divider"></view>
        <!-- 晚上 -->
        <view class="time-row">
          <view class="time-label evening">
            <text class="period">晚上</text>
          </view>
          <view class="day-cells">
            <view class="day-cell" v-for="day in weekDays" :key="day.date">
              <view
                class="course-card"
                v-for="course in getEveningCourses(day.date)"
                :key="course.id"
                :id="course.id"
                :class="[course.status]"
                @tap.stop="showCourseDetail($event, course)"
              >
                <image
                  v-if="billShow"
                  width="26rpx"
                  height="26rpx"
                  :src="
                    course.is_billable === 'YES'
                      ? 'https://tg-prod.oss-cn-beijing.aliyuncs.com/32c9ec6c-98fa-44e7-8285-00c910f5e105.png'
                      : 'https://tg-prod.oss-cn-beijing.aliyuncs.com/22aed7ec-9202-46ed-904a-9a9db94748a5.png'
                  "
                  mode="widthFix"
                  class="course-card-icon"
                ></image>
                <text class="time-label">{{ course.startTime }}</text>
                <text class="course-name">{{ course.name }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import { getSchedulingList } from "@/services/student/classSchedule";
import {
  getWeekDays,
  getTimeRange,
  parseTimeFromISO,
  formatDate
} from "@/utils/date";

export default {
  name: "WeekView",
  data() {
    return {
      weekDays: [],
      courses: [],
      schedulingList: [],
      isLoading: false,

      showPopover: false,
      selectedCourse: null,
      popoverPosition: {
        left: 0,
        top: 0
      },
      isLeaving: false,
      statusBarHeight: 0,
      // scrollViewHeight: 0,
      scheduleHeaderWeekHeight: 0
    };
  },
  props: {
    scheduleHeaderHeight: {
      type: Number,
      default: 0
    },
    date: {
      type: [Date, String],
      default: () => new Date()
    },
    billShow: {
      type: Boolean,
      default: false
    },
    open_from: {
      type: String,
      default: ""
    },
    student_id: {
      type: String,
      default: ""
    }
  },
  computed: {
    scrollViewHeight() {
      return (
        uni.getSystemInfoSync().windowHeight -
        this.statusBarHeight -
        44 -
        this.scheduleHeaderHeight -
        this.scheduleHeaderWeekHeight -
        30
      );
    }
  },

  mounted() {
    // 获取状态栏高度
    const statusBarHeight = uni.getSystemInfoSync().statusBarHeight;
    this.statusBarHeight = statusBarHeight;
    // 获取.schedule-header-week高度
    const scheduleHeaderWeekHeight = uni
      .createSelectorQuery()
      .in(this)
      .select(".schedule-header-week");

    scheduleHeaderWeekHeight
      .boundingClientRect((data) => {
        this.scheduleHeaderWeekHeight = data.height;
      })
      .exec();
    console.log("this.student_id", this.student_id);
    // 初始化周数据
    this.initWeekDays();
    // 获取课程数据
    this.getWeekCourses();
  },
  watch: {
    date: {
      handler(newDate) {
        // 当父组件传入的日期变化时，重新初始化周数据
        this.initWeekDays();
        // 重新获取课程数据
        this.getWeekCourses();
        this.showPopover = false;
      },
      immediate: true
    }
  },
  methods: {
    // 初始化周数据
    initWeekDays() {
      // 确保this.date是Date对象
      const dateObj =
        this.date instanceof Date ? this.date : new Date(this.date);
      // 使用父组件传入的日期获取周数据
      this.weekDays = getWeekDays(dateObj);
    },

    // 获取一周的课程数据
    async getWeekCourses() {
      const student_id =
        this.open_from === "notification"
          ? this.student_id
          : uni.getStorageSync("curStudentInfo")?.student_id;
      if (!student_id) {
        return;
      }

      // 获取本周的开始日期和结束日期
      const startDate = this.weekDays[0].fullDate;
      const endDate = this.weekDays[6].fullDate;

      try {
        this.isLoading = true;
        uni.showLoading({
          title: "加载中..."
        });
        const { code, data } = await getSchedulingList({
          student_id,
          start_time: startDate,
          end_time: endDate
        });
        this.isLoading = false;
        uni.hideLoading();

        if (code === 0) {
          this.schedulingList = data || [];
          // 处理课程数据
          this.processCourseData();
        }
      } catch (error) {
        this.isLoading = false;
        uni.hideLoading();
        uni.showToast({
          title: "获取课程数据失败",
          icon: "none"
        });
        console.error("获取周课程数据失败", error);
      }
    },

    // 处理课程数据
    processCourseData() {
      this.courses = [];

      this.schedulingList.forEach((item) => {
        // 根据状态设置对应的状态文本和样式类名
        let status = "";
        switch (item.is_attendance) {
          // case "is_cancelled":
          //   status = "finished";
          //   break;
          case "NO":
            status = "in-progress";
            break;
          case "YES":
            status = "waiting";
            break;
          default:
            status = "";
        }

        // 解析日期和时间
        const courseDate = formatDate(item.start_time);
        const startTime = parseTimeFromISO(item.start_time);
        const hour = parseInt(startTime.split(":")[0]);

        // 确定课程时间段
        let period = "";
        if (hour < 12) {
          period = "morning";
        } else if (hour < 18) {
          period = "afternoon";
        } else {
          period = "evening";
        }

        // 找到对应的日期在weekDays中的日期值
        const dayObj = this.weekDays.find((day) => day.fullDate === courseDate);
        if (!dayObj) return;

        this.courses.push({
          id: item.scheduling_id,
          date: dayObj.date,
          startTime,
          name: item.course_name,
          time: getTimeRange(item.start_time, item.end_time),
          status,
          statusText: item.status_chn,
          period,
          classroom:
            item.school_room_name ||
            (item.scheduling_form === "online" ? "线上" : "暂无教室"),
          teacher: item.teacher_name,
          is_attendance: item.is_attendance,
          is_billable: item.is_billable
        });
      });
    },

    // 获取上午的课程
    getMorningCourses(date) {
      return this.courses
        .filter((course) => course.date === date && course.period === "morning")
        .sort((a, b) => {
          // 按时间排序
          return a.startTime.localeCompare(b.startTime);
        });
    },

    // 获取下午的课程
    getAfternoonCourses(date) {
      return this.courses
        .filter(
          (course) => course.date === date && course.period === "afternoon"
        )
        .sort((a, b) => {
          // 按时间排序
          return a.startTime.localeCompare(b.startTime);
        });
    },

    // 获取晚上的课程
    getEveningCourses(date) {
      return this.courses
        .filter((course) => course.date === date && course.period === "evening")
        .sort((a, b) => {
          // 按时间排序
          return a.startTime.localeCompare(b.startTime);
        });
    },

    getCourse(date, time) {
      return (
        this.courses.find(
          (course) => course.date === date && course.startTime === time
        ) || {}
      );
    },
    getCourseStatus(date, time) {
      const course = this.getCourse(date, time);
      return course.status || "";
    },
    showCourseDetail(event, course) {
      this.showPopover = false;
      const query = uni.createSelectorQuery().in(this);
      query
        .select(`#${course.id}`)
        .boundingClientRect((data) => {
          if (data) {
            // 获取系统信息以获取屏幕宽高
            const systemInfo = uni.getSystemInfoSync();
            const screenWidth = systemInfo.windowWidth;
            const screenHeight = systemInfo.windowHeight;

            // 气泡框的尺寸（px）
            const popoverWidth = 400 * (screenWidth / 750); // 将rpx转换为px
            const popoverHeight = 200; // 预估高度，可以根据实际内容调整

            // 计算气泡框的位置
            let left = data.left + data.width / 2;
            let top = data.top;
            let arrowPosition = "50%";

            // 水平方向位置调整
            if (left - popoverWidth / 2 < 10) {
              // 太靠左
              left = 10 + popoverWidth / 2;
              arrowPosition = `${data.left + data.width / 2 - 10}px`;
            } else if (left + popoverWidth / 2 > screenWidth - 10) {
              // 太靠右
              left = screenWidth - 10 - popoverWidth / 2;
              arrowPosition = `${
                popoverWidth - (screenWidth - (data.left + data.width / 2) + 10)
              }px`;
            }

            // 垂直方向位置调整
            const spaceAbove = data.top;
            const spaceBelow = screenHeight - (data.top + data.height);

            let placement = "top";
            if (
              spaceAbove < popoverHeight + 20 &&
              spaceBelow > popoverHeight + 20
            ) {
              // 如果上方空间不足，但下方空间足够，则显示在下方
              placement = "bottom";
              top = data.top + data.height + 10;
            } else {
              // 默认显示在上方
              top = data.top - 10;
            }

            this.popoverPosition = {
              left,
              top,
              placement,
              arrowPosition,
              arrowOffset: "-50%"
            };

            this.selectedCourse = course;
            this.showPopover = true;
          }
        })
        .exec();
    },

    hidePopover() {
      if (this.showPopover && !this.isLeaving) {
        this.isLeaving = true;
        setTimeout(() => {
          this.showPopover = false;
          this.isLeaving = false;
        }, 150); // 动画持续时间
      }
    }
  },
  beforeUnmount() {
    document.removeEventListener("click", this.hidePopover);
  }
};
</script>

<style lang="scss" scoped>
.week-view {
  display: flex;
  flex-direction: column;
  background: #fff;
  position: relative;
  border-top-left-radius: 30rpx;
  border-top-right-radius: 30rpx;
  height: 100%; /* 修改为100% */
  overflow: hidden; /* 防止整体溢出 */
  padding-bottom: env(safe-area-inset-bottom);

  .schedule-header-week {
    display: flex;
    align-items: center;
    position: sticky;
    top: 0;
    z-index: 10;
    background: #fff;
    padding-top: 20rpx;
    flex-shrink: 0; /* 防止头部被压缩 */

    .month {
      width: 30rpx;
      font-size: 24rpx;
      font-weight: bold;
      color: #fb0;
      text-align: center;
      margin-left: 20rpx;
      margin-top: 8rpx;
      .month-text {
        font-size: 30rpx;
      }
    }

    .weekdays {
      display: flex;
      flex: 1;

      .weekday-item {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        .weekday {
          font-size: 30rpx;
          color: #333;
          margin-bottom: 14rpx;
        }

        .date {
          font-size: 26rpx;
          color: #999;
        }
      }
    }
  }
  .divider {
    height: 1px;
    width: 620rpx;
    margin: 6rpx auto;
    // 添加虚线
    border-bottom: 1px dashed #dfdfdf;
  }
  /* 添加课程状态图例样式 */
  .status-legend {
    display: flex;
    justify-content: space-around;
    align-items: center;
    background: #fff;
    padding: 16rpx 90rpx;
    flex-shrink: 0; /* 防止图例被压缩 */

    .legend-item {
      display: flex;
      align-items: center;
      margin: 0 20rpx;

      .legend-dot {
        width: 14rpx;
        height: 20rpx;
        border-radius: 4rpx;
        margin-right: 8rpx;

        &.waiting {
          background: #ff6da3;
        }

        &.in-progress {
          background: #ff9a3c;
        }

        // &.finished {
        //   background: #cecece;
        // }
      }
      .legend-icon {
        width: 26rpx;
        height: 26rpx;
        background-repeat: no-repeat;
        background-size: 100% 100%;
        margin-right: 8rpx;
        &.billable {
          background-image: url("https://tg-prod.oss-cn-beijing.aliyuncs.com/32c9ec6c-98fa-44e7-8285-00c910f5e105.png");
        }
        &.unbillable {
          background-image: url("https://tg-prod.oss-cn-beijing.aliyuncs.com/22aed7ec-9202-46ed-904a-9a9db94748a5.png");
        }
      }

      .legend-text {
        font-size: 24rpx;
        color: #666;
      }
    }
  }

  .schedule-body {
    // flex: 1;
    // overflow-y: auto; /* 修改为auto */
    // -webkit-overflow-scrolling: touch; /* 添加弹性滚动 */
    // padding-bottom: 40rpx;

    .scroll-content {
      display: flex;
      flex-direction: column;
      min-height: 100%;
      padding-bottom: 20rpx;
    }

    .time-row {
      display: flex;
      flex: 1;
      // height: 340rpx;
      flex-shrink: 0; /* 防止被压缩 */
      width: 100%;
      padding-top: 8px;
      padding-bottom: 4rpx;
      .time-label {
        width: 58rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f8f8f8;
        margin-right: 10rpx;
        border-radius: 4rpx;
        .period {
          font-size: 30rpx;
          writing-mode: vertical-lr;
          letter-spacing: 20rpx;
        }
        &.morning {
          background: rgba(123, 111, 255, 0.1);
          .period {
            color: #867bff;
          }
        }
        &.afternoon {
          background: rgba(233, 111, 255, 0.1);
          .period {
            color: #e96fff;
          }
        }
        &.evening {
          background: rgba(235, 227, 74, 0.1);
          .period {
            color: #cec963;
          }
        }
      }

      .day-cells {
        flex: 1;
        display: flex;

        .day-cell {
          flex: 1;
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 16rpx;
          .course-card {
            position: relative;
            width: 90rpx;
            padding: 8rpx;
            border-radius: 12rpx;
            display: flex;
            flex: 1;
            flex-direction: column;
            justify-content: center;
            // min-height: 80rpx;
            // margin: 6rpx 0;
            cursor: pointer;
            .course-card-icon {
              position: absolute;
              top: -10rpx;
              left: 0rpx;
              width: 26rpx;
              height: 26rpx;
              background-repeat: no-repeat;
              background-size: 100% 100%;
            }
            &.waiting {
              background-color: #ff6da3;
              color: #fff;
            }

            &.in-progress {
              background-color: #ff9a3c;
              color: #fff;
            }

            &.finished {
              background-color: #cecece;
              color: #fff;
            }

            .time-label {
              font-size: 24rpx;
              // margin-bottom: 4rpx;
              display: block;
              width: auto;
              background: none;
              text-align: center;
              margin-right: 0;
            }

            .course-name {
              font-size: 24rpx;
              line-height: 1;
              overflow: hidden;
              text-align: center;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
          }
        }
      }
    }
  }
}

.course-popover {
  position: fixed;
  z-index: 999;
  border-radius: 12rpx;
  // border: 1px solid #ff6da3;
  // background: #ffe9f1;
  // box-shadow: -3rpx 2rpx 10rpx #f789b2;
  padding: 16rpx;
  width: 400rpx;
  opacity: 0;
  transform-origin: 50% 100%;
  animation: popoverEnter 0.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  &.waiting {
    background: #ffe9f1;
    border: 1px solid #ff6da3;
    // box-shadow: -3rpx 2rpx 10rpx #f789b2;
  }
  &.in-progress {
    border: 1px solid #ff8e25;
    background: #ffeede;
    // box-shadow: -3rpx 2rpx 10rpx #ff9a3c;
  }
  &.finished {
    background: #f5f5f5;
    border: 1px solid #9f9f9f;
    // box-shadow: -3rpx 2rpx 10rpx #eee;
  }
  &.top {
    transform: translate(-50%, -100%) scale(0.8);
    transform-origin: 50% 100%;

    .popover-arrow {
      position: absolute;
      bottom: -22rpx;
      // border-left: 15rpx solid transparent;
      // border-right: 15rpx solid transparent;
      // // border-top: 20rpx solid #ff6da3;
      // filter: drop-shadow(0 4rpx 4rpx rgba(0, 0, 0, 0.1));

      &.waiting {
        // background-color: #ff6da3;
        background-image: url("~@/static/arrow1.png");
      }
      &.in-progress {
        background-image: url("~@/static/arrow2.png");
      }
      // &.finished {
      //   background-image: url("~@/static/arraw3.png");
      // }
    }
  }

  &.bottom {
    transform: translate(-50%, 0) scale(0.8);
    transform-origin: 50% 0%;

    .popover-arrow {
      position: absolute;
      top: -20rpx;
      // border-left: 15rpx solid transparent;
      // border-right: 15rpx solid transparent;
      // // border-bottom: 20rpx solid #ff6da3;
      // filter: drop-shadow(0 -4rpx 4rpx rgba(0, 0, 0, 0.1));
      &.waiting {
        border-bottom: 20rpx solid #ff6da3;
      }
      &.in-progress {
        border-bottom: 20rpx solid #ff8e25;
      }
      &.finished {
        border-bottom: 20rpx solid #cecece;
      }
    }
  }

  .popover-arrow {
    width: 16rpx;
    height: 28rpx;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
    transform: rotate(-90deg);
  }

  .popover-content {
    .course-title {
      font-size: 26rpx;
      // font-weight: 500;
      color: #333;
      // margin-bottom: 16rpx;
      padding-bottom: 8rpx;
      // border-bottom: 1rpx solid #f5f5f5;
    }

    .course-info {
      display: flex;
      font-size: 26rpx;
      margin-bottom: 8rpx;
      //   align-items: center;
      &:last-child {
        margin-bottom: 0;
      }
      .info-label {
        color: #666;
        // width: 100rpx;
        font-size: 24rpx;
      }

      .info-value {
        color: #666;
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .info-tag {
          border-radius: 6rpx;
          background: #fff;
          width: 80rpx;
          height: 34rpx;
          text-align: center;
          line-height: 34rpx;
          font-size: 22rpx;
          &.waiting {
            color: #ff6da3;
          }
          &.in-progress {
            color: #ff9a3c;
          }
          &.finished {
            color: #cecece;
          }
        }
      }
    }
  }
}

@keyframes popoverEnter {
  from {
    opacity: 0;
    transform: translate(-50%, -100%) scale(0.8);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -100%) scale(1);
  }
}

// 为底部显示的气泡添加单独的动画
@keyframes popoverEnterBottom {
  from {
    opacity: 0;
    transform: translate(-50%, 0) scale(0.8);
  }
  to {
    opacity: 1;
    transform: translate(-50%, 0) scale(1);
  }
}

// 添加动画类
.course-popover.top {
  animation-name: popoverEnter;
}

.course-popover.bottom {
  animation-name: popoverEnterBottom;
}

// 添加退出动画
.course-popover.leaving {
  animation: popoverLeave 0.15s cubic-bezier(0.4, 0, 1, 1) forwards;
}

@keyframes popoverLeave {
  from {
    opacity: 1;
    transform: translate(-50%, -100%) scale(1);
  }
  to {
    opacity: 0;
    transform: translate(-50%, -100%) scale(0.8);
  }
}
</style>
