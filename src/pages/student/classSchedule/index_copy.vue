<template>
  <div class="class-schedule-page">
    <u-navbar
      title="课表"
      bgColor="transparent"
      :titleStyle="{
        color: '#fff'
      }"
    >
    </u-navbar>
    <div class="header">
      <!-- <div class="classroom">教室5</div> -->
      <div class="time-picker">
        <!--  :customStyle="prevCustomStyle"  -->
        <u-button
          size="mini"
          :customStyle="{ fontSize: '12px' }"
          type="primary"
          class="prev"
          @click="prev"
          text="上周"
        ></u-button>
        <span class="date-text" @click="openCalendarModal"
          >{{ dayRange[0] }}至{{ dayRange[1]
          }}<img
            src="https://tg-prod.oss-cn-beijing.aliyuncs.com/511fa345-63fd-4b2e-816c-c27f1ed617a1.png"
            alt=""
          />
        </span>
        <u-button
          size="mini"
          :customStyle="{ fontSize: '12px' }"
          type="primary"
          class="next"
          @click="next"
          text="下周"
        ></u-button>
      </div>
    </div>
    <div class="main">
      <div class="placeholder"></div>
      <div class="main-content">
        <div class="main-wrap">
          <img class="fixed-img left" :src="fixedImage" alt="" />
          <img class="fixed-img right" :src="fixedImage" alt="" />
          <div class="month-name">{{ weekInfo.month }}月</div>
          <div class="main-content_wrap">
            <div class="day-picker">
              <!-- {{ value1 }} -->
              <div class="day-list">
                <div
                  v-for="item in weekInfo.weekdays"
                  :key="item.week"
                  :class="['day-item', curWeek === item.week ? 'active' : '']"
                  @click="handleDateChange(item)"
                >
                  <div :class="{ gray: !item.active }" class="week-name">
                    {{ item.week }}
                  </div>
                  <div :class="{ gray: !item.active }" class="date-name">
                    {{ item.date }}
                  </div>
                </div>
              </div>
            </div>
            <div class="calss-schedule-list" v-if="schedulingList.length">
              <div
                class="calss-schedule-item"
                v-for="item in schedulingList"
                :key="item.course_id"
              >
                <div class="info">
                  <div class="dot"></div>
                  <div class="label">班级名称：</div>
                  <div class="value">{{ item.classroom_alias_name }}</div>
                </div>
                <div class="info">
                  <div class="dot"></div>
                  <div class="label">上课形式：</div>
                  <div class="value">{{ item.scheduling_form_chn }}</div>
                </div>
                <div class="info">
                  <div class="dot"></div>
                  <div class="label">任课老师：</div>
                  <div class="value">{{ item.teacher_name }}</div>
                </div>
                <div class="info">
                  <div class="dot"></div>
                  <div class="label">上课教室：</div>
                  <div class="value">{{ item.school_room_name }}</div>
                </div>
                <div class="info">
                  <div class="dot"></div>
                  <div class="label">上课时间：</div>
                  <div class="value">
                    {{
                      $ljsPublic.date.formatTime(item.start_time, "{h}:{i}")
                    }}-{{
                      $ljsPublic.date.formatTime(item.end_time, "{h}:{i}")
                    }}
                  </div>
                </div>
                <div class="info">
                  <div class="dot"></div>
                  <div class="label">上课状态：</div>
                  <div class="value">{{ item.status_chn }}</div>
                </div>
              </div>
            </div>
            <div v-else style="height: calc(100% - 44rpx - 48rpx)">
              <u-empty style="height: 100%" text="暂无课表数据~"> </u-empty>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- <u-calendar
      showLunar
      :show="showCalendar"
      :monthNum="6"
      :minDate="minDate"
      @close="close"
      @confirm="confirm">
    </u-calendar> -->
    <u-datetime-picker
      :show="showCalendar"
      ref="datetimePicker"
      @cancel="close"
      @confirm="(e) => beforeConfirm(e, e.value)"
      v-model="value1"
      mode="date"
    ></u-datetime-picker>
    <!-- <TabBar
      ref="tabbar"
      :key="tabbarKey"
      tabField="mini_course_time_table"
    ></TabBar> -->
  </div>
</template>

<script>
// import TabBar from "../components/TabBar.vue";
import { getSchedulingList } from "@/services/student/classSchedule";
export default {
  // components: { TabBar },
  name: "ClassSchedule",
  data() {
    return {
      minDate: "",
      fixedImage:
        "https://tg-prod.oss-cn-beijing.aliyuncs.com/c7e4ae97-6f98-4d0d-811d-018984f1e538.png",
      showCalendar: false,
      curStudentInfo: {},
      tabbarKey: 0,
      checkedTime: 0,
      // prevCustomStyle: {
      //   background: "#DEE1E0",
      //   border: "1px solid #DEE1E0",
      //   fontWeight: "500",
      //   fontSize: "28rpx",
      //   color: "#8492A6"
      // },
      value1: 0,
      value2: 0,
      session: {},
      dayRange: [],
      curWeek: "周一",
      weekMultiple: 1,
      weekInfo: {
        weekdays: [],
        month: ""
      },
      schedulingList: []
    };
  },
  computed: {},
  methods: {
    formatter(type, value) {
      if (type === "year") {
        return `${value}年`;
      }
      if (type === "month") {
        return `${value}月`;
      }
      if (type === "day") {
        return `${value}日`;
      }
      return value;
    },
    openCalendarModal() {
      this.showCalendar = true;
    },
    getInitWeekRange(date) {
      const timeFormat = uni.$u.timeFormat;
      const weekInfo = this.$ljsPublic.date.circumferenceDay(
        "{m}/{d}",
        timeFormat(new Date(date).getTime(), "yyyy-mm-dd")
      );
      this.weekInfo = weekInfo;
      const start = timeFormat(weekInfo.weekdays[0].fullDate, "yyyy-mm-dd");
      const end = timeFormat(
        weekInfo.weekdays[weekInfo.weekdays.length - 1].fullDate,
        "yyyy-mm-dd"
      );
      // const formatTime = (date) => {
      //   return this.$ljsPublic.date.formatTime(
      //     new Date(date).getTime(),
      //     "{y}-{m}-{d}"
      //   );
      // };
      return [start, end];
    },
    async beforeConfirm(e, timestamp = new Date().getTime()) {
      console.log("e :>> ", e);
      console.log("timestamp :>> ", timestamp);
      await this.getHeaderCfg(timestamp);
      this.confirm(this.firstSelect, timestamp, "picker");
    },
    confirm(e, timestamp = new Date().getTime(), type) {
      const timeFormat = uni.$u.timeFormat;
      const value = e.value || e;
      this.checkedTime = timestamp;
      if (type === "picker") {
        this.weekMultiple = 0;
        this.value2 = e;
      }
      this.value1 = timestamp;
      this.$refs.datetimePicker.innerValue = timestamp;
      // console.log(value, "value", time, "time", new Date(time));
      // this.weekInfo = this.$ljsPublic.date.circumferenceDay(
      //   "{m}/{d}",
      //   timeFormat(value, "yyyy-mm-dd")
      // );
      const start = this.weekInfo.weekdays[0].fullDate;
      const end =
        this.weekInfo.weekdays[this.weekInfo.weekdays.length - 1].fullDate;
      const formatTime = (date) => {
        return this.$ljsPublic.date.formatTime(
          new Date(date).getTime(),
          "{m}月{d}日"
        );
      };
      this.dayRange = [formatTime(start), formatTime(end)];

      this.curWeek = this.$ljsPublic.date.getWeekday(
        timeFormat(value, "yyyy-mm-dd")
      );
      this.getSchedulingList(timeFormat(value, "yyyy-mm-dd"));
      this.showCalendar = false;
    },
    close() {
      this.showCalendar = false;
    },
    getLastTwoMonths() {
      const now = new Date();
      const currentMonth = now.getMonth(); // 获取当前月份，从0开始
      const lastMonth = new Date(now.getFullYear(), currentMonth, 0); // 获取上个月的最后一天
      const penultimateMonthStart = new Date(
        now.getFullYear(),
        currentMonth - 2,
        1
      ); // 获取倒数第二个月的第一天
      const penultimateMonthEnd = new Date(now.getFullYear(), currentMonth, 0); // 获取倒数第二个月的最后一天

      return {
        lastMonth,
        penultimateMonth: {
          start: penultimateMonthStart,
          end: penultimateMonthEnd
        }
      };
    },
    handleDateChange(item) {
      this.curWeek = item.week;
      this.getSchedulingList(uni.$u.timeFormat(item.fullDate, "yyyy-mm-dd"));
    },
    async prev() {
      this.weekMultiple--;
      const getMonday = this.getMonday("prve");
      const week = this.$ljsPublic.date.formatTime(
        new Date(getMonday).getTime(),
        "{y}-{m}-{d}"
      );
      console.log("++++++ ", week);
      console.log("------ ", Number(getMonday));
      // this.confirm(week, Number(getMonday));
      await this.getHeaderCfg(week);
      this.confirm(this.firstSelect);
    },
    async next() {
      this.weekMultiple++;
      const getMonday = this.getMonday("next");
      console.log("getMonday", getMonday);
      const week = this.$ljsPublic.date.formatTime(
        new Date(getMonday).getTime(),
        "{y}-{m}-{d}"
      );
      console.log("week", week);
      // this.confirm(week, Number(getMonday));
      await this.getHeaderCfg(week);
      this.confirm(this.firstSelect);
    },
    getMonday(type) {
      // this.value2
      const today = this.value2 !== 0 ? new Date(this.value2) : new Date();
      // const dayOfWeek = today.getDay(); // 0 表示星期日，1 到 6 表示星期一到星期六
      // const daysUntilMonday = dayOfWeek === 0 ? 1 : 8 - dayOfWeek; // 距离下周一还有几天
      // const daynum = this.weekMultiple === 1 ? daysUntilMonday : (this.weekMultiple * 8) - daysUntilMonday - this.weekMultiple + 1;
      const nextMonday = new Date(
        today.getTime() + this.weekMultiple * 7 * 24 * 60 * 60 * 1000
      );
      return nextMonday;
    },
    async getSchedulingList(start_time) {
      const { code, data } = await getSchedulingList({
        student_id: this.curStudentInfo.student_id,
        start_time,
        end_time: start_time
      });
      if (code === 0) {
        this.schedulingList = data || [];
      }
    },
    async getHeaderCfg(date) {
      // const firstSelect = "";
      // const dateActiveFlag = [];
      const daterange = this.getInitWeekRange(date);
      console.log("daterange :>> ", daterange);

      const resp = await getSchedulingList({
        student_id: this.curStudentInfo.student_id,
        start_time: daterange[0],
        end_time: daterange[1]
      });
      const { data, code } = resp;
      if (code === 0) {
        if (data) {
          const dates = data.map((item) =>
            uni.$u.timeFormat(item.start_time, "mm/dd")
          );
          console.log("dates :>> ", dates);
          this.weekInfo.weekdays.map((item) => {
            item.active = dates.includes(item.date);
          });
          const index = this.weekInfo.weekdays.findIndex((item) => item.active);
          if (index >= 0) {
            this.firstSelect = uni.$u.timeFormat(
              this.weekInfo.weekdays[index].fullDate,
              "yyyy-mm-dd"
            );
          }

          console.log("this.firstSelect :>> ", this.firstSelect);
        } else {
          this.firstSelect = daterange[0];
          // this.weekInfo.weekdays.map((item) => {
          //   item.active = false;
          // });
        }
      } else {
        this.firstSelect = daterange[0];
        // this.weekInfo.weekdays.map((item) => {
        //   item.active = false;
        // });
      }
      console.log("resp :>> ", resp);
      // .then((res) => {
      //   const { code, data } = res;
      //   if (code === 0) {
      //     console.log("data :>> ", data);
      //     // this.schedulingList = data || [];
      //     if (data) {
      //     } else {
      //       this.confirm(daterange[0]);
      //     }
      //   }
      // });
    }
  },
  watch: {},

  // 页面周期函数--监听页面加载
  onLoad() {},
  // 页面周期函数--监听页面初次渲染完成
  onReady() {
    this.$refs.datetimePicker.setFormatter(this.formatter);
  },
  // 页面周期函数--监听页面显示(not-nvue)
  async onShow() {
    this.tabbarKey++;
    this.weekMultiple = 0;
    this.value2 = 0;
    this.curStudentInfo = uni.getStorageSync("curStudentInfo");
    this.session = uni.getStorageSync("session");
    this.value1 = new Date().getTime();
    const curDay = this.$ljsPublic.date.formatTime(
      new Date().getTime(),
      "{y}-{m}-{d}"
    );
    // console.log("this. :>> ", this.getInitWeekRange());
    // this.confirm(curDay);
    // this.getSchedulingList("2024-09-30 00:00:00", "2024-10-06 23:59:59");
    this.$refs.tabbar.setMenuList();
    // const { lastMonth, penultimateMonth } = this.getLastTwoMonths();
    // this.minDate = this.$ljsPublic.date.formatTime(penultimateMonth.start, "{y}-{m}-{d}");
    // console.log("上个月的最后一天:", lastMonth.toLocaleDateString());
    // console.log("倒数第二个月的开始日期:", penultimateMonth.start);
    // console.log("倒数第二个月的结束日期:", penultimateMonth.end.toLocaleDateString());
    const cfgData = await this.getHeaderCfg(curDay);
    console.log("cfgData :>> ", cfgData);
    this.confirm(this.firstSelect);
  },
  onLaunch: function () {
    uni.hideTabBar();
  },
  // 页面周期函数--监听页面隐藏
  onHide() {},
  // 页面周期函数--监听页面卸载
  onUnload() {}
  // 页面处理函数--监听用户下拉动作
  // onPullDownRefresh() { uni.stopPullDownRefresh(); },
  // 页面处理函数--监听用户上拉触底
  // onReachBottom() {},
  // 页面处理函数--监听页面滚动(not-nvue)
  // onPageScroll(event) {},
  // 页面处理函数--用户点击右上角分享
  // onShareAppMessage(options) {},
};
</script>

<style lang="scss" scoped>
::v-deep .u-button__text {
  font-size: 12px !important;
}
.time-picker ::v-deep .u-button {
  width: 104rpx;
  border-radius: 44rpx;
  font-weight: 500;
}
::v-deep .u-empty {
  height: 100%;
}
::v-deep .u-navbar__content__left {
  display: none !important;
}
.class-schedule-page {
  width: 100%;
  height: calc(100vh - 50px);
  background: linear-gradient(0deg, #26ceff 1.02%, #00baff 100%);
  display: flex;
  flex-direction: column;
  padding-top: 190rpx;
  .header {
    height: 80rpx;
    background: #fff;
    margin: 0 23rpx 24rpx 23rpx;
    padding: 0 24rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 25rpx;
    .time-picker {
      display: flex;
      align-items: center;
      font-size: 28rpx;
      .prev {
        background: #dee1e0;
        color: #8492a6;
      }
      .date-text {
        padding: 0 16rpx;
        color: #3d5066;
        font-size: 28rpx;
        font-weight: 500;
        display: flex;
        align-items: center;
        img {
          width: 32rpx;
          height: 32rpx;
        }
      }
    }
  }
  .main {
    height: 100%;
    .placeholder {
      width: 100%;
      height: 78rpx;
    }
    .main-content {
      border-top-right-radius: 54rpx;
      border-top-left-radius: 54rpx;
      padding: 0 23rpx;
      background: #f4f8fc;
      height: calc(100% - 78rpx);
      position: relative;
      .main-wrap {
        position: relative;
        top: -38rpx;
        display: flex;
        flex-direction: column;
        border-radius: 48rpx;
        height: calc(100% - 68rpx);
        background: linear-gradient(
          180deg,
          #fcd24c 0%,
          #f6bf26 48.95%,
          #f9c93d 100%
        );
        padding: 0 31rpx 48rpx 31rpx;
        box-shadow: 0px 5px 10px 0px #ffebaf inset,
          0px -5px 10px 0px #e39d1e inset, 0px 4px 8px 0px rgba(0, 0, 0, 0.05);
        .fixed-img {
          position: absolute;
          top: -39rpx;
          width: 48rpx;
          height: 90rpx;
          &.left {
            left: 87rpx;
          }
          &.right {
            right: 87rpx;
          }
        }
        .month-name {
          padding: 28rpx 0 20rpx 0;
          text-align: center;
          color: #fff;
          text-shadow: 0px 0px 4px #feac00;
          font-size: 32rpx;
          font-weight: 600;
          letter-spacing: 0.6px;
        }
        .main-content_wrap {
          border-radius: 36rpx;
          background: #fff;
          height: calc(100% - 46rpx - 25rpx);
          box-shadow: 0px 0px 15px 0px rgba(253, 217, 164, 0.5) inset,
            0px 3px 10px 0px rgba(255, 158, 0, 0.8);
        }
      }
    }
    .day-picker {
      // padding: 0 30rpx 0 24rpx;
      border-top-left-radius: 36rpx;
      border-top-right-radius: 36rpx;
      background: rgba(250, 236, 209, 0.6);
      display: flex;
      .month {
        color: #475669;
        font-weight: 500;
        padding-top: 12rpx;
        padding-right: 11rpx;
        text-align: center;
        .month-num {
          font-size: 28rpx;
        }
        .month-unit {
          font-size: 20rpx;
        }
      }
      .day-list {
        display: flex;
        justify-content: space-between;
        align-items: center;
        .day-item {
          padding: 11rpx 17rpx 10rpx 19rpx;
          // display: inline-block;
          // vertical-align: middle;
          white-space: nowrap;
          color: #475669;
          font-weight: 500;
          .week-name {
            font-size: 28rpx;
            &.gray {
              color: #c4c4c4;
            }
          }
          .date-name {
            font-size: 20rpx;
            &.gray {
              color: #c4c4c4;
            }
          }
          &.active {
            background: #03bcff;
            border-top-left-radius: 8rpx;
            border-top-right-radius: 8rpx;
            .week-name,
            .date-name {
              color: #fff;
            }
          }
        }
      }
    }
    .calss-schedule-list {
      padding: 0 25rpx 0 25rpx;
      height: calc(100% - 44rpx - 75rpx);
      overflow: auto;
      margin-top: 24rpx;
      .calss-schedule-item {
        padding: 34rpx 57rpx;
        border-radius: 32rpx;
        background: rgba(252, 244, 227, 0.6);
        font-size: 22rpx;
        font-weight: 500;
        margin-bottom: 23rpx;
        &:last-child {
          margin-bottom: 0;
        }
        .info {
          margin-bottom: 24rpx;
          display: flex;
          align-items: center;
          &:last-child {
            margin-bottom: 0;
          }
          .dot {
            width: 12rpx;
            height: 12rpx;
            background: rgba(254, 172, 0, 0.5);
            border-radius: 50%;
            margin-right: 16rpx;
          }
          .label {
            color: #feac00;
          }
          .value {
            color: #475669;
          }
        }
      }
    }
  }
}
</style>
