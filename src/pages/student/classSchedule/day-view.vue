<template>
  <view class="day-view" @touchstart="handlePageTouch">
    <view class="day-header">
      <view class="day-info">
        <span class="month">
          <span class="month-text">{{ currentMonth }}</span>
          月
        </span>
        <view class="date-list">
          <view
            class="date-item"
            v-for="(day, index) in weekDays"
            :key="index"
            @click="selectDay(index)"
          >
            <text class="weekday" :class="{ 'has-course': day.hasCourse }">{{
              day.weekDay
            }}</text>
            <text
              class="date"
              :class="{
                'current-day': selectedDayIndex === index
              }"
              >{{ day.isToday ? "今" : day.date }}</text
            >
          </view>
        </view>
      </view>
    </view>

    <scroll-view
      :style="{ height: scrollViewHeight + 'px' }"
      class="course-list"
      scroll-y
      scroll-with-animation
    >
      <view class="course-list-wrapper">
        <view
          class="course-item"
          v-for="(course, index) in courses"
          :key="index"
          :class="[course.status]"
        >
          <view class="course-content">
            <view class="course-header">
              <view v-if="course.classroom" class="course-tag">{{
                course.classroom
              }}</view>
              <view class="course-title text-ellipsis">
                {{ course.name }}
              </view>
              <!-- 显示课程名称气泡 - 移到外部 -->
              <view v-if="showCourseNameIndex === index" class="course-tooltip">
                <view class="tooltip-arrow"></view>
                <view class="tooltip-content">{{ course.name }}</view>
              </view>
            </view>

            <view class="course-info">
              <text style="margin-bottom: 16rpx"
                >任课老师：{{ course.teacher }}</text
              >
              <text>上课时间：{{ course.time }}</text>
            </view>
            <view class="course-action">
              <view :class="['course-status', course.status]">{{
                course.statusText
              }}</view>
            </view>
          </view>
        </view>
      </view>
      <view v-if="courses.length === 0 && !isLoading" class="no-course">
        <EmptyIcon text="暂无排课数据~" />
      </view>
    </scroll-view>
  </view>
</template>

<script>
import { getSchedulingList } from "@/services/student/classSchedule";
import EmptyIcon from "@/components/empty";
import {
  getWeekDays,
  getCurrentDate,
  getTimeRange,
  formatDate
} from "@/utils/date";

export default {
  name: "DayView",
  components: {
    EmptyIcon
  },
  data() {
    return {
      currentMonth: getCurrentDate().month,
      selectedDayIndex: 0, // 默认选中第一天，会在mounted中更新为当天
      weekDays: [],
      courses: [],
      statusBarHeight: 0,
      dayHeaderHeight: 0,
      schedulingList: [],
      isLoading: false,
      todayIndex: 0, // 当天在weekDays中的索引
      isInitializing: false, // 添加初始化标志位，避免重复调用接口
      weekCoursesLoaded: false, // 标记周课程是否已加载
      showCourseNameIndex: null, // 显示课程名称全部的索引
      isFlag: false // 标记是否已初始化过
    };
  },
  props: {
    scheduleHeaderHeight: {
      type: Number,
      default: 0
    },
    date: {
      type: [Date, String],
      default: () => new Date()
    },
    billShow: {
      type: Boolean,
      default: false
    },
    open_from: {
      type: String,
      default: ""
    },
    student_id: {
      type: String,
      default: ""
    }
  },
  computed: {
    scrollViewHeight() {
      return (
        uni.getSystemInfoSync().windowHeight -
        this.statusBarHeight -
        44 -
        this.scheduleHeaderHeight -
        this.dayHeaderHeight
      );
    }
  },
  mounted() {
    // 获取状态栏高度
    const statusBarHeight = uni.getSystemInfoSync().statusBarHeight;
    this.statusBarHeight = statusBarHeight;
    // 获取.schedule-header-week高度
    const dayHeader = uni.createSelectorQuery().in(this).select(".day-header");

    dayHeader
      .boundingClientRect((data) => {
        this.dayHeaderHeight = data.height;
      })
      .exec();
    // 初始化周数据
    this.initWeekDays();

    // 获取课程数据
    // this.getSchedulingListData();
  },

  watch: {
    date: {
      handler(newDate) {
        console.log("newDate :>> ", newDate);
        // 确保newDate是Date对象
        const dateObj = newDate instanceof Date ? newDate : new Date(newDate);

        // 重置标志位
        this.weekCoursesLoaded = false;
        this.showCourseNameIndex = null;
        // 当父组件传入的日期变化时，重新初始化周数据
        this.initWeekDays();
        // 更新当前月份显示
        this.currentMonth = dateObj.getMonth() + 1;
      },
      immediate: true
    }
  },
  methods: {
    // 添加页面触摸处理方法
    handlePageTouch(event) {
      // 判断点击是否在课程名称区域外
      if (this.showCourseNameIndex !== null) {
        // 获取点击目标元素
        const target = event.target;
        // 如果不是点击的气泡内部元素，则关闭气泡
        if (!target.closest || !target.closest(".course-tooltip")) {
          this.showCourseNameIndex = null;
        }
      }
    },
    // 显示课程名称全部
    showCourseName(index, event) {
      // 如果点击的是已显示的气泡，则关闭它
      if (this.showCourseNameIndex === index) {
        this.showCourseNameIndex = null;
      } else {
        this.showCourseNameIndex = index;
      }

      // 阻止事件冒泡，避免触发页面的点击事件
      event && event.stopPropagation();
    },
    // 初始化周数据
    initWeekDays() {
      // 确保this.date是Date对象
      const dateObj =
        this.date instanceof Date ? this.date : new Date(this.date);
      // 使用父组件传入的日期获取周数据
      this.weekDays = getWeekDays(dateObj);

      // 找到当天在周数据中的索引
      this.todayIndex = this.weekDays.findIndex((day) => day.isToday);

      // 如果找到当天，则默认选中当天
      if (this.todayIndex !== -1) {
        if (this.open_from !== "notification") {
          this.selectedDayIndex = this.todayIndex;
        }
      } else {
        // 如果不是当前周，默认选中周一
        this.selectedDayIndex = 0;
      }
      // 获取一周内哪些天有课程
      if (!this.weekCoursesLoaded) {
        this.getWeekCourses();
      }
    },

    // 选择日期
    selectDay(index) {
      if (this.selectedDayIndex === index) return; // 避免重复选择同一天

      this.selectedDayIndex = index;
      this.$emit("day-selected", this.weekDays[index]);

      // 选择日期后重新获取该日期的课程数据
      this.getSchedulingListData();
    },

    // 获取课程数据
    async getSchedulingListData() {
      console.log("getSchedulingListData :>> ", this.isLoading);
      // if (this.isLoading) return; // 如果正在加载，则不重复请求

      const student_id =
        this.open_from === "notification"
          ? this.student_id
          : uni.getStorageSync("curStudentInfo")?.student_id;
      if (!student_id) {
        return;
      }

      // 获取选中日期的完整日期字符串
      const selectedDay = this.weekDays[this.selectedDayIndex];
      if (!selectedDay) return;

      const start_time = selectedDay.fullDate;
      const end_time = selectedDay.fullDate;
      this.isLoading = true;
      uni.showLoading({
        title: "加载中..."
      });
      try {
        const { code, data } = await getSchedulingList({
          student_id,
          start_time,
          end_time
        });

        if (code === 0) {
          this.schedulingList = data || [];
          // 处理课程数据
          this.processCourseData();
        }
      } catch (error) {
        uni.showToast({
          title: "获取课程数据失败",
          icon: "none"
        });
      } finally {
        this.isLoading = false;
        uni.hideLoading();
      }
    },
    getStatusText(item) {
      // 如果出勤状态未知，返回默认值
      if (item.is_attendance !== "YES" && item.is_attendance !== "NO") {
        return "--";
      }
      // 构建状态文本
      const attendanceText = item.is_attendance === "YES" ? "已出勤" : "未出勤";
      // 如果不显示计费信息，直接返回出勤状态
      if (!this.billShow) {
        return `·${attendanceText}`;
      }
      // 构建计费状态文本
      const billableText = item.is_billable === "YES" ? "已计费" : "未计费";
      return `·${attendanceText}${billableText}`;
    },
    // 处理课程数据
    processCourseData() {
      this.courses = this.schedulingList.map((item) => {
        // 根据状态设置对应的状态文本和样式类名
        let status = "";
        switch (item.is_attendance) {
          // case "is_cancelled":
          //   status = "finished";
          //   break;
          case "NO":
            status = "in-progress";
            break;
          case "YES":
            status = "waiting";
            break;
          default:
            status = "";
        }

        return {
          id: item.scheduling_id,
          classroom:
            item.school_room_name ||
            (item.scheduling_form === "online" ? "线上" : "暂无教室"),
          name: item.course_name,
          teacher: item.teacher_name,
          time: getTimeRange(item.start_time, item.end_time),
          status,
          statusText: this.getStatusText(item)
        };
      });
    },

    // 获取一周内哪些天有课程
    async getWeekCourses() {
      if (this.isLoading || this.weekCoursesLoaded) return; // 避免重复加载

      const student_id =
        this.open_from === "notification"
          ? this.student_id
          : uni.getStorageSync("curStudentInfo")?.student_id;
      if (!student_id) {
        return;
      }

      // 获取本周的开始日期和结束日期
      const startDate = this.weekDays[0].fullDate;
      const endDate = this.weekDays[6].fullDate;

      try {
        this.isLoading = true;
        uni.showLoading({
          title: "加载中..."
        });
        const { code, data } = await getSchedulingList({
          student_id,
          start_time: startDate,
          end_time: endDate
        });

        // 标记周课程已加载
        this.weekCoursesLoaded = true;

        if (code === 0) {
          // 重置所有日期的hasCourse属性
          this.weekDays.forEach((day) => {
            day.hasCourse = false;
          });

          // 标记有课程的日期
          data?.forEach((course) => {
            // 使用formatDate函数处理ISO格式的日期
            const courseDate = formatDate(course.start_time);
            const dayIndex = this.weekDays.findIndex(
              (day) => day.fullDate === courseDate
            );
            if (dayIndex !== -1) {
              this.weekDays[dayIndex].hasCourse = true;
            }
          });

          // 找到第一个有课程的日期下标
          const firstCourseDayIndex = this.weekDays.findIndex(
            (day) => day.hasCourse
          );

          const is_first_show = uni.getStorageSync("is_first_show");

          // 优化后的日期选择逻辑
          // 按优先级依次判断应该选中哪一天
          let newSelectedIndex;
          if (this.open_from === "notification" && !is_first_show) {
            // 找到微信通知日期在周数据中的下标
            const notificationDayIndex = this.weekDays.findIndex(
              (day) =>
                day.fullDate ===
                uni.$u.timeFormat(new Date(this.date).getTime(), "yyyy-mm-dd")
            );
            newSelectedIndex = notificationDayIndex;
            uni.setStorageSync("is_first_show", true);
          } else if (
            // 优先级1：今天在当前周且今天有课，则选中今天
            this.todayIndex !== -1 &&
            this.weekDays[this.todayIndex].hasCourse
          ) {
            newSelectedIndex = this.todayIndex;
          } else if (firstCourseDayIndex !== -1) {
            // 优先级2：本周有课的日期(选第一个)，则选中第一个有课的日期
            newSelectedIndex = firstCourseDayIndex;
          } else if (this.todayIndex !== -1) {
            // 优先级3：今天在当前周(但没课)，则选中今天
            newSelectedIndex = this.todayIndex;
          } else {
            // 优先级4：本周既没课也不包含今天，默认选周一
            newSelectedIndex = 0;
          }

          // 统一处理选中日期的变更和事件触发
          if (this.selectedDayIndex !== newSelectedIndex) {
            this.selectedDayIndex = newSelectedIndex;
            this.$emit("day-selected", this.weekDays[newSelectedIndex]);
          }

          this.getSchedulingListData();
        }
      } catch (error) {
        console.error("获取周课程数据失败", error);
      } finally {
        this.isLoading = false;
        uni.hideLoading();
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.day-view {
  background: #fff;
  border-top-left-radius: 30rpx;
  border-top-right-radius: 30rpx;
  overflow: hidden;
  .day-header {
    padding: 20rpx;
    // border-bottom: 1px solid #eee;

    .day-info {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .month {
        font-size: 24rpx;
        color: rgb(255, 187, 0);
        font-weight: 500;
        width: 30rpx;
        text-align: center;
        margin-right: 20rpx;

        .month-text {
          font-size: 30rpx;
        }
      }

      .date-list {
        display: flex;
        justify-content: space-between;
        flex: 1;
        .date-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          // padding: 10rpx 0;
          position: relative;

          // &.active {
          //   .weekday,
          //   .date {
          //     color: #ffbf0d;
          //   }

          //   &::after {
          //     content: "";
          //     position: absolute;
          //     bottom: -2rpx;
          //     left: 50%;
          //     transform: translateX(-50%);
          //     width: 40rpx;
          //     height: 4rpx;
          //     background: #ffbf0d;
          //     border-radius: 2rpx;
          //   }
          // }

          .weekday {
            font-size: 30rpx;
            color: #333;
            margin-bottom: 10rpx;
            width: 44rpx;
            height: 44rpx;
            text-align: center;
            line-height: 44rpx;
            &.has-course {
              color: #fff;
              background: #fe4f37;
              border-radius: 50px;
            }
          }

          .date {
            font-size: 26rpx;
            color: #999;
            &.current-day,
            &.active {
              color: #fff;
              background: #ffc525;
              width: 44rpx;
              height: 44rpx;
              text-align: center;
              line-height: 44rpx;
              border-radius: 50px;
            }
          }
        }
      }
    }
  }

  .course-list {
    // margin-top: 20rpx;
    .course-list-wrapper {
      padding: 24rpx 32rpx;
      display: flex;
      flex-direction: column;
      align-items: center;
      padding-bottom: env(safe-area-inset-bottom);
    }
    .course-item {
      background: #fff;
      border-radius: 24rpx;
      margin-bottom: 30rpx;
      overflow: hidden;
      width: 100%;
      box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
      border-left: 8rpx solid transparent;

      &.waiting {
        border: 1px solid #ff6da3;
        background: rgba(255, 136, 180, 0.1);
      }

      &.in-progress {
        border: 1px solid #ff8913;
        background: rgba(255, 168, 40, 0.1);
      }

      &.finished {
        border: 1px solid #b5b5b5;
        background: rgba(206, 206, 206, 0.1);
      }

      .course-content {
        padding: 20rpx;
        position: relative;
        min-height: 160rpx;

        .course-header {
          position: relative;
          display: flex;
          align-items: center;
          margin-bottom: 16rpx;

          .course-tag {
            // background: #f8f9fc;
            font-size: 22rpx;
            color: #ff7300;
            border-radius: 6rpx;
            margin-right: 12rpx;
            border-radius: 6rpx;
            border: 1px solid #ff7300;
            height: 34rpx;
            line-height: 34rpx;
            padding: 0 3px;
          }

          .course-title {
            position: relative;
            font-size: 30rpx;
            color: #333;
            font-weight: 500;
            flex: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          // 课程名称气泡 - 移到外部
          .course-tooltip {
            position: absolute;
            top: 100%; // 修改为顶部100%，即元素下方
            left: 50%;
            margin-top: 10rpx; // 添加上边距
            background: rgba(51, 51, 51, 0.6);
            border-radius: 8rpx;
            padding: 16rpx;
            z-index: 100;
            max-width: 500rpx;
            box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);

            // 气泡箭头
            .tooltip-arrow {
              position: absolute;
              top: -16rpx; // 修改为顶部
              left: 20rpx;
              width: 0;
              height: 0;
              border-left: 16rpx solid transparent;
              border-right: 16rpx solid transparent;
              border-bottom: 16rpx solid rgba(51, 51, 51, 0.6); // 修改为下边框
              border-top: none; // 移除上边框
            }

            // 气泡内容
            .tooltip-content {
              color: #fff;
              font-size: 26rpx;
              line-height: 1.5;
              word-break: break-all;
              white-space: normal;
            }
          }
        }

        .course-info {
          font-size: 28rpx;
          color: #666;
          padding-right: 40rpx;

          text {
            display: block;
          }
        }

        .course-action {
          position: absolute;
          right: 24rpx;
          bottom: 20rpx;
        }

        .course-status {
          font-size: 24rpx;
          text-align: center;
          border-radius: 50rpx;
          width: 188rpx;
          height: 50rpx;
          color: #fff;
          line-height: 50rpx;
          &.waiting {
            background: #ff6da3;
          }

          &.in-progress {
            background: #ff9d4b;
          }

          &.finished {
            background: linear-gradient(15deg, #cecece 18.1%, #cecece 83.29%);
            box-shadow: 0px -4rpx 8rpx 0px #a2a2a2 inset;
          }
        }
      }
    }
    .no-course {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 60%;
      color: #999;
      font-size: 26rpx;
    }
  }

  .text-ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
</style>
