<template>
  <view class="login-form">
    <!-- 手机号输入框 -->
    <view class="input-item">
      <u--input
        v-model="userphone"
        placeholder-style="color:#999999"
        placeholder="请输入报名手机号码"
        type="number"
        maxlength="11"
        border="none"
      />
    </view>

    <!-- 验证码输入框 -->
    <view class="input-item code-input">
      <u--input
        v-model="code"
        placeholder-style="color:#999999"
        placeholder="请输入验证码"
        type="text"
        maxlength="6"
        border="none"
      />
      <!-- :class="{ disabled: counting }" -->
      <text class="get-code" @tap="handleGetCodePhone">
        <text class="fe">|</text>
        {{ counting ? `重新获取(${countdown}s)` : "获取验证码" }}
      </text>
    </view>

    <!-- 登录按钮 -->
    <div
      class="login-btn"
      :class="{ 'login-btn-active': isButtonActive }"
      @tap="handleSubmit"
    >
      <div class="login-btn-text">登录</div>
    </div>
  </view>
</template>

<script>
import { getCode } from "@/services/student/home";
export default {
  name: "PhoneLogin",

  data() {
    return {
      userphone: "",
      code: "",
      counting: false,
      countdown: 60,
      isButtonActive: false
    };
  },
  watch: {
    code(newVal) {
      this.updateButtonState();
    },
    userphone() {
      this.updateButtonState();
    }
  },
  methods: {
    handleGetCodePhone() {
      // Validate phone number first
      if (!this.userphone) {
        uni.showToast({
          title: "请输入手机号",
          icon: "none"
        });
        return;
      }

      if (!/^1[3-9]\d{9}$/.test(this.userphone)) {
        uni.showToast({
          title: "请输入正确的手机号",
          icon: "none"
        });
        return;
      }
      getCode({
        mobile: this.userphone,
        template_code: "SMS_465317333",
        sign_name: "聂卫平围棋道场"
      })
        .then((res) => {
          if (res.code === 0) {
            uni.showToast({
              title: "验证码已发送",
              icon: "none"
            });
            // Start countdown timer if needed
            this.counting = true;
            this.countdown = 60;

            const timer = setInterval(() => {
              if (this.countdown > 0) {
                this.countdown--;
              } else {
                this.counting = false;
                clearInterval(timer);
              }
            }, 1000);
          } else {
            uni.showToast({
              title: "发送失败：" + res.message,
              icon: "none"
            });
          }
        })
        .catch((error) => {
          uni.showToast({
            title: "发送失败：" + error.message,
            icon: "none"
          });
        });
    },

    handleSubmit() {
      if (!this.userphone || !this.code) {
        uni.showToast({
          title: "请输入手机号和验证码",
          icon: "none"
        });
        return;
      }

      if (!/^1[3-9]\d{9}$/.test(this.userphone)) {
        uni.showToast({
          title: "请输入正确的手机号",
          icon: "none"
        });
        return;
      }

      this.$emit("submit", {
        mobile: this.userphone,
        template_code: this.code
      });
    },

    updateButtonState() {
      this.isButtonActive = this.code.length === 6 && this.userphone;
    }
  }
};
</script>

<style lang="scss" scoped>
.login-form {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
}

.input-item {
  width: 654rpx;
  height: 104rpx;
  border-radius: 60px;
  background: #fff;
  margin-bottom: 50rpx;
  padding: 0 32rpx;
  display: flex;
  align-items: center;

  input {
    flex: 1;
    height: 100%;
    font-size: 32rpx;
    color: #333;
  }
}

.placeholder-style {
  color: #999;
  font-size: 32rpx;
}

.code-input {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .get-code {
    color: #ffc422;
    font-size: 26rpx;
    font-style: normal;
    font-weight: 500;
    line-height: 39rpx; /* 150% */
    .fe {
      width: 5rpx;
      height: 32rpx;
      stroke-width: 1px;
      color: #e3e3e3;
      margin-right: 10rpx;
    }
    &.disabled {
      color: #999;
      pointer-events: none;
    }
  }
}

.login-btn {
  width: 654rpx;
  height: 100rpx;
  padding: 0;
  border-radius: 71px;
  background: linear-gradient(15deg, #ffbf0d 18.1%, #ffcb3c 83.29%);
  box-shadow: 0px -10px 18px 0px #f3b300 inset,
    0px 4px 20px 0px rgba(254, 197, 36, 0.47);
  color: #fff;
  font-size: 36rpx;
  font-style: normal;
  font-weight: 600;
  line-height: 40rpx;
  margin-top: 70rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.5;
  transition: opacity 0.3s;

  &.login-btn-active {
    opacity: 1;
  }
}
</style>
