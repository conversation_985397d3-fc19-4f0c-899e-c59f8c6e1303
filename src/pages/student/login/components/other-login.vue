<template>
  <view class="other-login">
    <div class="text">
      <div class="other-border" style="margin-right: 18rpx"></div>
      其他登录方式
      <div class="other-border" style="margin-left: 18rpx"></div>
    </div>
    <button open-type="getPhoneNumber" @getphonenumber="handleWxLogin">
      <image
        src="https://tg-prod.oss-cn-beijing.aliyuncs.com/4b5ff32b-0f0d-482c-b082-639a1f4508c3.webp"
        mode="aspectFit"
      />
    </button>
  </view>
</template>

<script>
export default {
  name: "OtherLogin",

  methods: {
    handleWxLogin(e) {
      this.$emit("wxLogin", e);
    }
  }
};
</script>

<style lang="scss" scoped>
.other-login {
  margin-top: 180rpx;
  text-align: center;
  .other-border {
    width: 227rpx;
    height: 1rpx;
    background: #eaeaea;
  }
  .text {
    font-size: 24rpx;
    color: #999999;
    margin-bottom: 30rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  image {
    margin-top: 40rpx;
    width: 100rpx;
    height: 100rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 40rpx auto;
  }
}
button {
  background: none;
  border: none;
  padding: 0;
  margin: 0;
  &::after {
    border: none;
  }
}
</style>
