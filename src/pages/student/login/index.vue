<template>
  <view class="login-container">
    <u-toast ref="uToast" style="z-index: 999999 !important"></u-toast>
    <!-- 顶部返回按钮 -->
    <view class="nav-back" @tap="handleBack">
      <image
        class="back-icon"
        src="https://tg-prod.oss-cn-beijing.aliyuncs.com/e9ac0f4b-6282-4bac-b602-189057c45c90.webp"
        mode="aspectFit"
      />
    </view>

    <!-- 顶部标题区域 -->
    <view class="header">
      <text class="title">聂卫平圈棋道场-家长端</text>
      <text class="subtitle">登录账号了解孩子学习情况</text>
    </view>

    <!-- 登录按钮区域 -->
    <view class="login-area">
      <button
        v-if="phoneType === 'click'"
        class="login-btn-wrapper"
        open-type="getPhoneNumber"
        @getphonenumber="onGetPhoneNumber"
      >
        <div class="login-btn">手机号快捷登录</div>
      </button>
      <div v-else>
        <phone-login @submit="handleLogin" />
        <!-- 其他登录方式 -->
        <other-login @wxLogin="handleWxLogin" />
      </div>
    </view>
  </view>
</template>

<script>
import PhoneLogin from "./components/phone-login.vue";
import OtherLogin from "./components/other-login.vue";
import {
  loginByPhone,
  getOpenId,
  getPhoneByCode,
  loginByCode,
  bindVisitor
} from "@/services/student/home";
import { rowIds } from "../my/config/index";
export default {
  name: "LoginIndex",

  components: {
    PhoneLogin,
    OtherLogin
  },

  data() {
    return {
      loading: false,
      phoneType: "click",
      role: "",
      session: {},
      studentList: [],
      haveStudent: false,
      curCheckedStudent: {},
      rowIdField: "",
      rowIds,
      openId: ""
    };
  },

  onLoad(options) {
    // 页面加载时自动触发微信登录
    console.log("🚀 ~ onLoad ~ options:", options);
    this.phoneType = options.type || "click";
  },

  methods: {
    async onGetPhoneNumber(e) {
      console.log("🚀 ~ onGetPhoneNumber ~ e:", e);
      if (this.loading) return;

      // 用户拒绝授权
      if (e.detail.errMsg !== "getPhoneNumber:ok") {
        this.phoneType = "input";
        // uni.showToast({
        //   title: "请先授权手机号",
        //   icon: "none"
        // });
        return;
      }

      this.loading = true;
      getPhoneByCode({
        code: e.detail.code
      }).then((response) => {
        console.log("🚀 ~ getPhoneByCode ~ res:", response);
        if (response.code === 0) {
          uni.login({
            provider: "weixin",
            success: async (loginRes) => {
              if (!loginRes.code) {
                this.loading = false;
                // this.phoneType = "input";
                return;
              }
              const { code } = loginRes;
              const res = await getOpenId({
                code,
                UNAUTHORIZED: true
              });
              if (res.code === 0) {
                const res2 = await loginByPhone({
                  open_id: res.data.openid,
                  mobile: response.data.purePhoneNumber,
                  UNAUTHORIZED: true
                });
                if (res2.code === 0) {
                  const role =
                    !res2.data.is_student && !res2.data.is_customer
                      ? "default"
                      : res2.data.is_student
                      ? "student"
                      : "customer";
                  this.role = role;
                  const session = { ...res2.data, role };
                  uni.setStorageSync("session", session);
                  this.session = session;
                  this.role = this.session.role;
                  this.rowIdField = this.rowIds[this.role];
                  console.log(uni.getStorageSync("curStudentInfo"));
                  uni.setStorageSync("copac_popup_show", "0");
                  // this.curCheckedStudent = uni.getStorageSync("curStudentInfo");
                  if (role === "default") {
                    const bindRes = await bindVisitor({
                      open_id: res.data.openid,
                      UNAUTHORIZED: true
                    });
                    if (
                      bindRes.code === 0 &&
                      bindRes.data &&
                      bindRes.data.length > 0
                    ) {
                      uni.navigateTo({
                        url: "/pages/student/studentPage/index?prospective=login"
                      });
                      return;
                    }
                  }
                  uni.switchTab({ url: "/pages/student/home/<USER>" });
                } else {
                  uni.showToast({
                    title: res2.message,
                    icon: "none"
                  });
                  this.loading = false;
                }
              } else {
                uni.showToast({
                  title: res.message,
                  icon: "none"
                });
                this.loading = false;
              }
            },
            fail: () => {
              uni.showToast({
                title: "登录失败，请重试",
                icon: "none"
              });
              this.loading = false;
            }
          });
        } else {
          uni.showToast({
            title: "登录失败，请重试",
            icon: "none"
          });
          this.loading = false;
        }
      });
    },

    handleBack() {
      // 使用 uni.navigateBack 的返回值判断是否可以返回
      uni.navigateBack({
        delta: 1,
        fail: () => {
          uni.reLaunch({
            url: "/pages/index/index"
          });
        }
      });
    },
    handleLogin(item) {
      uni.login({
        provider: "weixin",
        success: async (loginRes) => {
          if (!loginRes.code) {
            this.loading = false;
            // this.phoneType = "input";
            return;
          }
          const { code } = loginRes;
          const res = await getOpenId({
            code,
            UNAUTHORIZED: true
          });
          if (res.code === 0) {
            const res2 = await loginByCode({
              open_id: res.data.openid,
              ...item,
              UNAUTHORIZED: true
            });
            if (res2.code === 0) {
              const role =
                !res2.data.is_student && !res2.data.is_customer
                  ? "default"
                  : res2.data.is_student
                  ? "student"
                  : "customer";
              this.role = role;
              const session = { ...res2.data, role };
              uni.setStorageSync("session", session);
              this.session = session;
              this.role = this.session.role;
              this.rowIdField = this.rowIds[this.role];
              console.log(uni.getStorageSync("curStudentInfo"));
              uni.setStorageSync("copac_popup_show", "0");
              // this.curCheckedStudent = uni.getStorageSync("curStudentInfo");
              if (role === "default") {
                const bindRes = await bindVisitor({
                  open_id: res.data.openid,
                  UNAUTHORIZED: true
                });
                if (
                  bindRes.code === 0 &&
                  bindRes.data &&
                  bindRes.data.length > 0
                ) {
                  uni.navigateTo({
                    url: "/pages/student/studentPage/index?prospective=login"
                  });
                }
              }
              uni.switchTab({ url: "/pages/student/home/<USER>" });
            } else {
              uni.showToast({
                title: res2.message,
                icon: "none"
              });
              this.loading = false;
            }
          } else {
            uni.showToast({
              title: res.message,
              icon: "none"
            });
            this.loading = false;
          }
        },
        fail: () => {
          uni.showToast({
            title: "登录失败，请重试",
            icon: "none"
          });
          this.loading = false;
        }
      });
    },
    handleWxLogin(e) {
      this.onGetPhoneNumber(e);
    },
    async arouseLogin() {
      uni.login({
        provider: "weixin",
        onlyAuthorize: true, // 微信登录仅请求授权认证
        success: async (event) => {
          const { code } = event;
          const res = await getOpenId({ code, UNAUTHORIZED: true });
          if (res.code === 0) {
            const res2 = await loginByPhone({
              open_id: res.data.openid,
              UNAUTHORIZED: true
            });
            if (res2.code === 0) {
              const role =
                !res2.data.is_student && !res2.data.is_customer
                  ? "default"
                  : res2.data.is_student
                  ? "student"
                  : "customer";
              this.role = role;
              const session = { ...res2.data, role };
              uni.setStorageSync("session", session);
              this.session = session;
              this.role = this.session.role;
              this.rowIdField = this.rowIds[this.role];
              console.log(uni.getStorageSync("curStudentInfo"));
              // this.curCheckedStudent = uni.getStorageSync("curStudentInfo");
              if (role === "default") {
                const bindRes = await bindVisitor({
                  open_id: res.data.openid,
                  UNAUTHORIZED: true
                });
                if (
                  bindRes.code === 0 &&
                  bindRes.data &&
                  bindRes.data.length > 0
                ) {
                  uni.navigateTo({
                    url: "/pages/student/studentPage/index?prospective=login"
                  });
                }
              }
              uni.switchTab({ url: "/pages/student/home/<USER>" });
            } else {
              uni.hideLoading();
              // uni.navigateTo({ url: "/pages/student/login/index" });
              uni.showToast({
                title: res2.message,
                icon: "none"
              });
            }
          }
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.login-container {
  min-height: 100vh;
  background-image: url("https://tg-prod.oss-cn-beijing.aliyuncs.com/5807acde-14fa-4746-b32b-182bc0108a64.webp");
  background-size: 100% 100%;
  background-repeat: no-repeat;
  padding: 48rpx;
}

.nav-back {
  position: fixed;
  top: 112rpx;
  left: 32rpx;
  z-index: 100;

  .back-icon {
    width: 40rpx;
    height: 40rpx;
    flex-shrink: 0;
  }
}

.header {
  margin-top: 270rpx;
  display: flex;
  flex-direction: column;

  .title {
    color: #333;
    font-size: 44rpx;
    font-weight: 600;
    line-height: 56rpx;
    margin-bottom: 14rpx;
  }

  .subtitle {
    color: #979797;
    font-size: 26rpx;
    font-weight: 500;
    line-height: 39rpx;
  }
}

.login-area {
  margin-top: 200rpx;

  .login-btn-wrapper {
    padding: 0;
    margin: 0;
    background: none;
    border: none;
    line-height: 1;

    &::after {
      border: none;
    }
  }

  .login-btn {
    width: 654rpx;
    height: 100rpx;
    flex-shrink: 0;
    border-radius: 71rpx;
    background: linear-gradient(15deg, #ffbf0d 18.1%, #ffcb3c 83.29%);
    box-shadow: 0px -10px 18px 0px #f3b300 inset,
      0px 4px 20px 0px rgba(254, 197, 36, 0.47);
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 36rpx;
    font-style: normal;
    font-weight: 600;
    line-height: 40rpx; /* 111.111% */
  }
}
</style>
