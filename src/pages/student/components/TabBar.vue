<template>
  <view class="tabbar">
    <!-- <u-toast ref="uToast"></u-toast> -->
    <u-tabbar
      :value="curRouteUrl"
      class="tabbar"
      :fixed="true"
      :safeAreaInsetBottom="true"
      placeholder
      activeColor="#FFC525"
      :border="false"
    >
      <template>
        <u-tabbar-item
          @click="clickTabItem(item.index)"
          v-for="item in tabList"
          :key="item.icon"
          :name="item.index"
          :text="item.text"
          :dot="item.isHasDot"
        >
          <image
            slot="active-icon"
            class="u-page__item__slot-icon"
            :src="item.active_icon"
            width="48rpx"
            height="48rpx"
            :showMenuByLongpress="false"
          ></image>
          <image
            slot="inactive-icon"
            class="u-page__item__slot-icon"
            :src="item.inactive_icon"
            width="48rpx"
            height="48rpx"
            :showMenuByLongpress="false"
          ></image>
        </u-tabbar-item>
      </template>
    </u-tabbar>
  </view>
</template>

<script>
import {
  hasNewSurvey,
  getElectronContractMessage,
  getMessageTemplate
} from "@/services/student/my";
export default {
  name: "TabBar",
  props: {
    tabField: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      curRouteUrl: 0,
      clickTabVal: "",
      tabList: [],
      subscriptionState: {
        currentBatchIndex: 0,
        tmplIds: []
      },
      commonTabs: [
        {
          icon: "home",
          text: "首页",
          url: "pages/student/home/<USER>",
          isHasDot: "",
          index: 0,
          key: "",
          show: true,
          active_icon:
            "https://tg-prod.oss-cn-beijing.aliyuncs.com/416881b8-c9ab-4816-ae7e-8347ec84348c.webp",
          inactive_icon:
            "https://tg-prod.oss-cn-beijing.aliyuncs.com/98f71e83-61e0-4089-b64c-44efb7b9960d.webp"
        },
        {
          icon: "my",
          text: "我的",
          url: "pages/student/my/index",
          isHasDot: "",
          index: 3,
          key: "",
          show: true,
          active_icon:
            "https://tg-prod.oss-cn-beijing.aliyuncs.com/f283a758-5b1f-4835-8486-07fcb8133331.webp",
          inactive_icon:
            "https://tg-prod.oss-cn-beijing.aliyuncs.com/15739abe-7a84-4b8c-b97d-b0c9822acc5f.webp"
        }
      ],
      dynamicTabs: [
        {
          icon: "course",
          text: "课程",
          url: "pages/student/buyCourse/index",
          isHasDot: "",
          index: 1,
          key: "mini_buy_course",
          show: true,
          active_icon:
            "https://tg-prod.oss-cn-beijing.aliyuncs.com/56843577-a1fe-48c2-9c2a-8c7e346795be.webp",
          inactive_icon:
            "https://tg-prod.oss-cn-beijing.aliyuncs.com/2fb85de1-7ffa-4da6-923b-7a03d2d2a506.webp"
        },
        {
          icon: "competition",
          text: "聂道圈",
          url: "pages/student/subpages/niedaoCircle/index",
          isHasDot: "",
          index: 2,
          key: "niedao_circle",
          show: true,
          active_icon:
            "https://tg-prod.oss-cn-beijing.aliyuncs.com/847e7295-ae00-4bf4-a51a-ec1ec8546578.webp",
          inactive_icon:
            "https://tg-prod.oss-cn-beijing.aliyuncs.com/244e9d7b-a9e8-46ca-849e-66c2a17de1ed.webp"
        }
      ]
    };
  },
  methods: {
    async setMenuList(val) {
      // eslint-disable-next-line no-undef
      this.curRouteUrl = getApp().globalData.tabIndex;
      const deepCommonTbas = uni.$u.deepClone(this.commonTabs);
      const session = uni.getStorageSync("session");
      if (session.role === "student") {
        this.dynamicTabs[0] = {
          icon: "video",
          text: "精选视频",
          url: "pages/student/wonderfulVideo/index",
          isHasDot: "",
          index: 1,
          key: "wonderful_video",
          active_icon:
            "https://tg-prod.oss-cn-beijing.aliyuncs.com/7b5a665f-6a21-4c9e-bb48-484e74e7e582.webp",
          inactive_icon:
            "https://tg-prod.oss-cn-beijing.aliyuncs.com/2d9eb48b-0b60-4d57-8c7d-3bfe106cbbf3.webp",
          show: true
        };
      } else {
        this.dynamicTabs[0] = {
          icon: "course",
          text: "课程",
          url: "pages/student/buyCourse/index",
          isHasDot: "",
          index: 1,
          key: "mini_buy_course",
          show: true,
          active_icon:
            "https://tg-prod.oss-cn-beijing.aliyuncs.com/56843577-a1fe-48c2-9c2a-8c7e346795be.webp",
          inactive_icon:
            "https://tg-prod.oss-cn-beijing.aliyuncs.com/2fb85de1-7ffa-4da6-923b-7a03d2d2a506.webp"
        };
      }
      deepCommonTbas.splice(2, 0, ...this.dynamicTabs);
      this.tabList = deepCommonTbas.sort((a, b) => a.index - b.index);

      const curCheckedStudent = uni.getStorageSync("curStudentInfo");
      // if (curCheckedStudent) {
      try {
        const student_status =
          session.role === "student" ? 3 : session.role === "customer" ? 2 : 1;
        const student_id =
          session.role === "student"
            ? curCheckedStudent.student_id
            : session.role === "customer"
            ? session.customer_id
            : session.open_id;
        const res = await hasNewSurvey({
          student_id,
          student_status
        });
        const myHas = await getElectronContractMessage({
          student_id,
          student_status
        });
        if (res.code === 0) {
          // 处理新的数据结构
          const notificationData = res.data;
          const notificationTypes = [
            "class_notice",
            "course_summary",
            "parent_class",
            "survey"
          ];

          // 检查是否有任何一种通知类型有新内容
          const hasAnyNew = notificationTypes.some(
            (type) =>
              notificationData[type] && notificationData[type].has_new === true
          );
          // 设置首页的红点
          this.tabList[0].isHasDot = hasAnyNew ? "dot" : "";
          this.tabList[3].isHasDot = myHas.data.contract.has_new ? "dot" : "";
          // this.tabList[3].isHasDot = notificationData.e_sign.has_new || false;
          // 将通知数据发送给父组件
          this.$emit("changeDot", res.data, myHas.data);
        } else {
          // uni.$u.toast(res.message);
        }
      } catch (error) {
        console.error("获取通知状态失败:", error);
        // uni.$u.toast("获取通知状态失败");
      }
      // }
      // this.order();
    },

    async clickTabItem(val) {
      this.clickTabVal = val;
      this.setMenuList();
      // if (val === 0) {
      this.order();
      // }
      // eslint-disable-next-line no-undef
      getApp().globalData.tabIndex = val;
      // eslint-disable-next-line no-undef
      this.curRouteUrl = getApp().globalData.tabIndex;
      // eslint-disable-next-line no-undef
      this.$emit("changeTab", getApp().globalData.tabIndex);
      // uni.switchTab({
      //   url: "/" + val
      // });
    },

    async order() {
      // 如果tmplIds为空，重新获取模板
      if (this.subscriptionState.tmplIds.length === 0) {
        const { data, code, message } = await getMessageTemplate({
          UNAUTHORIZED: true
        });
        console.log("消息模板", data);
        if (code === 0 && data.length > 0) {
          this.subscriptionState.tmplIds = data;
          this.subscriptionState.currentBatchIndex = 0;
        } else if (code !== 0) {
          uni.$u.toast(message);
          return;
        }
      }

      // 计算当前批次的起始位置
      const startIndex = this.subscriptionState.currentBatchIndex * 3;

      // 如果已经订阅完所有批次，重置状态
      if (startIndex >= this.subscriptionState.tmplIds.length) {
        console.log("所有批次都已询问过，重置状态");
        this.subscriptionState.currentBatchIndex = 0;
        this.subscriptionState.tmplIds = [];
        return;
      }

      // 获取当前批次的模板ID（最多3个）
      const currentBatchTmplIds = this.subscriptionState.tmplIds.slice(
        startIndex,
        startIndex + 3
      );

      const that = this;
      try {
        await new Promise((resolve, reject) => {
          uni.requestSubscribeMessage({
            tmplIds: currentBatchTmplIds,
            success(res) {
              console.log(
                `第${
                  that.subscriptionState.currentBatchIndex + 1
                }批消息模板订阅结果:`,
                res
              );
              resolve(res);
            },
            fail(err) {
              console.log(
                `第${
                  that.subscriptionState.currentBatchIndex + 1
                }批消息模板订阅失败:`,
                err
              );
              reject(err);
            }
          });
        });

        // 更新批次索引
        this.subscriptionState.currentBatchIndex++;

        // 检查是否是最后一批
        if (
          this.subscriptionState.currentBatchIndex * 3 >=
          this.subscriptionState.tmplIds.length
        ) {
          // uni.$u.toast("所有消息订阅都已询问完毕");
          // 立即重置状态
          this.subscriptionState.currentBatchIndex = 0;
          this.subscriptionState.tmplIds = [];
        } else {
          // uni.$u.toast(
          //   `还有${
          //     Math.ceil(this.subscriptionState.tmplIds.length / 3) -
          //     this.subscriptionState.currentBatchIndex
          //   }批消息需要订阅`
          // );
        }
      } catch (error) {
        console.error("订阅消息失败:", error);
      }
    }
  },
  created() {
    // 获取当前页面栈
    // eslint-disable-next-line no-undef
    // const pages = getCurrentPages();
    // 获取当前页面的实例
    // const currentPage = pages[pages.length - 1];
    // 获取页面路由路径
    // this.curRouteUrl = currentPage.route;
  },
  // 组件周期函数--监听组件挂载完毕
  mounted() {},
  // 组件周期函数--监听组件数据更新之前
  beforeUpdate() {},
  // 组件周期函数--监听组件数据更新之后
  updated() {},
  // 组件周期函数--监听组件激活(显示)
  activated() {},
  // 组件周期函数--监听组件停用(隐藏)
  deactivated() {},
  // 组件周期函数--监听组件销毁之前
  beforeUnmount() {}
};
</script>
<style lang="scss">
.u-tabbar__content__item-wrapper {
  padding-top: 20rpx;
}
</style>
<style lang="scss" scoped>
::v-deep .u-tabbar-item__text {
  font-weight: bold;
}

.u-page__item__slot-icon {
  width: 48rpx;
  height: 48rpx;
}
</style>
