<template>
  <div>
    <u-tabbar
      :value="curRouteUrl"
      :fixed="true"
      @change="tabChange"
      :safeAreaInsetBottom="true"
      :placeholder="false"
      :zIndex="66"
      activeColor="#000"
    >
      <u-tabbar-item v-for="item in tabList" :key="item.icon" :name="item.url" :text="item.text">
        <image
          slot="active-icon"
          class="u-page__item__slot-icon"
          :src="item.active_icon"
        ></image>
        <image
          slot="inactive-icon"
          class="u-page__item__slot-icon"
          :src="item.inactive_icon"
        ></image>
      </u-tabbar-item>
    </u-tabbar>
  </div>
</template>

<script>
export default {
  name: "TabBar",
  props: {
    tabIndex: {
      type: Number,
      default: 0,
      require: true
    }
  },
  data() {
    return {
      curRouteUrl: "",
      tabList: [
        // {
        //   icon: "home",
        //   text: "工作台",
        //   url: "pages/teacher/home/<USER>",
        //   active_icon:
        //     "https://tg-prod.oss-cn-beijing.aliyuncs.com/643fb082-9f33-4439-af6c-dc4999d3466d.png",
        //   inactive_icon:
        //     "https://tg-prod.oss-cn-beijing.aliyuncs.com/2ce99229-40b9-4a40-abdb-de7c32bf3fac.png"
        // },
        {
          icon: "sort",
          text: "功能",
          url: "pages/teacher/sort/index",
          active_icon:
            "https://tg-prod.oss-cn-beijing.aliyuncs.com/999f37d6-6444-43e3-a1c5-a3c6a0c36b85.png",
          inactive_icon:
            "https://tg-prod.oss-cn-beijing.aliyuncs.com/6b2410d6-aab2-4099-8db3-2ac31a242caa.png"
        },
        // {
        //   icon: "report",
        //   text: "报表",
        //   url: "pages/teacher/report/index",
        //   active_icon:
        //     "https://tg-prod.oss-cn-beijing.aliyuncs.com/d3f29201-da27-4486-96fd-a5616524e1a2.png",
        //   inactive_icon:
        //     "https://tg-prod.oss-cn-beijing.aliyuncs.com/5872b245-ebcd-45ff-9cbd-0494cc50e2ad.png"
        // },
        {
          icon: "usercenter",
          text: "我的",
          url: "pages/teacher/usercenter/index",
          active_icon:
            "https://tg-prod.oss-cn-beijing.aliyuncs.com/f2ba8f70-0b65-40d3-9522-5564462e583f.png",
          inactive_icon:
            "https://tg-prod.oss-cn-beijing.aliyuncs.com/2a3cc377-b57f-4ea9-af92-f34b49a1afa9.png"
        }
      ]
    };
  },
  computed: {},
  methods: {
    tabChange(val) {
      const { tabIndex } = this;
      if (val !== tabIndex) {
        uni.redirectTo({
          url: "/" + val
        });
      }
      uni.setStorageSync("teacher_tabIndex", val);
    }
  },
  watch: {},

  // 组件周期函数--监听组件挂载完毕
  created() {
    // 获取当前页面栈
    const pages = getCurrentPages();
    // 获取当前页面的实例
    const currentPage = pages[pages.length - 1];
    // 获取页面路由路径
    this.curRouteUrl = currentPage.route;
    console.log(this.curRouteUrl);
  },
  // 组件周期函数--监听组件数据更新之前
  beforeUpdate() {},
  // 组件周期函数--监听组件数据更新之后
  updated() {},
  // 组件周期函数--监听组件激活(显示)
  activated() {},
  // 组件周期函数--监听组件停用(隐藏)
  deactivated() {},
  // 组件周期函数--监听组件销毁之前
  beforeUnmount() {}
};
</script>

<style lang="scss" scoped>
.u-page__item__slot-icon {
  width: 48rpx;
  height: 48rpx;
}
</style>
