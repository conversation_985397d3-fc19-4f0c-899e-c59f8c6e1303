<template>
  <div>
    <div class="tg_login_page">
      <!-- <div class="tg_navigation_bar"></div> -->
      <view class="tg_navigation_bar" @tap="handleBack">
        <image
          class="back-icon"
          src="https://tg-prod.oss-cn-beijing.aliyuncs.com/e9ac0f4b-6282-4bac-b602-189057c45c90.webp"
          mode="aspectFit"
        />
      </view>
      <!-- <div
        :style="{ top: topHeight + 'px' }"
        class="flex flex-center back-index"
        @tap="backIndex"
      >
        <u-icon
          name="arrow-leftward"
          color="#fff"
          :bold="true"
          size="24"
        ></u-icon>
      </div> -->

      <!-- <div class="tg_top_bg_img"></div> -->
      <div class="tg_login_summary">
        <div class="txt1">聂卫平围棋道场-教师端</div>
        <div class="txt2">请登录天工账号</div>
      </div>
      <div class="tg_login_content">
        <div class="tg_input_row">
          <!-- <div class="tg_sprite_01 tg_login_acc"></div> -->
          <u--input
            v-model="username"
            placeholder-style="color:#bababa;font-size:30rpx"
            placeholder="请输入账号"
            type="text"
            border="none"
            maxlength="20"
          />
        </div>
        <div class="tg_input_row">
          <!-- <div class="tg_sprite_01 tg_login_lock"></div> -->

          <u--input
            v-if="browse_actve"
            v-model="password"
            placeholder-style="color:#bababa;font-size:30rpx"
            placeholder="请输入密码"
            type="text"
            border="none"
            maxlength="20"
          />
          <u--input
            v-if="!browse_actve"
            v-model="password"
            placeholder-style="color:#bababa;font-size:30rpx"
            placeholder="请输入密码"
            type="password"
            border="none"
            maxlength="20"
          />
          <div
            @click="browseIconShow"
            class="tg_sprite_01 tg_login_browse"
            :class="{ dis: !browse_actve }"
          ></div>
        </div>
        <view style="padding-top: 120rpx">
          <u-button
            :disabled="login_btn_disable || isLoading"
            @click="handleLogin"
            size="large"
            shape="circle"
            :color="
              login_btn_disable || isLoading
                ? '#ffdd7f'
                : 'linear-gradient(15deg, #FFBF0D 18.1%, #FFCB3C 83.29%)'
            "
            type="primary"
            :text="isLoading ? '登录中...' : '登录'"
            :customStyle="{ fontWeight: '500' }"
          ></u-button>
        </view>
      </div>
      <div class="tg_login_footer">
        <!-- <u-checkbox-group @change="checkboxChange">
          <u-checkbox
            shape="circle"
            activeColor="#F3B300"
            v-model="checkbox"
            :checked="checkbox"
            size="40"
            customStyle="{'fontWeight':'500',border-color:'#FFC525'}"
          ></u-checkbox>
        </u-checkbox-group> -->
        <view class="checkbox" @click="checkboxChange">
          <image
            :src="
              checkboxVal
                ? 'https://tg-prod.oss-cn-beijing.aliyuncs.com/92d13fd0-0770-445e-a4cd-5623af65721b.webp'
                : 'https://tg-prod.oss-cn-beijing.aliyuncs.com/7d9846e7-9929-4316-b15e-0efb95668047.webp'
            "
          ></image>
        </view>
        我已阅读并同意
        <text @click="goUserAgreement" data-route="xieyi" class="txt"
          >《用户协议》</text
        >
        和
        <text @click="goUserPrivacy" data-route="zhengce" class="txt"
          >《隐私政策》</text
        >
      </div>
    </div>
  </div>
</template>

<script>
import { login, getPermission, getSchoolData } from "@/services/login";
export default {
  name: "loginIndex",
  components: {},
  data() {
    return {
      browse_actve: false,
      username: "",
      password: "",
      checkbox: false,
      checkboxVal: false,
      topHeight: 0,
      isLoading: false,
      clickTime: 0
    };
  },
  computed: {
    login_btn_disable(data) {
      return this.username === "" || this.password === "";
    }
  },
  methods: {
    checkboxChange(val) {
      this.checkboxVal = !this.checkboxVal;
    },
    browseIconShow() {
      this.browse_actve = !this.browse_actve;
    },
    getSchool() {
      return new Promise((resolve, reject) => {
        getSchoolData()
          .then((res) => {
            resolve(res);
          })
          .catch((err) => {
            reject(err);
          });
      });
    },
    getUserPermission() {
      return new Promise((resolve, reject) => {
        getPermission()
          .then((res) => {
            resolve(res);
          })
          .catch((err) => {
            reject(err);
          });
      });
    },
    toHome() {
      uni.redirectTo({ url: "/pages/teacher/sort/index" });
    },
    goUserAgreement() {
      uni.redirectTo({ url: "/pages/teacher/subpages/agreement/user" });
    },
    goUserPrivacy() {
      console.log(1);
      uni.redirectTo({ url: "/pages/teacher/subpages/agreement/privacy" });
    },
    backIndex() {
      uni.redirectTo({ url: "/pages/index/index" });
    },
    userLogin() {
      const { username, password } = this;
      const open_id = uni.getStorageSync("openid") || "";
      console.log("open_id :>> ", open_id);
      this.$ljsPublic.msg.loading("登录中...");
      login({
        auth_type: "password",
        login_type: "miniprogram",
        password,
        username,
        open_id
      }).then((res) => {
        console.log("8989 :>> ", res);
        const { token, full_office_post } = res;
        uni.setStorageSync("token", token);
        uni.setStorageSync("tokenTime", new Date().getTime());
        uni.setStorageSync("user", res);
        uni.setStorageSync("portType", "TEACHER");
        const { getUserPermission, getSchool } = this;
        Promise.all([getUserPermission(), getSchool()])
          .then((result) => {
            uni.setStorageSync("permission", result[0]);
            uni.setStorageSync("schoolData", result[1].school_data);
            // const checkedSchool = uni.getStorageSync("checkedSchool");
            const arr = [];
            // if (!checkedSchool) {
            result[1].school_data.forEach((item1) => {
              item1.child.forEach((item2) => {
                const { id, name } = item2;
                arr.push({
                  id,
                  name
                });
              });
            });
            // 多校区时，默认设置第一个
            const { department_id, department_name, department_type } =
              full_office_post;
            const defaultSchool = {
              id: department_id,
              name: department_name
            };
            console.log(result[1].school_data);
            const { name, id } = result[1].school_data[0].child[0];
            const firstSchool = { name, id };
            console.log(
              result[1].school_data[0].child[0],
              department_type,
              "result[1].school_data-----login"
            );
            uni.setStorageSync(
              "checkedSchool",
              department_type !== "school" ? [firstSchool] : [defaultSchool]
            );
            // 设置单校区数据
            this.setSingSchool(result[1].school_data);
            // }

            this.$ljsPublic.msg.msg_success("登录成功！");
            setTimeout(() => {
              this.toHome();
            }, 1000);
          })
          .catch((e) => {
            console.log(e);
            this.$ljsPublic.msg.msg_success("登录失败！");
            this.isLoading = false;
          });
      });
    },
    setSingSchool(school_data) {
      if (school_data) {
        if (school_data[0].child && school_data[0].child.length === 1) {
          uni.setStorageSync("singSchoolData", school_data[0].child[0]);
        }
      }
    },
    handleLogin() {
      const now = Date.now();
      if (this.isLoading || now - this.clickTime < 2000) {
        return;
      }

      this.clickTime = now;
      this.isLoading = true;

      this.login();

      setTimeout(() => {
        this.isLoading = false;
      }, 2000);
    },
    login() {
      if (!this.checkboxVal) {
        this.$ljsPublic.msg.msg_error("请勾选用户协议！");
        this.isLoading = false;
        return;
      }
      this.userLogin();
    },
    handleBack() {
      // 使用 uni.navigateBack 的返回值判断是否可以返回
      uni.navigateBack({
        delta: 1,
        fail: () => {
          uni.reLaunch({
            url: "/pages/index/index"
          });
        }
      });
    }
  },
  watch: {},

  // 页面周期函数--监听页面加载
  onLoad() {
    const windowInfo = uni.getWindowInfo();
    console.log(windowInfo.statusBarHeight);
    this.topHeight = windowInfo.statusBarHeight + 10;
  },
  // 页面周期函数--监听页面初次渲染完成
  onReady() {},
  // 页面周期函数--监听页面显示(not-nvue)
  onShow() {},
  // 页面周期函数--监听页面隐藏
  onHide() {},
  // 页面周期函数--监听页面卸载
  onUnload() {}
  // 页面处理函数--监听用户下拉动作
  // onPullDownRefresh() { uni.stopPullDownRefresh(); },
  // 页面处理函数--监听用户上拉触底
  // onReachBottom() {},
  // 页面处理函数--监听页面滚动(not-nvue)
  // onPageScroll(event) {},
  // 页面处理函数--用户点击右上角分享
  // onShareAppMessage(options) {},
};
</script>

<style lang="scss" scoped>
.tg_login_page {
  position: relative;
  min-height: 100vh;
  background-image: url("https://tg-prod.oss-cn-beijing.aliyuncs.com/5807acde-14fa-4746-b32b-182bc0108a64.webp");
  background-size: 100% 100%;
  background-repeat: no-repeat;
  padding: 48rpx;
  .tg_navigation_bar {
    position: fixed;
    top: 112rpx;
    left: 32rpx;
    z-index: 100;
    .back-icon {
      width: 40rpx;
      height: 40rpx;
      flex-shrink: 0;
    }
  }
  .back-index {
    position: absolute;
    top: 100rpx;
    left: 50rpx;
  }
  .tg_logo {
    position: absolute;
    top: 220rpx;
    left: 50rpx;
    width: 78rpx;
    height: 78rpx;
    background-color: #fff;
    border-radius: 16rpx;
    .tg_logo_img {
      width: 42rpx;
      height: 62rpx;
    }
  }
  .tg_top_bg_img {
    position: absolute;
    top: 170rpx;
    right: 0rpx;
    background-image: url("https://tg-dev.oss-cn-beijing.aliyuncs.com/d65f3fda-95b0-4d81-8587-b9305f6f76fb.png");
    width: 350rpx;
    height: 376rpx;
    display: block;
    background-size: cover;
    background-repeat: no-repeat;
  }
  .tg_login_summary {
    margin-top: 222rpx;
    display: flex;
    flex-direction: column;
    .txt1 {
      color: #333;
      font-size: 44rpx;
      font-weight: 600;
      line-height: 56rpx;
      margin-bottom: 14rpx;
    }
    .txt2 {
      color: #979797;
      font-size: 26rpx;
      font-weight: 400;
      line-height: 39rpx;
    }
  }
  .tg_login_content {
    margin-top: 136rpx;
    .tg_input_row {
      width: 654rpx;
      height: 104rpx;
      border-radius: 60px;
      background: #fff;
      margin-bottom: 50rpx;
      padding: 34rpx 32rpx;
      display: flex;
      align-items: center;
      position: relative;
      input {
        flex: 1;
        height: 100%;
        font-size: 32rpx;
        color: #333;
      }
      .tg_login_acc,
      .tg_login_lock {
        position: absolute;
        top: 30rpx;
        left: 0;
      }
      .tg_login_browse {
        position: absolute;
        top: 30rpx;
        right: 34rpx;
      }
      ::v-deep input {
        font-size: 36rpx !important;
        font-style: normal !important;
      }
    }
  }
  .tg_login_footer {
    // position: absolute;
    // left: 0;
    // bottom: 76rpx;
    // left: 0;
    margin-top: 28rpx;
    width: 100%;
    text-align: center;
    color: #999;
    font-size: 24rpx;
    line-height: 40rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: 400;
    .checkbox {
      width: 50rpx;
      height: 50rpx;
      display: flex;
      align-items: center;
      margin-right: -6rpx;
      image {
        width: 32rpx;
        height: 32rpx;
      }
    }
    .txt {
      color: #fb0;
      font-weight: 500;
    }
  }
}
::v-deep .u-button--disabled {
  opacity: 1 !important;
}
::v-deep .u-checkbox__icon-wrap {
  border-color: #ffc525 !important;
}
</style>
