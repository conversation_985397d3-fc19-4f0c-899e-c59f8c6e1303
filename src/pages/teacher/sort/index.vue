<template>
  <div class="tg_sort">
    <div class="tg-top-bg"></div>
    <!-- <u-sticky :zIndex="66" :customNavHeight="44"> -->
    <u-navbar
      :placeholder="true"
      bgColor="transparent"
      title="功能"
      titleStyle="color: #fff;font-size:36rpx;font-weight:500"
    >
      <div class="u-nav-slot" slot="left"></div>
    </u-navbar>
    <!-- </u-sticky> -->

    <div class="sort-accordion">
      <u-collapse :border="false" :value="['1', '2', '3', '4']">
        <u-collapse-item title="常用功能" name="1">
          <div class="u-collapse-content">
            <div class="collapse-gird">
              <div
                v-if="$hasPermission(['customer_create'])"
                @click="toIntendedCustomer"
                class="box"
              >
                <image src="@/static/meau_3.png" />
                <span>新增意向客户</span>
              </div>
              <div
                v-if="$hasPermission(['scheduling_list'])"
                @click="goScheduleManagement"
                class="box"
              >
                <image src="@/static/meau_4.png" />
                <span>排课管理</span>
              </div>
              <div
                v-if="$hasPermission(['scheduling_roll_call_list'])"
                @tap="goSchedule"
                class="box"
              >
                <image src="@/static/meau_2.png" />
                <span>课表管理</span>
              </div>
            </div>
          </div>
        </u-collapse-item>
        <u-collapse-item title="市场管理" name="4">
          <div class="u-collapse-content">
            <view class="collapse-gird">
              <view
                v-if="$hasPermission(['customer_list'])"
                @click="goCustomerList"
                class="box"
              >
                <image src="@/static/meau_5.png" />
                <span>意向客户管理</span>
              </view>
            </view>
          </div>
        </u-collapse-item>
        <u-collapse-item title="教务管理" name="2">
          <div class="u-collapse-content">
            <view class="collapse-gird">
              <view
                v-if="$hasPermission(['scheduling_list'])"
                @click="goScheduleManagement"
                class="box"
              >
                <image src="@/static/meau_4.png" />
                <span>排课管理</span>
              </view>
              <view
                v-if="$hasPermission(['scheduling_roll_call_list'])"
                @tap="goSchedule"
                class="box"
              >
                <image src="@/static/meau_2.png" />
                <span>课表管理</span>
              </view>
              <view
                v-if="$hasPermission(['flower_name_list'])"
                @tap="goFlowerName"
                class="box"
              >
                <image src="@/static/meau_1.png" />
                <span>花名册</span>
              </view>
              <view
                v-if="$hasPermission(['niedao_circle_list'])"
                @tap="goNiedaoCircle"
                class="box"
              >
                <image
                  src="https://tg-prod.oss-cn-beijing.aliyuncs.com/615799b8-a9e6-47b2-ae12-d32aa6053b4d.png"
                />
                <span>聂道圈</span>
              </view>
              <view
                v-if="$hasPermission(['time_album_meau'])"
                @tap="goTimeAlbum"
                class="box"
              >
                <image
                  src="https://tg-prod.oss-cn-beijing.aliyuncs.com/86a14eea-e7c2-4461-95e9-71998e731c5a.png"
                />
                <span>时光相册</span>
              </view>
            </view>
          </div>
        </u-collapse-item>
        <!-- <u-collapse-item title="财务管理" name="3">
          <div class="u-collapse-content">
            <view class="coming-soon"> 功能开发中，敬请期待! </view>
          </div>
        </u-collapse-item> -->
      </u-collapse>
    </div>
    <TabBar :tabIndex="tabIndex"></TabBar>
  </div>
</template>

<script>
import TabBar from "../components/tab-bar/index.vue";
export default {
  name: "sortIndex",
  components: { TabBar },
  data() {
    return {
      tabIndex: 0
    };
  },
  computed: {},
  methods: {
    toIntendedCustomer() {
      uni.navigateTo({
        url: "/pages/teacher/subpages/customer/addCustomer"
      });
    },
    goCustomerList() {
      uni.navigateTo({
        url: "/pages/teacher/subpages/customer/customerList"
      });
    },
    goScheduleManagement() {
      uni.navigateTo({
        url: "/pages/teacher/subpages/schoolServiceScheduling/list"
      });
    },
    //
    goSchedule() {
      uni.navigateTo({
        url: "/pages/teacher/subpages/schoolServiceScheduling/schedule"
      });
    },
    // 花名册
    goFlowerName() {
      uni.navigateTo({
        url: "/pages/teacher/subpages/flowerNameList/classroom"
      });
    },
    goNiedaoCircle() {
      uni.navigateTo({
        url: "/pages/teacher/subpages/niedaoCircle/index"
      });
    },
    goTimeAlbum() {
      uni.navigateTo({
        url: "/pages/teacher/subpages/customer/regularStudent"
      });
    }
  },
  watch: {},

  // 页面周期函数--监听页面加载
  onLoad() {
    const tabIndex = uni.getStorageSync("teacher_tabIndex");
    this.tabIndex = tabIndex || 0;
  },
  // 页面周期函数--监听页面初次渲染完成
  onReady() {},
  // 页面周期函数--监听页面显示(not-nvue)
  onShow() {},
  // 页面周期函数--监听页面隐藏
  onHide() {},
  // 页面周期函数--监听页面卸载
  onUnload() {}
  // 页面处理函数--监听用户下拉动作
  // onPullDownRefresh() { uni.stopPullDownRefresh(); },
  // 页面处理函数--监听用户上拉触底
  // onReachBottom() {},
  // 页面处理函数--监听页面滚动(not-nvue)
  // onPageScroll(event) {},
  // 页面处理函数--用户点击右上角分享
  // onShareAppMessage(options) {},
};
</script>

<style lang="scss" scoped>
.tg_sort {
  background: #f5f6fa;
  min-height: 100vh;
  padding-bottom: 80px;
  overflow-x: hidden;
  position: relative;
}
.sort-accordion {
  position: relative;
  z-index: 2;
  width: 700rpx;
  margin: 0 auto;

  ::v-deep .u-collapse-item {
    background-color: #fff;
    margin-bottom: 20rpx;
    border-radius: 10rpx;
    overflow: hidden;
    .u-cell {
      position: relative;
      &::before {
        position: absolute;
        background-color: #ffbf0d;
        width: 3px;
        height: 13px;
        top: 15px;
        content: "";
        left: 30rpx;
        border-radius: 20rpx;
      }
    }
    .u-cell__body {
      padding-left: 50rpx;
      border-bottom: 1rpx solid rgba(211, 220, 230, 0.48);
    }
    .u-icon__icon--info {
      font-size: 24rpx !important;
      color: #636469 !important;
    }
  }
  .collapse-gird {
    display: flex;
    flex-wrap: wrap;
    align-items: flex-start;
    padding-left: 0;
    .box {
      width: 25%;
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 16rpx 10rpx;
      box-sizing: border-box;
      image {
        width: 48rpx;
        height: 48rpx;
        // background-color: #8492a6;
      }
      span {
        font-size: 24rpx;
        color: #8492a6;
        margin-top: 16rpx;
        font-weight: 500;
      }
    }
  }
  .coming-soon {
    text-align: center;
    font-size: 22rpx;
    color: #8492a6;
  }
  ::v-deep .u-cell__title-text {
    font-weight: 500;
  }
}
</style>
