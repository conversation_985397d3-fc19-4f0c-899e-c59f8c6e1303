<template>
  <div>
    <web-view
      @load="pageLoad"
      :webview-styles="webviewStyles"
      @message="handleMessage"
      :src="webViewSrc"
    ></web-view>
  </div>
</template>

<script>
export default {
  name: "richTextIndex",
  data() {
    return {
      webviewStyles: {
        progress: {
          color: "#FF3333"
        }
      },
      webViewSrc: "",
      option: {},
      shareData: {},
      guideBottom: 0,
      guideLeft: 0,
      baseUrl: process.env.VUE_APP_TG_HOST,
      // baseUrl: "http://**************:8080",
      student_name: "",
      feedback_type: "",
      classroom_id: "",
      scheduling_id: ""
    };
  },
  onShareAppMessage() {
    const { feedback_id, student_id, pageTitle, teacher_name } = this.option;
    console.log(this.option, this.shareData, !!this.shareData, "this.option");
    if (this.shareData.teacher_name) {
      return this.shareData;
    } else {
      return {
        title: this.student_name
          ? this.student_name + "的" + pageTitle
          : teacher_name + "发布的" + pageTitle,
        // path: `/pages/teacher/subpages/richtext/index?openEdit=1&openPreview=1&isShare=1&feedback_id=${feedback_id}&student_id=${student_id}&pageTitle=${pageTitle}`,
        path: `/pages/student/subpages/richtext/index?type=class&feedback_id=${feedback_id}&student_id=${student_id}&pageTitle=${pageTitle}&student_name=${this.student_name}&feedback_type=${this.feedback_type}&classroom_id=${this.classroom_id}&scheduling_id=${this.scheduling_id}&teacher_name=${teacher_name}`,
        imageUrl:
          "https://tg-prod.oss-cn-beijing.aliyuncs.com/f860ad82-95fd-49c4-a8de-c629f069411b.png"
      };
    }
  },
  onLoad(option) {
    // wx?.onMessage((res) => {
    //   console.log(res, "res");
    //   if (res?.data?.type === "shareToFriend") {
    //     this.shareData = res.data.payload;
    //   }
    // });
    this.calcPosition();
    uni.showLoading({
      title: "加载中..."
    });
    const roleKey = {
      default: 1,
      student: 3,
      customer: 2
    };
    const visitor = roleKey[uni.getStorageSync("session").role] ?? 1;
    option.visitor = visitor;
    option.token = uni.getStorageSync("token");
    this.student_name = option.student_name || "";
    this.feedback_type = option.pageType;
    this.classroom_id = option.classroom_id;
    this.scheduling_id = option.scheduling_id;
    console.log(option);
    this.option = option;
    const urlParams = uni.$u.queryParams(option);
    this.webViewSrc = `${this.baseUrl}/mini-richText${urlParams}&guideBottom=${this.guideBottom}&guideLeft=${this.guideLeft}`;
    console.log(this.webViewSrc, "this.webViewSrc");
    uni.$u.sleep(3000).then(() => {
      uni.hideLoading();
    });
    // console.log(this.webViewSrc);
  },
  methods: {
    calcPosition() {
      uni.getSystemInfo({
        success: (res) => {
          const menuButton = uni.getMenuButtonBoundingClientRect();
          const systemInfo = uni.getSystemInfoSync();

          const pxPerRpx = systemInfo.windowWidth / 750;
          const imgWidth = 575 * pxPerRpx;
          const popupPadding = 36 * pxPerRpx + 43 * pxPerRpx;
          const popupTitleMarginBottom = 55 * pxPerRpx;
          // 计算图片 bottom = 屏幕高度 - 菜单按钮 bottom - 安全区域底部 - 弹窗padding - 弹窗标题marginBottom
          console.log(res.safeAreaInsets, "res.safeAreaInsets");
          this.guideBottom =
            systemInfo.windowHeight -
            menuButton.bottom -
            res.safeAreaInsets.bottom -
            popupPadding -
            popupTitleMarginBottom;

          // 可调位置：比如右对齐、靠右侧留 16px
          this.guideLeft = systemInfo.windowWidth - imgWidth - 16;
          console.log(this.guideBottom, this.guideLeft, "guideBottom");
        }
      });
    },
    pageLoad(e) {
      uni.hideLoading();
    },
    handleMessage({ detail }) {
      this.shareData = detail.data[0].payload;
      console.log(detail.data[0].payload, "detail");
    }
  }
};
</script>

<style lang="scss" scoped></style>
