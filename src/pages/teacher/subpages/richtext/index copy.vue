<template>
  <div class="bottom-safe-area rich-text-box">
    <u-toast ref="uToast"></u-toast>
    <u-loading-page
      bg-color="#00000091"
      :loading="uploadLoading"
      fontSize="12"
      color="#fff"
      loading-mode="spinner"
      loading-text="图片上传中..."
    ></u-loading-page>
    <!-- <div class="top-bg"></div> -->
    <u-navbar
      :placeholder="true"
      bgColor="#3061f2"
      title="编辑内容"
      titleStyle="color: #fff;font-size:36rpx;font-weight:500"
      leftIconSize="20px"
      leftIconColor="#fff"
      :autoBack="false"
      @leftClick="back"
    ></u-navbar>
    <view class="tg_sp-editor-box" :style="'height:' + height + 'px'">
      <div class="data-bank" @click="toGoDataBank">
        <span></span>
        <image
          class="data-bank-icon"
          src="https://tg-prod.oss-cn-beijing.aliyuncs.com/c8291bd9-8e0c-411b-a0eb-68cb3fc85309.png"
          mode="scaleToFill"
        />
      </div>
      <div :style="'height:' + (height - 100) + 'px'" class="tg_sp-editor">
        <sp-editor
          :toolbar-config="toolbarList"
          placeholder="请在此处输入内容 ..."
          ref="editorRef"
          @upinImage="upinImage"
          @upinVideo="upinVideo"
          @init="initEditor"
        ></sp-editor>
      </div>
      <div class="ctrl-box">
        <span @tap="previewClick" class="preview">预览</span>
        <span @tap="beforSend" class="send">发送</span>
      </div>
    </view>
    <u-modal
      content="确定发送内容？"
      @confirm="confirmTips"
      @cancel="tips_visible = false"
      :showCancelButton="true"
      :show="tips_visible"
      confirmText="发送"
      cancelText="取消"
      width="500rpx"
    ></u-modal>
    <u-overlay :show="isShowDataBankModal" @click="isShowDataBankModal = false">
      <!-- <video
        v-else-if="item.category_id === '2'"
        id="myVideo"
        style="width: 216rpx; height: 100%; border-radius: 16rpx;"
        :src="item.content"
        @error="videoErrorCallback"
        :controls="false"
      >
      </video> -->
      <view class="data-bank-wrap bottom-safe-area" @tap.stop>
        <div class="data-bank">
          <div class="tab-list">
            <div
              :class="[
                'tab-item',
                params.category_id === item.key ? 'active' : ''
              ]"
              @click="tabclick(item)"
              v-for="item in tab_list"
              :key="item.key"
            >
              {{ item.name }}
            </div>
          </div>
          <div style="padding: 20rpx 25rpx; background: #f5f6fa">
            <u--input
              :placeholder="`搜索${types[params.category_id]}`"
              shape="circle"
              :customStyle="searchInputSty"
              @blur="search"
              v-model="params.name"
              prefixIcon="search"
              prefixIconStyle="font-size: 22px;color: #909399"
            ></u--input>
          </div>
          <div class="data-list">
            <u-list v-if="dataList.length" @scrolltolower="scrolltolower">
              <u-list-item v-for="(item, index) in dataList" :key="index">
                <div class="data-item">
                  <div class="data-name">{{ item.name }}</div>
                  <div class="content">
                    <u-album
                      v-if="item.category_id === '1'"
                      :urls="[item.content]"
                    >
                    </u-album>
                    <div v-else class="text">
                      {{ item.content }}
                    </div>
                  </div>
                  <div
                    class="preview"
                    @click="handlePreview(item)"
                    v-if="item.category_id !== '1'"
                  >
                    预览
                  </div>
                  <div
                    @click="handleClickItem(item, index)"
                    :class="[
                      'checkbox',
                      curIndex === index ? 'is-checked' : ''
                    ]"
                  >
                    <image
                      v-if="curIndex === index"
                      style="width: 16rpx; height: 16rpx"
                      src="https://tg-prod.oss-cn-beijing.aliyuncs.com/748e7d15-0582-44a1-894d-9c4509c6a926.png"
                      mode="scaleToFill"
                    />
                  </div>
                </div>
              </u-list-item>
              <u-loadmore :status="status" />
            </u-list>
            <u-empty text="暂无资料~" v-else> </u-empty>
          </div>
          <div class="footer">
            <u-button type="primary" @click="save" text="确定"></u-button>
          </div>
        </div>
      </view>
    </u-overlay>
    <u-popup
      :show="isShowText"
      @tap.stop
      :round="10"
      mode="bottom"
      closeable
      @close="close"
    >
      <view style="height: 700rpx; overflow: auto; margin-top: 60rpx">
        <div style="padding: 0 60rpx">{{ curContent }}</div>
      </view>
    </u-popup>
  </div>
</template>

<script>
import { feedbackSend, feedbackKindsSend } from "@/services/flowerNameList";
import { getList } from "@/services/dataBank";
export default {
  name: "richTextIndex",
  components: {},
  data() {
    return {
      uploadUrl: "",
      isShowText: false,
      curContent: "",
      header: "",
      formData: "",
      content: "",
      editorIns: null,
      uploadLoading: false,
      isShowDataBankModal: false,
      tips_visible: false,
      toolbarList: {
        keys: [
          "header", // 标题
          "bold", // 加粗
          "alignLeft", // 左对齐
          "alignCenter", // 居中对齐
          "alignRight", // 右对齐
          "alignJustify", // 两端对齐
          // "fontSize", // 字号
          "color", // 文字颜色,
          "image" // 图片
        ],
        iconSize: "20px" // 工具栏字体大小
      },
      height: "100vh",
      topHeight: 0,
      current: 0,
      types: {
        1: "图片",
        2: "视频",
        3: "文案"
      },
      searchInputSty: {
        border: "1px solid #FFBF0D"
      },
      /*
        {
          name: "视频",
          key: "2",
        },
      */
      tab_list: [
        {
          name: "图片",
          key: "1"
        },
        {
          name: "文案",
          key: "3"
        }
      ],
      status: "nomore",
      dataList: [],
      count: 0,
      params: {
        category_id: "1",
        page: 1,
        page_size: 10,
        name: ""
      },
      options: {},
      curItem: {},
      curIndex: ""
    };
  },
  computed: {},
  methods: {
    getMessage(e) {
      console.log("接收webview发来的数据", e.detail);
      // 处理接收到的数据
    },
    initEditor(editor) {
      this.editorIns = editor;
      const { dataType, richtextData } = this.options;
      console.log(richtextData);
      if (dataType === "1") {
        this.insetImg(richtextData);
      } else if (dataType === "2") {
        /* empty */
      } else if (dataType === "3") {
        this.insetText(richtextData);
      }
      // setTimeout(() => {
      //   this.upinVideo();
      // }, 2000)
    },
    msgToast(msg, type) {
      uni.hideToast();
      this.$refs.uToast.show({
        message: msg,
        type,
        duration: 10 * 1000
      });
    },
    back() {
      uni.navigateBack();
    },
    async toGoDataBank() {
      console.log(1);
      this.isShowDataBankModal = true;
      // const { errMsg, html } = await this.editorIns.getContents()
      // if (errMsg === "ok") {
      //   uni.setStorageSync("richtextData", html);
      // }
      // const params = uni.$u.queryParams(this.options);
      // uni.redirectTo({
      //   url: `/pages/teacher/subpages/dataBank/index${params}`
      // });
    },
    insetImg(src) {
      this.editorIns.insertImage({
        src,
        width: "80%"
      });
    },
    insetText(content) {
      console.log(content);
      this.editorIns.insertText({
        text: content
      });
    },
    upinVideo() {
      console.log(this.$refs.editorRef);
      this.$refs.editorRef.editorCtx.insertImage({
        src: "https://tg-dev.oss-cn-beijing.aliyuncs.com/1581727416858_.pic_a48b70e3-7c21-4160-9a43-ef9e56f52b88.jpg",
        width: "80%", // 默认不建议铺满宽度100%，预留一点空隙以便用户编辑
        alt: "https://tg-dev.oss-cn-beijing.aliyuncs.com/黑猫警长测试视频_d66c7804-0915-4150-a7cc-a75bf4946f1b_91923189-db72-4fc4-a371-55806a7219a6.mp4",
        success: function () {}
      });
    },
    upinImage(tempFiles, editorCtx) {
      // 注意微信小程序的图片路径是在tempFilePath字段中
      console.log(tempFiles, editorCtx);
      // const file_extension = tempFiles[0].split(".").pop();
      const size = tempFiles[0].size;
      const type = tempFiles[0].fileType;
      const isLt2M = size / 1024 / 1024 < 2;
      if (!isLt2M && type.indexOf("image") !== -1) {
        this.$refs.uToast.show({
          message: "上传图片大小不能超过2MB!",
          type: "error"
        });
        return;
      }
      if (tempFiles[0].fileType !== "video") {
        this.Oss.uploadFile(tempFiles[0], (res) => {
          console.log("res :>> ", res);
          editorCtx.insertImage({
            src: res,
            width: "100%", // 默认不建议铺满宽度100%，预留一点空隙以便用户编辑
            success: function () {}
          });
        });
      } else {
        this.$refs.uToast.show({
          message: "上传文件格式有误",
          type: "error"
        });
      }
      // for (let index = 0; index < tempFiles.length; index++) {
      //   const images = tempFiles[index];

      //   if (images.tempFilePath) {
      //     that.uploadLoading = true;
      //     // 获取文件后缀类型
      //     const file_extension = images.tempFilePath.split(".").pop();
      //     uni.uploadFile({
      //       url: `${process.env.VUE_APP_BASE_API}/api/school-service/image/upload`,
      //       filePath: images.tempFilePath,
      //       name: "image_file",
      //       header: {
      //         token: uni.getStorageSync("token")
      //       },
      //       formData: {
      //         image_type: file_extension
      //       },
      //       success: (uploadFileRes) => {
      //         that.uploadLoading = false;
      //         const { statusCode, data: resp } = uploadFileRes;

      //         if (statusCode === 200) {
      //           const result = JSON.parse(resp);
      //           const { code, data } = result;
      //           if (code === 0) {
      //             editorCtx.insertImage({
      //               src: data.image_url,
      //               width: "100%", // 默认不建议铺满宽度100%，预留一点空隙以便用户编辑
      //               success: function () {}
      //             });
      //           } else {
      //             that.msgToast("图片上传失败，请重试！", "error");
      //           }
      //         } else {
      //           that.msgToast("图片上传失败，请重试！", "error");
      //         }
      //       },
      //       fail: (err) => {
      //         console.error(err);
      //         that.uploadLoading = false;
      //         that.msgToast("图片上传失败，请重试！", "error");
      //       }
      //     });
      //   }
      // }
    },
    // 判断内容是否为空
    isHTMLContentEmpty(html) {
      console.log("html :>> ", html);
      if (html === "<p><br></p>") {
        uni.showToast({
          title: "请输入内容",
          icon: "none"
        });
      }
      return html === "<p><br></p>";
    },
    previewClick() {
      const _this = this;
      this.editorIns.getContents().then((res) => {
        const html = res.html;
        if (_this.isHTMLContentEmpty(html)) {
          return;
        }
        uni.navigateTo({
          url: `/pages/teacher/subpages/flowerNameList/previewArticle`,
          success(res) {
            console.log(html, _this.options);
            res.eventChannel.emit("editorInsHtml", {
              html,
              options: _this.options
            });
          }
        });
      });
    },
    beforSend() {
      this.editorIns.getContents().then((res) => {
        const html = res.html;
        if (this.isHTMLContentEmpty(html)) {
          return;
        }
        this.tips_visible = true;
      });
    },
    confirmTips() {
      const sourcePages = ["classroom", "student"];
      if (sourcePages.includes(this.options.source)) {
        uni.$u.throttle(this.sendKindsClick, 1000);
      } else {
        uni.$u.throttle(this.sendClick, 1000);
      }
    },
    sendKindsClick() {
      this.editorIns.getContents().then(async (res) => {
        const content = res.html;
        const { classroom_id, classroom_name, source, type } = this.options;
        const paramsStudent =
          source === "classroom"
            ? []
            : JSON.parse(uni.getStorageSync("checkedStudents"));
        console.log(paramsStudent);
        const params = {
          content,
          classroom: {
            classroom_id,
            classroom_name
          },
          students: paramsStudent,
          type
        };
        const { code, message } = await feedbackKindsSend(params);
        if (code === 0) {
          uni.removeStorageSync("checkedStudents");
          uni.showToast({ title: "操作成功", icon: "success" });
          setTimeout(() => {
            uni.navigateBack();
          }, 1000);
        } else {
          uni.showToast({ title: message, icon: "error" });
        }
      });
    },
    sendClick() {
      const _this = this;
      this.editorIns.getContents().then((res) => {
        const html = res.html;
        const {
          teacher_id,
          student_id,
          classroom_id,
          classroom_name,
          teacher_name,
          student_gender,
          student_name
        } = _this.options;
        feedbackSend({
          classroom: {
            classroom_id,
            classroom_name
          },
          content: html,
          students: [
            {
              student_gender,
              student_id,
              student_name
            }
          ],
          teacher: {
            teacher_id,
            teacher_name
          }
        })
          .then((res) => {
            const { code, message } = res;
            this.tips_visible = false;
            if (code === 0) {
              uni.redirectTo({
                url: `/pages/teacher/subpages/flowerNameList/convoHistory?classroom_id=${classroom_id}`
              });
            } else {
              this.msgToast(message, "error");
            }
          })
          .catch(() => {
            this.msgToast("发送失败！", "error");
            this.tips_visible = false;
          });
      });
    },

    tabclick(item) {
      this.params.category_id = item.key;
      this.curIndex = "";
      this.dataList = [];
      this.getDataBankList();
    },
    search() {
      this.dataList = [];
      console.log(this.params);
      this.getDataBankList();
    },
    async getDataBankList() {
      this.status = "loading";
      const { code, data } = await getList(this.params);
      if (code === 0) {
        for (let i = 0; i < data.results.length; i++) {
          this.dataList.push(data.results[i]);
        }
        console.log(this.dataList);
        this.count = data.count;
      }
      this.status = "nomore";
    },
    handlePreview(item) {
      if (item.category_id === "3") {
        this.curContent = item.content;
        this.isShowText = true;
      }
    },
    handleClickItem(item, index) {
      if (this.curItem.file_name === item.file_name) {
        this.curItem = {};
        this.curIndex = "";
      } else {
        this.curIndex = index;
        this.curItem = item;
      }
    },
    close() {
      // this.isShowDataBankModal = false;
      this.isShowText = false;
    },
    scrolltolower() {
      if (this.dataList.length >= this.count) {
        this.status = "nomore";
        return;
      }
      this.listParams.page++;
      this.getList();
    },
    save() {
      // const img = (content) => `<img src="${content}" alt="" />`;
      // const text = (content) => `<p>${content}</p>`;
      if (this.curItem.category_id === "1") {
        this.insetImg(this.curItem.content);
      } else if (this.curItem.category_id === "2") {
        /* empty */
      } else {
        this.insetText(this.curItem.content);
      }
      this.curIndex = "";
      this.isShowDataBankModal = false;
    }
  },
  watch: {},

  // 页面周期函数--监听页面加载
  onLoad(options) {
    console.log("options :>> ", options);
    this.options = options;
    const winInfo = uni.getWindowInfo();
    console.log("winInfo :>> ", winInfo);
    const top = winInfo.statusBarHeight;
    const height = winInfo.windowHeight - (44 + top);
    this.height = height;
    console.log(uni.getStorageSync("richtextData"));
    this.getDataBankList();
    if (options.source === "dataBank") {
      setTimeout(() => {
        this.editorIns.setContents({
          html: uni.getStorageSync("richtextData")
        });
      }, 1000);
    }
  },
  // 页面周期函数--监听页面初次渲染完成
  onReady() {
    this.Oss.getAliyun();
  },
  // 页面周期函数--监听页面显示(not-nvue)
  onShow() {},
  // 页面周期函数--监听页面隐藏
  onHide() {},
  // 页面周期函数--监听页面卸载
  onUnload() {}
  // 页面处理函数--监听用户下拉动作
  // onPullDownRefresh() { uni.stopPullDownRefresh(); },
  // 页面处理函数--监听用户上拉触底
  // onReachBottom() {},
  // 页面处理函数--监听页面滚动(not-nvue)
  // onPageScroll(event) {},
  // 页面处理函数--用户点击右上角分享
  // onShareAppMessage(options) {},
};
</script>

<style lang="scss" scoped>
.rich-text-box {
  // height: 100vh;
  // overflow: scroll;
  // background: #f5f6fa;
  background: linear-gradient(180deg, #3061f2 0%, #659ef8 100%);
  height: 100vh;
  overflow: hidden;
  // .top-bg {
  //   position: absolute;
  //   top: 0;
  //   left: 0;
  //   background: linear-gradient(180deg, #3061f2 0%, #659ef8 100%);
  //   width: 750rpx;
  //   height: 50vh;
  // }
  ::v-deep .u-fade-enter-active {
    z-index: 9;
  }
  ::v-deep .sp-editor-toolbar {
    background: #fff;
    width: 600rpx;
    margin-left: 30rpx;
    margin-top: 30rpx;
    margin-bottom: 30rpx;
    border-radius: 18rpx;
    overflow: hidden;
    padding: 10rpx 30rpx !important;
    display: flex !important;
    justify-content: space-around !important;
    grid-template-columns: none !important;
    border-bottom: 0px !important;
  }
  ::v-deep .sp-editor-wrapper {
    padding: 40rpx;
    padding-bottom: 0;
    overflow: hidden;
    background: #fff;
    border-top-left-radius: 30rpx;
    border-top-right-radius: 30rpx;
  }
  ::v-deep .editor-container {
    border-radius: 20rpx;
    border: 2px solid #7f9cea;
    background: #fff;
    font-size: 14px !important;
    // height: calc(100% - 140rpx) !important;
    padding: 20rpx !important;
  }
  .tg_sp-editor-box {
    position: relative;
    display: flex;
    flex-direction: column;
    .tg_sp-editor {
      // height: 100%;
    }
    .data-bank {
      width: 80rpx;
      height: 93rpx;
      background: #fff;
      position: absolute;
      border-radius: 18rpx;
      right: 20rpx;
      top: 30rpx;
      z-index: 1;
      text-align: center;
      span {
        display: inline-block;
        width: 0;
        height: 100%;
        vertical-align: middle;
      }
      .data-bank-icon {
        width: 37rpx;
        height: 23px;
        margin: 0 auto;
        display: inline-block;
        vertical-align: middle;
      }
    }
    .ctrl-box {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 90rpx;
      width: 100%;
      height: 100px;
      border-bottom-left-radius: 30rpx;
      border-bottom-right-radius: 30rpx;
      background-color: #fff;
      span {
        width: 268rpx;
        height: 50px;
        display: block;
        border-radius: 50px;
        text-align: center;
        line-height: 50px;
        font-size: 34rpx;
        font-weight: 500;
        &.preview {
          color: #8492a6;
          background: rgba(211, 220, 230, 0.4);
        }
        &.send {
          color: #fff;
          background: linear-gradient(270deg, #3667f0 0%, #568ff5 100%);
        }
        &:active {
          opacity: 0.5;
        }
      }
    }
  }
}
.data-bank-wrap {
  width: 100%;
  height: 80%;
  background: #f5f6fa;
  position: absolute;
  // top: 50%;
  // left: 50%;
  // transform: translate(-50%, -50%);
  bottom: 0;
  border-radius: 40rpx;
  .data-bank {
    display: flex;
    flex-direction: column;
    height: 100%;
  }
  .tab-list {
    width: 100%;
    height: 80rpx;
    background: #fff;
    display: flex;
    padding: 0 20rpx;
    border-top-left-radius: 40rpx;
    border-top-right-radius: 40rpx;
    .tab-item {
      padding: 20rpx 40rpx;
      &.active {
        border-bottom: 3px solid #3c9cff;
      }
    }
  }
  .data-list {
    flex: 1;
    overflow: auto;
    padding: 20rpx;
    .data-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      background: #fff;
      border-radius: 20rpx;
      box-shadow: 0 2px 12rpx 0 rgba(0, 0, 0, 0.1);
      padding: 19rpx 42rpx;
      margin-bottom: 28rpx;
      .data-name {
        color: #475669;
        font-family: "PingFang SC";
        font-size: 32rpx;
        font-style: normal;
        font-weight: 500;
        width: 170rpx;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .content {
        margin: 0 48rpx;
        height: 180rpx;
        width: 216rpx;
        flex-shrink: 0;
        .text {
          display: flex;
          align-items: center;
          width: 100%;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 3;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
      .preview {
        color: #ffbf0d;
        font-family: "PingFang SC";
        font-size: 24rpx;
        font-style: normal;
        margin-right: 56rpx;
        font-weight: 500;
        flex-shrink: 0;
      }
      .checkbox {
        width: 32rpx;
        height: 32rpx;
        border-radius: 4px;
        border: 2px solid #bdbdbd;
        flex-shrink: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        &.is-checked {
          border-color: #ffbf0d;
        }
      }
    }
  }
  .footer {
    height: 60rpx;
    // border-bottom-left-radius: 40rpx;
    // border-bottom-right-radius: 40rpx;
  }
}
</style>

<style lang="scss">
::v-deep .u-album {
  height: 100%;
  width: 216rpx;
  .u-album__row {
    height: 100%;
    width: 100%;
    .u-album__row__wrapper {
      height: 100%;
      width: 100%;
      image {
        width: 100% !important;
        height: 100% !important;
        border-radius: 16rpx;
      }
    }
  }
}
</style>
