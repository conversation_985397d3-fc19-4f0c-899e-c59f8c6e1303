<template>
  <div
    class="dtbTeacherIndex"
    :style="{
      background: `url(${staticImg.bg}) no-repeat;`,
      'background-size': '100% 115%'
    }"
  >
    <!-- <div class="tg-top-bg"></div> -->
    <u-navbar
      :placeholder="false"
      bgColor="#fff"
      title="我的地推码"
      titleStyle="color: #000;font-size:36rpx;font-weight:500"
      :autoBack="true"
    >
    </u-navbar>
    <div class="top-bg">
      <div
        style="
          display: flex;
          justify-content: space-between;
          padding: 0 33rpx;
          align-items: flex-end;
        "
      >
        <div class="qipan">
          <img :src="staticImg.qipan" alt="" />
        </div>
        <div class="huli">
          <img :src="staticImg.iphuli" alt="" />
        </div>
      </div>
      <div class="bg2">
        <img :src="staticImg.bg2" alt="" />
      </div>
    </div>
    <div class="qrcode-wrap">
      <div style="text-align: center">
        <div class="qrcode-box" @longpress="longpress">
          <uv-qrcode
            :value="qrcode_opt.data"
            ref="qrcode"
            :foregroundImageBorderRadius="20"
            :backgroundImageBorderRadius="20"
            :size="size"
            :options="qrcode_opt"
            :auto="true"
            type="2d"
          ></uv-qrcode>
        </div>
      </div>
      <div class="info-list">
        <div class="info-item" v-for="item in infoList" :key="item.prop">
          <div class="dot"></div>
          <div class="label">{{ item.label }}</div>
          <div class="value">{{ item.value }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import QRCodeStyleRound from "@/uqrcode-plugin/round/uqrcode.plugin.round.es.js";
export default {
  name: "dtbTeacher",
  components: {},
  data() {
    return {
      staticImg: {
        bg: "https://tg-prod.oss-cn-beijing.aliyuncs.com/2c437a61-8106-49b1-b4a7-318583b02923.png",
        bg2: "https://tg-prod.oss-cn-beijing.aliyuncs.com/46d68db0-d3de-45d2-87dd-75e59a82c482.png",
        iphuli:
          "https://tg-prod.oss-cn-beijing.aliyuncs.com/7e5ba065-7566-42d9-8b5f-ff36b9e7d99d.png",
        qipan:
          "https://tg-prod.oss-cn-beijing.aliyuncs.com/9f967959-3492-420a-90f1-9287685edfb5.png"
      },
      size: "465rpx",
      infoList: [
        {
          label: "校区：",
          prop: "department_name",
          value: ""
        },
        {
          label: "岗位信息：",
          prop: "office_post_name",
          value: ""
        },
        {
          label: "员工姓名：",
          prop: "name",
          value: ""
        },
        {
          label: "联系方式：",
          prop: "mobile",
          value: ""
        },
        {
          label: "生成时间：",
          prop: "create_at",
          value: ""
        }
      ],
      // 二维码相关参数
      qrcode_opt: {
        // data: "",
        backgroundImageBorderRadius: 10,
        size: 261,
        useDynamicSize: true,
        errorCorrectLevel: "L",
        margin: 20,
        areaColor: "transparent",
        backgroundColor: "#ffffff",
        borderRadius: 10,
        style: "round",
        foregroundImageSrc:
          "https://tg-prod.oss-cn-beijing.aliyuncs.com/74f358be-dd75-457a-8616-e55e096f2285.png"
        // areaColor: "#F95F6B",
        // backgroundColor: "#3c9cff"
        // 指定二维码前景，一般可在中间放logo
        // foregroundImageSrc: "https://www.uvui.cn/common/logo.png"
      }
    };
  },
  computed: {},
  methods: {
    showQrcode() {
      // uni.showLoading()
      // setTimeout(function () {
      // uni.hideLoading()
      // _this.$refs.qrcode.crtQrCode();
      // }, 50);
    },
    findProp(prop, value) {
      for (let i = 0; i < this.infoList.length; i++) {
        const item = this.infoList[i];
        if (item.prop === prop) {
          item.value = value;
        }
      }
    },
    longpress() {
      this.$refs.qrcode.save({
        success: () => {
          uni.showToast({
            icon: "success",
            title: "保存成功，请在相册中查看地推码！"
          });
        }
      });
    },
    getWidth() {
      // 创建查询对象
      const query = uni.createSelectorQuery().in(this);
      // 选择元素并获取宽度
      query
        .select(".qrcode-box")
        .boundingClientRect((data) => {
          if (data) {
            console.log("元素的宽度:", data.width);
          }
        })
        .exec(); // 执行查询
    }
  },
  watch: {},

  // 页面周期函数--监听页面加载
  onLoad(options) {
    console.log(options, "options");
    const { employee_id, organization_id } = uni.getStorageSync("user");
    const openid = uni.getStorageSync("openid");
    const host = process.env.VUE_APP_TG_HOST;
    // tg.estar-go.com/miniprogram/dtb/?openid=oFb-75Hj6j196Cy8Wz-ROi3nZRJw&employee_id=1&organization_id=1&department_id=&department_name=&name=管理员
    // tg-dev.estar-go.com/miniprogram/dtb/?openid=oFb-75CcRKouarqysmLxOfvlsFnY&employee_id=cnk5gnfta6ec73e04fq0&organization_id=1
    // tg-dev.estar-go.com/miniprogram/dtb/?openid=oFb-75BcOua2x_6bxiAvOvQvZq8E&employee_id=chunffdsrj8s739m5rsg&organization_id=1&department_id=ci25edtsrj8s73e4nv4g&department_name=唐校长的校区
    this.qrcode_opt.data = `${host}/miniprogram/dtb/?openid=${openid}&employee_id=${employee_id}&organization_id=${organization_id}&department_id=${options.department_id}&department_name=${options.department_name}&name=${options.name}&channel_id=${options.channelId}&channel_on=${options.channelOn}`;
    console.log("qrcode_url :>> ", this.qrcode_opt.data);
    console.log(this.$refs.qrcodeBx);
    this.$refs.qrcode.registerStyle(QRCodeStyleRound);
    for (const i in options) {
      this.findProp(i, options[i]);
    }
    const create_at = this.$ljsPublic.date.formatTime(
      new Date().getTime(),
      "{y}-{m}-{d} {h}:{i}:{s}"
    );
    this.findProp("create_at", create_at);
  },
  mounted() {
    this.getWidth();
  },
  // 页面周期函数--监听页面初次渲染完成
  onReady() {
    console.log(this.$refs);
  },
  // 页面周期函数--监听页面显示(not-nvue)
  onShow() {},
  // 页面周期函数--监听页面隐藏
  onHide() {},
  // 页面周期函数--监听页面卸载
  onUnload() {}
  // 页面处理函数--监听用户下拉动作
  // onPullDownRefresh() { uni.stopPullDownRefresh(); },
  // 页面处理函数--监听用户上拉触底
  // onReachBottom() {},
  // 页面处理函数--监听页面滚动(not-nvue)
  // onPageScroll(event) {},
  // 页面处理函数--用户点击右上角分享
  // onShareAppMessage(options) {},
};
</script>

<style lang="scss" scoped>
.dtbTeacherIndex {
  height: 100vh;
  width: 100vw;
  padding-top: 200rpx;
  padding-bottom: 56rpx;
  background-size: 100% 130%;
  overflow-x: hidden;
  position: relative;

  // .tg-top-bg {
  //   border-radius: 50%;
  //   background: linear-gradient(180deg, #2956ef 0%, #69a2f8 100%);
  //   width: 1096rpx;
  //   height: 550rpx;
  //   position: absolute;
  //   left: 50%;
  //   top: 0rpx;
  //   transform: translate(-50%, -100rpx);
  //   z-index: 1;
  // }
  .top-bg {
    position: relative;
    margin-bottom: 50rpx;
    .qipan {
      display: inline-block;
      vertical-align: bottom;
      width: 287rpx;
      height: 270rpx;
      position: relative;
      overflow: hidden;
      img {
        width: 100%;
        height: 100%;
        width: 100%;
        height: 100%;
        position: absolute;
        bottom: -18px;
      }
    }
    .huli {
      display: inline-block;
      vertical-align: middle;
      width: 231rpx;
      height: 322rpx;
      position: relative;
      img {
        width: 100%;
        height: 100%;
        position: absolute;
        top: -9;
      }
    }
    .bg2 {
      position: absolute;
      bottom: 0;
      z-index: -1;
      width: 100%;
      height: 50px;
    }
  }
  .qrcode-wrap {
    padding-top: 30rpx;
    // height: 928rpx;

    // width: 100%;
    margin: 0 50rpx;
    border-radius: 48rpx;
    padding: 49rpx 56rpx;
    border: 8rpx solid #18bbff;
    background: #f9ffff;
    box-shadow: 0px 10px 4px 0px #f5fee1 inset, 0px -10px 4px 0px #b5e2ef inset,
      0px 4px 6px 0px #00a4d3;
    .qrcode-box {
      // width: 100%;
      width: 488rpx;
      height: 488rpx;
      border-radius: 22prx;
      border: 8rpx solid #00c6ff;
      opacity: 0.8;
      border-radius: 20px;
      overflow: hidden;
    }
    .info-list {
      margin-top: 20rpx;
      .info-item {
        display: flex;
        align-items: center;
        padding-bottom: 20rpx;
        margin-bottom: 20rpx;
        border-bottom: 1px dashed #00acf680;
        &:last-child {
          padding-bottom: 0;
          margin-bottom: 0;
          border-bottom: none;
        }
        .dot {
          width: 20rpx;
          margin-right: 16rpx;
          height: 20rpx;
          border-radius: 50%;
          background: #00acf6;
        }
        .label {
          font-size: 28rpx;
          color: #00acf6;
          font-weight: 500;
        }
        .value {
          color: #333;
          font-size: 28rpx;
          font-weight: 500;
        }
      }
    }
  }
}
</style>
