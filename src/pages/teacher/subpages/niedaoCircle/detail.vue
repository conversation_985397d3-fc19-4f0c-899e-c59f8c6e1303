<template>
  <div class="detail">
    <u-toast ref="uToast"></u-toast>
    <div class="detail-wrap">
      <div class="avatar">
        <img :src="momentDetail.title_img" alt="" />
      </div>
      <div class="content">
        <div class="name">{{ momentDetail.operator_name }}</div>
        <div class="value">{{ momentDetail.content }}</div>
        <div
          class="media"
          v-if="momentDetail.image_url && momentDetail.image_url.length"
        >
          <template v-if="momentDetail.image_type === 1">
            <div
              class="media-item"
              v-for="(imageItem, index) in momentDetail.image_url"
              :key="index"
            >
              <u-image
                :src="`${imageItem.url}${
                  imageItem.formatSize >= 10
                    ? '?x-oss-process=image/resize,w_500/quality,q_80'
                    : ''
                }`"
                radius="16rpx"
                @tap="handleImageClick(index, momentDetail)"
                :width="
                  momentDetail.image_url.length <= 1
                    ? getImgShowShape(momentDetail.image_url).width
                    : '150rpx'
                "
                :height="
                  momentDetail.image_url.length <= 1
                    ? getImgShowShape(momentDetail.image_url).height
                    : '150rpx'
                "
                :class="
                  momentDetail.image_url.length <= 1
                    ? 'single-img'
                    : 'multiple-img'
                "
                mode="aspectFill"
              >
                <template v-slot:loading>
                  <u-loading-icon size="14" color="#999"></u-loading-icon>
                </template>
              </u-image>
            </div>
          </template>
          <div
            class="video-wrap"
            :style="{
              width: getVideoShowShape(momentDetail).width,
              height: getVideoShowShape(momentDetail).height
            }"
            @tap="fullScreenPlayVideo"
            v-else-if="momentDetail.image_type === 2"
          >
            <img
              v-if="isShowPlayIcon"
              :src="playIcon"
              alt=""
              class="play-btn"
            />
            <video
              id="myVideo"
              :show-center-play-btn="false"
              direction="0"
              :controls="isShowControls"
              @fullscreenchange="fullscreenchange"
              object-fit="contain"
              class="video"
              :src="momentDetail.image_url[0].url"
              :poster="momentDetail.cover_url"
            >
              <view class="full-screen-mask" @tap="handleFullScreenMask"></view>
            </video>
          </div>
        </div>
        <div class="row-bar">
          <div class="post-time">
            <span>{{ momentDetail.publish_time }}</span>
            <div v-if="source !== 'share'" class="btns">
              <img
                src="https://tg-prod.oss-cn-beijing.aliyuncs.com/5b3b4f35-f5ba-4012-bf2a-7e16f59301b4.webp"
                v-if="momentDetail.top_status === 1"
                alt=""
              />
              <img
                :src="scopeIcon"
                v-if="!momentDetail.visibility_scope.includes('open')"
                @tap="handleScope(momentDetail)"
                alt=""
              />
              <img
                :src="deleteIcon"
                v-if="
                  $hasPermission(['niedao_circle_delete']) &&
                  isOneself === 'true'
                "
                @tap="handleDeleteBtn(momentDetail)"
                alt=""
              />
            </div>
          </div>
          <div class="controls">
            <div class="like">
              <div class="label">点赞数</div>
              <div class="text">
                {{ formatWNumber(momentDetail.likes_count) }}
              </div>
            </div>
            <!-- <div class="share" @tap="openSharePopup(momentDetail)">
              <div class="img">
                <img :src="shareIcon" alt="" />
              </div>
              <div class="text">分享</div>
            </div> -->
          </div>
        </div>
      </div>
    </div>
    <Confirm
      :visible="isShowDeletePopup"
      :content="deleteContent"
      :confirm-text="deleteButtonText"
      :cancel-text="cancelButtonText"
      @confirm="handleDelete(momentDetail)"
      @close="handleCloseDeletePopup"
    ></Confirm>
    <share-popup
      :showSharePopup="isShowSharePopup"
      :shareFriendInfo="currentMoment"
      webviewPath="tg-minigram/niedaoCircle/shareCard"
      :webviewParams="webviewParams"
      @closeSharePopup="closeSharePopup"
      @share="handleShare"
    ></share-popup>
  </div>
</template>

<script>
import Confirm from "@/components/confirm/index.vue";
import { momentDetail, momentDelete } from "@/services/niedaoCircle";
import { shareMomentsDetail } from "@/services/student/niedaoCircle";
import SharePopup from "@/components/sharePopup/index.vue";
export default {
  name: "tearcherNiedaoCircleDetail",
  components: {
    Confirm,
    SharePopup
  },
  data() {
    return {
      user: {},
      isShowSharePopup: false,
      isShowControls: false,
      isShowPlayIcon: true,
      videoContext: null,
      deleteContent: "删除本条内容？",
      deleteButtonText: "删除",
      cancelButtonText: "取消",
      visibilityScopeList: [
        {
          label: "公开",
          value: "open"
        },
        {
          label: "在读",
          value: "in_school"
        },
        {
          label: "休学",
          value: "out_school"
        },
        {
          label: "试听",
          value: "audition"
        },
        {
          label: "临时",
          value: "temp"
        },
        {
          label: "意向",
          value: "customer"
        }
      ],
      source: "",
      isShowDeletePopup: false,
      shareIcon:
        "https://tg-prod.oss-cn-beijing.aliyuncs.com/5e8ff99d-156c-4063-9ba0-509e0bb4170c.png",
      playIcon:
        "https://tg-prod.oss-cn-beijing.aliyuncs.com/7c8ff79b-619f-4842-b640-87504abe4ed1.png",
      deleteIcon:
        "https://tg-prod.oss-cn-beijing.aliyuncs.com/28e926b6-8e26-4570-91f8-4ad23ca9df23.png",
      scopeIcon:
        "https://tg-prod.oss-cn-beijing.aliyuncs.com/0eddff73-5a30-4ba8-ab61-df05f0c1fa2b.png",
      id: "",
      momentDetail: {},
      videoMuted: "",
      showSharePopup: false,
      currentMoment: {},
      webviewParams: {}
    };
  },
  computed: {},
  onShareAppMessage(res) {
    if (res.from === "button") {
      // 来自页面内分享按钮
      console.log(res.target);
    }

    // 来自页面内分享按钮
    const item = this.momentDetail;
    let imageUrl = "";
    if (item.image_type === 1) {
      if (item.image_url?.length) {
        imageUrl = item.image_url[0].url;
      } else {
        imageUrl =
          "https://tg-prod.oss-cn-beijing.aliyuncs.com/f860ad82-95fd-49c4-a8de-c629f069411b.png";
      }
    } else if (item.image_type === 2) {
      if (item.cover_url) {
        imageUrl = item.cover_url;
      } else {
        imageUrl =
          item.image_url[0].url +
          "?x-oss-process=video/snapshot,t_1000,f_jpg,w_0,h_0,m_fast";
      }
    }

    return {
      title: item.content || this.momentDetail.operator_name + "的聂道圈详情",
      imageUrl,
      path: `/pages/teacher/subpages/niedaoCircle/detail?id=${item.id}&source=share`
    };
  },
  onShareTimeline(res) {
    if (res.from === "button") {
      // 来自页面内分享按钮
      console.log(res.target);
    }
    let imageUrl = "";
    if (this.momentDetail.image_type === 1) {
      if (this.momentDetail.image_url?.length) {
        imageUrl = this.momentDetail.image_url[0].url;
      }
    } else if (this.momentDetail.image_type === 2) {
      imageUrl = this.momentDetail.cover_url;
    }
    this.isShowSharePopup = false;
    return {
      title:
        this.momentDetail.content ||
        this.momentDetail.operator_name + "的聂道圈详情",
      imageUrl,
      query: `id=${this.id}&source=share`,
      path: `/pages/teacher/subpages/niedaoCircle/detail`
    };
  },
  methods: {
    getDisplaySize(imgW, imgH) {
      // 判断类型
      if (imgH / imgW >= 3) {
        // 超长竖图
        return {
          width: 150,
          height: 454
        };
      } else if (imgW / imgH >= 3) {
        // 超长横图
        return {
          width: 454,
          height: 150
        };
      } else if (imgH > imgW) {
        // 普通竖图
        return {
          width: 256,
          height: 341
        };
      } else {
        // 普通横图
        return {
          width: 341,
          height: 256
        };
      }
    },
    getImgShowShape(image_url) {
      const { width, height } = image_url[0];
      const imgInfo = this.getDisplaySize(width, height);
      return {
        width: imgInfo.width + "rpx",
        height: imgInfo.height + "rpx"
      };
    },
    getVideoShowShape(item) {
      const { width, height } = item.image_url[0];
      return {
        width: width > height ? "341rpx" : "256rpx",
        height: width > height ? "256rpx" : "341rpx"
      };
    },
    formatWNumber(num) {
      if (num >= 10000) {
        return (num / 10000).toFixed(1) + "w";
      }
      return num.toString();
    },
    getMomentDetail() {
      momentDetail({ id: this.id }).then((res) => {
        console.log(res, "res");
        this.momentDetail = res.data;
      });
    },
    handleImageClick(current, urls) {
      uni.previewImage({
        current, // 当前点击的图片索引
        urls: urls.image_url.map((item) => item.url) // 所有图片的 URL 列表
      });
    },
    getShareMomentDetail() {
      shareMomentsDetail({
        id: this.id,
        student_id: this.user.id
      }).then((res) => {
        this.momentDetail = res.data;
        this.handleContentOpen(res.data);
      });
    },
    handleDeleteBtn(item) {
      this.isShowDeletePopup = true;
    },
    handleScope(item) {
      const message = item.visibility_scope
        .map(
          (i) => this.visibilityScopeList.find((item) => item.value === i).label
        )
        .join("、");
      this.$refs.uToast.show({
        type: "default",
        message
      });
    },
    openSharePopup(item) {
      console.log(item, "item");
      this.currentMoment = item;
      this.webviewParams = {
        id: item.id,
        token: this.user.token
      };
      this.isShowSharePopup = true;
    },
    closeSharePopup() {
      this.isShowSharePopup = false;
    },
    async handleDelete(item) {
      const res = await momentDelete({ id: item.id });
      if (res.code === 0) {
        this.$refs.uToast.show({
          type: "success",
          message: "删除成功"
        });
        uni.navigateBack();
      } else {
        this.$refs.uToast.show({
          type: "error",
          message: "删除失败"
        });
      }
    },
    handleCloseDeletePopup() {
      this.isShowDeletePopup = false;
    },
    fullScreenPlayVideo(index) {
      const videoContext = uni.createVideoContext(`myVideo`, this);
      console.log(index, this.videoContext, "videoContext");
      this.isShowControls = true;
      this.isShowPlayIcon = false;
      videoContext.requestFullScreen();
    },
    fullscreenchange({ detail }) {
      console.log(detail, "detail");
      const videoContext = uni.createVideoContext(`myVideo`, this);
      if (detail.fullscreen) {
        videoContext.play();
        this.isShowPlayIcon = false;
        this.isShowControls = true;
      } else {
        videoContext.pause();
        this.isShowControls = false;
        this.isShowPlayIcon = true;
      }
    },
    handleFullScreenMask() {
      // 关闭全屏
      this.videoContext.exitFullScreen();
    }
  },
  watch: {},

  // 页面周期函数--监听页面加载
  onLoad(options) {
    this.id = options.id;
    this.isOneself = options.isOneself;
    console.log(options, "options");
    this.user = uni.getStorageSync("user");
    this.source = options.source;
    if (options.source === "share") {
      this.getShareMomentDetail();
    } else {
      this.getMomentDetail();
    }
  },
  // 页面周期函数--监听页面初次渲染完成
  onReady() {},
  // 页面周期函数--监听页面显示(not-nvue)
  onShow() {},
  // 页面周期函数--监听页面隐藏
  onHide() {},
  // 页面周期函数--监听页面卸载
  onUnload() {}
  // 页面处理函数--监听用户下拉动作
  // onPullDownRefresh() { uni.stopPullDownRefresh(); },
  // 页面处理函数--监听用户上拉触底
  // onReachBottom() {},
  // 页面处理函数--监听页面滚动(not-nvue)
  // onPageScroll(event) {},
  // 页面处理函数--用户点击右上角分享
  // onShareAppMessage(options) {},
};
</script>

<style lang="scss" scoped>
.detail {
  padding: 29rpx 32rpx;
  border-bottom: 1rpx solid #eee;
  .detail-wrap {
    display: flex;
  }
  .avatar {
    flex-shrink: 0;
    width: 85rpx;
    height: 85rpx;
    img {
      width: 100%;
      height: 100%;
      border-radius: 50%;
    }
  }
  .content {
    flex: 1;
    margin-left: 20rpx;

    .name {
      font-size: 32rpx;
      color: #333;
      font-weight: 500;
    }
    .value {
      margin-top: 10rpx;
      font-size: 30rpx;
      color: #333;
      margin-bottom: 18rpx;
    }
    .media {
      display: flex;
      flex-flow: wrap;
      .media-item {
        margin-right: 12rpx;
        margin-bottom: 12rpx;
        .single-img {
          width: 256rpx;
          height: 341rpx;
          border-radius: 16rpx;
        }
        .multiple-img {
          width: 150rpx;
          height: 150rpx;
          border-radius: 16rpx;
        }
      }
      .video-wrap {
        width: 256rpx;
        height: 341rpx;
        position: relative;
        .play-btn {
          width: 50rpx;
          height: 50rpx;
          position: absolute;
          top: 50%;
          left: 50%;
          z-index: 1;
          transform: translate(-50%, -50%);
        }
        .video {
          border-radius: 16rpx;
          width: 100%;
          height: 100%;
        }
      }
    }
    .row-bar {
      padding-top: 10rpx;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .is-top,
      .post-time {
        font-size: 26rpx;
        font-weight: 400;
        color: #999;
        display: flex;
        white-space: nowrap;
        align-items: center;
        .btns {
          display: flex;
          align-items: center;
          margin-left: 20rpx;
          img {
            margin-right: 20rpx;
            width: 30rpx;
            height: 30rpx;
          }
        }
      }
      .controls {
        display: flex;
        align-items: center;
        .text {
          font-size: 26rpx;
          font-weight: 400;
          color: #999;
          margin-left: 12rpx;
        }
        .like {
          white-space: nowrap;
          margin-right: 20rpx;
        }
        .share {
          .img {
            width: 30rpx;
            height: 30rpx;
            img {
              width: 100%;
              height: 100%;
            }
          }
        }
        .share,
        .like {
          display: flex;
          align-items: center;
          white-space: nowrap;
          font-size: 24rpx;
          font-weight: 400;
          color: #999;
        }
      }
    }
  }
  .delete-popup {
    padding: 60rpx 34rpx;
    padding-bottom: 0;
    .tips {
      font-size: 32rpx;
      font-weight: 500;
      color: #333;
      text-align: center;
    }
    .btns {
      display: flex;
      justify-content: space-between;
      margin-top: 70rpx;
      .btn {
        width: 238rpx;
        height: 88rpx;
        border-radius: 35rpx;
        text-align: center;
        font-size: 34rpx;
        font-weight: 500;
        line-height: 88rpx;
        &.delete {
          color: #fb0;
          margin-right: 38rpx;
          background: url(https://tg-prod.oss-cn-beijing.aliyuncs.com/6e7622cd-ead3-499e-831c-78387da56c2a.png)
            no-repeat center center;
          background-size: 100% 100%;
        }
        &.cancel {
          color: #fff;
          background: url(https://tg-prod.oss-cn-beijing.aliyuncs.com/e748bb8a-a903-43d4-95ac-a269b67b9ae1.png)
            no-repeat center center;
          background-size: 100% 100%;
        }
      }
    }
  }
}
.full-screen-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 150rpx;
}
</style>
