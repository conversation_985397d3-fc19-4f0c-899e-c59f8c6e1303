<template>
  <view class="niedao-circle-page">
    <u-navbar
      title="聂道圈"
      :autoBack="true"
      bgColor="transparent"
      :placeholder="true"
    >
    </u-navbar>
    <!-- <view class="niedao-circle-title">聂道圈</view> -->
    <div class="niedao-circle-content">
      <div class="niedao-circle-header">
        <div class="niedao-circle-school">
          <div class="pisition">
            <img
              src="https://tg-prod.oss-cn-beijing.aliyuncs.com/8a33aef0-9633-4c42-bb32-7da5b494b4a2.png"
              alt=""
            />
          </div>
          <div class="school-name">{{ checkedSchool[0].name }}</div>
        </div>
        <div
          class="my-entry"
          @tap="handleToMyHomePage(userInfo.employee_id, userInfo.name)"
        >
          <div class="avatar">
            <img :src="teacherObj.title_img" alt="" />
          </div>
          <div class="my-entry-btn">我的</div>
        </div>
      </div>
      <u-list
        v-if="list.length > 0"
        @scrolltolower="loadMore"
        @refresherrefresh="refresh"
        @scroll="onScroll"
        :refresher-enabled="true"
        lowerThreshold="100"
        height="calc(100vh - 74px - 78px)"
        :refresher-triggered="isRefreshing"
      >
        <!-- 列表内容 -->
        <u-list-item v-for="item in list" :key="item.id" class="ulist-item">
          <div class="niedao-circle-content-item">
            <div
              class="avatar"
              @tap="handleToMyHomePage(item.operate_id, item.operator_name)"
            >
              <img :src="item.title_img" alt="" />
            </div>
            <div class="content">
              <div class="teacher-name">{{ item.operator_name }}</div>
              <div class="value">
                <ExpandText :text="item.content" :lines="5" />
              </div>
              <div
                class="media"
                v-if="item.image_url && item.image_url.length"
                :class="{
                  'four-grid': item.image_url.length === 4
                }"
              >
                <template v-if="item.image_type === 1">
                  <div
                    class="media-item"
                    v-for="(imageItem, index1) in item.image_url"
                    :key="index1"
                  >
                    <u-image
                      :src="`${imageItem.url}${
                        imageItem.formatSize >= 10
                          ? '?x-oss-process=image/resize,h_300,w_500'
                          : ''
                      }`"
                      lazyLoad
                      radius="16rpx"
                      @tap="handleImageClick(index1, item)"
                      :width="
                        item.image_url.length <= 1
                          ? getImgShowShape(item.image_url).width
                          : '150rpx'
                      "
                      :height="
                        item.image_url.length <= 1
                          ? getImgShowShape(item.image_url).height
                          : '150rpx'
                      "
                      :class="
                        item.image_url.length <= 1
                          ? 'single-img'
                          : 'multiple-img'
                      "
                      mode="aspectFill"
                    >
                      <template v-slot:loading>
                        <u-loading-icon size="14" color="#999"></u-loading-icon>
                      </template>
                    </u-image>
                  </div>
                </template>
                <div
                  class="video-wrap"
                  @tap="fullScreenPlayVideo(item)"
                  :style="{
                    width: getVideoShowShape(item).width,
                    height: getVideoShowShape(item).height
                  }"
                  v-else-if="item.image_type === 2"
                >
                  <img
                    v-if="item.isShowPlayIcon"
                    :src="playIcon"
                    alt=""
                    class="play-btn"
                  />
                  <video
                    :id="'video-' + item.id"
                    direction="0"
                    :muted="videoMuted !== 'video-' + item.id"
                    :autoplay="false"
                    :loop="true"
                    :showCenterPlayBtn="false"
                    @fullscreenchange="fullscreenchange"
                    object-fit="contain"
                    class="video"
                    :data-id="item.id"
                    @play="onPlay(item.id)"
                    @pause="onPause(item.id)"
                    @ended="onVideoEnded(item.id)"
                    :src="item.image_url[0].url"
                    :poster="item.cover_url"
                    :controls="videoMuted === 'video-' + item.id"
                    :enable-progress-gesture="false"
                    :custom-cache="false"
                  >
                    <view
                      class="full-screen-mask"
                      @tap="handleFullScreenMask(item.id)"
                    ></view>
                  </video>
                </div>
              </div>
              <div class="row-bar">
                <span class="is-top" v-if="item.top_status === 1"
                  >置顶内容</span
                >
                <span class="post-time" v-else>{{
                  formatPostTime(item.publish_time)
                }}</span>
                <div class="controls">
                  <div class="like" @tap="handleLike(item)">
                    <div class="label">点赞数</div>
                    <div class="text" style="padding-left: 1rpx">
                      {{ formatWNumber(item.likes_count) }}
                    </div>
                  </div>
                  <div class="share" @tap="handleShare(item)">
                    <div class="img">
                      <img :src="shareIcon" alt="" />
                    </div>
                    <div class="text">分享</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </u-list-item>

        <!-- 加载更多提示 -->
        <u-loadmore
          :status="loadStatus"
          :icon-type="iconType"
          :line="true"
          :load-text="loadText"
        />
      </u-list>
      <EmptyIcon
        v-if="list.length === 0"
        :style="{ height: listHeight, marginTop: '-45rpx' }"
        :text="loadText.nomore"
      />
      <div
        class="create-btn"
        @tap="handleCreate"
        v-if="$hasPermission(['niedao_circle_create'])"
      >
        <div class="create-btn-wrapper">
          <img
            src="https://tg-prod.oss-cn-beijing.aliyuncs.com/564e7287-0c4c-4dea-8879-3bc18a4cb799.png"
            class="create-btn-icon"
            alt=""
          />
        </div>
      </div>
    </div>
    <share-popup
      :showSharePopup="showSharePopup"
      :shareFriendInfo="currentMoment"
      webviewPath="tg-minigram/niedaoCircle/shareCard"
      :webviewParams="webviewParams"
      :isShowTimeLine="false"
      @closeSharePopup="closeSharePopup"
      @share="handleShare"
    ></share-popup>
  </view>
</template>

<script>
import { momentsList, teacherInfo } from "@/services/niedaoCircle";
import EmptyIcon from "@/components/empty/index.vue";
import ExpandText from "@/components/expandText/index.vue";
import SharePopup from "@/components/sharePopup/index.vue";
export default {
  name: "tearcherNiedaoCircleIndex",
  components: {
    EmptyIcon,
    ExpandText,
    SharePopup
  },
  onShareAppMessage(res) {
    if (res.from === "button") {
      // 来自页面内分享按钮
      const item = res.target.dataset.item;
      let imageUrl = "";
      if (item.image_type === 1) {
        if (item.image_url?.length) {
          imageUrl = item.image_url[0].url;
        } else {
          imageUrl =
            "https://tg-prod.oss-cn-beijing.aliyuncs.com/f860ad82-95fd-49c4-a8de-c629f069411b.png";
        }
      } else if (item.image_type === 2) {
        if (item.cover_url) {
          imageUrl = item.cover_url;
        } else {
          imageUrl =
            item.image_url[0].url +
            "?x-oss-process=video/snapshot,t_1000,f_jpg,w_0,h_0,m_fast";
        }
      }

      return {
        title: item.content,
        imageUrl,
        path: `/pages/student/subpages/niedaoCircle/detail?id=${item.id}&source=share`
      };
    }

    this.$refs.uToast.show({
      type: "default",
      duration: "2000",
      message: "请选择聂道圈进行分享！"
    });
    return false;
  },
  data() {
    return {
      isShowPlayIcon: true,
      shareIcon:
        "https://tg-prod.oss-cn-beijing.aliyuncs.com/b20ca5e2-b55e-4c98-83b9-17de96a7c39e.png",
      playIcon:
        "https://tg-prod.oss-cn-beijing.aliyuncs.com/7c8ff79b-619f-4842-b640-87504abe4ed1.png",
      shadowStyle: {
        backgroundImage: "none"
      },
      sexImg: {
        male: "https://tg-prod.oss-cn-beijing.aliyuncs.com/0a4b48aa-5bfd-44ee-af56-b05af8ccbcd0.png",
        female:
          "https://tg-prod.oss-cn-beijing.aliyuncs.com/950f7aa7-d6c8-4097-a0af-441261350a22.png"
      },
      userInfo: {},
      checkedSchool: {},
      list: [],
      listParams: {
        page: 1,
        page_size: 8,
        employee_id: "",
        department_id: "",
        student_id: "",
        search_count: false,
        view_type: 1
      },
      isRefreshing: false,
      loadStatus: "loadmore",
      iconType: "arrow",
      loadText: {
        loadmore: "上拉加载更多",
        loading: "正在加载...",
        nomore: "没有更多了"
      },
      videoContext: null,
      teacherObj: {},
      observers: [],
      videoMuted: "",
      showSharePopup: false,
      currentMoment: {},
      webviewParams: {}
    };
  },
  methods: {
    getDisplaySize(imgW, imgH) {
      // 判断类型
      if (imgH / imgW >= 3) {
        // 超长竖图
        return {
          width: 150,
          height: 454
        };
      } else if (imgW / imgH >= 3) {
        // 超长横图
        return {
          width: 454,
          height: 150
        };
      } else if (imgH > imgW) {
        // 普通竖图
        return {
          width: 256,
          height: 341
        };
      } else {
        // 普通横图
        return {
          width: 341,
          height: 256
        };
      }
    },
    getVideoShowShape(item) {
      const { width, height } = item.image_url[0];
      return {
        width: width > height ? "341rpx" : "256rpx",
        height: width > height ? "256rpx" : "341rpx"
      };
    },
    getImgShowShape(image_url) {
      const { width, height } = image_url[0];
      const imgInfo = this.getDisplaySize(width, height);

      return {
        width: imgInfo.width + "rpx",
        height: imgInfo.height + "rpx"
      };
    },
    formatWNumber(num) {
      if (num >= 10000) {
        return (num / 10000).toFixed(1) + "w";
      }
      return num.toString();
    },
    handleShare(item) {
      this.showSharePopup = true;
      this.currentMoment = item;
      this.webviewParams = {
        id: item.id,
        token: this.userInfo.token
      };
    },
    closeSharePopup() {
      this.showSharePopup = false;
    },
    parseLoad() {
      this.$nextTick(() => {
        this.$refs.uReadMore.reduce((item) => {
          item.init();
        });
      });
    },
    async getTeacherInfo() {
      const res = await teacherInfo();
      this.teacherObj = res.data;
    },
    handleCreate() {
      uni.navigateTo({
        url: "/pages/teacher/subpages/niedaoCircle/create"
      });
    },
    formatPostTime(postTime) {
      const compatibilityPostTime = (timeStr) => {
        // 替换 '-' 为 '/'，兼容 iOS
        const iosCompatibleStr = timeStr.replace(/-/g, "/");
        return new Date(iosCompatibleStr);
      };

      const now = new Date(); // 当前时间
      const postDate = new Date(compatibilityPostTime(postTime)); // 发布时间
      const timeDiff = now - postDate; // 时间差（毫秒）

      const seconds = Math.floor(timeDiff / 1000); // 转换为秒
      const minutes = Math.floor(seconds / 60); // 转换为分钟
      const hours = Math.floor(minutes / 60); // 转换为小时
      const days = Math.floor(hours / 24); // 转换为天数

      if (seconds < 60) {
        return "刚刚";
      } else if (minutes < 60) {
        return `${minutes}分钟前`;
      } else if (hours < 24) {
        return `${hours}小时前`;
      } else if (days === 1) {
        return "昨天";
      } else if (days <= 7) {
        return `${days}天前`;
      } else {
        // 超过 7 天，显示具体日期
        const year = postDate.getFullYear();
        const month = postDate.getMonth() + 1; // 月份从 0 开始
        const day = postDate.getDate();
        return `${year}-${month.toString().padStart(2, "0")}-${day
          .toString()
          .padStart(2, "0")}`;
      }
    },
    async getMyCircle(source) {
      try {
        const res = await momentsList(this.listParams);
        const results = res.data.results.map((item) => ({
          ...item,
          isShowPlayIcon: true
        }));
        if (this.listParams.page === 1) {
          this.list = results; // 第一页直接覆盖
        } else {
          this.list = [...this.list, ...results]; // 追加数据
        }
        // 判断是否还有更多数据
        if (res.data.results.length < this.listParams.page_size) {
          this.loadStatus = "nomore";
        } else {
          this.loadStatus = "loadmore";
        }
        if (source === "onShow") {
          this.$nextTick(() => {
            this.checkVideoInView();
          });
        }
      } catch (error) {
      } finally {
        this.isRefreshing = false; // 结束刷新状态
      }
    },
    // 下拉刷新
    refresh() {
      this.isRefreshing = true;
      this.listParams.page = 1; // 重置页码
      this.getMyCircle(); // 重新加载数据
    },
    // 上拉加载更多
    loadMore() {
      if (this.loadStatus === "nomore") return; // 没有更多数据时不再加载
      this.loadStatus = "loading"; // 设置加载状态
      this.listParams.page += 1; // 页码加 1
      this.getMyCircle(); // 加载更多数据
    },
    handleToMyHomePage(operate_id, operator_name) {
      uni.navigateTo({
        url: `/pages/teacher/subpages/niedaoCircle/myHomePage?operate_id=${operate_id}&operator_name=${operator_name}`
      });
    },
    onScroll(e) {
      if (this.scrollTimer) return;
      this.scrollTimer = setTimeout(() => {
        this.checkVideoInView();
        this.scrollTimer = null;
      }, 200);
    },
    checkVideoInView() {
      const query = uni.createSelectorQuery().in(this);
      query
        .selectAll(".video")
        .boundingClientRect((rects) => {
          if (!rects || rects.length === 0) return;

          const windowHeight = uni.getSystemInfoSync().windowHeight;
          const screenCenter = windowHeight / 2;

          let minDistance = Infinity;
          let targetId = null;

          rects.forEach((rect) => {
            if (!rect) return;

            const { top, bottom, height, dataset } = rect;

            // 只处理在可视区域内的视频
            if (bottom > 0 && top < windowHeight) {
              const videoCenter = top + height / 2;
              const distance = Math.abs(videoCenter - screenCenter);

              // 更新距离屏幕中心最近的视频
              if (distance < minDistance) {
                minDistance = distance;
                targetId = dataset.id;
              }
            }
          });

          // 如果找到了合适的视频，切换到该视频
          if (targetId) {
            this.switchToVideo(targetId);
          } else {
            this.stopCurrentVideo();
          }
        })
        .exec();
    },
    switchToVideo(id) {
      if (this.playingId && this.playingId !== id) {
        const oldContext = uni.createVideoContext(
          "video-" + this.playingId,
          this
        );
        oldContext.stop(); // 使用stop而不是pause
        this.updatePlayIcon(this.playingId, true);

        // 释放旧的视频上下文
        if (oldContext) {
          oldContext.exitFullScreen();
          oldContext.seek(0);
        }
      }

      if (this.playingId !== id) {
        const newContext = uni.createVideoContext("video-" + id, this);
        newContext.play();
        this.updatePlayIcon(id, false);
        this.playingId = id;
      }
    },
    stopCurrentVideo() {
      if (this.playingId) {
        const context = uni.createVideoContext("video-" + this.playingId, this);
        context.stop(); // 使用stop而不是pause
        this.updatePlayIcon(this.playingId, true);

        // 释放视频上下文
        if (context) {
          context.exitFullScreen();
          context.seek(0);
        }

        this.playingId = null;
      }
    },
    onPlay(id) {
      this.updatePlayIcon(id, false);
    },
    onPause(id) {
      this.updatePlayIcon(id, true);
    },
    clickPlay(id) {
      const context = uni.createVideoContext("video-" + id, this);
      context.play();
      this.updatePlayIcon(id, false);
      this.playingId = id;
    },
    updatePlayIcon(id, isShow) {
      const idx = this.list.findIndex((item) => item.id === id);
      if (idx !== -1) {
        this.$set(this.list[idx], "isShowPlayIcon", isShow);
      }
    },
    fullScreenPlayVideo(item) {
      this.videoContext = uni.createVideoContext(`video-${item.id}`, this);
      this.videoContext.requestFullScreen();
    },
    fullscreenchange(e) {
      if (!e.detail.fullScreen) {
        const videoContext = uni.createVideoContext(e.currentTarget.id, this);
        videoContext.play();
        this.videoMuted = false;
      } else {
        // 解除静音
        this.videoMuted = e.currentTarget.id;
      }
    },
    handleImageClick(current, urls) {
      uni.previewImage({
        current, // 当前点击的图片索引
        urls: urls.image_url.map((item) => item.url) // 所有图片的 URL 列表
      });
    },
    handleFullScreenMask(id) {
      const videoContext = uni.createVideoContext(`video-${id}`, this);
      // 关闭全屏
      videoContext.exitFullScreen();
    },
    onVideoEnded(id) {
      // 停止播放
      if (this.playingId === id) {
        const videoContext = uni.createVideoContext("video-" + id, this);
        videoContext.stop(); // 停止播放而不是暂停，释放资源
        this.updatePlayIcon(id, true);
        this.playingId = null;

        // 手动触发垃圾回收
        if (videoContext) {
          videoContext.exitFullScreen();
          videoContext.seek(0);
        }
      }
    }
  },
  onPullDownRefresh() {
    this.refresh();
  },
  onShow() {
    this.checkedSchool = uni.getStorageSync("checkedSchool");
    this.userInfo = uni.getStorageSync("user");
    this.listParams.department_id = this.checkedSchool[0].id;
    this.getMyCircle("onShow");
    this.getTeacherInfo();
  }
};
</script>

<style lang="scss" scoped>
::v-deep .u-read-more__toggle {
  justify-content: start !important;
  .u-icon__icon,
  .u-text__value {
    font-weight: 500 !important;
  }
}
::v-deep .u-navbar__content__title {
  font-size: 34rpx !important;
  font-weight: 500 !important;
  color: #333 !important;
}
// ::v-deep .u-read-more__content {
//   max-height: 150px !important;
// }
.niedao-circle-page {
  width: 100%;
  min-height: 100vh;
  background: url(https://tg-prod.oss-cn-beijing.aliyuncs.com/88cd9858-d973-46f7-96c5-a46e7f42fb76.png)
    no-repeat center center;
  background-size: 100% 30%;
  background-position: top;
}
.niedao-circle-title {
  color: #333;
  text-align: center;
  padding-top: 105rpx;
  font-size: 34rpx;
  font-weight: 500;
}
.niedao-circle-content {
  .niedao-circle-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 31rpx 32rpx 0 32rpx;
    .niedao-circle-school {
      display: flex;
      align-items: center;
      .pisition {
        width: 26rpx;
        height: 30rpx;
        margin-right: 12rpx;
        img {
          width: 100%;
          height: 100%;
        }
      }
      .school-name {
        font-size: 30rpx;
        font-weight: 500;
        color: #333;
      }
    }
    .my-entry {
      border-radius: 40rpx;
      border: 1px solid #ffc663;
      background: #fff4e9;
      font-size: 26rpx;
      color: #ff8401;
      font-weight: 500;
      padding: 5rpx 16rpx 5rpx 4rpx;
      display: flex;
      align-items: center;
      .avatar {
        width: 44rpx;
        height: 44rpx;
        border-radius: 50%;
        margin-right: 11rpx;
        background-color: #eee;
        img {
          width: 100%;
          height: 100%;
        }
      }
    }
  }
  .ulist-item {
    .niedao-circle-content-item {
      &:last-child {
        border-bottom: none;
      }
    }
  }
  .niedao-circle-content-item {
    border-bottom: 1rpx solid #eee;
    padding: 34rpx 32rpx 18rpx 32rpx;
    display: flex;
    .avatar {
      flex-shrink: 0;
      width: 85rpx;
      height: 85rpx;
      border-radius: 50%;
      img {
        width: 100%;
        height: 100%;
        border-radius: 50%;
      }
      background-color: #eee;
    }
    .content {
      margin-left: 20rpx;
      flex: 1;
      .teacher-name {
        font-size: 32rpx;
        color: #333;
        font-weight: 500;
      }
      .value {
        margin-top: 10rpx;
        font-size: 28rpx;
        color: #666;
        margin-bottom: 24rpx;
      }
      .media {
        display: flex;
        flex-wrap: wrap;
        margin-bottom: 24rpx;
        .media-item {
          margin-right: 12rpx;
          margin-bottom: 12rpx;
          .single-img {
            width: 256rpx;
            height: 341rpx;
            border-radius: 16rpx;
          }
          .multiple-img {
            width: 150rpx;
            height: 150rpx;
            border-radius: 16rpx;
          }
        }
        .video-wrap {
          width: 256rpx;
          height: 341rpx;
          position: relative;
          .play-btn {
            width: 50rpx;
            height: 50rpx;
            position: absolute;
            top: 50%;
            left: 50%;
            z-index: 1;
            transform: translate(-50%, -50%);
          }
          .video {
            border-radius: 16rpx;
            width: 100%;
            height: 100%;
          }
        }
      }
      .four-grid {
        display: grid;
        grid-template-columns: repeat(2, 150rpx);
        width: 100%;

        .media-item {
          margin-right: 0;

          .u-image {
            width: 100% !important;
            height: 100% !important;
          }
        }
      }
      .row-bar {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .is-top,
        .post-time {
          font-size: 26rpx;
          font-weight: 400;
          color: #999;
        }
        .controls {
          display: flex;
          align-items: center;
          // width: 280rpx;
          justify-content: space-between;
          .text {
            font-size: 26rpx;
            font-weight: 400;
            color: #999;
            padding-left: 12rpx;
          }
          .like {
            margin-right: 32rpx;
          }
          .share {
            display: flex;
            align-items: center;
            .img {
              width: 30rpx;
              height: 30rpx;
              img {
                width: 100%;
                height: 100%;
              }
            }
          }
          .like {
            display: flex;
            align-items: center;
            font-size: 24rpx;
            font-weight: 400;
            color: #999;
          }
        }
      }
    }
  }
  .create-btn {
    position: fixed;
    bottom: 100rpx;
    right: 32rpx;
    .create-btn-wrapper {
      width: 100rpx;
      height: 100rpx;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      background: linear-gradient(0deg, #fb0 6.82%, #ffda54 87.82%);
      box-shadow: 0px 0px 25px 0px rgba(255, 218, 84, 0.5);
      .create-btn-icon {
        width: 50rpx;
        height: 50rpx;
      }
    }
  }
}
.full-screen-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 150rpx;
}
</style>
