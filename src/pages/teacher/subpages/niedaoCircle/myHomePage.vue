<template>
  <div class="myHomePage">
    <u-navbar
      :title="operator_name"
      :placeholder="true"
      :autoBack="true"
      titleStyle="font-weight: 500;"
    >
    </u-navbar>
    <u-sticky offset-top="44" customNavHeight="44">
      <div class="myHomePage-search-date" @tap="handleSearchDateClick">
        <!-- {{ searchDate.month }}月 -->
        {{ searchDate.year }}年<span class="sanjiao"></span>
      </div>
      <!-- v-if="searchDate.year && showSticky" -->
      <!-- <view v-else style="height: 40rpx"></view> -->
    </u-sticky>
    <div class="myHomePage-content">
      <u-list
        v-if="formattedData.length > 0"
        @scrolltolower="loadMore"
        @scrolltoupper="handleScrollToUpper"
        @scroll="handleScroll"
        lowerThreshold="800"
        height="calc(100vh - 88px - 32px - 40rpx)"
        upperThreshold="10"
        preLoadScreen="2"
        :scrollable="true"
        offset-accuracy="5"
        ref="scrollWrap"
        :scrollTop="scrollTop"
      >
        <!-- 顶部加载更多，只在筛选时且还有数据时显示 -->
        <u-loadmore
          v-if="
            listParams.date &&
            topLoadStatus !== 'nomore' &&
            topLoadStatus !== 'loadmore'
          "
          :status="topLoadStatus"
          :load-text="loadText"
          loadmoreText="继续上滑加载更早内容"
          margin-top="20"
          margin-bottom="20"
          :showLoadingMore="true"
          lineColor="#DADADA"
          line
          color="#999999"
          :fontSize="'26rpx'"
        />
        <u-list-item
          v-for="(item, index) in formattedData"
          :key="index"
          :anchor="item.id"
        >
          <template v-if="today != item.publish_time">
            <div
              v-if="item.type === 'year'"
              class="year-line"
              :data-year="item.publish_time"
              :id="item.publish_time"
            >
              {{ item.publish_time }}
            </div>
            <template v-else>
              <div
                :class="'myHomePage-content-item ' + item.class"
                :data-year="item.publish_time.year"
                :id="item.id || item.publish_time"
                :style="{
                  marginTop: item.type === 'create' ? '40rpx' : '0'
                }"
              >
                <div class="time">
                  <template v-if="item.publish_time">
                    <span v-if="item.timeType === 'precision'" class="date">
                      <span class="day">{{ item.publish_time.day }}</span>
                      <span class="month">{{ item.publish_time.month }}月</span>
                    </span>
                    <span v-else class="lately">{{ item.publish_time }}</span>
                  </template>
                </div>
                <div
                  class="await-examine"
                  :style="{
                    paddingLeft: item.publish_status === 2 ? '23rpx' : '10rpx'
                  }"
                  v-if="
                    item.publish_status && [2, 5].includes(item.publish_status)
                  "
                >
                  {{ item.publish_status === 2 ? "待审核" : "审核驳回" }}
                </div>
                <div
                  v-if="item.type === 'create'"
                  @tap="handleCreate"
                  class="create-entry"
                >
                  <img
                    src="https://tg-prod.oss-cn-beijing.aliyuncs.com/9e84f9b9-df5b-48dd-9cd0-f7fad64e374f.png"
                    alt=""
                  />
                </div>
                <template v-else>
                  <div
                    class="content"
                    v-if="item.image_url && item.image_url.length"
                  >
                    <img
                      v-if="
                        item.visibility_scope &&
                        !item.visibility_scope.includes('open')
                      "
                      src="https://tg-prod.oss-cn-beijing.aliyuncs.com/fda7506f-ea4d-49bb-95e4-1fcbd8629ebc.png"
                      class="visibility-scope-icon"
                      alt=""
                    />
                    <div
                      v-if="item.image_type === 1"
                      :class="'album ' + layoutClass(item.image_url.length)"
                      @tap="handleImageClick(0, item)"
                    >
                      <image
                        mode="aspectFill"
                        v-for="(image, index) in item.image_url"
                        :src="image.url"
                        :key="index"
                        alt=""
                      />
                    </div>

                    <div
                      class="video-wrap"
                      @tap="fullScreenPlayVideo(index)"
                      v-else-if="item.image_type === 2"
                    >
                      <img :src="playIcon" alt="" class="play-btn" />
                      <video
                        :id="'video-' + index"
                        direction="0"
                        :muted="videoMuted !== 'video-' + index"
                        :showCenterPlayBtn="false"
                        :loop="true"
                        @fullscreenchange="fullscreenchange"
                        object-fit="contain"
                        class="video"
                        :src="item.image_url[0].url"
                        :poster="item.cover_url"
                        :controls="videoMuted === 'video-' + index"
                        :enable-progress-gesture="false"
                        :custom-cache="false"
                      >
                        <view
                          class="full-screen-mask"
                          @tap="handleFullScreenMask(index)"
                        ></view>
                      </video>
                    </div>
                  </div>
                  <div
                    @tap="handleContentClick(item)"
                    class="content-txt-wrap"
                    :class="[
                      'value',
                      !item.image_url || item.image_url.length === 0
                        ? 'text'
                        : ''
                    ]"
                  >
                    <div class="content-txt">
                      {{ item.content }}
                    </div>
                    <img
                      v-if="
                        item.visibility_scope &&
                        !item.visibility_scope.includes('open')
                      "
                      src="https://tg-prod.oss-cn-beijing.aliyuncs.com/fda7506f-ea4d-49bb-95e4-1fcbd8629ebc.png"
                      class="visibility-scope-icon"
                      alt=""
                    />
                  </div>
                </template>
              </div>
            </template>
          </template>
        </u-list-item>
        <!-- 底部加载更多 -->
        <u-loadmore
          :status="loadStatus"
          :load-text="loadText"
          loadmoreText="点击加载更多"
          margin-top="20"
          margin-bottom="20"
          :showLoadingMore="true"
          lineColor="#DADADA"
          line
          color="#999999"
          :fontSize="'26rpx'"
          @loadmore="loadMore"
        />
      </u-list>
      <EmptyIcon
        v-if="formattedData.length === 0"
        :style="{ height: listHeight, marginTop: '-45rpx' }"
        :text="loadText.nomore"
      />
    </div>
    <view @touchmove.stop.prevent="">
      <u-picker
        :show="isShowDatePicker"
        ref="uPicker"
        :columns="columns"
        title="请选择发表的日期"
        @confirm="handleDateConfirm"
        @cancel="handleDateCancel"
        @change="changeHandler"
        confirmColor="#333"
      ></u-picker>
    </view>
  </div>
</template>

<script>
import { momentsList, momentsDate } from "@/services/niedaoCircle";
import EmptyIcon from "@/components/empty/index.vue";
export default {
  name: "tearcherNiedaoCircleMyHomePage",
  components: {
    EmptyIcon
  },
  data() {
    return {
      isOneself: false,
      playIcon:
        "https://tg-prod.oss-cn-beijing.aliyuncs.com/7c8ff79b-619f-4842-b640-87504abe4ed1.png",
      operator_name: "",
      formattedData: [],
      allPosts: [],
      isShowDatePicker: false,
      columns: [],
      monthColumns: [],
      value1: "",
      searchDate: {
        year: "",
        month: ""
      },
      curStudentInfo: {},
      loadStatus: "loadmore",
      topLoadStatus: "loadmore",
      loadText: {
        loadmore: "上拉加载更多",
        loading: "正在加载...",
        nomore: "没有更多了"
      },
      userInfo: {},
      checkedSchool: [],
      listParams: {
        page: 1,
        page_size: 8,
        employee_id: "",
        department_id: "",
        student_id: "",
        search_count: false,
        view_type: 2
      },
      pageInfo: {
        upPage: 1,
        downPage: 2
      },
      videoMuted: "",
      today: "",
      showSticky: false,
      scrollTop: 0,
      lastVisibleItemId: "",
      newIds: [],
      allowRefresh: false, // 是否允许刷新的标志
      isFirstLoad: true, // 是否首次加载
      fromCreatePage: false
    };
  },
  methods: {
    handleCreate() {
      this.fromCreatePage = true;
      uni.navigateTo({
        url: "/pages/teacher/subpages/niedaoCircle/create"
      });
    },
    // 获取聂道圈日期
    async getMomentsDate() {
      const res = await momentsDate({
        employee_id: this.listParams.employee_id,
        department_id: this.listParams.department_id
      });
      if (res.data?.length) {
        this.columns = [res.data.map((i) => i.year), res.data[0].month];
        this.monthColumns = res.data.map((i) => i.month);
      }
      console.log(res, "res");
    },
    truncateWithEmoji(element, maxLines = 3) {
      const lineHeight = parseInt(getComputedStyle(element).lineHeight);
      const maxHeight = lineHeight * maxLines;

      // 检查内容是否超出
      if (element.scrollHeight > maxHeight) {
        // 这里可以添加自定义的截断逻辑
        // 可能需要处理emoji位置
      }
    },
    layoutClass(count) {
      const classes = [
        "one",
        "two",
        "three",
        "four",
        "five",
        "six",
        "seven",
        "eight",
        "nine"
      ];
      if (count >= 1 && count <= 9) {
        return classes[count - 1];
      } else {
        return "default-layout";
      }
    },
    handleScrollToUpper() {
      console.log("上滑加载");
      // 找出第一个有效的id
      const firstItem = this.formattedData.find((item) => item.id);
      if (firstItem) {
        this.lastVisibleItemId =
          firstItem.id || firstItem.year || firstItem.publish_time;
      }
      console.log(firstItem, "this.lastVisibleItemId");
      if (this.listParams.date && this.topLoadStatus !== "nomore") {
        this.listParams.operate_type = "up";
        this.topLoadStatus = "loadmore";
        uni.$u.throttle(() => {
          this.loadMore("up");
        }, 500);
      }
    },
    // 加载更多数据
    async loadMore(type) {
      let currentLoadStatus;
      if (type) {
        currentLoadStatus =
          this.listParams.operate_type === "up"
            ? "topLoadStatus"
            : "loadStatus";
        if (this[currentLoadStatus] !== "loadmore") return;
        this[currentLoadStatus] = "loading";
      } else {
        currentLoadStatus = "loadStatus";
        this.listParams.operate_type = "down";
        if (this[currentLoadStatus] !== "loadmore") return;
        this[currentLoadStatus] = "loading";
      }

      try {
        // 获取当前元素位置信息
        // const oldPosition = null;

        // 只在筛选和特定滚动方向时设置页码
        if (type === "clear") {
          console.log("清除数据的情况");
          this.allPosts = [];
          this.formattedData = [];
          this.listParams.page = 1;
          this.pageInfo.upPage = 1;
          this.pageInfo.downPage = 2;
          this.topLoadStatus = "loadmore";
          this.loadStatus = "loadmore";
        } else if (this.listParams.operate_type) {
          // 只有当明确设置了operate_type时才处理滚动方向
          if (this.listParams.operate_type === "up") {
            this.listParams.page = this.pageInfo.upPage;
            this.pageInfo.upPage += 1;
          } else if (this.listParams.operate_type === "down") {
            this.listParams.page = this.pageInfo.downPage;
            this.pageInfo.downPage += 1;
          }
        } else {
          // 默认加载更多的情况（向下加载）
          this.listParams.page = this.pageInfo.downPage;
          this.pageInfo.downPage += 1;
        }

        // 如果不是筛选后的数据，则删除operate_type
        if (!this.listParams.date) {
          delete this.listParams.operate_type;
        }
        const res = await momentsList(this.listParams);
        const newPosts = res.data.results || [];
        this.total = res.data.count || 0;

        // 合并数据
        this.allPosts = [...this.allPosts, ...newPosts];

        // 如果是向上加载，先设置滚动位置
        // if (this.listParams.operate_type === "up") {
        //   // 使用 nextTick 确保在数据更新后再设置滚动位置
        //   this.$nextTick(async () => {
        //     if (
        //       this.listParams.operate_type === "up" &&
        //       this.lastVisibleItemId
        //     ) {
        //       const query = uni.createSelectorQuery().in(this);
        //       await new Promise((resolve) => {
        //         query
        //           .select("#" + this.lastVisibleItemId)
        //           .boundingClientRect((data) => {
        //             console.log(data, "data");
        //             if (data) {
        //               oldPosition = {
        //                 bottom: data.bottom,
        //                 height: data.height,
        //                 top: data.top
        //               };
        //             }
        //             resolve();
        //           })
        //           .exec();
        //       });
        //     }
        //     setTimeout(() => {
        //       this.scrollTop = oldPosition.top - 100 - oldPosition.height;
        //     }, 10);
        //   });
        // }

        // 格式化数据
        this.formattedData = this.formatData(this.allPosts);

        // 检查是否全部加载完成
        if (res.data.results.length === 0) {
          this[currentLoadStatus] = "nomore";
        } else {
          this[currentLoadStatus] = "loadmore";
        }
        if (!this.listParams.date && this.total === this.allPosts.length) {
          this[currentLoadStatus] = "nomore";
        }
        this.$nextTick(() => {
          if (
            this.listParams.date &&
            this.listParams.operate_type === "up" &&
            this.topLoadStatus !== "nomore"
          ) {
            this.scrollTop = 0;
            setTimeout(() => {
              this.scrollTop = 20;
            }, 10);
          }
          console.log(
            this.lastVisibleItemId,
            this.scrollTop,
            "this.lastVisibleItemId"
          );
        });
      } catch (error) {
        console.error("加载数据失败:", error);
      } finally {
        console.log(this.loadStatus, "this.loadStatus");
        this.isRefreshing = false;
      }
    },
    // 添加日期格式化方法
    formatDateString(dateStr) {
      if (!dateStr) return "";
      // 将 "yyyy-MM-dd HH:mm:ss" 转换为 "yyyy-MM-ddTHH:mm:ss"
      return dateStr.replace(" ", "T");
    },
    isSameDay(date1, date2) {
      if (!date1 || !date2) return false;
      return (
        date1.getFullYear() === date2.getFullYear() &&
        date1.getMonth() === date2.getMonth() &&
        date1.getDate() === date2.getDate()
      );
    },
    formatData(data) {
      const today = new Date();
      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);

      const groupedData = [];
      if (this.isOneself) {
        groupedData.push({
          id: "",
          timeType: "semanticization",
          date: today,
          publish_time: "今天",
          type: "create",
          content: ""
        });
      }
      let lastYear = null;
      let lastDate = null;
      data
        .map((post) => {
          // 确保日期格式兼容 iOS
          const formattedDate = this.formatDateString(post.publish_time);
          const date = new Date(formattedDate);
          // 验证日期是否有效
          if (isNaN(date.getTime())) {
            console.error("Invalid date:", post.publish_time);
            return null;
          }
          return {
            ...post,
            date,
            year: date.getFullYear()
          };
        })
        .filter((item) => item !== null) // 过滤掉无效的日期
        .sort((a, b) => b.date - a.date) // 按日期倒序排序
        .forEach((post, index) => {
          // 添加年份分隔符
          if (lastYear !== post.year) {
            groupedData.push({
              type: "year",
              publish_time: post.year.toString()
            });
            lastYear = post.year;
          }

          // 处理日期显示逻辑
          const isToday = this.isSameDay(post.date, today);
          const isYesterday = this.isSameDay(post.date, yesterday);

          let publishTime = "";
          let timeType = "";
          if (isToday) {
            publishTime = this.isOneself ? "" : "今天";
            timeType = "semanticization";
          } else if (isYesterday) {
            publishTime = "昨天";
            timeType = "semanticization";
          } else if (!lastDate || !this.isSameDay(post.date, lastDate)) {
            publishTime = {
              day: post.date.getDate(),
              month: post.date.getMonth() + 1,
              year: post.date.getFullYear()
            };
            timeType = "precision";
          }
          groupedData.push({
            id: post.id,
            image_url: post.image_url,
            visibility_scope: post.visibility_scope,
            publish_status: post.publish_status,
            cover_url: post.cover_url,
            image_type: post.image_type,
            content: post.content,
            publish_time:
              lastDate && this.isSameDay(post.date, lastDate)
                ? ""
                : publishTime,
            timeType,
            type: "day",
            class:
              lastDate && this.isSameDay(post.date, lastDate) ? "" : "same-date"
          });

          lastDate = post.date;
        });
      return groupedData;
    },

    handleImageClick(current, urls) {
      uni.previewImage({
        current, // 当前点击的图片索引
        urls: urls.image_url.map((item) => item.url) // 所有图片的 URL 列表
      });
    },
    handleContentClick(item) {
      uni.navigateTo({
        url: `/pages/teacher/subpages/niedaoCircle/detail?id=${item.id}&source=myHomePage&isOneself=${this.isOneself}`
      });
    },
    fullScreenPlayVideo(index) {
      const videoContext = uni.createVideoContext(`video-${index}`, this);
      videoContext.requestFullScreen();
    },
    handleSearchDateClick() {
      this.isShowDatePicker = true;
    },
    changeHandler(e) {
      const {
        columnIndex,
        index,
        // 微信小程序无法将picker实例传出来，只能通过ref操作
        picker = this.$refs.uPicker
      } = e;
      // 当第一列值发生变化时，变化第二列(后一列)对应的选项
      if (columnIndex === 0) {
        // picker为选择器this实例，变化第二列对应的选项
        picker.setColumnValues(1, this.monthColumns[index]);
      }
    },
    handleDateConfirm(data) {
      this.loadStatus = "loadmore";
      this.listParams.date = data.value.join("-");
      this.listParams.search_count = true;
      this.listParams.page = 1;
      this.searchDate = {
        year: data.value[0],
        month: data.value[1]
      };
      this.listParams.operate_type = "down";
      this.scrollTop = 20;
      this.loadMore("clear");
      this.isShowDatePicker = false;
    },
    handleDateCancel() {
      this.isShowDatePicker = false;
    },
    fullscreenchange(e) {
      const videoContext = uni.createVideoContext(e.currentTarget.id, this);
      if (e.detail.fullscreen) {
        // 解除静音
        this.videoMuted = e.currentTarget.id;
        videoContext.play();
      } else {
        this.videoMuted = "";
        // 停止播放
        videoContext.pause();
      }
    },
    handleScroll(e) {
      this.showSticky = e > 100;
      // this.scrollTop = e;
      // console.log(e, "this.showSticky");
      console.log(e, "e");
      // 获取 myHomePage-search-date 元素的位置
      const searchDateQuery = uni.createSelectorQuery().in(this);
      searchDateQuery
        .select(".myHomePage-search-date")
        .boundingClientRect((searchDateRect) => {
          if (searchDateRect) {
            // 获取所有 myHomePage-content-item 元素
            const query = uni.createSelectorQuery().in(this);
            query
              .selectAll(".myHomePage-content-item")
              .boundingClientRect((data) => {
                if (data && data.length) {
                  // 找到距离最近的 myHomePage-content-item
                  let closestItem = null;
                  let minDistance = Infinity;

                  data.forEach((item) => {
                    const distance = Math.abs(item.top - searchDateRect.bottom);
                    if (distance < minDistance) {
                      minDistance = distance;
                      closestItem = item;
                    }
                  });

                  // 当找到最近的元素时
                  if (closestItem) {
                    // console.log('距离最近的列表项:', closestItem);
                    // 这里可以添加您想要触发的逻辑
                    if (closestItem.dataset && closestItem.dataset.year) {
                      this.$nextTick(() => {
                        this.searchDate.year = closestItem.dataset.year;
                      });
                    }
                  }
                }
              })
              .exec();
          }
        })
        .exec();
    },
    handleFullScreenMask(index) {
      const videoContext = uni.createVideoContext(`video-${index}`, this);
      // 关闭全屏
      videoContext.exitFullScreen();
    }
  },
  watch: {},

  // 页面周期函数--监听页面加载
  onLoad(options) {
    console.log(options, "options");
    this.userInfo = uni.getStorageSync("user");
    this.checkedSchool = uni.getStorageSync("checkedSchool");
    this.listParams.department_id = this.checkedSchool[0].id;
    this.listParams.employee_id = options.operate_id;
    this.isOneself = options.operate_id === this.userInfo.employee_id;
    this.operator_name = this.isOneself ? "我的聂道圈" : options.operator_name;

    // 判断来源页面，只有从 create 或 index 页面进入时才允许刷新
    const pages = getCurrentPages();
    if (pages.length >= 2) {
      const prevPage = pages[pages.length - 2];
      const prevRoute = prevPage.route;
      console.log("上一个页面路由:", prevRoute);

      // 检查是否来自 create 或 index 页面
      if (prevRoute.includes("create") || prevRoute.includes("index")) {
        this.allowRefresh = true;
      }
    } else {
      // 如果是首次进入应用，也允许刷新
      this.allowRefresh = true;
    }

    console.log("是否允许刷新:", this.allowRefresh);
    console.log(this.isOneself);
    this.getMomentsDate();
    const date = new Date();
    this.searchDate = {
      year: date.getFullYear(),
      month: date.getMonth() + 1
    };
    this.today = date.getFullYear();
  },
  mounted() {},
  beforeUnmount() {},
  // 页面周期函数--监听页面初次渲染完成
  onReady() {},
  // 页面周期函数--监听页面显示(not-nvue)
  onShow() {
    console.log("onShow");

    // 只有在允许刷新或首次加载时才进行数据刷新
    if (this.allowRefresh || this.isFirstLoad || this.fromCreatePage) {
      console.log("执行数据刷新");
      this.loadStatus = "loadmore";
      this.listParams.page = 1;
      this.loadMore("clear");
      this.isFirstLoad = false; // 首次加载后设置为false
    } else {
      console.log("跳过数据刷新");
    }

    // 重置刷新标志，确保下次进入时不会自动刷新（除非来自允许的页面）
    this.allowRefresh = false;
    this.fromCreatePage = false;
  },
  // 页面周期函数--监听页面隐藏
  onHide() {},
  // 页面周期函数--监听页面卸载
  onUnload() {},
  // 页面处理函数--监听用户下拉动作
  onPullDownRefresh() {
    this.listParams.page = 1;
    this.loadMore();
    uni.stopPullDownRefresh();
  }
  // 页面处理函数--监听用户上拉触底
  // onReachBottom() {},
  // 页面处理函数--监听页面滚动(not-nvue)
  // onPageScroll(event) {},
  // 页面处理函数--用户点击右上角分享
  // onShareAppMessage(options) {},
};
</script>

<style lang="scss" scoped>
.myHomePage {
  .myHomePage-search-date {
    height: 64rpx;
    width: 100%;
    display: flex;
    color: #666;
    font-size: 26rpx;
    align-items: center;
    background-color: #f5f5f5;
    padding: 0 32rpx;
    align-items: center;
    .sanjiao {
      margin-left: 8rpx;
      width: 0;
      height: 0;
      border-left: 8rpx solid transparent;
      border-right: 8rpx solid transparent;
      border-top: 10rpx solid #666;
      border-radius: 6rpx;
    }
  }
  .myHomePage-content {
    // padding-top: 40rpx;
    .year-line {
      font-size: 32rpx;
      color: #333;
      font-weight: 500;
      padding: 0 32rpx;
      margin: 20px 0 -24rpx 0;
    }
    .myHomePage-content-item {
      display: flex;
      padding: 0 32rpx;
      margin-bottom: 16rpx;
      position: relative;
      // min-height: 100rpx;
      .await-examine {
        background: url(https://tg-prod.oss-cn-beijing.aliyuncs.com/9d7cb1d2-afd8-4e80-b96a-45b631d088f3.png)
          no-repeat;
        background-size: 100% 100%;
        width: 128rpx;
        height: 40rpx;
        line-height: 40rpx;
        font-size: 24rpx;
        color: #fff;
        font-weight: 500;
        position: absolute;
        z-index: 1;
        left: 146rpx;
        top: -7rpx;
      }
      &.same-date {
        margin-top: 40rpx !important;
      }
      .time {
        .lately {
          font-size: 36rpx;
          color: #333;
          font-weight: 500;
        }
        .date {
          .day {
            font-size: 38rpx;
            color: #333;
            font-weight: 500;
            margin-right: 5rpx;
          }
          .month {
            font-size: 24rpx;
            color: #666;
            font-weight: 500;
          }
        }
        width: 109rpx;
        flex-shrink: 0;
        margin-right: 16rpx;
      }
      .create-entry {
        width: 195rpx;
        height: 164rpx;
        border-radius: 16rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f7f7f7;
        img {
          width: 50rpx;
          height: 50rpx;
        }
      }
      .content {
        position: relative;
        .visibility-scope-icon {
          position: absolute;
          z-index: 1;
          right: 10rpx;
          bottom: 6rpx;
          width: 30rpx;
          height: 30rpx;
        }
        .video-wrap {
          width: 190rpx;
          height: 175rpx;
          position: relative;
          .play-btn {
            width: 50rpx;
            height: 50rpx;
            position: absolute;
            top: 50%;
            left: 50%;
            z-index: 1;
            transform: translate(-50%, -50%);
          }
          .video {
            border-radius: 16rpx;
            width: 100%;
            height: 100%;
          }
        }
      }
    }
    .content-txt {
      line-height: 44rpx;
      img {
        border-radius: 12rpx !important;
      }
      // padding: 12rpx 0;
    }
    .content-txt-wrap {
      margin: 12rpx 0;
      border-radius: 4rpx;
    }
    .value {
      &.text {
        position: relative;
        width: 100%;
        margin-left: 0;
        // padding: 0rpx 16rpx;
        background-color: #f5f5f5;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
        line-height: 1.5em;
        text-overflow: ellipsis;
        word-break: break-word; /* 优先在单词间断行 */
        line-break: anywhere;
        height: auto !important;
        font-size: 30rpx;
        .content-txt {
          margin: 12rpx;
        }
        .visibility-scope-icon {
          display: block !important;
          position: absolute;
          z-index: 1;
          right: 10rpx;
          bottom: 6rpx;
          width: 30rpx;
          height: 30rpx;
        }
      }
      .await-examine {
        display: none;
      }
      .visibility-scope-icon {
        display: none;
      }
      margin-left: 16rpx;
      flex: 1;
      width: 100%;
      height: 100%;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 3;
      line-height: 1.5em;
      overflow: hidden;
      text-overflow: ellipsis;
      word-break: break-word; /* 优先在单词间断行 */
      line-break: anywhere;
    }
  }
}
.album {
  margin: 0 auto;
  display: grid;
  grid-template-columns: repeat(6, 15px);
  grid-template-rows: repeat(6, 14px);
  grid-column-gap: 2px;
  grid-row-gap: 2px;
  background-color: #f9f9f9;
  border-radius: 16rpx;
  overflow: hidden;
}
.album image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}
.album.one image {
  grid-column: span 6;
  grid-row: span 6;
}
.album.two image {
  grid-column: span 3;
  grid-row: span 6;
}
.album.three image {
  grid-column: span 3;
  grid-row: span 3;
}
.album.three image:nth-child(1) {
  grid-column: span 3;
  grid-row: span 6;
}
.album.four image {
  grid-column: span 3;
  grid-row: span 3;
}
.album.five image {
  grid-column: span 2;
  grid-row: span 2;
}
.album.five image:nth-child(1) {
  grid-column: span 4;
  grid-row: span 4;
}
.album.five image:nth-child(2) {
  grid-column: span 2;
  grid-row: span 4;
}
.album.six image {
  grid-column: span 2;
  grid-row: span 2;
}
.album.six image:nth-child(1) {
  grid-column: span 4;
  grid-row: span 4;
}
.album.seven image {
  grid-column: span 2;
  grid-row: span 2;
}
.album.seven image:nth-child(1),
.album.seven image:nth-child(2) {
  grid-column: span 2;
  grid-row: span 4;
}
.album.eight image {
  grid-column: span 2;
  grid-row: span 2;
}
.album.eight image:nth-child(1) {
  grid-column: span 2;
  grid-row: span 4;
}
.album.nine image {
  grid-column: span 2;
  grid-row: span 2;
}
.full-screen-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 150rpx;
}
::v-deep .u-toolbar__title {
  font-weight: 500;
}
</style>
