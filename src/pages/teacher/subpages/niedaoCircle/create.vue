<template>
  <div class="create">
    <u-toast ref="uToast"></u-toast>
    <u-navbar :placeholder="true">
      <view class="u-nav-slot" @tap="goBack" slot="left"> 取消 </view>
    </u-navbar>
    <div class="create-content">
      <div style="height: calc(100% - 92rpx); overflow: auto">
        <u--textarea
          v-model="params.content"
          placeholder="写点什么..."
          maxlength="500"
          :autoHeight="true"
          border="none"
        ></u--textarea>
        <div class="upload-wrap">
          <template v-if="params.mediaType === 'image'">
            <div
              class="upload-item"
              v-for="(item, index) in params.image_urls"
              :key="index"
            >
              <image
                :src="item.url"
                @tap="handleUploadImg(index)"
                mode="aspectFill"
              />
              <view @tap="delImage(index)" class="delete-icon-wrap">
                <img
                  class="delete-icon"
                  src="https://tg-prod.oss-cn-beijing.aliyuncs.com/7d2b660a-78e4-45ee-9bd7-1450f284e706.png"
                  alt=""
                />
              </view>
            </div>
          </template>
          <div
            @tap="fullScreenPlayVideo"
            v-else-if="params.mediaType === 'video'"
            style="width: 100%; display: flex; position: relative"
          >
            <video
              id="myVideo"
              :show-center-play-btn="false"
              direction="0"
              :controls="isShowControls"
              @fullscreenchange="fullscreenchange"
              @loadedmetadata="handleVideoLoaded"
              style="width: 100%; margin-right: 20rpx"
              :src="params.image_urls[0].url"
              :poster="params.cover_url"
            ></video>
            <!-- <div style="display: flex; justify-content: center">
              <u-icon
                name="trash-fill"
                size="24"
                @tap.stop="delVideo"
                color="#3c9cff"
              ></u-icon>
            </div> -->
            <view
              @tap.stop="delVideo"
              class="delete-icon-wrap"
              style="
                position: absolute;
                top: -10rpx;
                right: 0rpx;
                z-index: 1;
                width: 100rpx;
                height: 100rpx;
              "
            >
              <img
                class="delete-icon"
                style="width: 40rpx; height: 40rpx; float: right"
                src="https://tg-prod.oss-cn-beijing.aliyuncs.com/7d2b660a-78e4-45ee-9bd7-1450f284e706.png"
                alt=""
              />
            </view>
          </div>
          <template
            v-if="
              !(params.mediaType === 'image' && params.image_urls.length === 9)
            "
          >
            <div
              class="upload-btn upload-item"
              :style="{ marginRight: params.mediaType === '' ? '30rpx' : '0' }"
              v-if="['', 'image'].includes(params.mediaType)"
              @tap="afterRead('image')"
            >
              <img
                src="https://tg-prod.oss-cn-beijing.aliyuncs.com/9e84f9b9-df5b-48dd-9cd0-f7fad64e374f.png"
                alt=""
              />
              <span>添加图片</span>
            </div>
          </template>
          <template v-if="!(params.mediaType === 'video')">
            <div
              class="upload-btn upload-item"
              v-if="['', 'video'].includes(params.mediaType)"
              @tap="afterRead('video')"
            >
              <img
                src="https://tg-prod.oss-cn-beijing.aliyuncs.com/c198cbba-a178-46e2-be30-3aef8626e65f.png"
                alt=""
              />
              <span>上传视频</span>
            </div>
          </template>
        </div>
        <div class="conditions">
          <div class="conditions-item">
            <div class="label">
              <div class="label-text">当前校区</div>
            </div>
            <div class="value">{{ checkedSchool[0].name }}</div>
          </div>
          <div class="conditions-item" @tap="handOpenScope">
            <div class="label">
              <div class="label-text">可见范围</div>
              <div class="visible_scope">
                {{
                  paramsData.visible_scope
                    .map(
                      (item) =>
                        visibilityScopeList.find((i) => i.value === item).label
                    )
                    .join("，")
                }}
              </div>
            </div>
            <div class="value"><u-icon name="arrow-right"></u-icon></div>
          </div>
        </div>
      </div>
      <div
        class="save-btn btn-style"
        @tap="$u.throttle(handleSave, 2000)"
        :class="{
          'btn-loading': btnLoading,
          'btn-disabled': !params.content && !params.image_urls.length
        }"
      >
        <span v-if="!btnLoading">发表</span>
        <span v-else>发表中...</span>
      </div>
    </div>
    <u-popup
      :show="isOpenVisibilityScope"
      mode="bottom"
      :safeAreaInsetTop="true"
      @close="close"
      @open="open"
    >
      <u-navbar title="可见范围" style="font-weight: 500" @leftClick="close">
      </u-navbar>
      <u-toast ref="popupToast"></u-toast>
      <div class="visibility-scope-popup">
        <div class="collapse">
          <div class="collapse-item" @tap="handleCollapseOpen('open')">
            <div class="content">
              <div class="collapse-cell">
                <div class="radio">
                  <img
                    v-if="curVisibilityScope === 'open'"
                    :src="checkedIcon"
                    alt=""
                  />
                </div>
                <div class="collapse-cell-right">
                  <span>公开</span>
                  <span></span>
                </div>
              </div>
            </div>
          </div>
          <div class="collapse-item" @tap="handleCollapseOpen('section')">
            <div class="content">
              <div class="collapse-cell">
                <div class="radio">
                  <img
                    v-if="curVisibilityScope === 'section'"
                    :src="checkedIcon"
                    alt=""
                  />
                </div>
                <div class="collapse-cell-right">
                  <span>部分状态用户可见</span>
                  <!-- <span class="unfold-icon"
                    ><img :src="unfoldIcon" alt=""
                  /></span> -->
                </div>
              </div>
              <div class="cell-content">
                <div class="label">选择状态</div>
                <div
                  class="checked-visibility-scope"
                  v-if="
                    params.visible_scope.length &&
                    params.visible_scope.some((i) => i !== 'open')
                  "
                >
                  {{
                    params.visible_scope
                      .map(
                        (item) =>
                          visibilityScopeList.find((i) => i.value === item)
                            .label
                      )
                      .join("，")
                  }}
                </div>
                <div class="tips" v-else>选中状态的用户可见</div>
              </div>
            </div>
          </div>
        </div>
        <u-popup
          :show="isOpenVisibilityScopeList"
          @close="closeVisibilityScopeList"
          :safeAreaInsetBottom="false"
          @open="open"
        >
          <div class="visibility-scope-list">
            <div
              class="visibility-scope-item"
              v-for="(item, index) in visibilityScopeList.filter(
                (i) => i.value !== 'open'
              )"
              :key="index"
              @tap="handleVisibilityScopeItemClick(item)"
            >
              <div
                :class="[
                  'checkbox',
                  visible_scope_view.includes(item.value) ? 'is-checked' : ''
                ]"
              >
                <u-icon
                  name="checkmark"
                  size="14"
                  bold
                  color="#fff"
                  slot="icon"
                  v-if="visible_scope_view.includes(item.value)"
                ></u-icon>
              </div>
              <div class="label">{{ item.label }}</div>
            </div>
            <div class="visibility-scope-btn">
              <div @tap="handleCheckAll" class="left">
                <div :class="['checkbox', isAllChecked ? 'is-checked' : '']">
                  <u-icon
                    name="checkmark"
                    size="14"
                    bold
                    color="#fff"
                    slot="icon"
                    v-if="isAllChecked"
                  ></u-icon>
                </div>
                <span>全选</span>
              </div>
              <div class="btn-style save-btn" @tap="handleSaveVisibilityScope">
                确定
              </div>
            </div>
          </div>
        </u-popup>
        <div
          class="btn-style done-btn"
          @tap="$u.throttle(handleDoneVisibilityScope, 500)"
        >
          完成
        </div>
      </div>
    </u-popup>
    <view @touchmove.stop.prevent="">
      <Confirm
        :visible="isShowSaveDraftTips"
        :content="deleteContent"
        :confirm-text="confirmButtonText"
        :cancel-text="cancelButtonText"
        @confirm="saveDraft"
        @cancel="closeDraft"
      ></Confirm>
      <alertPup
        :visible="alertVisible"
        ref="alert"
        :content="content"
        @confirm="handleAlerConfirm"
      />
    </view>
  </div>
</template>

<script>
import Confirm from "@/components/confirm/index.vue";
import { momentCreate } from "@/services/niedaoCircle";
import alertPup from "@/components/alert/index.vue";
export default {
  name: "tearcherNiedaoCircleCreate",
  components: { Confirm, alertPup },
  data() {
    return {
      isShowSaveDraftTips: false,
      confirmType: "",
      deleteContent: "将此次编辑保留？",
      confirmButtonText: "保留",
      cancelButtonText: "不保留",
      checkedIcon:
        "https://tg-prod.oss-cn-beijing.aliyuncs.com/df346198-b064-401e-a742-7108c66f0b3d.png",
      unfoldIcon:
        "https://tg-prod.oss-cn-beijing.aliyuncs.com/98087d55-eaff-4aaf-9dba-7e92d2e7d1eb.webp",
      curVisibilityScope: "open",
      isOpenVisibilityScope: false,
      isOpenVisibilityScopeList: false,
      params: {
        content: "",
        mediaType: "",
        cover_url: "",
        image_urls: [],
        image_type: 1,
        visible_scope: ["open"]
      },
      visibilityScopeList: [
        {
          label: "公开",
          value: "open"
        },
        {
          label: "在读",
          value: "in_school"
        },
        {
          label: "休学",
          value: "out_school"
        },
        {
          label: "试听",
          value: "audition"
        },
        {
          label: "临时",
          value: "temp"
        },
        {
          label: "意向",
          value: "customer"
        }
      ],
      visible_scope_view: [],
      isSaveing: false,
      isAllChecked: false,
      userInfo: {},
      checkedSchool: [],
      isShowControls: false,
      btnLoading: false,
      btnLoadingType: "",
      paramsData: {
        content: "",
        mediaType: "",
        cover_url: "",
        image_urls: [],
        image_type: 1,
        visible_scope: ["open"]
      },
      alertVisible: false,
      content: "视频大小不能超过200MB"
    };
  },
  computed: {},
  methods: {
    close() {
      this.isOpenVisibilityScope = false;
    },
    open() {
      this.isOpenVisibilityScope = true;
    },
    closeVisibilityScopeList() {
      this.isOpenVisibilityScopeList = false;
    },
    fullScreenPlayVideo(index) {
      this.videoContext = uni.createVideoContext(`myVideo`, this);
      console.log(index, this.videoContext, "videoContext");
      this.isShowControls = true;
      this.videoContext.requestFullScreen();
    },
    fullscreenchange({ detail }) {
      console.log(detail, "detail");
      const videoContext = uni.createVideoContext(`myVideo`, this);
      if (detail.fullscreen) {
        videoContext.play();
        this.isShowControls = true;
      } else {
        videoContext.pause();
        this.isShowControls = false;
      }
    },
    handleVideoLoaded(e) {
      // 通过事件对象获取宽高（小程序和 H5 兼容）
      const { width, height } = e.detail || {
        width: e.target.videoWidth,
        height: e.target.videoHeight
      };
      this.params.image_urls[0] = {
        url: this.params.image_urls[0].url,
        size: this.params.image_urls[0].size,
        width,
        height
      };
      console.log(this.params.image_urls, "videoLoaded");
    },
    goBack() {
      console.log(this.params);
      if (
        this.params.content ||
        this.params.image_urls.length ||
        (this.curVisibilityScope === "section" &&
          this.params.visible_scope.length)
      ) {
        this.isShowSaveDraftTips = true;
        this.confirmType = "saveDraftTipsConfirm";
        this.deleteContent = "将此次编辑保留？";
        this.confirmButtonText = "保留";
        this.cancelButtonText = "不保留";
      } else {
        uni.navigateBack();
        uni.removeStorageSync("draft");
      }
    },
    saveDraft() {
      console.log(this.confirmType, "saveDraft");
      if (this.confirmType === "deleteVideoConfirm") {
        this.params.cover_url = "";
        this.params.image_urls = [];
        this.params.mediaType = "";
      } else {
        uni.setStorageSync("draft", this.params);
        uni.navigateBack();
      }
      console.log(this.params, "saveDraft");
      this.paramsData = { ...this.params };
      this.isShowSaveDraftTips = false;
    },
    closeDraft() {
      console.log(this.confirmType, "123");
      if (this.confirmType === "saveDraftTipsConfirm") {
        uni.navigateBack();
        // console.log(this.confirmType);
        uni.setStorageSync("draft", "");
      }
      this.isShowSaveDraftTips = false;
    },
    handleVisibilityScopeItemClick(item) {
      if (this.visible_scope_view.includes(item.value)) {
        this.visible_scope_view = this.visible_scope_view.filter(
          (i) => i !== item.value
        );
      } else {
        this.visible_scope_view.push(item.value);
      }
      console.log(
        this.visible_scope_view.length,
        this.visibilityScopeList.length - 1,
        "this.visible_scope_view"
      );
      if (
        this.visible_scope_view.length !==
        this.visibilityScopeList.length - 1
      ) {
        this.isAllChecked = false;
      } else {
        this.isAllChecked = true;
      }
    },
    handleCheckAll() {
      this.isAllChecked = !this.isAllChecked;
      if (this.isAllChecked) {
        this.visible_scope_view = this.visibilityScopeList
          .filter((i) => i.value !== "open")
          .map((item) => item.value);
        // this.visible_scope_view = this.params.visible_scope;
      } else {
        this.visible_scope_view = [];
        this.params.visible_scope = [];
      }
    },
    handleSaveVisibilityScope() {
      console.log(this.params.visible_scope, "this.params.visible_scope");
      this.params.visible_scope = this.visible_scope_view;
      this.isOpenVisibilityScopeList = false;
    },
    handleDoneVisibilityScope() {
      console.log(
        this.params.visible_scope,
        this.visible_scope_view,
        "this.params.visible_scope"
      );
      if (
        this.visible_scope_view.length === 0 &&
        this.curVisibilityScope !== "open"
      ) {
        this.$refs.popupToast.show({
          type: "default",
          message: "请选择可见范围"
        });
        return;
      }
      if (this.params.visible_scope.length === 0) {
        this.params.visible_scope = ["open"];
      }
      this.paramsData = { ...this.params };
      this.isOpenVisibilityScope = false;
    },
    handleCollapseOpen(type) {
      this.curVisibilityScope = type;
      console.log(type);
      if (type === "section") {
        console.log(this.params, "this.params.visible_scope");
        if (
          this.params.visible_scope.length === 0 ||
          this.visible_scope_view.length === 0
        ) {
          this.params.visible_scope = [];
          this.visible_scope_view = [];
        }
        this.isOpenVisibilityScopeList = true;
      } else {
        this.isAllChecked = false;
        this.visible_scope_view = [];
        this.params.visible_scope = ["open"];
      }
    },
    handleUploadImg(current) {
      uni.previewImage({
        current, // 当前点击的图片索引
        urls: this.params.image_urls.map((item) => item.url) // 所有图片的 URL 列表
      });
    },
    delVideo() {
      this.isShowSaveDraftTips = true;
      this.confirmType = "deleteVideoConfirm";
      this.deleteContent = "要删除此视频吗？";
      this.confirmButtonText = "删除";
      this.cancelButtonText = "取消";
    },
    delImage(index) {
      if (this.params.image_urls.length <= 1) {
        this.params.mediaType = "";
      }
      this.params.image_urls.splice(index, 1);
    },
    afterRead(type) {
      console.log(type, "type");
      if (type === "video") {
        if (this.params.image_urls.length >= 1) {
          return false;
        }
      } else {
        // this.params.mediaType = type;
        // this.paramsData.mediaType = type;
        if (this.params.image_urls.length >= 9) {
          return false;
        }
      }
      this.btnLoading = true;
      this.btnLoadingType = "save";
      uni.chooseMedia({
        count: type === "image" ? 9 - this.params.image_urls.length : 1,
        mediaType: [type],
        sizeType: ["original", "compressed"],
        success: (res) => {
          uni.showLoading({
            title: "上传中..."
          });
          console.log(res, "res");
          if (type === "video") {
            const videoFile = res.tempFiles[0];
            const maxSize = 200 * 1024 * 1024;
            if (videoFile.size > maxSize) {
              this.btnLoading = false;
              this.btnLoadingType = "";
              this.paramsData = { ...this.params };
              uni.hideLoading();
              this.alertVisible = true;
              // this.$refs.uToast.show({
              //   type: "error",
              //   message: "视频大小不能超过200MB"
              // });
              return;
            }
          }

          if (type === "video") {
            this.Oss.uploadFile(
              res.tempFiles[0],
              (res) => {
                this.params.cover_url = res;
                this.paramsData.cover_url = res;
              },
              "thumbTempFilePath",
              false
            );
          }
          res.tempFiles.forEach(async (item) => {
            console.log(item, type, "item");
            console.log(this.params, "afterRead");
            if (type === "image") {
              uni.getImageInfo({
                src: item.tempFilePath, // 图片临时路径
                success: (info) => {
                  console.log("图片宽高:", this.params, info.height);
                  this.Oss.uploadFile(item, (res) => {
                    this.params.image_urls.push({
                      url: res,
                      size: item.size,
                      width: info.width,
                      height: info.height
                    });
                    this.params.mediaType = type;
                    this.paramsData.mediaType = type;
                    this.paramsData = { ...this.params };
                    uni.hideLoading();
                  });
                  this.params.mediaType = type;
                  this.paramsData.mediaType = type;
                },
                fail: (err) => {
                  console.error("获取图片信息失败:", err);
                  // this.params.mediaType = "";
                  // this.paramsData.mediaType = "";
                  uni.hideLoading();
                  uni.showToast({
                    title: "图片上传失败！",
                    icon: "none"
                  });
                }
              });
            } else {
              item.loading = false;
              this.Oss.uploadFile(item, (res) => {
                console.log(res, "res", item);
                this.params.image_urls.push({
                  url: res,
                  size: item.size
                });
                this.paramsData = { ...this.params };
                this.params.mediaType = type;
                this.paramsData.mediaType = type;
                uni.hideLoading();
              });
            }
          });
          this.btnLoading = false;
          this.btnLoadingType = "";
          console.log(this.params.image_urls, "this.params.image_urls");
        },
        fail: (err) => {
          this.btnLoading = false;
          this.btnLoadingType = "";
          this.paramsData = { ...this.params };
          // this.params.mediaType = "";
          // this.paramsData.mediaType = "";
          uni.hideLoading();
          console.error("选择媒体文件失败:", err);
        }
      });
    },
    async handleSave() {
      if (this.isSaveing) {
        return;
      }
      if (
        this.btnLoading ||
        (!this.params.content && !this.params.image_urls.length)
      ) {
        return;
      }
      if (this.params.visible_scope.length === 0) {
        this.$refs.uToast.show({
          type: "default",
          message: "请选择可见范围"
        });
        return;
      }
      if (this.params.image_urls.length <= 1) {
        const videoTypes = ["mp4", "mpeg", "webm", "ogg", "mov"];
        if (
          videoTypes.some((type) =>
            this.params.image_urls[0]?.url
              ? this.params.image_urls[0]?.url.endsWith(`.${type}`)
              : this.params.image_urls[0]?.url.endsWith(`.${type}`)
          )
        ) {
          this.params.image_type = 2;
        } else {
          this.params.image_type = 1;
        }
      } else {
        this.params.image_type = 1;
      }
      console.log(this.params);
      if (!uni.$u.trim(this.params.content) && !this.params.image_urls.length) {
        this.$refs.uToast.show({
          type: "default",
          message: "请输入内容或上传图片"
        });
        return;
      }
      this.params.employee_id = this.userInfo.employee_id;
      this.params.department_ids = [this.checkedSchool[0].id];
      console.log("params :>> ", this.params);
      this.isSaveing = true;
      uni.showLoading({
        title: "发表中"
      });
      const res = await momentCreate(this.params);
      if (res.code === 0) {
        this.$refs.uToast.show({
          type: "default",
          message: "发送成功，审核通过后用户可见"
        });
        uni.hideLoading();
        uni.removeStorageSync("draft");
        setTimeout(() => {
          uni.navigateBack();
        }, 1000);
      } else {
        this.$refs.uToast.show({
          type: "error",
          message: res.message
        });
      }
      this.isSaveing = false;
      console.log("res :>> ", res);
    },
    handOpenScope() {
      const status = this.paramsData.visible_scope.some((i) => i !== "open")
        ? "section"
        : "open";
      if (status === "open") {
        this.paramsData.visible_scope = ["open"];
        this.visible_scope_view = [];
      }
      this.curVisibilityScope = status;
      if (
        this.visible_scope_view.length !==
        this.visibilityScopeList.length - 1
      ) {
        this.isAllChecked = false;
      } else {
        this.isAllChecked = true;
      }
      console.log(this.params, "this.paramsData");
      this.paramsData.content = this.params.content;
      this.paramsData.image_urls = this.params.image_urls;
      this.paramsData.mediaType = this.params.mediaType;
      this.paramsData.image_type = this.params.image_type;
      console.log(this.paramsData, "this.paramsData");
      this.params = { ...this.paramsData };
      this.isOpenVisibilityScope = true;
    },
    handleAlerConfirm() {
      this.alertVisible = false;
    }
  },
  watch: {},

  // 页面周期函数--监听页面加载
  // onLoad() {
  //   if (uni.getStorageSync("draft")) {
  //     console.log("222");
  //     this.params = uni.getStorageSync("draft");
  //   }
  // },
  // 页面周期函数--监听页面初次渲染完成
  onReady() {
    this.Oss.getAliyun();
  },
  // 页面周期函数--监听页面显示(not-nvue)
  onShow() {
    this.userInfo = uni.getStorageSync("user");
    this.checkedSchool = uni.getStorageSync("checkedSchool");
    console.log(uni.getStorageSync("draft"), "onShow里获取draft");

    if (uni.getStorageSync("draft")) {
      this.params = uni.getStorageSync("draft");
      this.paramsData = { ...this.params };
      this.visible_scope_view = this.params.visible_scope;
    }
  },
  // 页面周期函数--监听页面隐藏
  onHide() {},
  // 页面周期函数--监听页面卸载
  onUnload() {}
  // 页面处理函数--监听用户下拉动作
  // onPullDownRefresh() { uni.stopPullDownRefresh(); },
  // 页面处理函数--监听用户上拉触底
  // onReachBottom() {},
  // 页面处理函数--监听页面滚动(not-nvue)
  // onPageScroll(event) {},
  // 页面处理函数--用户点击右上角分享
  // onShareAppMessage(options) {},
};
</script>

<style lang="scss" scoped>
::v-deep .u-navbar__content {
  border-bottom: 1rpx solid #eee;
}
::v-deep .u-upload__wrap__preview__image {
  width: 196rpx !important;
  height: 196rpx !important;
  border-radius: 16rpx;
}
::v-deep .u-textarea {
  padding: 0 !important;
}
::v-deep .u-popup__content {
  border-radius: 22rpx;
  overflow: hidden;
}
.btn-style {
  border-radius: 32px;
  background: linear-gradient(15deg, #ffbf0d 18.1%, #ffcb3c 83.29%);
  box-shadow: 0px -4px 8px 0px #eaac00 inset;
  text-align: center;
  line-height: 92rpx;
  color: #fff;
  font-size: 24rpx;
  font-weight: 600;
}
.create {
  width: 100%;
  height: 100vh;
  .create-header {
    width: 100%;
    height: 100rpx;
  }
  .create-content {
    width: 100%;
    height: calc(100% - 62rpx - 80rpx);
    display: flex;
    flex-direction: column;
    padding: 29rpx 62rpx;
    padding-right: 50rpx;
    padding-bottom: 80rpx;
    position: relative;
    .save-btn {
      // position: absolute;
      // bottom: 122rpx;
      // left: 50%;
      // transform: translateX(-50%);
      // width: calc(100% - 64rpx);
      font-size: 32rpx;
      margin-top: 20rpx;
      height: 92rpx;
      padding: 0 32rpx;
    }
    .upload-wrap {
      margin-top: 80rpx;
      margin-bottom: 80rpx;
      width: 100%;
      display: flex;
      flex-wrap: wrap;
      .upload-item {
        width: 196rpx;
        height: 196rpx;
        border-radius: 16rpx;
        margin: 0 17rpx 40rpx 0;
        position: relative;
        .delete-icon-wrap {
          width: 100rpx;
          height: 100rpx;
          position: absolute;
          top: -10rpx;
          right: -8rpx;
        }
        .delete-icon {
          width: 40rpx;
          height: 40rpx;
          float: right;
        }
        image {
          width: 100%;
          height: 100%;
          border-radius: 16rpx;
        }
        ::v-deep .u-icon {
          justify-content: center;
        }
      }
      .upload-btn {
        display: flex;
        align-items: center;
        flex-direction: column;
        justify-content: center;
        background-color: #f7f7f7;
        img {
          width: 60rpx;
          height: 60rpx;
          margin-bottom: 8rpx;
        }
        span {
          font-size: 26rpx;
          color: #999;
          font-weight: 400;
        }
      }
    }
    .conditions {
      .conditions-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 28rpx 0;
        border-bottom: 1rpx solid #eee;
        &:first-child {
          border-top: 1rpx solid #eee;
        }
        .label {
          font-size: 28rpx;
          color: #333;
          font-weight: 500;
          .visible_scope {
            color: #666;
            margin-top: 12rpx;
            font-weight: 400;
            font-size: 26rpx;
          }
          .label-text {
            font-size: 28rpx;
            color: #333;
            font-weight: 500;
          }
        }
        .value {
          font-size: 28rpx;
          color: #666;
        }
      }
    }
  }
  .visibility-scope-popup {
    height: 100vh;
    width: 100%;
    padding-top: 250rpx;
    background-color: #fff;
    position: relative;
    .collapse {
      padding-left: 38rpx;
      .collapse-item {
        display: flex;
        align-items: center;
        .content {
          flex: 1;
          .collapse-cell {
            display: flex;
            align-items: center;
            .radio {
              width: 30rpx;
              height: 30rpx;
              img {
                width: 100%;
                height: 100%;
              }
            }
            .collapse-cell-right {
              border-bottom: 1rpx solid #eee;
              flex: 1;
              display: flex;
              justify-content: space-between;
              align-items: center;
              color: #333;
              padding: 30rpx 41rpx 30rpx 0;
              margin-left: 38rpx;
            }
            .unfold-icon {
              width: 30rpx;
              height: 30rpx;
              img {
                width: 100%;
                height: 100%;
                transform: rotate(180deg);
              }
            }
          }
          .cell-content {
            padding: 20rpx 40rpx 20rpx 0rpx;
            margin-left: 68rpx;
            border-bottom: 1rpx solid #eee;
            .label {
              font-size: 28rpx;
              color: #fb0;
              font-weight: 500;
              margin-bottom: 4rpx;
            }
            .checked-visibility-scope {
              display: flex;
              align-items: center;
              font-weight: 400;
              font-size: 26rpx;
              color: #333;
            }
            .tips {
              font-size: 24rpx;
              color: #999;
              margin-top: 4rpx;
              font-weight: 400;
            }
          }
        }
      }
    }
    .visibility-scope-list {
      .checkbox {
        width: 40rpx;
        height: 40rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        border: 3rpx solid #666;
        margin-right: 28rpx;
        ::v-deep .u-icon__icon {
          display: none;
        }
        &.is-checked {
          background-color: #ffc525;
          border: 1rpx solid #ffc525;
          ::v-deep .u-icon__icon {
            display: block;
            color: #fff;
          }
        }
      }
      .visibility-scope-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-left: 32rpx;
        &:last-child {
          .label {
            border-bottom: none;
          }
        }
        .label {
          flex: 1;
          padding: 38rpx 0;
          border-bottom: 1rpx solid #eee;
          font-size: 28rpx;
          color: #333;
          // font-weight: 500;
        }
      }
      .visibility-scope-btn {
        width: 100%;
        height: 160rpx;
        background-color: #f5f5f5;
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        padding: 0 32rpx;
        padding-top: 36rpx;

        .left {
          display: flex;
          align-items: center;
          padding-top: 14rpx;
          span {
            font-size: 28rpx;
            color: #333;
          }
        }
        .save-btn {
          width: 160rpx;
          height: 64rpx;
          line-height: 64rpx;
        }
      }
    }
    .done-btn {
      position: fixed;
      bottom: 40rpx;
      left: 50%;
      font-size: 32rpx;
      width: calc(100% - 64rpx);
      height: 92rpx;
      transform: translateX(-50%);
    }
  }
}
.btn-disabled {
  opacity: 0.5;
}
::v-deep .u-navbar__content__title {
  font-weight: 500;
}
#myVideo {
  border-radius: 16rpx;
}
</style>
