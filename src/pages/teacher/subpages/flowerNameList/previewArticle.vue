<template>
  <div class="bottom-safe-area preview-article">
    <div class="nav-wrapper">
      <u-navbar
        :placeholder="true"
        bgColor="transparent"
        :title="options.feedback_id ? '详情' : '预览'"
        titleStyle="color: #fff;font-size:36rpx;font-weight:500"
        leftIconSize="20px"
        leftIconColor="#fff"
        :autoBack="true"
      ></u-navbar>
    </div>

    <div :style="'height:' + midHeight + 'px'" class="content-wrapper">
      <div class="coentet">
        <div class="aticle-content">
          <div class="flex title">
            <div class="avatar-box">
              <u-image
                shape="circle"
                width="90rpx"
                height="90rpx"
                :showMenuByLongpress="false"
                :src="
                  options.student_gender === 'female'
                    ? 'https://tg-prod.oss-cn-beijing.aliyuncs.com/cf769e6f-5f97-41c6-a3c8-b796ca0df32c.png'
                    : options.student_gender === 'male'
                    ? 'https://tg-prod.oss-cn-beijing.aliyuncs.com/9e8b7d64-c249-45e8-ac42-3a67a921f84d.png'
                    : 'https://tg-prod.oss-cn-beijing.aliyuncs.com/db5c1013-4e99-41c7-bfa4-f93500fe0622.png'
                "
                :lazy-load="true"
              ></u-image>
              <div class="crown">
                <u-image
                  width="50rpx"
                  height="32rpx"
                  src="https://tg-prod.oss-cn-beijing.aliyuncs.com/e7d687cf-1aae-46ab-b539-ab4bd68d75ed.png"
                  :lazy-load="true"
                ></u-image>
              </div>
            </div>

            <div class="top">
              <div class="text name">{{ options.student_name }}</div>
              <div class="text time">{{ previewTime }}</div>
            </div>
          </div>
          <div class="tg_editor-content">
            <sp-editor :readOnly="true" @init="initEditor"></sp-editor>
          </div>
        </div>
      </div>
    </div>
    <div
      v-if="!options.feedback_id && $hasPermission(['feedback_send'])"
      class="ctrl-box"
    >
      <span @tap="tips_visible = true" class="send">发送</span>
      <span @tap="backClick" class="preview">返回</span>
    </div>
    <div
      v-if="options.is_cancel == 2 && $hasPermission(['feedback_cancel'])"
      class="ctrl-box"
    >
      <span @tap="tips_revoke_visible = true" class="send">撤销</span>
      <span @tap="backClick" class="preview">返回</span>
    </div>
    <u-modal
      content="确定发送内容？"
      @confirm="confirmTips"
      @cancel="tips_visible = false"
      :showCancelButton="true"
      :show="tips_visible"
      confirmText="发送"
      cancelText="取消"
      width="500rpx"
    ></u-modal>
    <u-modal
      content="确定撤销内容？"
      @confirm="confirmRevoke"
      @cancel="tips_revoke_visible = false"
      :showCancelButton="true"
      :show="tips_revoke_visible"
      confirmText="撤销"
      cancelText="取消"
      width="500rpx"
    ></u-modal>
  </div>
</template>

<script>
import {
  getFeedbackDetail,
  feedbackSend,
  feedbackCancel,
  feedbackKindsSend
} from "@/services/flowerNameList";
export default {
  name: "previewArticle",
  components: {},
  data() {
    return {
      content: "",
      options: "",
      previewTime: "",
      tips_visible: false,
      tips_revoke_visible: false,
      editorIns: null,
      midHeight: 0
    };
  },
  computed: {},
  methods: {
    htmlDecode(input) {
      const htmlEntities = {
        "&quot;": '"',
        "&apos;": "'",
        "&amp;": "&",
        "&lt;": "<",
        "&gt;": ">"
        // 可根据需要添加更多HTML实体
      };
      const regex = new RegExp(
        "(" + Object.keys(htmlEntities).join("|") + ")",
        "g"
      );
      return input.replace(regex, (match) => htmlEntities[match]);
    },
    getFeedbackConetent() {
      getFeedbackDetail({
        ...this.options
      }).then((res) => {
        const { content, student_name, created_at, student_gender } = res.data;
        this.content = content || "";

        this.options.student_name = student_name ?? "";
        this.options.student_gender = student_gender ?? "";
        this.previewTime = this.$u.timeFormat(
          created_at,
          "yyyy-mm-dd hh:MM:ss"
        );

        setTimeout(() => {
          this.editorIns.setContents({
            html: content
          });
        }, 500);
        // this.editorIns.setContents({
        //   html: content
        // });
      });
    },
    confirmTips() {
      const sourcePages = ["classroom", "student"];
      if (sourcePages.includes(this.options.source)) {
        uni.$u.throttle(this.sendKindsClick, 500);
      } else {
        uni.$u.throttle(this.sendClick, 500);
      }
    },
    sendKindsClick() {
      this.editorIns.getContents().then(async (res) => {
        const content = res.html;
        const { classroom_id, classroom_name, source, type } = this.options;
        const paramsStudent =
          source === "classroom"
            ? []
            : JSON.parse(uni.getStorageSync("checkedStudents"));
        console.log(paramsStudent);
        const params = {
          content,
          classroom: {
            classroom_id,
            classroom_name
          },
          students: paramsStudent,
          type
        };
        const { code, message } = await feedbackKindsSend(params);
        if (code === 0) {
          uni.removeStorageSync("checkedStudents");
          uni.showToast({ title: "操作成功", icon: "success" });
          setTimeout(() => {
            uni.redirectTo({
              url: `/pages/teacher/subpages/flowerNameList/convoHistory?classroom_id=${classroom_id}`
            });
          }, 1000);
        } else {
          uni.showToast({ title: message, icon: "error" });
        }
      });
    },
    confirmRevoke() {
      uni.$u.throttle(this.revokClick, 500);
    },
    // 撤销
    revokClick() {
      // 撤销评价
      const { feedback_id } = this.options;
      feedbackCancel({
        feedback_id,
        is_cancel: 1
      })
        .then((res) => {
          this.tips_revoke_visible = false;
          console.log(res);
          const { code, message } = res;
          if (code === 0) {
            uni.$u.toast("撤销成功!");
            uni.$u.sleep(1000).then(() => {
              // uni.navigateBack({
              //   delta: 1,
              //   success(res) {
              //     res.eventChannel.emit("revokEvent", {
              //       feedback_id
              //     });
              //   }
              // });
              uni.$emit("revokEvent", {
                feedback_id
              });
              uni.navigateBack();
            });
          } else {
            uni.$u.toast(message);
          }
        })
        .catch(() => {
          this.tips_revoke_visible = false;
          uni.hideToast();
          uni.$u.toast("撤销失败!");
        });
    },
    sendClick() {
      const _this = this;
      const html = _this.content;
      const {
        teacher_id,
        student_id,
        classroom_id,
        classroom_name,
        teacher_name,
        student_gender,
        student_name
      } = _this.options;
      feedbackSend({
        classroom: {
          classroom_id,
          classroom_name
        },
        content: html,
        students: [
          {
            student_gender,
            student_id,
            student_name
          }
        ],
        teacher: {
          teacher_id,
          teacher_name
        }
      })
        .then((res) => {
          const { code, message } = res;
          if (code === 0) {
            uni.redirectTo({
              url: `/pages/teacher/subpages/flowerNameList/convoHistory?classroom_id=${classroom_id}`
            });
          } else {
            this.msgToast(message, "error");
          }
        })
        .catch(() => {
          this.msgToast("发送失败！", "error");
        });
    },
    backClick() {
      uni.navigateBack();
    },
    initEditor(editor) {
      // this.editorIns = editor; // 保存编辑器实例
      // 保存编辑器实例后，可以在此处获取后端数据，并赋值给编辑器初始化内容
      this.editorIns = editor;
    }
  },
  watch: {},

  // 页面周期函数--监听页面加载
  onLoad(options) {
    const winInfo = uni.getWindowInfo();
    console.log("winInfo :>> ", winInfo);
    this.midHeight = winInfo.windowHeight - winInfo.statusBarHeight - 80;
    const { feedback_id } = options;
    console.log(options);
    // 查看详情
    if (feedback_id) {
      this.options = options;
      this.getFeedbackConetent();
    } else {
      const eventChannel = this.getOpenerEventChannel();
      eventChannel.on("editorInsHtml", (data) => {
        this.content = data.html;
        this.options = data.options;
        setTimeout(() => {
          this.editorIns.setContents({
            html: this.htmlDecode(data.html)
          });
        }, 500);
      });
      this.previewTime = uni.$u.timeFormat(
        new Date().getTime(),
        "yyyy-mm-dd hh:MM:ss"
      );
    }
  },
  // 页面周期函数--监听页面初次渲染完成
  onReady() {},
  // 页面周期函数--监听页面显示(not-nvue)
  onShow() {},
  // 页面周期函数--监听页面隐藏
  onHide() {},
  // 页面周期函数--监听页面卸载
  onUnload() {}
  // 页面处理函数--监听用户下拉动作
  // onPullDownRefresh() { uni.stopPullDownRefresh(); },
  // 页面处理函数--监听用户上拉触底
  // onReachBottom() {},
  // 页面处理函数--监听页面滚动(not-nvue)
  // onPageScroll(event) {},
  // 页面处理函数--用户点击右上角分享
  // onShareAppMessage(options) {},
};
</script>

<style lang="scss" scoped>
.preview-article {
  height: 100vh;
  width: 100%;
  overflow: hidden;
  background: linear-gradient(180deg, #204fef 0%, #69a2f8 36.96%);
  display: flex;
  flex-direction: column;
  .content-wrapper {
    padding-top: 20rpx;
    overflow: hidden;
    .coentet {
      position: relative;
      width: 687rpx;
      padding: 50rpx 50rpx 60rpx 40rpx;
      display: grid;
      margin: 0 auto;
      height: 100%;
      background-image: url("https://tg-prod.oss-cn-beijing.aliyuncs.com/87a40c2e-1488-4887-a2ea-c7bb8c171766.png");
      background-repeat: no-repeat;
      background-size: 100% 100%;
      .aticle-content {
        border-radius: 20rpx;
        border: 2px solid #7f9cea;
        z-index: 2;
        padding: 36rpx;
        // max-height: 960rpx;
        overflow-y: scroll;
        overflow-x: hidden;
        display: flex;
        flex-direction: column;
        .title {
          display: flex;
          align-items: center;
          margin-bottom: 20rpx;
          .top {
            display: grid;
            margin-left: 20rpx;
          }
          .name {
            color: #ff950f;
            font-size: 32rpx;
            font-weight: 500;
            margin-bottom: 12rpx;
          }
          .time {
            color: #8492a6;
            font-size: 24rpx;
            font-weight: 400;
            line-height: 24rpx;
          }
        }
        .tg_editor-content {
          flex: 1;
          padding-bottom: 20rpx;
        }
        ::v-deep .editor-container {
          padding: 0 !important;
        }
        .avatar-box {
          position: relative;
          .crown {
            position: absolute;
            top: -9rpx;
            left: -12rpx;
          }
        }
      }
    }
  }
  .ctrl-box {
    // position: absolute;
    // bottom: 0rpx;
    // left: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 45px;
    // padding-bottom: calc(env(safe-area-inset-bottom) + 20rpx);
    width: 100%;
    height: 100px;
    span {
      width: 240rpx;
      height: 50px;
      display: block;
      border-radius: 50px;
      text-align: center;
      line-height: 50px;
      font-size: 34rpx;
      font-weight: 500;
      &.preview {
        color: #8492a6;
        background: rgb(247 250 253);
      }
      &.send {
        color: #ffbf0d;
        background: #fff;
      }
      &:active {
        opacity: 0.5;
      }
    }
  }
}
</style>
