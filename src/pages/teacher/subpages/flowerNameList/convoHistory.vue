<template>
  <div class="customer-list">
    <u-toast ref="uToast"></u-toast>
    <u-loading-icon
      :show="pageLoading"
      color="#FFBF0D"
      textColor="#545556"
      mode="semicircle"
      :vertical="true"
      text="加载中..."
    ></u-loading-icon>
    <next-paging
      ref="paging"
      v-model="data"
      @query="getList"
      :autoShowBackToTop="true"
      :defaultPageSize="searchForm.page_size"
    >
      <!--top插槽-->
      <template #top
        ><div class="slotTop">
          <u-navbar
            :placeholder="true"
            bgColor="#3061f2"
            title="学员沟通记录"
            titleStyle="color: #fff;font-size:36rpx;font-weight:500"
            leftIconSize="20px"
            leftIconColor="#fff"
            :autoBack="false"
            @leftClick="back"
          >
          </u-navbar>
          <div class="customer-list-content">
            <div class="border-around search-box">
              <u--input
                placeholder="搜索学员姓名"
                prefixIcon="search"
                v-model="searchForm.student_name"
                border="none"
                color="#fff"
                confirmType="search"
                @confirm="search"
                prefixIconStyle="font-size: 24px;color: #D3DCE6"
              ></u--input>
              <div @click="search" style="color: #fff">搜索</div>
            </div>
          </div>
        </div></template
      >
      <!-- next-paging默认铺满全屏，此时页面所有view都应放在next-paging标签内，否则会被盖住 -->
      <!-- 需要固定在页面顶部的view请通过slot="top"插入，包括自定义的导航栏 -->
      <div class="list-box">
        <block :key="item.student_id" v-for="(item, index) in data">
          <div @tap="goDetail(index)" class="list-item">
            <div class="left">
              <u-image
                shape="circle"
                width="90rpx"
                height="90rpx"
                :showMenuByLongpress="false"
                :src="
                  item.student_gender === 'female'
                    ? 'https://tg-prod.oss-cn-beijing.aliyuncs.com/cf769e6f-5f97-41c6-a3c8-b796ca0df32c.png'
                    : item.student_gender === 'male'
                    ? 'https://tg-prod.oss-cn-beijing.aliyuncs.com/9e8b7d64-c249-45e8-ac42-3a67a921f84d.png'
                    : 'https://tg-prod.oss-cn-beijing.aliyuncs.com/db5c1013-4e99-41c7-bfa4-f93500fe0622.png'
                "
                :lazy-load="true"
              ></u-image>
              <div class="name">
                <div class="text">{{ item.student_name }}</div>
                <div class="text school">发送人：{{ item.teacher_name }}</div>
              </div>
            </div>
            <div class="right">
              <span class="read off" v-if="item.is_cancel === 1">已撤销</span>
              <span
                v-else
                :class="item.read_status === 1 ? 'on' : ''"
                class="read"
                >{{ item.read_status === 1 ? "已读" : "未读" }}</span
              >
            </div>
            <div class="time">
              {{ $u.timeFormat(item.created_at, "yyyy-mm-dd hh:MM:ss") }}
            </div>
          </div>
        </block>
      </div>
      <!--bottom插槽-->
      <!-- <template #bottom
      ><view class="slotBottom"><text>我是底部插槽内容</text></view></template
    > -->
    </next-paging>
  </div>
</template>

<script>
import { getFeedbackStudentList } from "@/services/flowerNameList";
export default {
  name: "studentList",
  components: {},
  data() {
    return {
      searchForm: {
        student_name: "",
        show_leave_student: true,
        status: "in_classroom",
        page: 1,
        page_size: 20
      },
      data: [],
      count: 0, // 总条数
      pageCount: 0, // 总页数
      pageLoading: false,
      isFisrtEnter: true,
      classroom_id: ""
    };
  },
  computed: {},
  created() {},
  methods: {
    search() {
      // const { name } = this.searchForm;
      // if (name === "") {
      //   this.$refs.uToast.show({
      //     message: "请输入关键字"
      //   });
      //   return;
      // }
      this.$refs.paging.reload();
    },
    getList(pageNo) {
      // if (pageNo > this.pageCount) {
      //   this.$refs.paging.complete([]);
      // } else {
      this.searchForm.page = pageNo;
      const schId = uni.getStorageSync("checkedSchool");
      if (schId.length) {
        this.searchForm.department_id = schId.map((item) => item.id);
      }
      if (this.isFisrtEnter) {
        this.pageLoading = true;
        this.isFisrtEnter = false;
      }

      getFeedbackStudentList({
        classroom_id: this.classroom_id,
        ...this.searchForm,
        // 班级类型
        type: "class_notice"
      })
        .then((res) => {
          const data = res?.data?.results ?? [];
          if (data) {
            this.count = res.count;
            // 获取数据总页数
            this.pageCount = Math.ceil(this.count / this.searchForm.page_size);
            // const arr = data.map((item) => {
            //   const {
            //     student_name,
            //     student_mobile,
            //     student_id,
            //     teacher_id,
            //     classroom_id,
            //     student_gender
            //   } = item;
            //   return {
            //     student_name,
            //     student_mobile,
            //     student_id,
            //     teacher_id,
            //     classroom_id,
            //     student_gender
            //   };
            // });
            this.$refs.paging.complete(data);
            this.pageLoading = false;
          }
        })
        .catch(() => {
          this.pageLoading = false;
        });
      // }
    },
    goDetail(index) {
      const {
        student_id,
        feedback_id,
        student_feedback_id,
        teacher_name,
        classroom_id,
        classroom_name
      } = this.data[index];
      if (this.$hasPermission(["feedback_detail"])) {
        const is_cancel = this.data[index].is_cancel;
        const paramsData = {
          student_id,
          feedback_id,
          is_cancel,
          openPreview: is_cancel === 1 ? undefined : 1,
          student_feedback_id,
          isShowBack: is_cancel !== 1 ? undefined : 1,
          isShowBar: is_cancel !== 1 ? 1 : undefined,
          pageType: "class_notice",
          pageTitle: "班级通知",
          teacher_name,
          classroom_id,
          classroom_name
        };
        const urlParams = uni.$u.queryParams(paramsData);
        uni.navigateTo({
          url: `/pages/teacher/subpages/richtext/index${urlParams}`
        });
      }
    },
    back() {
      // uni.redirectTo({
      //   url: `/pages/teacher/sort/index`
      // });
      uni.navigateBack();
    },
    // // 局部更新列表
    // updateList(res) {
    //   const { id, department_name, student_mobile, student_name } = res;
    //   const { data } = this;
    //   const index = data.findIndex((item) => item.id === id);
    //   if (index > -1) {
    //     data[index].department_name = department_name;
    //     data[index].student_mobile = student_mobile;
    //     data[index].student_name = student_name;
    //   }
    //   this.data = [...data];
    // },
    // 撤销后更新列表
    revokEvent(res) {
      console.log(res);
      const { feedback_id } = res;
      if (feedback_id) {
        const index = this.data.findIndex(
          (item) => item.feedback_id === feedback_id
        );
        this.$set(this.data[index], "status", 1);
      }
    }
  },
  watch: {},

  // 页面周期函数--监听页面加载
  onLoad(options) {
    const { classroom_id } = options;
    this.classroom_id = classroom_id;
    // this.getList();
    // uni.$on("updateList", this.updateList);
    uni.$on("revokEvent", this.revokEvent);
  },
  // 页面周期函数--监听页面初次渲染完成
  onReady() {},
  // 页面周期函数--监听页面显示(not-nvue)
  onShow() {
    this.getList(1);
  },
  // 页面周期函数--监听页面隐藏
  onHide() {},
  // 页面周期函数--监听页面卸载
  onUnload() {
    // uni.$off("updateList", this.updateList);
    uni.$off("revokEvent", this.revokEvent);
  }
  // 页面处理函数--监听用户下拉动作
  // onPullDownRefresh() { uni.stopPullDownRefresh(); },
  // 页面处理函数--监听用户上拉触底
  // onReachBottom() {},
  // 页面处理函数--监听页面滚动(not-nvue)
  // onPageScroll(event) {},
  // 页面处理函数--用户点击右上角分享
  // onShareAppMessage(options) {},
};
</script>

<style lang="scss" scoped>
.customer-list {
  background: #f5f6fa;
  height: 100vh;
  width: 100vw;
  position: relative;
  display: block;
  .customer-list-content {
    padding: 40rpx 0;
    position: relative;
    z-index: 2;
    border-radius: 0px 0px 16rpx 16rpx;
    background: linear-gradient(180deg, #3061f2 0%, #659ef8 100%);
  }
  .search-box {
    position: relative;

    height: 56rpx;
    width: 680rpx;
    margin: 0 auto;
    border-radius: 20px;
    display: flex;
    align-items: center;
    padding: 0 20rpx;
    .text {
      color: #d3dce6;
      font-size: 28rpx;
      font-style: normal;
      line-height: 32rpx;
    }
  }
  .list-box {
    width: 700rpx;
    margin: 0 auto;
    background: #f5f6fa;
    padding: 20rpx 0;
  }
  .list-item {
    position: relative;
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    background-color: #fff;
    box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.05);
    height: 160rpx;
    border-radius: 8rpx;
    padding: 50rpx 36rpx 24rpx 36rpx;
    margin-bottom: 20rpx;
    transition: all 0.3s ease-in-out;
    // transition-delay: 0.5s;
    &.hidden {
      height: 0;
      overflow: hidden;
      padding: 0;
      margin: 0;
    }
    .left {
      display: flex;
      align-items: center;
      .name {
        margin-left: 14rpx;
      }
      .text {
        display: block;
        color: #333;
        padding-left: 14rpx;
        color: #333;
        font-size: 32rpx;
        font-weight: 500;
        margin-bottom: 12rpx;
        &.school {
          color: #475669;
          text-align: center;
          font-size: 24rpx;
          font-style: normal;
          font-weight: 500;
          line-height: 24rpx;
          border-radius: 18px;
          padding: 6rpx 16rpx;
          margin-bottom: 0;
        }
      }
    }
    .right {
      display: flex;
      justify-content: center;
      flex-direction: column;
      height: 100%;
      .read {
        color: #ffbf0d;
        font-size: 24rpx;
        font-style: normal;
        font-weight: 500;
        line-height: 24rpx;
        &.on {
          color: #00bf8a;
        }
        &.off {
          color: #999;
        }
      }
    }
    .time {
      position: absolute;
      top: 0;
      left: 0;
      color: #fff;
      font-size: 22rpx;
      font-style: normal;
      font-weight: 500;
      line-height: 32rpx;
      width: 273rpx;
      height: 32rpx;
      background-image: url("https://tg-prod.oss-cn-beijing.aliyuncs.com/3d72c683-32d5-476a-beec-0f753d5a617d.png");
      background-size: cover;
      background-repeat: no-repeat;
      text-align: center;
    }
  }
  ::v-deep .u-loading-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 5;
  }
  .top-bg {
    border-radius: 0px 0px 16rpx 16rpx;
    background: linear-gradient(180deg, #3061f2 0%, #659ef8 100%);
    width: 750rpx;
    height: 260rpx;
  }
}
</style>
