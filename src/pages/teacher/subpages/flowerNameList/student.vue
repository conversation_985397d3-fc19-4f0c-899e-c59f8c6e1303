<template>
  <div class="customer-list">
    <u-toast ref="uToast"></u-toast>
    <u-loading-icon
      :show="pageLoading"
      color="#FFBF0D"
      textColor="#545556"
      mode="semicircle"
      :vertical="true"
      text="加载中..."
    ></u-loading-icon>
    <next-paging
      ref="paging"
      v-model="data"
      @query="getList"
      :autoShowBackToTop="true"
      :defaultPageSize="searchForm.page_size"
    >
      <template #top>
        <div class="slotTop">
          <u-navbar
            :placeholder="true"
            bgColor="#3061f2"
            title="班级学员信息"
            titleStyle="color: #fff;font-size:36rpx;font-weight:500"
            leftIconSize="20px"
            leftIconColor="#fff"
            :autoBack="false"
            @leftClick="back"
          >
          </u-navbar>
          <div class="customer-list-content">
            <div class="border-around search-box">
              <u--input
                placeholder="搜索学员姓名"
                prefixIcon="search"
                v-model="searchForm.student_name"
                border="none"
                color="#fff"
                confirmType="search"
                @confirm="search"
                prefixIconStyle="font-size: 24px;color: #D3DCE6"
              ></u--input>
              <div @click="search" style="color: #fff">搜索</div>
            </div>
          </div>
        </div>
      </template>
      <!--top插槽-->
      <!-- next-paging默认铺满全屏，此时页面所有view都应放在next-paging标签内，否则会被盖住 -->
      <!-- 需要固定在页面顶部的view请通过slot="top"插入，包括自定义的导航栏 -->
      <div class="is-all-checked">
        <div style="display: flex">
          <span>全选：</span>
          <u-checkbox-group v-model="allCheckboxValue">
            <u-checkbox :name="true" @change="handleAllCheckedChange">
            </u-checkbox>
          </u-checkbox-group>
        </div>
        <span style="color: #2979ff; font-size: 28rpx" @click="handleOperate"
          >操作</span
        >
      </div>
      <div class="list-box">
        <u-checkbox-group
          v-model="checkboxValue1"
          placement="column"
          @change="checkboxChange"
        >
          <block :key="item.student_id" v-for="item in data">
            <div class="list-item" @click="handleItemClick(item)">
              <div class="left">
                <u-image
                  shape="circle"
                  width="90rpx"
                  height="90rpx"
                  :showMenuByLongpress="false"
                  :src="
                    item.student_gender === 'female'
                      ? 'https://tg-prod.oss-cn-beijing.aliyuncs.com/cf769e6f-5f97-41c6-a3c8-b796ca0df32c.png'
                      : item.student_gender === 'male'
                      ? 'https://tg-prod.oss-cn-beijing.aliyuncs.com/9e8b7d64-c249-45e8-ac42-3a67a921f84d.png'
                      : 'https://tg-prod.oss-cn-beijing.aliyuncs.com/db5c1013-4e99-41c7-bfa4-f93500fe0622.png'
                  "
                  :lazy-load="true"
                ></u-image>
                <div class="name">
                  <div class="text">{{ item.student_name }}</div>
                  <div class="text school">
                    最近沟通时间：{{ item.last_feedback_time || "无" }}
                  </div>
                </div>
              </div>
              <div
                v-if="$hasPermission(['feedback_send'])"
                @tap.stop="goDetail(item)"
                class="right"
              >
                <u-image
                  shape="circle"
                  width="36rpx"
                  height="36rpx"
                  src="https://tg-prod.oss-cn-beijing.aliyuncs.com/16da240a-eac3-4a48-9736-f63248ebabd4.png"
                  :lazy-load="true"
                ></u-image>
                <u-checkbox :name="item.student_id"> </u-checkbox>
              </div>
            </div>
          </block>
        </u-checkbox-group>
      </div>
      <!--bottom插槽-->
      <!-- <template #bottom
      ><view class="slotBottom"><text>我是底部插槽内容</text></view></template
    > -->
    </next-paging>
    <u-action-sheet
      :actions="actionList"
      :show="actionShow"
      :safeAreaInsetBottom="true"
      :closeOnClickAction="true"
      round="10"
      cancelText="取消"
      @select="selectClick"
      @close="actionShow = false"
      :description="`对已选（${checkedCount}）个学员发送`"
    ></u-action-sheet>
  </div>
</template>

<script>
import { getClassroomStudentList } from "@/services/flowerNameList";
// import { Base64 } from "js-base64";
export default {
  name: "studentList",
  components: {},
  data() {
    return {
      isSingle: true,
      checkedCount: 0,
      searchForm: {
        student_name: "",
        show_leave_student: true,
        status: "in_classroom",
        page: 1,
        page_size: 20
      },
      allCheckboxValue: false,
      data: [],
      count: 0, // 总条数
      pageCount: 0, // 总页数
      pageLoading: false,
      isFisrtEnter: true,
      classroom_id: "",
      checkboxValue1: [],
      // actionList: [
      //   {
      //     index: 3,
      //     name: "发送家长课堂"
      //   },
      //   {
      //     index: 4,
      //     name: "发送班级通知"
      //   },
      //   {
      //     index: 5,
      //     name: "发送课程总结"
      //   }
      // ],
      actionShow: false
    };
  },
  computed: {
    actionList() {
      const arr = [];
      const list = [
        {
          index: 3,
          name: "发送家长课堂",
          permissionName: "parent_class"
        },
        {
          index: 4,
          name: "发送班级通知",
          permissionName: "class_notice"
        },
        // {
        //   index: 5,
        //   name: "发送课程总结",
        //   permissionName: "course_summary"
        // },
        {
          index: 6,
          name: "发送时光相册",
          permissionName: "time_album_batch_add"
        }
      ];
      for (let i = 0; i < list.length; i++) {
        if (this.isPermitPush(list[i])) {
          arr.push(this.isPermitPush(list[i]));
        }
      }
      return arr;
    }
  },
  created() {},
  methods: {
    isPermitPush(actionInfo) {
      if (this.$hasPermission([actionInfo.permissionName])) {
        return {
          index: actionInfo.index,
          name: actionInfo.name
        };
      }
    },
    search() {
      // const { name } = this.searchForm;
      // if (name === "") {
      //   this.$refs.uToast.show({
      //     message: "请输入关键字"
      //   });
      //   return;
      // }
      this.checkboxValue1 = [];
      this.$refs.paging.reload();
    },
    getList(pageNo) {
      // if (pageNo > this.pageCount) {
      //   this.$refs.paging.complete([]);
      // } else {
      this.searchForm.page = pageNo;
      const schId = uni.getStorageSync("checkedSchool");
      if (schId.length) {
        this.searchForm.department_id = schId.map((item) => item.id);
      }
      if (this.isFisrtEnter) {
        this.pageLoading = true;
        this.isFisrtEnter = false;
      }

      getClassroomStudentList({
        classroom_id: this.classroom_id,
        ...this.searchForm
      })
        .then((res) => {
          const data = res.results ?? [];
          if (data) {
            this.count = res.count;
            // 获取数据总页数
            this.pageCount = Math.ceil(this.count / this.searchForm.page_size);
            const arr = data.map((item) => {
              const {
                department_name,
                department_id,
                student_name,
                student_mobile,
                student_id,
                teacher_id,
                teacher_name,
                classroom_id,
                student_gender,
                classroom_name,
                last_feedback_time
              } = item;
              return {
                department_name,
                department_id,
                student_name,
                student_mobile,
                student_id,
                teacher_id,
                teacher_name,
                classroom_id,
                student_gender,
                classroom_name,
                last_feedback_time
              };
            });
            this.allCheckboxValue =
              this.checkboxValue1.length === arr.length ? [true] : [false];
            this.$refs.paging.complete(arr);
            this.pageLoading = false;
          }
        })
        .catch(() => {
          this.pageLoading = false;
        });
      // }
    },
    handleItemClick(item) {
      if (this.checkboxValue1.includes(item.student_id)) {
        this.checkboxValue1.splice(
          this.checkboxValue1.indexOf(item.student_id),
          1
        );
      } else {
        this.checkboxValue1.push(item.student_id);
      }
      this.allCheckboxValue =
        this.checkboxValue1.length === this.data.length ? [true] : [false];
    },
    goDetail(item) {
      this.checkedCount = 1;
      this.actionShow = true;
      this.isSingle = true;
      console.log(item, "item------");
      this.curStudentInfo = item;
    },
    back() {
      // uni.redirectTo({
      //   url: `/pages/teacher/sort/index`
      // });
      // uni.setStorageSync("teacher_tabIndex", 1);
      uni.navigateBack();
    },
    handleAllCheckedChange(isAll) {
      if (isAll) {
        this.checkboxValue1 = this.data.map((i) => i.student_id);
      } else {
        this.checkboxValue1 = [];
      }
    },
    handleOperate() {
      if (this.checkboxValue1.length) {
        this.checkedCount = this.checkboxValue1.length;
        this.actionShow = true;
        this.isSingle = false;
      } else {
        this.$refs.uToast.show({
          message: "请先勾选学员"
        });
      }
    },
    selectClick(sel) {
      console.log(sel, "sel");
      const sendTypes = {
        3: {
          type: "parent_class",
          pageTitle: "家长课堂"
        }, // 发送家长课堂
        4: {
          type: "class_notice",
          pageTitle: "班级通知"
        }, // 发送班级通知
        // 5: {
        //   type: "course_summary",
        //   pageTitle: "课程总结"
        // }, // 发送课程总结
        6: {
          type: "time_album_batch_add",
          pageTitle: "时光相册"
        } // 发送时间相册
      };
      if (sel.index === 6) {
        const selectedStudents = this.getSelectedStudents();
        if (!this.$hasPermission(["time_album_batch_add"])) {
          uni.showToast({
            title: "无权限操作,请联系管理员!",
            icon: "none"
          });
          return;
        }
        this.handleSendTimeAlbum(selectedStudents);
        return;
      }
      const students = this.checkboxValue1.map((i) => {
        const cur = this.data.find((j) => i === j.student_id);
        return {
          student_gender: cur.student_gender,
          student_id: cur.student_id,
          student_name: cur.student_name
        };
      });
      // uni.setStorageSync("checkedStudents", JSON.stringify(students));
      console.log(sendTypes[sel.index], "sendTypes[sel.index]");
      const paramsData = {
        classroom_id: this.data[0].classroom_id,
        classroom_name: this.data[0].classroom_name,
        department_id: this.data[0].department_id,
        source: "student",
        // students: Base64.encode(JSON.stringify(students)),
        students: encodeURI(JSON.stringify(students)),
        pageType: sendTypes[sel.index]?.type || "",
        pageTitle: sendTypes[sel.index]?.pageTitle || "",
        isFirstEdit: 1,
        isShowBack: 1
      };

      let urlParams = "";
      if (this.isSingle) {
        const {
          // teacher_id,
          student_id,
          classroom_id,
          classroom_name,
          // teacher_name,
          student_gender,
          student_name,
          department_id
        } = this.curStudentInfo;
        console.log(this.curStudentInfo);
        const curParamsData = {
          // teacher_id,
          student_id,
          classroom_id,
          classroom_name,
          // teacher_name,
          student_gender,
          student_name,
          department_id,
          source: "student",
          pageType: sendTypes[sel.index]?.type || "",
          pageTitle: sendTypes[sel.index]?.pageTitle || "",
          isFirstEdit: 1,
          isShowBack: 1
        };
        urlParams = uni.$u.queryParams(curParamsData);
      } else {
        urlParams = uni.$u.queryParams(paramsData);
      }
      console.log(urlParams);
      uni.navigateTo({
        url: `/pages/teacher/subpages/richtext/index${urlParams}`
      });
    },
    checkboxChange(val) {
      this.allCheckboxValue =
        val.length === this.data.length ? [true] : [false];
      console.log(this.checkboxValue1, val);
    },
    // 局部更新列表
    updateList(res) {
      const {
        id,
        department_name,
        department_id,
        student_mobile,
        student_name
      } = res;
      const { data } = this;
      const index = data.findIndex((item) => item.id === id);
      if (index > -1) {
        data[index].department_name = department_name;
        data[index].department_id = department_id;
        data[index].student_mobile = student_mobile;
        data[index].student_name = student_name;
      }
      this.data = [...data];
    },
    // 发送时光相册处理方法
    handleSendTimeAlbum(selectedStudents) {
      console.log("发送时光相册:", selectedStudents);
      const studentData = selectedStudents.map((item) => {
        return {
          student_id: item.student_id,
          student_number: item.student_number,
          student_name: item.student_name
        };
      });
      uni.navigateTo({
        url: `/pages/student/subpages/timeAlbum/edit?student_data=${JSON.stringify(
          studentData
        )}`
      });
    },
    // 获取选中的学员数据
    getSelectedStudents() {
      return this.data.filter((item) =>
        this.checkboxValue1.includes(item.student_id)
      );
    }
  },
  watch: {},

  // 页面周期函数--监听页面加载
  onLoad(options) {
    const { classroom_id } = options;
    this.classroom_id = classroom_id;
    // this.getList();
    uni.$on("updateList", this.updateList);
  },
  // 页面周期函数--监听页面初次渲染完成
  onReady() {},
  // 页面周期函数--监听页面显示(not-nvue)
  onShow() {},
  // 页面周期函数--监听页面隐藏
  onHide() {},
  // 页面周期函数--监听页面卸载
  onUnload() {
    uni.$off("updateList", this.updateList);
  }
  // 页面处理函数--监听用户下拉动作
  // onPullDownRefresh() { uni.stopPullDownRefresh(); },
  // 页面处理函数--监听用户上拉触底
  // onReachBottom() {},
  // 页面处理函数--监听页面滚动(not-nvue)
  // onPageScroll(event) {},
  // 页面处理函数--用户点击右上角分享
  // onShareAppMessage(options) {},
};
</script>

<style lang="scss" scoped>
.customer-list {
  background: #f5f6fa;
  height: 100vh;
  width: 100vw;
  position: relative;
  display: block;
  .customer-list-content {
    padding: 40rpx 0;
    position: relative;
    z-index: 2;
    border-radius: 0px 0px 16rpx 16rpx;
    background: linear-gradient(180deg, #3061f2 0%, #659ef8 100%);
  }
  .search-box {
    position: relative;

    height: 56rpx;
    width: 680rpx;
    margin: 0 auto;
    border-radius: 20px;
    display: flex;
    align-items: center;
    padding: 0 20rpx;
    .text {
      color: #d3dce6;
      font-size: 28rpx;
      font-style: normal;
      line-height: 32rpx;
    }
  }
  .is-all-checked {
    display: flex;
    justify-content: space-between;
    padding: 20rpx 60rpx 0 37rpx;
  }
  .list-box {
    width: 700rpx;
    margin: 0 auto;
    height: calc(100% - 200rpx);
    overflow: auto;
    flex: 1;
    background: #f5f6fa;
    padding: 20rpx 0;
  }
  .aa {
    display: flex;
    flex-direction: column;
    height: 100%;
  }
  .bb {
    display: flex;
    flex-direction: column;
  }
  .list-item {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    background-color: #fff;
    box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.05);
    height: 160rpx;
    border-radius: 8rpx;
    padding: 36rpx;
    margin-bottom: 20rpx;
    .left {
      display: flex;
      align-items: center;
      .name {
        margin-left: 14rpx;
      }
      .text {
        display: block;
        color: #333;
        padding-left: 14rpx;
        color: #333;
        font-size: 32rpx;
        font-weight: 500;
        margin-bottom: 12rpx;
        &.school {
          color: #ffbf0d;
          text-align: center;
          font-size: 24rpx;
          font-style: normal;
          font-weight: 500;
          line-height: 24rpx;
          border-radius: 18px;
          background: rgba(45, 128, 237, 0.1);
          padding: 10rpx 16rpx;
          margin-bottom: 0;
        }
      }
    }
    .right {
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      .phone {
        color: #ffbf0d;
        font-size: 28rpx;
        font-style: normal;
        font-weight: 500;
        line-height: 32rpx;
        margin-bottom: 18rpx;
      }
      ::v-deep .u-icon--right {
        justify-content: flex-end;
      }
    }
  }
  .footer {
    width: 100%;
    height: 180rpx;
    background: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  ::v-deep .u-loading-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 5;
  }
  .top-bg {
    border-radius: 0px 0px 16rpx 16rpx;
    background: linear-gradient(180deg, #3061f2 0%, #659ef8 100%);
    width: 750rpx;
    height: 260rpx;
  }
}
</style>
