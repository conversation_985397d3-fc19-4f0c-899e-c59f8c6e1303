<template>
  <div class="webViewRichtext">
    <web-view :src="webViewSrc" @message="getMessage"></web-view>
  </div>
</template>

<script>
export default {
  components: {},
  data() {
    return {
      baseUrl: process.env.VUE_APP_TG_HOST,
      // baseUrl: "http://*************:8080",
      webViewSrc: ""
    };
  },
  computed: {},
  methods: {},
  watch: {},

  // 页面周期函数--监听页面加载
  onLoad(option) {
    option.token = uni.getStorageSync("token");
    option.isShowInstructionBtn = this.$hasPermission(["feedback_cancel"]);
    const urlParams = uni.$u.queryParams(option);
    this.webViewSrc = `${this.baseUrl}/preview${urlParams}`;
    console.log(this.webViewSrc);
  },
  // 页面周期函数--监听页面初次渲染完成
  onReady() {},
  // 页面周期函数--监听页面显示(not-nvue)
  onShow() {},
  // 页面周期函数--监听页面隐藏
  onHide() {},
  // 页面周期函数--监听页面卸载
  onUnload() {}
  // 页面处理函数--监听用户下拉动作
  // onPullDownRefresh() { uni.stopPullDownRefresh(); },
  // 页面处理函数--监听用户上拉触底
  // onReachBottom() {},
  // 页面处理函数--监听页面滚动(not-nvue)
  // onPageScroll(event) {},
  // 页面处理函数--用户点击右上角分享
  // onShareAppMessage(options) {},
};
</script>

<style lang="scss" scoped></style>
