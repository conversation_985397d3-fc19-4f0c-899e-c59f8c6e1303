<template>
  <div class="school-service-scheduling">
    <div class="tg-top-bg"></div>
    <u-toast ref="uToast"></u-toast>

    <u-loading-icon
      :show="pageLoading"
      color="#FFBF0D"
      textColor="#545556"
      mode="semicircle"
      :vertical="true"
      text="加载中..."
    ></u-loading-icon>
    <next-paging
      ref="paging"
      v-model="data"
      @query="getList"
      :autoShowBackToTop="true"
      :defaultPageSize="searchForm.page_size"
    >
      <!--top插槽-->
      <template #top
        ><div class="slotTop">
          <u-navbar
            :placeholder="true"
            title="花名册"
            leftIconSize="20px"
            leftIconColor="#fff"
            :autoBack="false"
            @leftClick="back"
            bgColor="transparent"
            titleStyle="color: #fff;font-size:36rpx;font-weight:500"
          >
          </u-navbar>
          <div class="customer-list-content">
            <div class="search-switch">
              <div class="checked-bg" :style="{ left: leftOffset }"></div>
              <div class="position-wrap">
                <div style="display: flex; height: 100%">
                  <div
                    @click="handleSwitchChange('class')"
                    class="search-switch-item"
                  >
                    <img
                      :src="type === 'class' ? icon.classActive : icon.class"
                      alt=""
                    />
                  </div>
                  <div
                    @click="handleSwitchChange('student')"
                    class="search-switch-item"
                  >
                    <img
                      :src="
                        type === 'student' ? icon.studentActive : icon.student
                      "
                      alt=""
                    />
                  </div>
                </div>
              </div>
            </div>
            <div class="border-around search-box">
              <template v-if="type === 'class'">
                <u--input
                  placeholder="搜索班级名称/别名"
                  prefixIcon="search"
                  v-model="searchForm.name_alias_name"
                  border="none"
                  color="#fff"
                  confirmType="search"
                  @confirm="getList(1)"
                  prefixIconStyle="font-size: 24px;color: #D3DCE6"
                ></u--input>
                <div @click="getList(1)" style="color: #fff">搜索</div>
              </template>
              <template v-else>
                <u-icon name="search" color="#D3DCE6" size="24"></u-icon>
                <span
                  class="student-search"
                  @click="toStudentList"
                  :style="{ color: searchForm.student_id ? '#fff' : '#c1c4cb' }"
                >
                  {{ searchForm.student_name }}
                </span>
                <!-- <u--input
                  placeholder="搜索学员姓名/手机号"
                  prefixIcon="search"
                  v-model="searchForm.student_name"
                  border="none"
                  color="#fff"
                  confirmType="search"
                  @confirm="getList(1)"
                  prefixIconStyle="font-size: 24px;color: #D3DCE6"
                ></u--input> -->
              </template>
            </div>
          </div>
          <!-- <div @tap="showStatusPicker" class="flex flex-v-center status-box">
            <span class="text-white status">结业状态：{{ statusText }}</span>
            <u-icon name="arrow-down" color="#fff" size="14"></u-icon>
          </div> -->
        </div>
      </template>
      <!-- next-paging默认铺满全屏，此时页面所有view都应放在next-paging标签内，否则会被盖住 -->
      <!-- 需要固定在页面顶部的view请通过slot="top"插入，包括自定义的导航栏 -->
      <div class="list-box">
        <div v-for="item in data" :key="item.id" class="list-item">
          <div
            class="flex border-around content"
            :class="{ hide: item.contentHide }"
          >
            <div class="left">
              <div class="classroom_title">
                <div>{{ item.name }}</div>
                <div class="bg"></div>
              </div>
              <div class="text-row">
                <span>班级别名：</span><span>{{ item.alias_name }}</span>
              </div>
              <div class="text-row">
                <span>课程名称：</span><span>{{ item.course_name }}</span>
              </div>
              <div class="text-row">
                <span>任课老师：</span><span>{{ item.teacher_name }}</span>
              </div>
              <div class="text-row">
                <span>班主任：</span><span>{{ item.header_teacher_name }}</span>
              </div>
              <div class="text-row">
                <span>班级人数：</span><span>{{ item.student_numb }}</span>
              </div>
            </div>
            <div class="flex flex-center border-left-1px right">
              <u-button
                @click="openActions(item)"
                type="primary"
                size="small"
                text="操作"
                :customStyle="{ fontWeight: '500' }"
              ></u-button>
            </div>
          </div>
        </div>
      </div>
      <!--bottom插槽-->
      <!-- <template #bottom
      ><view class="slotBottom"><text>我是底部插槽内容</text></view></template
    > -->
    </next-paging>
    <u-action-sheet
      :actions="actionList"
      :show="actionShow"
      :safeAreaInsetBottom="true"
      :closeOnClickAction="true"
      round="10"
      cancelText="取消"
      @select="selectClick"
      @close="actionShow = false"
    ></u-action-sheet>
    <!-- <u-picker
      ref="statusPicker"
      :show="statusShow"
      keyName="name"
      :columns="[statusList]"
      :immediateChange="true"
      @cancel="statusShow = false"
      @confirm="statusConfirm"
    ></u-picker> -->
  </div>
</template>

<script>
import { getClassroomList } from "@/services/flowerNameList";

export default {
  name: "ClassroomList",
  components: {},
  data() {
    return {
      type: "class",
      icon: {
        class:
          "https://tg-prod.oss-cn-beijing.aliyuncs.com/5826fc1e-9cda-410c-bcf9-8646b6818943.png",
        classActive:
          "https://tg-prod.oss-cn-beijing.aliyuncs.com/897b2a4d-5450-4230-9ed9-546a5ddc77ae.png",
        student:
          "https://tg-prod.oss-cn-beijing.aliyuncs.com/960e323f-6f1b-4dab-a3d1-b5710f77d367.png",
        studentActive:
          "https://tg-prod.oss-cn-beijing.aliyuncs.com/004fb758-a0f1-4cca-a8cb-611bc61d08c2.png"
      },
      leftOffset: 0,
      searchForm: {
        page: 1,
        page_size: 20,
        student_id: "",
        student_name: "搜索学员姓名/手机号",
        name_alias_name: "",
        status: "not_start"
      },
      classroom_name: "",
      classroom_id: "",
      department_id: "",
      dateTxt: "---",
      data: [],
      count: 0, // 总条数
      pageCount: 0, // 总页数
      pageLoading: false,
      isFisrtEnter: true,
      actionShow: false,
      teacher_name: "",
      // actionList: [
      //   {
      //     index: 1,
      //     name: "查看在班学员"
      //   },
      //   {
      //     index: 2,
      //     name: "查看学员沟通记录"
      //   }
      // ],
      // statusList: [
      //   {
      //     id: "",
      //     name: "全部"
      //   },
      //   {
      //     index: "not_start",
      //     name: "未结业"
      //   },
      //   {
      //     index: "is_end",
      //     name: "已结业"
      //   }
      // ],
      from: "",
      statusShow: false,
      statusText: "未结业"
    };
  },
  computed: {
    actionList() {
      const arr = [];
      const list = [
        {
          index: 1,
          name: "查看在班学员",
          permissionName: "classroom_student_list"
        },
        {
          index: 2,
          name: "查看学员沟通记录",
          permissionName: "feedback_student_list"
        },
        // {
        //   index: 3,
        //   name: "发送家长课堂",
        //   permissionName: "parent_class"
        // },
        {
          index: 4,
          name: "发送班级通知",
          permissionName: "class_notice"
        }
        // {
        //   index: 5,
        //   name: "发送课程总结",
        //   permissionName: "course_summary"
        // }
      ];
      for (let i = 0; i < list.length; i++) {
        if (this.isPermitPush(list[i])) {
          arr.push(this.isPermitPush(list[i]));
        }
      }
      return arr;
    }
  },
  methods: {
    isPermitPush(actionInfo) {
      if (this.$hasPermission([actionInfo.permissionName])) {
        return {
          index: actionInfo.index,
          name: actionInfo.name
        };
      }
    },
    // showStatusPicker() {
    //   const index = this.statusList.findIndex(
    //     (item) => item.name === this.statusText
    //   );
    //   this.statusShow = true;
    //   this.$refs.statusPicker.setIndexs([index]);
    // },
    // statusConfirm(e) {
    //   console.log(e);
    //   const { index, name } = e.value[0];

    //   this.searchForm.status = index;
    //   this.statusText = name;
    //   this.statusShow = false;
    //   this.$refs.paging.reload();
    // },
    handleSwitchChange(type) {
      this.type = type;
      this.searchForm = {
        page: 1,
        page_size: 20,
        student_id: "",
        student_name: "搜索学员姓名/手机号",
        name_alias_name: "",
        status: "not_start"
      };
      this.leftOffset = type === "class" ? "0rpx" : "80rpx";
      if (type === "student") {
        this.toStudentList();
      } else {
        this.getList(1);
      }
    },
    toStudentList() {
      uni.redirectTo({
        url: `/pages/teacher/subpages/flowerNameList/studentList`
      });
    },
    getList(pageNo) {
      // if (pageNo > this.pageCount) {
      //   this.$refs.paging.complete([]);
      // } else {
      this.searchForm.page = pageNo;
      console.log(this.searchForm, pageNo);
      const schId = uni.getStorageSync("checkedSchool");
      // const employee_id = uni.getStorageSync("user").employee_id;
      if (schId.length) {
        this.searchForm.department_id = schId.map((item) => item.id);
      }
      if (this.isFisrtEnter) {
        this.pageLoading = true;
        this.isFisrtEnter = false;
      }

      getClassroomList({
        // teacher_id: employee_id,
        ...this.searchForm
      })
        .then((res) => {
          const data = res.results ?? [];
          this.count = res.count ?? 0;
          // 获取数据总页数
          this.pageCount = Math.ceil(this.count / this.searchForm.page_size);
          const arr = data.map((item) => {
            const {
              name,
              alias_name,
              header_teacher_name,
              start_time,
              department_id,
              id,
              department_name,
              teacher_name,
              course_name,
              student_numb
            } = item;

            return {
              name,
              id,
              alias_name,
              header_teacher_name,
              start_time,
              department_id,
              department_name,
              teacher_name,
              course_name,
              student_numb
            };
          });
          this.$refs.paging.complete(arr);
          this.pageLoading = false;
        })
        .catch(() => {
          this.pageLoading = false;
        });
      // }
    },

    back() {
      uni.redirectTo({
        url: `/pages/teacher/sort/index`
      });
    },
    selectClick(sel) {
      const sendTypes = {
        3: {
          type: "parent_class",
          pageTitle: "家长课堂"
        }, // 发送家长课堂
        4: {
          type: "class_notice",
          pageTitle: "班级通知"
        }, // 发送班级通知
        5: {
          type: "course_summary",
          pageTitle: "课程总结"
        } // 发送课程总结
      };
      console.log(sendTypes[sel.index], sel);
      const paramsData = {
        classroom_id: this.classroom_id,
        classroom_name: this.classroom_name,
        department_id: this.department_id,
        source: "classroom",
        students: "",
        isFirstEdit: 2,
        isShowBack: 1,
        pageType: sendTypes[sel.index]?.type || "",
        pageTitle: sendTypes[sel.index]?.pageTitle || "",
        student_id: "123",
        teacher_name: this.teacher_name
      };
      const urlParams = uni.$u.queryParams(paramsData);
      console.log(urlParams);
      switch (sel.index) {
        case 1:
          // this.checkLock();
          uni.navigateTo({
            url: `/pages/teacher/subpages/flowerNameList/student?classroom_id=${this.classroom_id}`
          });
          break;
        case 2:
          uni.navigateTo({
            url: `/pages/teacher/subpages/flowerNameList/convoHistory?classroom_id=${this.classroom_id}`
          });
          break;
        case 3:
          uni.navigateTo({
            url: `/pages/teacher/subpages/richtext/index${urlParams}`
          });
          break;
        case 4:
          uni.navigateTo({
            url: `/pages/teacher/subpages/richtext/index${urlParams}`
          });
          break;
        case 5:
          uni.navigateTo({
            url: `/pages/teacher/subpages/richtext/index${urlParams}`
          });
          break;
      }
    },
    openActions(item) {
      this.classroom_id = item.id;
      this.classroom_name = item.name;
      this.department_id = item.department_id;
      this.selectRow = item;
      this.actionShow = true;
      this.teacher_name = item.teacher_name;
    }
    // 局部更新列表
    // updateList(res) {
    //   const { id, roll_called_student_numb } = res;
    //   const { data } = this;
    //   const index = data.findIndex((item) => item.id === id);
    //   if (index > -1) {
    //     data[index].roll_called_student_numb = roll_called_student_numb;
    //     data[index].status_chn = "已上课";
    //   }
    //   this.data = [...data];
    // }
  },
  watch: {},

  // 页面周期函数--监听页面加载
  onLoad(options) {
    console.log(options);
    if (options.type) {
      this.type = options.type;
      this.searchForm.student_id = options.student_id || "";
      this.searchForm.student_name =
        options.student_name || "搜索学员姓名/手机号";
      this.leftOffset = "80rpx";
      // this.searchForm.name_alias_name = options.student_name;
      this.getList(1);
    }
    // uni.$on("updateList", this.updateList);
  },

  // 页面周期函数--监听页面初次渲染完成
  onReady() {},
  // 页面周期函数--监听页面显示(not-nvue)
  onShow() {},
  // 页面周期函数--监听页面隐藏
  onHide() {},
  // 页面周期函数--监听页面卸载
  onUnload() {
    // uni.$off("updateList", this.updateList);
  }
  // 页面处理函数--监听用户下拉动作
  // onPullDownRefresh() { uni.stopPullDownRefresh(); },
  // 页面处理函数--监听用户上拉触底
  // onReachBottom() {},
  // 页面处理函数--监听页面滚动(not-nvue)
  // onPageScroll(event) {},
  // 页面处理函数--用户点击右上角分享
  // onShareAppMessage(options) {},
};
</script>

<style lang="scss" scoped>
.customer-list-content {
  padding: 40rpx 0;
  position: relative;
  z-index: 2;
  border-radius: 0px 0px 16rpx 16rpx;
  display: flex;
  background: linear-gradient(180deg, #3061f2 0%, #659ef8 100%);
  .search-switch {
    width: 220rpx;
    height: 64rpx;
    margin-right: 16rpx;
    background: #f6f6f6;
    border-radius: 30rpx;
    position: relative;
    .checked-bg {
      position: absolute;
      top: 0;
      width: 100rpx;
      height: 64rpx;
      background-color: #ffbf0d;
      border-radius: 30rpx;
      z-index: 0;
      transition: all 0.6s;
    }
    .position-wrap {
      width: 100%;
      height: 100%;
      position: absolute;
      z-index: 1;
    }
    .search-switch-item {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 50%;
      height: 100%;
      img {
        width: 42rpx;
        height: 42rpx;
      }
    }
  }
}
.search-box {
  position: relative;

  height: 56rpx;
  width: 680rpx;
  margin: 0 auto;
  border-radius: 20px;
  display: flex;
  align-items: center;
  padding: 0 20rpx;
  .text {
    color: #d3dce6;
    font-size: 28rpx;
    font-style: normal;
    line-height: 32rpx;
  }
  .student-search {
    display: block;
    width: 100%;
    margin-left: 4px;
    font-size: 15px;
  }
}
.status-box {
  height: 88rpx;
  width: 100%;
  padding: 25rpx 25rpx 0 40rpx;
  font-size: 28rpx;
  font-weight: 500;
  .status {
    margin-right: 10rpx;
  }
}
.list-box {
  .list-item {
    .title {
      background: #ffbf0d;
      color: #fff;
      height: 64rpx;
      line-height: 64rpx;
      font-size: 24rpx;
      font-weight: 500;
      padding: 0 40rpx;
    }
    .content {
      width: 700rpx;
      height: 336rpx;
      margin: 25rpx auto;
      border-color: #ffbf0d;
      border-radius: 24rpx;
      background-color: #fff;
      box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.05);
      overflow: hidden;
      .left {
        flex: 1;
        padding: 34rpx 12rpx 34rpx 46rpx;

        .classroom_title {
          font-size: 28rpx;
          font-weight: 500;
          color: #000;
          line-height: 38rpx;
          margin-bottom: 24rpx;
          position: relative;
          label {
            display: table;
          }
          .bg {
            position: absolute;
            bottom: 0;
            width: 276rpx;
            height: 22rpx;
            border-radius: 4px;
            opacity: 0.6;
            background: linear-gradient(
              90deg,
              #ffbf0d 0%,
              rgba(45, 128, 237, 0) 100%
            );
          }
        }
        .text-row {
          font-size: 22rpx;
          font-style: normal;
          font-weight: 500;
          line-height: 24rpx;
          margin-bottom: 20rpx;
          display: flex;
          span:nth-child(1) {
            color: #ffbf0d;
            position: relative;
            padding-left: 24rpx;
            width: 140rpx;
            display: block;
            &::before {
              position: absolute;
              top: 10rpx;
              left: 0rpx;
              content: "";
              width: 12rpx;
              height: 12rpx;
              border-radius: 30px;
              background-color: #a2ccff;
            }
          }
          span:nth-child(2) {
            color: #475669;
          }
        }
      }
      .right {
        width: 160rpx;
        border-color: #ffbf0d;
        padding: 0 40rpx;
        background-color: #ebf4ff;
      }
    }
  }
}
::v-deep .u-loading-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 5;
}
.data-input {
  height: 100%;
  .text {
    color: #475669;
    font-size: 28rpx;
    font-style: normal;
    font-weight: 500;
  }
}
</style>
