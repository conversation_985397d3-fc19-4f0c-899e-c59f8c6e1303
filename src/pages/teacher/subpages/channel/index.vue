<template>
  <div class="channel-page">
    <div class="channel-list">
      <div
        class="channel-item"
        v-for="(item, index) in channelList"
        :key="index"
        @tap="selectChannel(item)"
      >
        <div class="item-name">{{ item.name }}</div>
        <div class="item-arrow">
          <u-icon name="arrow-right" color="#C8C9CC" size="28"></u-icon>
        </div>
      </div>
    </div>
  </div>
</template>

  <script>
// import { getOpenId } from "@/services/student/home";
import { getOfflineChannelList } from "@/services/intention";
export default {
  name: "ChannelSelector",
  data() {
    return {
      channelList: [
        // { name: "商超", value: "shopping" },
        // { name: "生活社区", value: "community" },
        // { name: "幼儿园、学校", value: "education" },
        // { name: "赛事", value: "competition" },
        // { name: "讲座", value: "lecture" },
        // { name: "公园", value: "park" },
        // { name: "展会", value: "exhibition" },
        // { name: "异业合作活动", value: "cooperation" },
        // { name: "新校预售", value: "new_school" },
        // { name: "其它", value: "other" }
      ],
      options: {},
      customer_id: "",
      surveyListParams: {},
      channelOn: ""
    };
  },
  methods: {
    selectChannel(item) {
      this.options.channelId = item.id;
      this.options.channelOn = this.channelOn;
      // 构建查询字符串，简单拼接
      let queryString = "";
      for (const key in this.options) {
        if (queryString) queryString += "&";
        queryString += `${key}=${this.options[key]}`;
      }

      // 导航到详情页
      uni.navigateTo({
        url: `/pages/teacher/subpages/dtb/index?${queryString}`
      });
    },
    getOfflineChannelList() {
      getOfflineChannelList({ department_id: this.options.department_id }).then(
        (res) => {
          this.channelList = res.data.child_data;
          this.channelOn = res.data.id;
        }
      );
    }
  },
  onLoad(options) {
    console.log(options, "options");
    this.options = options;
    this.getOfflineChannelList();
  }
};
</script>

  <style lang="scss" scoped>
.channel-page {
  background-color: #f5f5f5;
  min-height: 100vh;
}

.channel-list {
  background-color: #fff;
  margin-top: 10px;
}

.channel-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 16px;
  position: relative;

  &:after {
    content: "";
    position: absolute;
    left: 16px;
    right: 0;
    bottom: 0;
    height: 1px;
    background-color: #f2f2f2;
    transform: scaleY(0.5);
  }

  &:last-child:after {
    display: none;
  }

  .item-name {
    font-size: 16px;
    color: #333;
  }

  .item-arrow {
    color: #c8c9cc;
  }
}
</style>
