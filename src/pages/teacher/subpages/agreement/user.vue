<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <div class="user">
    <div v-html="content.replace(/(\n|\r|\r\n|↵)/g, '<br/>')"></div>
  </div>
</template>

<script>
export default {
  components: {},
  data() {
    return {
      content: "欢迎加入到聂卫平围棋道场（即：北京弈友围棋文化传播有限责任公司）的大家庭。聂道全体教职员工用心承诺：我们将用优质的教学和周到的服务让学员在快乐的学习体验中感受围棋的奇妙世界。聂卫平围棋道场（以下简称:“聂道”）为具有符合国家法定资质的专业围棋教育培训机构，具有提供在本协议中注明的教学和服务的资格和能力。\n\n1、学习时间和学习资源\n1.1 学员报名参加聂道围棋课程，聂道将提前通知学员和/或其监护人开班时间以及常规课程安排，在寒暑假或节假日期间聂道变更常规课程安排时，会至少提前10天通知学员和/或其监护人。\n1.2 聂道对其提供的课程资料（包括但不限于：教材、习题集等纸质或电子资料以及授课及活动形成的录音录像制品）享有完整的知识产权，未经聂道许可，禁止学员及学员的监护人擅自传播（包括但不限于复制、通过网络传播等），无论是否出于商业化目的。如违反此规定，聂道有权追究其相应的法律责任。\n\n2、教学服务\n2.1 聂道主讲老师每期课程首节课会向家长讲解本期教学目标及体系，期末会组织结业典礼及介绍下一级别课程规划。\n2.2 学员的监护人应尽量保证学员的出勤，如遇特殊情况应至少提前24小时向主讲老师请假；聂道为提前请假的学员提供回放视频课形式的补课服务，\n2.3 聂道尽合理努力保证主讲老师的连续性和稳定性。尽管如此，聂道保留根据师资力量安排和主讲老师个人原因变动等原因调整主讲老师的权利。\n\n3、学籍管理\n3.1 服务方式\n本课程的授课形式为直播课+回放视频课；直播课开课前将发送开课提醒，回放视频课将于直播课结束后生成并推送至学员账户。\n3.2 信息说明\n课程人数：大班课程均有开班人数限制，如未满足开班条件，我们将以转班等方式进行处理。\n插班学员：学员因调整班次、上课时间规划等原因，报名后无法从首节课开始参加直播课程的学员未插班学员。\n转班权益：学员报名后，如有转班意向，在班主任核实欲转班次有名额可转后，享有1次免费转班权益；如第1次转班完成后，仍有转班需求，欲转班次也可转，需缴纳转班费100元/次；已安排的课程结束，则无法办理转班。\n3.3 发货说明\n本课程配套教辅随材将在学员报名缴费成功后一周内包邮寄出，如有收货地址变更问题，请及时与销售人员沟通，以便工作人员安排寄出。\n3.4 退费规则\n3.4.1 学员可申请退还报名时的实际缴费金额；课程所赠送教辅随材如签收，将扣除全套教辅随材费用；\n3.4.2 所报课程正常开课后，学员发起退费，退费金额将按剩余课次计算，即 申请退费金额=【实际缴费金额/总课次】*剩余课次-赠送签收教辅随材费用；赠送教辅随材无论拆封与否均不予退费。\n3.4.3 退费权益终止，学员最终选择班级的授课时间安排结束日为退费权益终止日，如申请退费日期晚于退费权益终止日，不予退费。 \n为保证学员更清楚自身权益和注意事项，请务必阅读以上信息。\n\n4、协议效力\n4.1 在任何情况下，学员和/或其监护人不得以任何方式全部或部分出售或转让聂道的培训课程，否则视为违约。\n4.2 聂道未能提供本协议所列的教学及服务内容且未及时提供补救方案的，视为聂道违约；学员和/或其监护人转让课程、违反本协议约定的，违反治安管理条例等相关国家法律法规的，违反聂道纪律规定的，视为学员违约；双方同意在出现上述违约情况时，非违约方有权解除本协议并要求违约方支付违约金，违约金金额相当于剩余培训费用的实际金额，如违约金不足以弥补非违约方损失的，应进一步赔偿损失。因本协议引起的或与本协议有关的任何争议，均提请北京仲裁委员会/北京国际仲裁中心按照其仲裁规则进行仲裁。仲裁裁决是终局的，对双方均有约束力。\n4.3 本协议构成聂道和学员及其监护人就本协议所述事项的完整协议，并取代双方此前做出的一切口头或书面沟通、协议或讨论、陈述、保证及承诺。"
    };
  },
  computed: {},
  methods: {},
  watch: {},

  // 页面周期函数--监听页面加载
  onLoad() {},
  // 页面周期函数--监听页面初次渲染完成
  onReady() {},
  // 页面周期函数--监听页面显示(not-nvue)
  onShow() {},
  // 页面周期函数--监听页面隐藏
  onHide() {},
  // 页面周期函数--监听页面卸载
  onUnload() {}
  // 页面处理函数--监听用户下拉动作
  // onPullDownRefresh() { uni.stopPullDownRefresh(); },
  // 页面处理函数--监听用户上拉触底
  // onReachBottom() {},
  // 页面处理函数--监听页面滚动(not-nvue)
  // onPageScroll(event) {},
  // 页面处理函数--用户点击右上角分享
  // onShareAppMessage(options) {},
};
</script>

<style lang="scss" scoped>
.user {
  padding: 25rpx;
}
</style>
