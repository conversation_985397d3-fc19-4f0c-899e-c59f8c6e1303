<template>
  <div class="schedule">
    <u-toast ref="uToast"></u-toast>
    <u-loading-icon
      :show="pageLoading"
      color="#FFBF0D"
      textColor="#545556"
      mode="semicircle"
      :vertical="true"
      text="加载中..."
    ></u-loading-icon>
    <u-navbar
      :placeholder="true"
      bgColor="#2956ef"
      title="课表"
      titleStyle="color: #fff;font-size:36rpx;font-weight:500"
      leftIconSize="20px"
      leftIconColor="#fff"
      :autoBack="true"
    >
    </u-navbar>
    <u-sticky
      index="attendance-list"
      :customNavHeight="topHeight"
      bgColor="#fff"
    >
      <div class="flex flex-v-center flex-around border-top-1px date-box">
        <u-button
          @click="datePrevClick"
          type="primary"
          size="small"
          :plain="true"
          text="前一天"
          :customStyle="{
            fontWeight: '500',
            width: '110rpx',
            height: '48rpx'
          }"
        ></u-button>
        <u-button
          @click="dateNextClick"
          type="primary"
          size="small"
          :plain="true"
          text="后一天"
          :customStyle="{
            fontWeight: '500',
            width: '110rpx',
            height: '48rpx'
          }"
        ></u-button>
        <div @click="dateClick" class="flex flex-center data-input">
          <span class="text">{{ dateTxt }}</span>
          <u-icon
            :name="item.contentHide ? 'arrow-up' : 'arrow-down'"
            color="#FFBF0D"
            size="14"
          ></u-icon>
        </div>
        <u-button
          @click="dateTodayClick"
          type="primary"
          size="small"
          text="今天"
          :customStyle="{
            fontWeight: '500',
            width: '110rpx',
            height: '48rpx'
          }"
        ></u-button>
      </div>
      <view class="flex flex-between school-popup-content">
        <view @tap="switchSchool" class="flex flex-v-center school-popup-title"
          ><span class="ft-assist text-small">当前：</span
          ><span class="ft-assist text-primary">{{
            searchForm.department_name
          }}</span>
          <u-icon size="12" name="arrow-down"></u-icon>
        </view>
        <view
          @tap="switchClassroom"
          class="flex flex-v-center school-popup-title"
          ><span class="ft-assist text-small">教室：</span
          ><span class="ft-assist text-primary">{{
            searchForm.school_room_name
          }}</span>
          <u-icon size="12" name="arrow-down"></u-icon
        ></view>
      </view>
    </u-sticky>
    <div class="list-box">
      <div v-for="item in data" :key="item.id" class="list-item">
        <!-- <div @click="arrowClick(item)" class="flex flex-between title">
          <span>{{ item.rang_time + " " + item.week_day_chn }}</span
          ><u-icon
            :name="item.contentHide ? 'arrow-up' : 'arrow-down'"
            color="#fff"
            size="14"
          ></u-icon>
        </div> -->
        <div class="flex content">
          <div class="left">
            <div class="text-row">
              <span>班级名称：</span><span>{{ item.classroom_name }}</span>
            </div>

            <div class="text-row">
              <span>上课形式：</span><span>{{ item.scheduling_form_chn }}</span>
            </div>
            <div class="text-row">
              <span>任课老师：</span><span>{{ item.teacher_name }}</span>
            </div>
            <!-- <div class="text-row">
              <span>上课教室：</span><span>{{ item.school_room_name }}</span>
            </div> -->
            <div class="text-row">
              <span>上课时间：</span
              ><span>{{ item.rang_time + " " + item.week_day_chn }}</span>
            </div>
            <div class="text-row">
              <span>上课时长：</span><span>{{ item.class_duration }}</span>
            </div>
            <div class="text-row">
              <span>上课状态：</span><span>{{ item.status_chn }}</span>
            </div>
          </div>
          <!-- <div class="flex flex-center border-left-1px right">
            <u-button
              @click="openActions(item)"
              type="primary"
              size="small"
              :plain="true"
              text="操作"
              :customStyle="{ fontWeight: '500' }"
            ></u-button>
          </div> -->
        </div>
      </div>
      <u-empty
        :show="!pageLoading && !data.length"
        mode="data"
        width="160rpx"
        marginTop="60%"
        text="暂无课表数据"
        icon="https://tg-prod.oss-cn-beijing.aliyuncs.com/f14816ba-c2e2-4f63-8b18-e1d2190c1fb3.png"
      >
      </u-empty>
    </div>
    <l-calendar
      v-model="calendarShow"
      @change="calendarChange"
      :minDate="minDate"
      :maxDate="maxDate"
      :isRange="false"
      activeBgColor="#FFBF0D"
      :initStartDate="searchForm.class_time_begin"
      @close="calendarClose"
    ></l-calendar>
    <gq-tree
      ref="gqTree"
      :range="range"
      idKey="id"
      nameKey="name"
      childKey="child"
      pidKey="pid"
      allKey="pid"
      :showSearch="true"
      :multiple="false"
      :cascade="true"
      :selectParent="false"
      :maskClick="false"
      :lessOne="true"
      confirmColor="#007aff"
      cancelColor="#757575"
      title="校区关键字"
      titleColor="#757575"
      @cancel="treeCancel"
      @confirm="treeConfirm"
    >
    </gq-tree>
    <!-- <u-popup
      :overlay="false"
      :show="popupShow"
      :closeOnClickOverlay="false"
      :round="8"
      mode="bottom"
      :customStyle="{ boxShadow: '0 10px 10px 10px #d4cece' }"
    >
      <view class="flex flex-between school-popup-content">
        <view @tap="switchSchool" class="flex flex-v-center school-popup-title"
          ><span class="ft-assist text-small">当前：</span
          ><span class="ft-assist text-primary">{{
            searchForm.department_name
          }}</span>
          <u-icon size="12" name="arrow-down"></u-icon>
        </view>
        <view
          @tap="switchClassroom"
          class="flex flex-v-center school-popup-title"
          ><span class="ft-assist text-small">教室：</span
          ><span class="ft-assist text-primary">{{
            searchForm.school_room_name
          }}</span>
          <u-icon size="12" name="arrow-down"></u-icon
        ></view>
      </view>
    </u-popup> -->
    <u-picker
      ref="classroomPicker"
      :show="classroomShow"
      keyName="name"
      :columns="[classroomList]"
      :immediateChange="true"
      @cancel="classroomShow = false"
      @confirm="classroomConfirm"
    ></u-picker>
  </div>
</template>

<script>
import {
  getSchedulingClassroom,
  getRollCallList
} from "@/services/schoolServiceScheduling";
import lCalendar from "@/uni_modules/l-calendar/components/l-calendar/l-calendar.vue";
import gqTree from "@/uni_modules/gq-tree/gq-tree.vue";
import { formatSeconds } from "@/utils/index";

export default {
  name: "ScheduleIndex",
  components: { lCalendar, gqTree },
  data() {
    return {
      searchForm: {
        class_time_begin: "",
        class_time_over: "",
        department_id: "",
        school_room_id: "",
        school_room_name: "",
        department_name: ""
      },
      pageLoading: true,
      calendarShow: false,
      dateTxt: "---",
      minDate: "",
      maxDate: "",
      classroomList: [],
      data: [],
      topHeight: 0,
      range: [],
      popupShow: false,
      classroomShow: false
    };
  },
  computed: {},
  methods: {
    schoolShowHandle() {
      const schoolData = uni.getStorageSync("schoolData");
      // const checkedSchool = uni.getStorageSync("checkedSchool");
      if (schoolData) {
        const idKeysResult = this.searchForm.department_id;
        const options = JSON.parse(JSON.stringify(schoolData));
        options.forEach((parent) => {
          if (parent.child) {
            // eslint-disable-next-line array-callback-return
            parent.child.map((item) => {
              item.pid = parent.id;
            });
          }
        });
        const newOptions = this.deepCheckValue(
          options,
          idKeysResult,
          "id",
          "child"
        );
        this.range = [...newOptions];
        this.$refs.gqTree._initTree();
        this.$refs.gqTree._show();
      } else {
        this.$refs.uToast.show({
          type: "error",
          message: "获取缓存校区失败，请退出重新登录！"
        });
      }
    },
    deepCheckValue(options, values, idKey, childKey) {
      return options.map((i) => {
        if (values.indexOf(i[idKey]) > -1) {
          i.isGqAddChecked = true;
        } else {
          i.isGqAddChecked = false;
        }
        if (i[childKey] && i[childKey].length > 0) {
          this.deepCheckValue(i[childKey], values, idKey, childKey);
        }
        return i;
      });
    },
    getClassroom() {
      getSchedulingClassroom({
        department_id: this.searchForm.department_id
      }).then((res) => {
        if (res) {
          const arr = res.map((item) => {
            return {
              id: item.id,
              name: item.name
            };
          });
          this.classroomList = arr ?? [];
          if (this.classroomList.length) {
            if (this.classroomList[0].id) {
              delete this.searchForm.scheduling_form;
              this.searchForm.school_room_id = this.classroomList[0].id;
            } else {
              this.searchForm.scheduling_form = "online";
              this.searchForm.school_room_id = "";
            }
            this.searchForm.school_room_name = this.classroomList[0].name;
            this.popupShow = true;
            this.getSchedulingList();
          }
        }
      });
    },
    getSchedulingList() {
      this.pageLoading = true;
      this.data = [];
      getRollCallList(this.searchForm)
        .then((res) => {
          if (res) {
            const arr = [];

            res.results?.forEach((item) => {
              const obj = {};
              const time1 = this.$ljsPublic.date.formatTime(
                item.start_time,
                "{y}.{m}.{d} {h}:{i}"
              );
              const time2 = this.$ljsPublic.date.formatTime(
                item.end_time,
                "{h}:{i}"
              );
              const rang_time = `${time1} - ${time2}`;
              obj.classroom_name = item.classroom_name;
              obj.teacher_name = item.teacher_name;
              obj.week_day_chn = item.week_day_chn;
              obj.status_chn = item.status_chn;
              obj.scheduling_form_chn = item.scheduling_form_chn;
              obj.classroom_name = item.classroom_name;
              obj.rang_time = rang_time;
              obj.class_duration = formatSeconds(item.class_duration);
              arr.push(obj);
            });
            this.data = arr;
            this.pageLoading = false;
          }
        })
        .catch(() => {
          this.pageLoading = false;
        });
    },
    datePrevClick() {
      // 获取某个日期的前一天
      const init_date = new Date(this.searchForm.class_time_begin);
      init_date.setDate(init_date.getDate() - 1);
      const prevDate = this.$ljsPublic.date.formatTime(
        init_date,
        "{y}-{m}-{d}"
      );
      this.searchForm.class_time_begin = prevDate;
      this.searchForm.class_time_over = prevDate;
      const weekDay = this.$ljsPublic.date.getWeekday(new Date(prevDate));
      this.dateTxt = `${prevDate}（${weekDay}）`;
      this.getSchedulingList();
    },
    dateNextClick() {
      // 获取某个日期的后一天
      const init_date = new Date(this.searchForm.class_time_begin);
      init_date.setDate(init_date.getDate() + 1);
      const nextDate = this.$ljsPublic.date.formatTime(
        init_date,
        "{y}-{m}-{d}"
      );
      this.searchForm.class_time_begin = nextDate;
      this.searchForm.class_time_over = nextDate;
      const weekDay = this.$ljsPublic.date.getWeekday(new Date(nextDate));
      this.dateTxt = `${nextDate}（${weekDay}）`;
      this.getSchedulingList();
    },
    dateTodayClick() {
      const today = this.$ljsPublic.date.formatTime(new Date(), "{y}-{m}-{d}");
      const weekDay = this.$ljsPublic.date.getWeekday(new Date());
      this.searchForm.class_time_begin = today;
      this.searchForm.class_time_over = today;
      this.dateTxt = `${today}（${weekDay}）`;
      this.getSchedulingList();
    },
    dateClick() {
      this.popupShow = false;
      this.calendarShow = true;
    },
    calendarClose() {
      this.popupShow = true;
    },
    calendarChange(date) {
      const { result } = date;
      const { class_time_begin } = this.searchForm;
      if (result !== class_time_begin) {
        this.searchForm.class_time_begin = result;
        this.searchForm.class_time_over = result;
        const weekDay = this.$ljsPublic.date.getWeekday(new Date(result));
        this.dateTxt = `${result}（${weekDay}）`;
        this.getSchedulingList();
      }
      this.popupShow = true;
    },

    treeCancel(e) {
      this.$refs.gqTree._hide();
      this.popupShow = true;
    },
    treeConfirm(e) {
      if (!e.length) {
        this.$refs.uToast.show({
          type: "info",
          message: "请至少选择一个校区！"
        });
        return;
      }
      this.searchForm.department_id = e[0].id;
      this.searchForm.department_name = e[0].name;
      this.getClassroom();
    },
    // 切换校区
    switchSchool() {
      this.popupShow = false;
      this.schoolShowHandle();
    },
    switchClassroom() {
      const index = this.classroomList.findIndex(
        (item) => item.name === this.searchForm.school_room_name
      );
      this.classroomShow = true;
      this.$refs.classroomPicker.setIndexs([index]);
    },
    classroomConfirm(e) {
      const { id, name } = e.value[0];
      if (id) {
        this.searchForm.school_room_id = id;
        this.searchForm.scheduling_form = "";
      } else {
        this.searchForm.school_room_id = "";
        this.searchForm.scheduling_form = "online";
      }
      this.searchForm.school_room_name = name;
      this.popupShow = true;
      this.classroomShow = false;
      this.getSchedulingList();
    }
  },
  watch: {},

  // 页面周期函数--监听页面加载
  onLoad() {
    const windowInfo = uni.getWindowInfo();
    this.topHeight = 44 + windowInfo.statusBarHeight;
    const today = this.$ljsPublic.date.formatTime(new Date(), "{y}-{m}-{d}");
    const weekDay = this.$ljsPublic.date.getWeekday(new Date());

    this.searchForm.class_time_begin = today;
    this.searchForm.class_time_over = today;
    // this.defaultDate = [today];
    this.dateTxt = `${today}（${weekDay}）`;

    const year = new Date().getFullYear();
    this.minDate = `${year - 3}-01-01`;
    this.maxDate = `${year + 3}-12-31`;

    const singSchoolData = uni.getStorageSync("singSchoolData");
    const checkedSchool = uni.getStorageSync("checkedSchool");
    if (checkedSchool) {
      const { id, name } = checkedSchool[0];
      this.searchForm.department_id = id;
      this.searchForm.department_name = name;
    } else {
      this.searchForm.department_id = singSchoolData.id;
      this.searchForm.department_name = singSchoolData.name;
    }
    this.getClassroom();
  },
  // 页面周期函数--监听页面初次渲染完成
  onReady() {},
  // 页面周期函数--监听页面显示(not-nvue)
  onShow() {},
  // 页面周期函数--监听页面隐藏
  onHide() {},
  // 页面周期函数--监听页面卸载
  onUnload() {}
  // 页面处理函数--监听用户下拉动作
  // onPullDownRefresh() { uni.stopPullDownRefresh(); },
  // 页面处理函数--监听用户上拉触底
  // onReachBottom() {},
  // 页面处理函数--监听页面滚动(not-nvue)
  // onPageScroll(event) {},
  // 页面处理函数--用户点击右上角分享
  // onShareAppMessage(options) {},
};
</script>

<style lang="scss" scoped>
.schedule {
  // height: 100vh;
  // padding-bottom: constant(safe-area-inset-bottom); /* 兼容 iOS < 11.2 */
  // padding-bottom: env(safe-area-inset-bottom);
  ::v-deep .u-loading-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 5;
  }
  .date-box {
    height: 88rpx;
    width: 100%;
    //box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.05);
    padding: 20rpx;
    .data-input {
      height: 100%;
      .text {
        color: #475669;
        font-size: 28rpx;
        font-style: normal;
        font-weight: 500;
      }
    }
  }
  .list-box {
    padding-bottom: 100rpx;
    .list-item {
      .content {
        width: 700rpx;
        margin: 25rpx auto;
        border-color: #ffbf0d;
        border-radius: 8rpx;
        transition: height 0.3s ease-out;
        border: 1px solid #ffbf0d !important;
        .left {
          flex: 1;
          padding: 24rpx 12rpx;
          .text-row {
            font-size: 22rpx;
            font-style: normal;
            font-weight: 500;
            line-height: 50rpx;
            span:nth-child(1) {
              color: #ffbf0d;
            }
            span:nth-child(2) {
              color: #475669;
            }
          }
        }
        .right {
          width: 200rpx;
          border-color: #ffbf0d;
          padding: 0 40rpx;
        }
      }
    }
  }
  .school-popup-content {
    padding: 30rpx;
    box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.05);
    .school-popup-title {
      font-weight: 500;
      ::v-deep .u-icon {
        margin-left: 10rpx;
      }
    }
  }
}
</style>
