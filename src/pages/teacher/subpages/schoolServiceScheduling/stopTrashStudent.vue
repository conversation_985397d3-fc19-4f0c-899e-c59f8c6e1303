<template>
  <div class="attendance-list">
    <u-loading-icon
      :show="pageLoading"
      color="#568ff5"
      textColor="#545556"
      mode="semicircle"
      :vertical="true"
      text="加载中..."
    ></u-loading-icon>
    <u-navbar
      :placeholder="true"
      bgColor="#204fef"
      title="点名上课"
      titleStyle="color: #fff;font-size:36rpx;font-weight:500"
      leftIconSize="20px"
      leftIconColor="#fff"
      :autoBack="true"
    >
    </u-navbar>

    <div class="border-top-1px tab-conetnt">
      <u-sticky
        index="attendance-list"
        :customNavHeight="topHeight"
        bgColor="#fff"
      >
        <u-tabs
          :current="current"
          lineWidth="60"
          :activeStyle="{
            color: '#568ff5',
            fontWeight: 'bold',
            transform: 'scale(1.05)'
          }"
          :inactiveStyle="{
            color: '#606266',
            transform: 'scale(1)'
          }"
          itemStyle="width:50%;padding-left: 30rpx; background-color: #fff; padding-right: 30rpx; height: 80rpx;"
          :list="tab_list"
          @click="tabclick"
        ></u-tabs>
        <u-gap height="6" bgColor="#f5f5f5"></u-gap>
        <div class="border-bottom-1px checkInStuInfo-header">
          <u-row>
            <u-col span="3">
              <view class="flex flex-center cell">学员姓名</view>
            </u-col>
            <u-col span="3">
              <view class="flex flex-center cell">状态</view>
            </u-col>
            <u-col span="3">
              <view class="flex flex-center cell">上课来源</view>
            </u-col>
            <u-col span="3">
              <view class="flex flex-center cell">联系电话</view>
            </u-col>
          </u-row>
        </div>
      </u-sticky>
      <div v-if="current === 0" class="content">
        <div class="student-box" :key="index" v-for="(item, index) in trash">
          <u-row>
            <u-col span="3">
              <view class="cell">{{ item.student_name }} </view>
            </u-col>
            <u-col span="3">
              <view class="cell">{{ item.student_type_chn }}</view>
            </u-col>
            <u-col span="3">
              <view class="cell">{{ item.handler_type_chn }}</view>
            </u-col>
            <u-col span="3">
              <view class="cell">{{ item.student_mobile }}</view>
            </u-col>
          </u-row>
        </div>
        <u-empty
          :show="!pageLoading && !trash.length"
          mode="data"
          width="160rpx"
          marginTop="60%"
          text="暂无数据"
          icon="https://tg-prod.oss-cn-beijing.aliyuncs.com/f14816ba-c2e2-4f63-8b18-e1d2190c1fb3.png"
        >
        </u-empty>
      </div>
      <div v-if="current === 1" class="content">
        <div class="student-box" :key="index" v-for="(item, index) in stop">
          <u-row>
            <u-col span="3">
              <view class="cell">{{ item.student_name }} </view>
            </u-col>
            <u-col span="3">
              <view class="cell">{{ item.student_type_chn }}</view>
            </u-col>
            <u-col span="3">
              <view class="cell">{{ item.handler_type_chn }}</view>
            </u-col>
            <u-col span="3">
              <view class="cell">{{ item.handler_type_chn }}</view>
            </u-col>
          </u-row>
        </div>
        <u-empty
          :show="!pageLoading && !stop.length"
          mode="data"
          width="160rpx"
          marginTop="60%"
          text="暂无数据"
          icon="https://tg-prod.oss-cn-beijing.aliyuncs.com/f14816ba-c2e2-4f63-8b18-e1d2190c1fb3.png"
        >
        </u-empty>
      </div>
    </div>
  </div>
</template>

<script>
import { leave_reason } from "@/utils/dict";
import { getSchedulingStudentList } from "@/services/schoolServiceScheduling";
export default {
  name: "StopTrashStudent",
  components: {},
  data() {
    return {
      scheduling_id: "",
      current: 0,
      tab_list: [
        {
          name: "已移除学员"
        },
        {
          name: "已停课学员"
        }
      ],
      trash: [],
      stop: [],
      topHeight: 0,
      pageLoading: false
    };
  },
  computed: {},
  filters: {},
  methods: {
    tabclick(item) {
      this.current = item.index;
    },
    getReasonCh(val) {
      const index = leave_reason.findIndex((item) => item.id === val);
      if (index === -1) return val;
      return leave_reason[index].name;
    },
    getList() {
      this.pageLoading = true;
      getSchedulingStudentList({
        scheduling_id: this.scheduling_id
      })
        .then((res) => {
          console.log("res :>> ", res);
          this.stop = res.stop ?? [];
          this.trash = res.trash ?? [];
          this.pageLoading = false;
        })
        .catch(() => {
          this.pageLoading = false;
        });
    }
  },
  watch: {},

  // 页面周期函数--监听页面加载
  onLoad(options) {
    const windowInfo = uni.getWindowInfo();
    console.log(windowInfo.statusBarHeight);
    this.topHeight = 44 + windowInfo.statusBarHeight;
    this.scheduling_id = options.id;
    this.getList();
  },
  // 页面周期函数--监听页面初次渲染完成
  onReady() {},
  // 页面周期函数--监听页面显示(not-nvue)
  onShow() {},
  // 页面周期函数--监听页面隐藏
  onHide() {},
  // 页面周期函数--监听页面卸载
  onUnload() {}
  // 页面处理函数--监听用户下拉动作
  // onPullDownRefresh() { uni.stopPullDownRefresh(); },
  // 页面处理函数--监听用户上拉触底
  // onReachBottom() {},
  // 页面处理函数--监听页面滚动(not-nvue)
  // onPageScroll(event) {},
  // 页面处理函数--用户点击右上角分享
  // onShareAppMessage(options) {},
};
</script>

<style lang="scss" scoped>
.attendance-list {
  background: #fff;
  height: 100vh;
  ::v-deep .u-tabs {
    // border-bottom: 1px solid #d3dce6;
    box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.05);
  }
  .cell {
    font-size: 26rpx;
    color: #607d86;
    padding: 10rpx 0;
    font-weight: 500;
  }
  .checkInStuInfo-header {
    .cell {
      height: 88rpx;
      line-height: 88rpx;
      color: #b3b7c6;
      font-size: 28rpx;
      font-weight: 500;
    }
  }
  .content {
    padding-bottom: 40rpx;
  }
  .student-box {
    // box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.05);
    // margin: 20rpx;
    background-color: #fff;
    ::v-deep .u-row {
      border-bottom: 1px solid #eaecef;
    }
    .cell {
      padding: 0 10rpx;
      height: 88rpx;
      line-height: 88rpx;
      text-align: center;
    }
  }

  ::v-deep .u-loading-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 5;
  }
}
</style>
