<template>
  <div class="attendance-list">
    <u-loading-icon
      :show="pageLoading"
      color="#FFBF0D"
      textColor="#545556"
      mode="semicircle"
      :vertical="true"
      text="加载中..."
    ></u-loading-icon>
    <u-navbar
      :placeholder="true"
      bgColor="#fff"
      title="实到人数"
      titleStyle="color: #000;font-size:36rpx;font-weight:500"
      leftIconSize="20px"
      leftIconColor="#000"
      :autoBack="true"
    >
    </u-navbar>

    <div class="border-top-1px tab-conetnt">
      <u-sticky
        index="attendance-list"
        :customNavHeight="topHeight"
        bgColor="#fff"
      >
        <u-tabs
          :current="current"
          lineWidth="60"
          :activeStyle="{
            color: '#FFBF0D',
            fontWeight: 'bold',
            transform: 'scale(1.05)'
          }"
          :inactiveStyle="{
            color: '#606266',
            transform: 'scale(1)'
          }"
          itemStyle="width:50%;padding-left: 30rpx; background-color: #fff; padding-right: 30rpx; height: 80rpx;"
          :list="tab_list"
          @click="tabclick"
        ></u-tabs>
      </u-sticky>
      <div v-if="current === 0" class="content">
        <div class="student-box" :key="index" v-for="(item, index) in data2">
          <u-row justify="space-between" gutter="10">
            <u-col span="5">
              <view class="cell"> 姓名：{{ item.student_name }} </view>
            </u-col>
            <u-col span="3">
              <view class="cell">状态：{{ item.student_type_chn }}</view>
            </u-col>
            <u-col span="4">
              <view class="cell">上课来源：{{ item.handler_type_chn }}</view>
            </u-col>
          </u-row>
          <u-row justify="space-between" gutter="10">
            <u-col span="8">
              <view class="cell"> 联系电话：{{ item.student_mobile }} </view>
            </u-col>
            <u-col span="4">
              <view class="cell">缺勤原因：{{ item.leave_reason }}</view>
            </u-col>
          </u-row>
        </div>
      </div>
      <div v-if="current === 1" class="content">
        <div class="student-box" :key="index" v-for="(item, index) in data1">
          <u-row justify="space-between" gutter="10">
            <u-col span="5">
              <view class="cell"> 姓名：{{ item.student_name }} </view>
            </u-col>
            <u-col span="3">
              <view class="cell">状态：{{ item.student_type_chn }}</view>
            </u-col>
            <u-col span="4">
              <view class="cell">上课来源：{{ item.handler_type_chn }}</view>
            </u-col>
          </u-row>
          <u-row justify="space-between" gutter="10">
            <u-col span="6">
              <view class="cell"> 联系电话：{{ item.student_mobile }} </view>
            </u-col>
          </u-row>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getAttendanceList } from "@/services/schoolServiceScheduling";
import { leave_reason } from "@/utils/dict";
export default {
  name: "AttendanceList",
  components: {},
  data() {
    return {
      scheduling_id: "",
      current: 0,
      tab_list: [
        {
          name: "未出勤(-)"
        },
        {
          name: "已出勤(-)"
        }
      ],
      data1: [],
      data2: [],
      topHeight: 0,
      pageLoading: false
    };
  },
  computed: {},
  filters: {},
  methods: {
    tabclick(item) {
      this.current = item.index;
    },
    getReasonCh(val) {
      const index = leave_reason.findIndex((item) => item.id === val);
      if (index === -1) return val;
      return leave_reason[index].name;
    },
    getAttendanceList() {
      this.pageLoading = true;
      getAttendanceList({
        scheduling_id: this.scheduling_id
      })
        .then((res) => {
          console.log(res);
          const data1 = []; // 出勤
          const data2 = []; // 缺勤
          let is_attendance_num = 0; // 出勤
          let not_attendance_num = 0; // 未出勤
          res?.available.forEach((item) => {
            const obj = {
              student_name: item.student_name,
              student_type_chn: item.student_type_chn,
              is_attendance: item.is_attendance,
              student_mobile: item.student_mobile,
              leave_reason: this.getReasonCh(item.leave_reason),
              handler_type_chn: item.handler_type_chn
            };
            if (obj.is_attendance === "YES") {
              is_attendance_num++;
              data1.push(obj);
            } else {
              not_attendance_num++;
              data2.push(obj);
            }
          });
          this.tab_list[0].name = `未出勤(${not_attendance_num})`;
          this.tab_list[1].name = `出勤(${is_attendance_num})`;
          this.data1 = data1;
          this.data2 = data2;
          this.pageLoading = false;
        })
        .catch(() => {
          this.pageLoading = false;
        });
    }
  },
  watch: {},

  // 页面周期函数--监听页面加载
  onLoad(options) {
    const windowInfo = uni.getWindowInfo();
    console.log(windowInfo.statusBarHeight);
    this.topHeight = 44 + windowInfo.statusBarHeight;
    this.scheduling_id = options.scheduling_id;
    this.getAttendanceList();
  },
  // 页面周期函数--监听页面初次渲染完成
  onReady() {},
  // 页面周期函数--监听页面显示(not-nvue)
  onShow() {},
  // 页面周期函数--监听页面隐藏
  onHide() {},
  // 页面周期函数--监听页面卸载
  onUnload() {}
  // 页面处理函数--监听用户下拉动作
  // onPullDownRefresh() { uni.stopPullDownRefresh(); },
  // 页面处理函数--监听用户上拉触底
  // onReachBottom() {},
  // 页面处理函数--监听页面滚动(not-nvue)
  // onPageScroll(event) {},
  // 页面处理函数--用户点击右上角分享
  // onShareAppMessage(options) {},
};
</script>

<style lang="scss" scoped>
.attendance-list {
  background: #f5f6fa;
  height: 100vh;
  ::v-deep .u-tabs {
    box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.05);
  }
  .content {
    padding-bottom: 40rpx;
  }
  .student-box {
    box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.05);
    margin: 20rpx;
    background-color: #fff;
    padding: 20rpx;
    border-radius: 8rpx;
  }
  .cell {
    font-size: 24rpx;
    color: #607d86;
    padding: 10rpx 0;
  }
  ::v-deep .u-loading-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 5;
  }
}
</style>
