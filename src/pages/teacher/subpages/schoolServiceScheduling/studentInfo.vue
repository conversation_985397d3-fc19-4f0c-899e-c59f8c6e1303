<!-- eslint-disable vue/no-mutating-props -->
<template>
  <div class="studentInfo">
    <div class="bg"></div>
    <div class="info-title">学员详情</div>
    <div class="info-content">
      <div class="flex info-item">
        <label class="info-label">学员姓名：</label>
        <label class="info-value">{{ info.student_name }}</label>
      </div>
      <div class="info-item flex">
        <label class="info-label">状态：</label>
        <label class="info-value">{{ info.student_type_chn }}</label>
      </div>
      <div class="flex info-item">
        <label class="info-label">上课来源：</label>
        <label class="info-value">{{ info.handler_type_chn }}</label>
      </div>
      <div class="flex info-item">
        <label class="info-label">联系电话：</label>
        <label class="info-value">{{ info.student_mobile }}</label>
      </div>
      <div class="info-item flex">
        <label class="info-label">课时：</label>
        <div class="info-value" style="flex: 1">
          <div
            v-for="(item, index) in info.lesson_list"
            :key="index"
            class="flex flex-between red"
            :class="{ 'text-danger': index > 0 }"
          >
            <label>{{ item.course_name }}</label
            ><label>剩余{{ item.total_lesson }}课时</label>
          </div>
        </div>
      </div>
      <div class="flex info-item">
        <label class="info-label">缺勤统计：</label>
        <label class="info-value">{{ info.number_present }}</label>
      </div>
      <div class="info-item flex">
        <label class="info-label">备注：</label>
        <u--textarea
          v-model="info.memo"
          placeholder="请输入(200字符以内)"
          maxlength="200"
          height="80"
        ></u--textarea>
      </div>
    </div>
    <div class="flex flex-between info-btn">
      <!-- 取消按钮、确定按钮 -->
      <div class="btn-item cancel" @tap="cancelClick">取消</div>
      <div class="btn-item confirm" @tap="confirmClick">确定</div>
    </div>
  </div>
</template>

<script>
export default {
  name: "studentInfo",
  props: {
    info: {}
  },
  data() {
    return {
      stu_data: {}
    };
  },
  computed: {},
  methods: {
    cancelClick() {
      this.$emit("cancel");
    },
    confirmClick() {
      this.$emit("confirm", this.info.memo);
    }
  },
  watch: {},

  // 组件周期函数--监听组件挂载完毕
  mounted() {
    // this.stu_data = this.info;
  },
  // 组件周期函数--监听组件数据更新之前
  beforeUpdate() {},
  // 组件周期函数--监听组件数据更新之后
  updated() {},
  // 组件周期函数--监听组件激活(显示)
  activated() {},
  // 组件周期函数--监听组件停用(隐藏)
  deactivated() {},
  // 组件周期函数--监听组件销毁之前
  beforeUnmount() {}
};
</script>

<style lang="scss" scoped>
.studentInfo {
  position: relative;
  .bg {
    position: absolute;
    top: 0;
    right: -20rpx;
    width: 293rpx;
    height: 198rpx;
    z-index: 5;
    background: url("https://tg-prod.oss-cn-beijing.aliyuncs.com/d0980916-6734-4fa6-966a-a27b23c5f14d.png");
    background-repeat: no-repeat;
    background-size: cover;
  }
  padding: 44rpx;
  width: 600rpx;
  background-image: linear-gradient(
    180deg,
    rgba(45, 128, 237, 0.2) 0%,
    rgba(217, 217, 217, 0) 100%
  );

  .info-title {
    margin-bottom: 20rpx;
    color: #333;
    text-align: center;
    font-size: 34rpx;
    font-weight: 500;
    line-height: 48rpx;
  }
  .info-content {
    .info-item {
      font-size: 28rpx;
      font-style: normal;
      font-weight: 500;
      line-height: 48rpx;
      .info-label {
        color: #8492a6;
        width: 150rpx;
        text-align: right;
      }
      .info-value {
        color: #475669;
      }
    }
  }
  .info-btn {
    margin-top: 30rpx;
    .btn-item {
      width: 232rpx;
      height: 88rpx;
      line-height: 88rpx;
      text-align: center;
      border-radius: 30px;
      &.cancel {
        background: rgba(211, 220, 230, 0.4);
        color: #8492a6;
      }
      &.confirm {
        background: linear-gradient(270deg, #3667f0 0%, #568ff5 100%);
        color: #fff;
      }
    }
  }
}
</style>
