<template>
  <div class="school-service-scheduling">
    <u-toast ref="uToast"></u-toast>
    <u-loading-icon
      :show="pageLoading"
      color="#FFBF0D"
      textColor="#545556"
      mode="semicircle"
      :vertical="true"
      text="加载中..."
    ></u-loading-icon>
    <next-paging
      ref="paging"
      v-model="data"
      @query="getList"
      :autoShowBackToTop="true"
      :defaultPageSize="searchForm.page_size"
    >
      <!--top插槽-->
      <template #top
        ><div class="slotTop">
          <u-navbar
            :placeholder="true"
            bgColor="#fff"
            title="排课管理"
            titleStyle="color: #000;font-size:36rpx;font-weight:500"
            leftIconSize="20px"
            leftIconColor="#000"
            :autoBack="true"
          >
          </u-navbar>
          <div class="flex flex-v-center flex-around border-top-1px date-box">
            <u-button
              @click="datePrevClick"
              type="primary"
              size="small"
              :plain="true"
              text="前一天"
              :customStyle="{
                fontWeight: '500',
                width: '110rpx',
                height: '48rpx'
              }"
            ></u-button>
            <u-button
              @click="dateNextClick"
              type="primary"
              size="small"
              :plain="true"
              text="后一天"
              :customStyle="{
                fontWeight: '500',
                width: '110rpx',
                height: '48rpx'
              }"
            ></u-button>
            <div @click="dateClick" class="flex flex-center data-input">
              <span class="text">{{ dateTxt }}</span>
              <u-icon
                :name="item.contentHide ? 'arrow-up' : 'arrow-down'"
                color="#FFBF0D"
                size="14"
              ></u-icon>
            </div>
            <u-button
              @click="dateTodayClick"
              type="primary"
              size="small"
              text="今天"
              :customStyle="{
                fontWeight: '500',
                width: '110rpx',
                height: '48rpx'
              }"
            ></u-button>
          </div>
        </div>
      </template>
      <!-- next-paging默认铺满全屏，此时页面所有view都应放在next-paging标签内，否则会被盖住 -->
      <!-- 需要固定在页面顶部的view请通过slot="top"插入，包括自定义的导航栏 -->
      <div class="list-box">
        <div v-for="(item, index) in data" :key="item.id" class="list-item">
          <div @click="arrowClick(item)" class="flex flex-between title">
            <span>{{ item.rang_time + " " + item.week_day_chn }}</span
            ><u-icon
              :name="item.contentHide ? 'arrow-up' : 'arrow-down'"
              color="#fff"
              size="14"
            ></u-icon>
          </div>
          <div
            class="flex border-around content"
            :class="{ hide: item.contentHide }"
          >
            <div class="left">
              <!-- classroom_name,
                classroom_alias_name,
                scheduling_form_chn,
                id,
                school_room_name,
                status_chn,
                roll_called_student_numb,
                total_student_numb,
                week_day_chn,
                rang_time -->
              <div class="text-row">
                <span>班级名称：</span><span>{{ item.classroom_name }}</span>
              </div>
              <div class="text-row">
                <span>班级别名：</span
                ><span>{{ item.classroom_alias_name }}</span>
              </div>
              <div class="text-row">
                <span>任课老师：</span><span>{{ item.teacher_name }}</span>
              </div>
              <div class="text-row">
                <span>班主任：</span><span>{{ item.header_teacher_name }}</span>
              </div>
              <div class="text-row">
                <span>上课形式：</span
                ><span>{{ item.scheduling_form_chn }}</span>
              </div>
              <div class="text-row">
                <span>上课教室：</span><span>{{ item.school_room_name }}</span>
              </div>
              <div class="text-row">
                <span>上课状态：</span><span>{{ item.status_chn }}</span>
              </div>
              <div class="text-row">
                <span>实到人数：</span
                ><span
                  >{{ item.roll_called_student_numb }}/{{
                    item.total_student_numb
                  }}</span
                >
              </div>
            </div>
            <div class="flex flex-center border-left-1px right">
              <u-button
                @click="openActions(item)"
                type="primary"
                size="small"
                :plain="true"
                text="操作"
                :customStyle="{ fontWeight: '500' }"
              ></u-button>
            </div>
          </div>
          <u-gap
            v-if="index + 1 !== count"
            height="10"
            bgColor="#f5f6fa"
          ></u-gap>
        </div>
      </div>
      <!--bottom插槽-->
      <!-- <template #bottom
      ><view class="slotBottom"><text>我是底部插槽内容</text></view></template
    > -->
    </next-paging>
    <u-action-sheet
      :actions="actionList"
      :show="actionShow"
      :safeAreaInsetBottom="true"
      :closeOnClickAction="true"
      round="10"
      cancelText="取消"
      @select="selectClick"
      @close="actionShow = false"
    ></u-action-sheet>

    <l-calendar
      v-model="calendarShow"
      @change="calendarChange"
      :minDate="minDate"
      :maxDate="maxDate"
      :isRange="false"
      activeBgColor="#FFBF0D"
      :initStartDate="searchForm.class_time_begin"
    ></l-calendar>
  </div>
</template>

<script>
import lCalendar from "@/uni_modules/l-calendar/components/l-calendar/l-calendar.vue";
import {
  getSchoolServiceSchedulingList,
  lockAccount,
  rollCallLock
} from "@/services/schoolServiceScheduling";
export default {
  name: "SchedulingList",
  components: { lCalendar },
  data() {
    return {
      searchForm: {
        page: 1,
        page_size: 20,
        class_time_begin: "",
        class_time_over: ""
      },
      dateTxt: "---",
      data: [],
      count: 0, // 总条数
      pageCount: 0, // 总页数
      pageLoading: false,
      isFisrtEnter: true,
      actionShow: false,
      calendarShow: false,
      minDate: "",
      maxDate: "",
      // defaultDate: "",
      actionList: [],
      // actionList: [
      //   {
      //     index: 1,
      //     name: "点名上课"
      //   },
      //   {
      //     index: 2,
      //     name: "查看实到人数"
      //   }
      // ],
      from: ""
    };
  },
  computed: {
    // actionList() {
    //   console.log(this.selectRow);
    //   const arr = [];
    //   if (this.$hasPermission(["course_summary_list"])) {
    //     arr.push({
    //       index: 3,
    //       name: "发送课程总结",
    //       disabled: this.selectRow?.status === "not_start"
    //     });
    //   }
    //   if (this.$hasPermission(["parent_class_list"])) {
    //     arr.push({
    //       index: 4,
    //       name: "发送家长课堂",
    //       disabled: this.selectRow?.status === "not_start"
    //     });
    //   }
    //   if (this.$hasPermission(["roll_call"])) {
    //     arr.push({
    //       index: 1,
    //       name: "点名上课"
    //     });
    //   }
    //   if (this.$hasPermission(["scheduling_student_list"])) {
    //     arr.push({
    //       index: 2,
    //       name: "查看实到人数"
    //     });
    //   }
    //   console.log(arr, "arr");
    //   return arr;
    // }
  },
  methods: {
    getList(pageNo) {
      // if (pageNo > this.pageCount) {
      //   this.$refs.paging.complete([]);
      // } else {
      this.searchForm.page = pageNo;
      const schId = uni.getStorageSync("checkedSchool");
      if (schId.length) {
        this.searchForm.department_id = schId.map((item) => item.id);
      }
      if (this.isFisrtEnter) {
        this.pageLoading = true;
        this.isFisrtEnter = false;
      }

      getSchoolServiceSchedulingList({
        ...this.searchForm
      })
        .then((res) => {
          if (res.results) {
            this.count = res.count;
            // 获取数据总页数
            this.pageCount = Math.ceil(this.count / this.searchForm.page_size);
            const arr = res.results.map((item) => {
              const {
                classroom_name,
                classroom_alias_name,
                scheduling_form_chn,
                id,
                department_id,
                school_room_name,
                status_chn,
                teacher_name,
                roll_called_student_numb,
                header_teacher_name,
                total_student_numb,
                week_day_chn,
                start_time,
                end_time
              } = item;
              const time1 = this.$ljsPublic.date.formatTime(
                start_time,
                "{y}.{m}.{d} {h}:{i}"
              );
              const time2 = this.$ljsPublic.date.formatTime(
                end_time,
                "{h}:{i}"
              );
              const rang_time = `${time1} - ${time2}`;
              return {
                classroom_name,
                classroom_alias_name,
                scheduling_form_chn,
                id,
                start_time,
                department_id,
                school_room_name,
                status_chn,
                teacher_name,
                header_teacher_name,
                roll_called_student_numb,
                total_student_numb,
                week_day_chn,
                rang_time,
                contentHide: false
              };
            });
            this.$refs.paging.complete(arr);
            this.pageLoading = false;
          }
        })
        .catch(() => {
          this.pageLoading = false;
        });
      // }
    },
    datePrevClick() {
      // 获取某个日期的前一天
      const init_date = new Date(this.searchForm.class_time_begin);
      init_date.setDate(init_date.getDate() - 1);
      const prevDate = this.$ljsPublic.date.formatTime(
        init_date,
        "{y}-{m}-{d}"
      );
      this.searchForm.class_time_begin = prevDate;
      this.searchForm.class_time_over = prevDate;
      const weekDay = this.$ljsPublic.date.getWeekday(new Date(prevDate));
      this.dateTxt = `${prevDate}（${weekDay}）`;
      this.isFisrtEnter = true;
      this.$refs.paging.reload();
    },
    dateNextClick() {
      // 获取某个日期的后一天
      const init_date = new Date(this.searchForm.class_time_begin);
      init_date.setDate(init_date.getDate() + 1);
      const nextDate = this.$ljsPublic.date.formatTime(
        init_date,
        "{y}-{m}-{d}"
      );
      this.searchForm.class_time_begin = nextDate;
      this.searchForm.class_time_over = nextDate;
      const weekDay = this.$ljsPublic.date.getWeekday(new Date(nextDate));
      this.dateTxt = `${nextDate}（${weekDay}）`;
      this.isFisrtEnter = true;
      this.$refs.paging.reload();
    },
    dateTodayClick() {
      const today = this.$ljsPublic.date.formatTime(new Date(), "{y}-{m}-{d}");
      const weekDay = this.$ljsPublic.date.getWeekday(new Date());
      this.searchForm.class_time_begin = today;
      this.searchForm.class_time_over = today;
      this.dateTxt = `${today}（${weekDay}）`;
      this.isFisrtEnter = true;
      this.$refs.paging.reload();
    },
    dateClick() {
      // this.defaultDate = [this.searchForm.class_time_begin];
      this.calendarShow = true;
    },
    calendarChange(date) {
      const { result } = date;
      const { class_time_begin } = this.searchForm;
      if (result !== class_time_begin) {
        this.searchForm.class_time_begin = result;
        this.searchForm.class_time_over = result;
        const weekDay = this.$ljsPublic.date.getWeekday(new Date(result));
        this.dateTxt = `${result}（${weekDay}）`;
        this.isFisrtEnter = true;
        this.$refs.paging.reload();
      }
    },

    arrowClick(item) {
      item.contentHide = !item.contentHide;
    },
    checkLock() {
      const data = {
        date_time: this.$ljsPublic.date.formatTime(
          this.selectRow.start_time,
          "{y}-{m}-{d} {h}:{i}:{s}"
        ),
        department_id: this.selectRow.department_id
      };
      // 判断是否处在锁账周期内
      lockAccount(data).then((result) => {
        if (result.code === 0) {
          // false 则正常操作
          if (!result.data) {
            // 提交点名上锁
            rollCallLock({ scheduling_id: this.scheduling_id }).then((res) => {
              if (res.code === 0) {
                const code_no = res.data.code_no;
                uni.navigateTo({
                  url: `/pages/teacher/subpages/schoolServiceScheduling/rollCallClass?id=${this.scheduling_id}&code_no=${code_no}`
                });
              } else {
                const { data, message } = res;
                if (data) {
                  uni.hideToast();
                  this.$refs.uToast.show({
                    type: "error",
                    message: `该排课【${data}】正在点名中，请稍后再试`
                  });
                } else {
                  this.$refs.uToast.show({
                    type: "error",
                    message
                  });
                }
              }
            });
          } else {
            this.$refs.uToast.show({
              type: "warning",
              message: `处于锁账周期内，暂不支持点名上课!`
            });
          }
        }
      });
    },
    selectClick(sel) {
      console.log(sel, this.scheduling_id, "sel");
      switch (sel.index) {
        case 1:
          this.checkLock();

          break;
        case 2:
          uni.navigateTo({
            url: `/pages/teacher/subpages/schoolServiceScheduling/attendanceList?scheduling_id=${this.scheduling_id}`
          });
          break;
        case 3:
          uni.navigateTo({
            url: `/pages/teacher/subpages/schoolServiceScheduling/sendContentList?scheduling_id=${this.scheduling_id}&pageType=course_summary`
          });
          break;
        case 4:
          uni.navigateTo({
            url: `/pages/teacher/subpages/schoolServiceScheduling/sendContentList?scheduling_id=${this.scheduling_id}&pageType=parent_class`
          });
          break;
      }
    },
    openActions(item) {
      console.log(item, "item");

      const arr = [];
      console.log(item.status === "not_start", "item.status");
      if (this.$hasPermission(["course_summary_list"])) {
        arr.push({
          index: 3,
          name: "发送课程总结",
          disabled: item.status_chn === "未上课"
        });
      }
      if (this.$hasPermission(["parent_class_list"])) {
        arr.push({
          index: 4,
          name: "发送家长课堂",
          disabled: item.status_chn === "未上课"
        });
      }
      if (this.$hasPermission(["roll_call"])) {
        arr.push({
          index: 1,
          name: "点名上课"
        });
      }
      if (this.$hasPermission(["scheduling_student_list"])) {
        arr.push({
          index: 2,
          name: "查看实到人数"
        });
      }
      console.log(arr, "arr");
      this.actionList = arr;
      this.scheduling_id = item.id;
      this.selectRow = item;
      this.actionShow = true;
    },
    // 局部更新列表
    updateList(res) {
      const { id, roll_called_student_numb } = res;
      const { data } = this;
      const index = data.findIndex((item) => item.id === id);
      if (index > -1) {
        data[index].roll_called_student_numb = roll_called_student_numb;
        data[index].status_chn = "已上课";
      }
      this.data = [...data];
    }
  },
  watch: {},

  // 页面周期函数--监听页面加载
  onLoad(options) {
    const { from } = options;
    this.from = from;
    const today = this.$ljsPublic.date.formatTime(new Date(), "{y}-{m}-{d}");
    const weekDay = this.$ljsPublic.date.getWeekday(new Date());

    this.searchForm.class_time_begin = today;
    this.searchForm.class_time_over = today;
    // this.defaultDate = [today];
    this.dateTxt = `${today}（${weekDay}）`;

    const year = new Date().getFullYear();
    this.minDate = `${year - 3}-01-01`;
    this.maxDate = `${year + 3}-12-31`;
    uni.$on("updateList", this.updateList);
  },

  // 页面周期函数--监听页面初次渲染完成
  onReady() {},
  // 页面周期函数--监听页面显示(not-nvue)
  onShow() {},
  // 页面周期函数--监听页面隐藏
  onHide() {},
  // 页面周期函数--监听页面卸载
  onUnload() {
    uni.$off("updateList", this.updateList);
  }
  // 页面处理函数--监听用户下拉动作
  // onPullDownRefresh() { uni.stopPullDownRefresh(); },
  // 页面处理函数--监听用户上拉触底
  // onReachBottom() {},
  // 页面处理函数--监听页面滚动(not-nvue)
  // onPageScroll(event) {},
  // 页面处理函数--用户点击右上角分享
  // onShareAppMessage(options) {},
};
</script>

<style lang="scss" scoped>
.date-box {
  height: 88rpx;
  width: 100%;
  box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.05);
  padding: 20rpx;
}
.list-box {
  padding-top: 16rpx;
  .list-item {
    .title {
      background: #ffbf0d;
      color: #fff;
      height: 64rpx;
      line-height: 64rpx;
      font-size: 24rpx;
      font-weight: 500;
      padding: 0 40rpx;
    }
    .content {
      width: 700rpx;
      margin: 25rpx auto;
      border-color: #ffbf0d;
      border-radius: 8rpx;
      transition: height 0.3s ease-out;
      &.hide {
        height: 0;
        overflow: hidden;
        margin: 0;
        border: 0;
        opacity: 0;
      }
      .left {
        flex: 1;
        padding: 24rpx 12rpx;
        .text-row {
          font-size: 22rpx;
          font-style: normal;
          font-weight: 500;
          line-height: 40rpx;
          span:nth-child(1) {
            color: #ffbf0d;
          }
          span:nth-child(2) {
            color: #475669;
          }
        }
      }
      .right {
        width: 200rpx;
        border-color: #ffbf0d;
        padding: 0 40rpx;
      }
    }
  }
}
::v-deep .u-loading-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 5;
}
.data-input {
  height: 100%;
  .text {
    color: #475669;
    font-size: 28rpx;
    font-style: normal;
    font-weight: 500;
  }
}
</style>
