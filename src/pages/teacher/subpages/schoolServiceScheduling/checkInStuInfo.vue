<template>
  <div class="checkInStuInfo">
    <div class="ft-normal text-primary checkInStuInfo-title">点名信息</div>
    <div class="checkInStuInfo-header">
      <u-row>
        <u-col span="3">
          <view class="flex flex-center cell">学员姓名</view>
        </u-col>
        <u-col span="2">
          <view class="flex flex-center cell">点名</view>
        </u-col>
        <u-col span="2">
          <view class="flex flex-center cell">计费</view>
        </u-col>
        <u-col span="5">
          <view class="flex flex-center cell">状态</view>
        </u-col>
      </u-row>
    </div>
    <div class="checkInStuInfo-body">
      <u-row :key="index" v-for="(row, index) in list">
        <u-col span="3">
          <view class="flex flex-center border-bottom-1px cell">{{
            row.student_name
          }}</view>
        </u-col>
        <u-col span="2">
          <view class="flex flex-center border-bottom-1px cell">
            {{ row.is_attendance == "YES" ? "出勤" : "未出勤" }}</view
          >
        </u-col>
        <u-col span="2">
          <view class="flex flex-center border-bottom-1px cell">{{
            row.is_billable == "YES" ? "计费" : "未计费"
          }}</view>
        </u-col>
        <u-col span="5">
          <view class="flex flex-center border-bottom-1px cell">
            <u-loading-icon
              v-if="row.loading === 'loading'"
              size="14"
              color="#5d5d5d"
            ></u-loading-icon>
            <u-icon
              v-if="row.loading === 'success'"
              name="checkmark"
              color="#5fb446"
              size="12"
            ></u-icon>

            <u-icon
              v-if="row.loading === 'error'"
              name="close"
              color="#de1c24"
              size="12"
            ></u-icon>
            <label class="text-danger" v-if="row.loading === 'error'">{{
              row.msg
            }}</label>
          </view>
        </u-col>
      </u-row>
    </div>
  </div>
</template>

<script>
export default {
  name: "checkInStuInfo",
  props: {
    list: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      // table_data: []
    };
  },
  computed: {},
  methods: {},
  watch: {},
  created() {},
  onLoad() {},
  // 组件周期函数--监听组件挂载完毕
  mounted() {},
  // 组件周期函数--监听组件数据更新之前
  beforeUpdate() {},
  // 组件周期函数--监听组件数据更新之后
  updated() {},
  // 组件周期函数--监听组件激活(显示)
  activated() {
    // this.table_data = this.list;
  },
  // 组件周期函数--监听组件停用(隐藏)
  deactivated() {},
  // 组件周期函数--监听组件销毁之前
  beforeUnmount() {}
};
</script>

<style lang="scss" scoped>
.checkInStuInfo {
  width: 700rpx;

  .checkInStuInfo-title {
    padding: 40rpx;
    font-size: 30rpx;
    text-align: center;
    font-weight: 500;
  }
  .checkInStuInfo-header {
    margin: 0 10rpx;
    .cell {
      font-size: 28rpx;
      font-weight: 500;
      padding: 8rpx 10rpx;
      background: rgba(45, 128, 237, 0.1);
      color: #475669;
    }
  }
  .checkInStuInfo-body {
    margin: 0 10rpx;
    padding-bottom: 30rpx;
    max-height: 70vh;
    overflow: auto;
    .cell {
      font-size: 24rpx;
      font-weight: 500;
      padding: 16rpx 10rpx;
      color: #5d5d5d;
      height: 100rpx;
    }
  }
}
</style>
