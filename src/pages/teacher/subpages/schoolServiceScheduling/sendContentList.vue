<template>
  <div class="sendContentList">
    <div class="sendContentList-header">
      <u-navbar
        :title="`${pageTypeChn[pageType]}`"
        :placeholder="true"
        bgColor="transparent"
        leftIconColor="#fff"
        titleStyle="color: #fff;font-size:36rpx;font-weight:600"
        @rightClick="rightClick"
        :autoBack="true"
      >
      </u-navbar>
      <div class="sendContentList-header-search">
        <u--input
          placeholder="搜索学员姓名"
          prefixIcon="search"
          v-model="student_name"
          color="#fff"
          shape="circle"
          placeholderStyle="color: #D3DCE6"
          confirmType="search"
          clearable
          @clear="getList('clear')"
          @confirm="getList()"
          prefixIconStyle="font-size: 24px;color: rgba(211, 220, 230, 1)"
        ></u--input>
      </div>
    </div>
    <div class="list-operation">
      <div>
        <u-checkbox-group
          v-model="isAllChecked"
          @change="checkboxAllChange"
          iconPlacement="right"
          placement="row"
        >
          <span
            style="
              color: #606266;
              font-size: 28rpx;
              font-weight: 500;
              margin-right: 14rpx;
            "
            >全选</span
          ><u-checkbox></u-checkbox>
        </u-checkbox-group>
      </div>
      <div @tap="handleOperate">操作</div>
    </div>
    <view class="u-page">
      <u-checkbox-group
        v-model="checkboxValue1"
        placement="column"
        @change="checkboxChange"
      >
        <div
          class="sendContentList-item"
          v-for="(item, index) in list"
          :key="index"
        >
          <div class="sendContentList-item-left" @tap.stop="goDetail(item)">
            <div class="sendContentList-item-left-avatar">
              <u-avatar
                :src="
                  item.student_gender === 'female'
                    ? 'https://tg-prod.oss-cn-beijing.aliyuncs.com/cf769e6f-5f97-41c6-a3c8-b796ca0df32c.png'
                    : item.student_gender === 'male'
                    ? 'https://tg-prod.oss-cn-beijing.aliyuncs.com/9e8b7d64-c249-45e8-ac42-3a67a921f84d.png'
                    : 'https://tg-prod.oss-cn-beijing.aliyuncs.com/db5c1013-4e99-41c7-bfa4-f93500fe0622.png'
                "
              ></u-avatar>
            </div>
            <div class="sendContentList-item-left-info">
              <div class="sendContentList-item-left-info-name-container">
                <div class="sendContentList-item-left-info-name">
                  {{ item.student_name }}
                </div>
                <div
                  :class="[
                    'read-status',
                    item.is_cancel === 1
                      ? 'blue'
                      : item.read_status === 1
                      ? 'green'
                      : 'red'
                  ]"
                  v-if="item.teacher_name"
                >
                  {{
                    item.is_cancel === 1
                      ? "已撤回"
                      : item.read_status === 1
                      ? "已读"
                      : "未读"
                  }}
                </div>
              </div>
              <div class="attendance-status-container">
                <div style="margin-right: 16rpx" class="attendance-status">
                  出勤状态：{{
                    item.is_attendance === "YES" ? "已出勤" : "未出勤"
                  }}
                </div>
                <div class="is-charge">
                  {{ item.is_billable === "YES" ? "已计费" : "未计费" }}
                </div>
              </div>
              <div
                v-if="item.teacher_name"
                style="color: #475669; font-size: 24rpx; font-weight: 500"
              >
                <div class="sender">发送人：{{ item.teacher_name }}</div>
                <div class="send-time">
                  {{ item.is_cancel === 1 ? "撤销" : "发送" }}时间：{{
                    item.is_cancel === 1
                      ? item.cancel_date
                      : $u.timeFormat(item.send_time, "yyyy-mm-dd hh:MM:ss")
                  }}
                </div>
              </div>
            </div>
          </div>
          <div>
            <u-checkbox
              :customStyle="{ height: '100%' }"
              :key="item.student_id"
              :name="item.student_id"
            >
            </u-checkbox>
          </div>
        </div>
      </u-checkbox-group>
    </view>
    <u-action-sheet
      :actions="actionList"
      :show="actionShow"
      :safeAreaInsetBottom="true"
      :closeOnClickAction="true"
      round="10"
      cancelText="取消"
      @select="selectClick"
      @close="closeActionSheet"
      :description="`对已选（${checkboxValue1.length}）个学员发送`"
    ></u-action-sheet>
  </div>
</template>

<script>
import {
  getFeedbackStudentList,
  cancelFeedback
} from "@/services/schoolServiceScheduling";
export default {
  components: {},
  data() {
    return {
      student_name: "",
      pageType: "",
      pageTypeChn: {
        parent_class: "家长课堂",
        class_notice: "班级通知",
        course_summary: "课程总结"
      },
      actionShow: false,
      keyword: "",
      isAllChecked: false,
      checkboxValue1: [],
      checkboxList1: [{ name: "全选" }, { name: "全选" }],
      scheduling_id: "",
      pageLoading: false,
      checkInStuInfo: false,
      list: [],
      top_info: {},
      isSingle: false,
      curStudentInfo: {}
    };
  },
  computed: {
    actionList() {
      const arr = [
        {
          index: 4,
          name: `发送${this.pageTypeChn[this.pageType]}`,
          permissionName: this.pageType + "_send"
        },
        {
          index: 5,
          name: `撤销${this.pageTypeChn[this.pageType]}`,
          permissionName: this.pageType + "_cancel"
        }
      ];
      return arr;
    }
  },
  methods: {
    isPermitPush(actionInfo) {
      if (this.$hasPermission([actionInfo.permissionName])) {
        return {
          index: actionInfo.index,
          name: actionInfo.name
        };
      }
    },
    getList(type) {
      this.pageLoading = true;
      getFeedbackStudentList({
        student_name: type === "clear" ? "" : this.student_name,
        scheduling_id: this.scheduling_id,
        feedback_type: this.pageType
      })
        .then((res) => {
          console.log(res, "res");
          const { code, data } = res;
          if (code === 0) {
            this.list = data;
          }
        })
        .catch((err) => {
          console.log(err);
          this.pageLoading = false;
        });
    },
    checkboxChange(e) {
      if (e.length === this.list.length) {
        this.isAllChecked = [""];
      } else {
        this.isAllChecked = [];
      }
      this.checkboxValue1 = e;
    },
    checkboxAllChange(e) {
      if (e.length) {
        this.checkboxValue1 = this.list.map((i) => i.student_id);
      } else {
        this.checkboxValue1 = [];
      }
    },
    goDetail(item) {
      if (
        this.pageType === "course_summary" &&
        ["", "NO"].includes(item.is_attendance)
      ) {
        return;
      }
      this.curStudentInfo = item;
      this.isSingle = true;
      if (!this.checkboxValue1.includes(item.student_id)) {
        this.checkboxValue1 = [item.student_id];
      }
      const commonParams = {
        classroom_id: item.classroom_id,
        classroom_name: item.classroom_name,
        department_id: item.department_id,
        student_id: item.student_id,
        teacher_name: item.teacher_name,
        scheduling_id: this.scheduling_id,
        source: "student",
        students: encodeURI(
          JSON.stringify([
            {
              student_gender: item.student_gender,
              student_id: item.student_id,
              student_name: item.student_name
            }
          ])
        ),
        feedback_id: item.feedback_id,
        student_feedback_id: item.student_feedback_id,
        pageType: this.pageType,
        pageTitle: this.pageTypeChn[this.pageType]
      };
      // 点击已发送评价的学员
      if (item.teacher_name && item.is_cancel !== 1) {
        console.log(item, "点击已发送评价的学员");
        const urlParams = {
          ...commonParams,
          isShowBar: item.teacher_name && item.is_cancel !== 1 ? 1 : 0, // 1表示显示底部bar，0表示不显示
          openEdit: 1,
          openPreview: 1
        };
        uni.navigateTo({
          url: `/pages/teacher/subpages/richtext/index${uni.$u.queryParams(
            urlParams
          )}`
        });
      } else if (!item.teacher_name) {
        // 点击未发送评价的学员
        this.actionShow = true;
        console.log(item, "点击未发送评价的学员");
      } else if (item.is_cancel === 1) {
        // 点击已撤销的学员
        console.log(item, "点击已撤销的学员");
        const urlParams = {
          ...commonParams,
          isShowBar: 0,
          isShowBack: 1,
          openEdit: 1
        };
        uni.navigateTo({
          url: `/pages/teacher/subpages/richtext/index${uni.$u.queryParams(
            urlParams
          )}`
        });
      }
    },
    handleOperate() {
      const checked_student = this.list.filter((i) =>
        this.checkboxValue1.includes(i.student_id)
      );
      console.log(checked_student, "checked_student");
      if (!checked_student.length) {
        uni.showToast({
          title: "请选择学员",
          icon: "none"
        });
        return false;
      }
      this.actionShow = true;
      this.isSingle = false;
    },
    selectClick(sel) {
      console.log(sel, "sel");
      const checked_student = this.list.filter((i) =>
        this.checkboxValue1.includes(i.student_id)
      );
      if (sel.index === 4) {
        if (
          checked_student.some(
            (item) => item.teacher_name && item.is_cancel !== 1
          )
        ) {
          uni.showToast({
            title: "您勾选含有已发送的学员，无法发送！",
            icon: "none"
          });
          return;
        }
        if (this.pageType === "course_summary") {
          if (checked_student.some((item) => item.is_attendance !== "YES")) {
            uni.showToast({
              title: "您勾选的学员含有未出勤的学员，无法发送！",
              icon: "none"
            });
            return;
          }
        }
        // 发送
        const students = this.checkboxValue1.map((i) => {
          const cur = this.list.find((j) => i === j.student_id);
          return {
            student_gender: cur.student_gender,
            student_id: cur.student_id,
            student_name: cur.student_name
          };
        });
        const paramsData = {
          classroom_id: this.list[0].classroom_id,
          classroom_name: this.list[0].classroom_name,
          department_id: this.list[0].department_id,
          source: "student",
          isShowBar: 0,
          isShowBack: 1,
          scheduling_id: this.scheduling_id,
          students: encodeURI(JSON.stringify(students)),
          pageType: this.pageType,
          pageTitle: this.pageTypeChn[this.pageType]
        };
        let urlParams = "";
        if (this.isSingle) {
          const {
            student_id,
            classroom_id,
            classroom_name,
            student_gender,
            student_name,
            department_id
          } = this.curStudentInfo;
          console.log(this.curStudentInfo);
          const curParamsData = {
            student_id,
            classroom_id,
            classroom_name,
            student_gender,
            student_name,
            department_id,
            source: "student",
            students: encodeURI(
              JSON.stringify([
                {
                  student_gender: this.curStudentInfo.student_gender,
                  student_id: this.curStudentInfo.student_id,
                  student_name: this.curStudentInfo.student_name
                }
              ])
            ),
            isShowBar: 0,
            isShowBack: 1,
            pageType: this.pageType,
            scheduling_id: this.scheduling_id,
            pageTitle: this.pageTypeChn[this.pageType]
          };
          urlParams = uni.$u.queryParams(curParamsData);
        } else {
          urlParams = uni.$u.queryParams(paramsData);
        }
        console.log(urlParams);
        uni.navigateTo({
          url: `/pages/teacher/subpages/richtext/index${urlParams}`
        });
      } else if (sel.index === 5) {
        if (checked_student.some((item) => !item.teacher_name)) {
          uni.showToast({
            title: "您勾选含有未发送的学员，无法撤销！",
            icon: "none"
          });
          return;
        }
        if (checked_student.some((item) => item.is_cancel === 1)) {
          uni.showToast({
            title: "您勾选含有已撤销的学员，无法撤销！",
            icon: "none"
          });
          return;
        }
        const cancelParams = {
          student_feedback_id: [],
          is_cancel: 1
        };
        if (this.isSingle) {
          cancelParams.student_feedback_id = [
            this.curStudentInfo.student_feedback_id
          ];
        } else {
          cancelParams.student_feedback_id = this.list
            .filter((i) => this.checkboxValue1.includes(i.student_id))
            .map((i) => i.student_feedback_id);
        }
        console.log(cancelParams, "cancelParams");
        // 撤销
        cancelFeedback(cancelParams).then((res) => {
          if (res.code === 0) {
            this.getList();
            uni.showToast({
              title: "撤销成功",
              icon: "none"
            });
          } else {
            uni.showToast({
              title: res.message,
              icon: "none"
            });
          }
        });
      }
    },
    closeActionSheet() {
      this.actionShow = false;
      this.checkboxValue1 = this.checkboxValue1.filter(
        (i) => i !== this.curStudentInfo.student_id
      );
      if (this.checkboxValue1.length === 0) {
        this.isAllChecked = false;
      }
    }
  },
  watch: {},

  // 页面周期函数--监听页面加载
  onLoad(options) {
    console.log(options, "options");
    this.scheduling_id = options.scheduling_id;
    this.pageType = options.pageType;
    this.getList();
    console.log("onLoad", this.scheduling_id, this.pageType);
  },
  // 页面周期函数--监听页面初次渲染完成
  onReady() {},
  // 页面周期函数--监听页面显示(not-nvue)
  onShow() {
    if (this.scheduling_id) {
      this.getList();
    }
    console.log("onShow", this.scheduling_id, this.pageType);
  },
  // 页面周期函数--监听页面隐藏
  onHide() {},
  // 页面周期函数--监听页面卸载
  onUnload() {}
  // 页面处理函数--监听用户下拉动作
  // onPullDownRefresh() { uni.stopPullDownRefresh(); },
  // 页面处理函数--监听用户上拉触底
  // onReachBottom() {},
  // 页面处理函数--监听页面滚动(not-nvue)
  // onPageScroll(event) {},
  // 页面处理函数--用户点击右上角分享
  // onShareAppMessage(options) {},
};
</script>

<style lang="scss" scoped>
.sendContentList {
  background-color: #f5f5f5;
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  .sendContentList-header {
    border-radius: 0px 0px 16px 16px;
    background: linear-gradient(180deg, #3061f2 0%, #659ef8 100%);
    .sendContentList-header-search {
      padding: 43rpx 34rpx 44rpx 36rpx;
    }
  }
  .list-operation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 32rpx 18rpx 30rpx 24rpx;
  }
  .u-page {
    padding: 0rpx 24rpx 50rpx 24rpx;
    overflow-y: auto;
    .sendContentList-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 35rpx 42rpx 35rpx 34rpx;
      background-color: #fff;
      box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.05);
      border-radius: 16rpx;
      margin-bottom: 20rpx;
      .sendContentList-item-left {
        display: flex;
        align-items: center;
        .sendContentList-item-left-avatar {
          width: 40px;
          height: 40px;
          margin-right: 14rpx;
        }
        .sendContentList-item-left-info {
          .sendContentList-item-left-info-name-container {
            display: flex;
            align-items: center;
          }
          .sendContentList-item-left-info-name {
            font-size: 32rpx;
            font-weight: 500;
            color: #333;
          }
          .read-status {
            font-size: 22rpx;
            font-weight: 400;
            padding: 3rpx 8rpx;
            margin-left: 16rpx;
            border-radius: 6rpx;
            &.blue {
              border: 1px solid #2d80ed;
              color: #2d80ed;
            }
            &.green {
              border: 1px solid #049b0c;
              color: #049b0c;
            }
            &.red {
              border: 1px solid #ff7300;
              color: #ff7300;
            }
          }
          .attendance-status-container {
            display: flex;
            align-items: center;
            color: #475669;
            font-size: 24rpx;
            font-weight: 500;
            margin-top: 8rpx;
          }
          .sender {
            margin-top: 8rpx;
          }
          .send-time {
            margin-top: 8rpx;
          }
        }
      }
    }
  }
}
</style>
