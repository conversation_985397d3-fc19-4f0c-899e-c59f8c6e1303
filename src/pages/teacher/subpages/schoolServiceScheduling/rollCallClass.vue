<template>
  <div class="bottom-safe-area rollCallClass">
    <u-toast ref="uToast"></u-toast>
    <u-navbar
      :placeholder="true"
      bgColor="#204fef"
      title="点名上课"
      titleStyle="color: #fff;font-size:36rpx;font-weight:500"
      leftIconSize="20px"
      leftIconColor="#fff"
      :autoBack="true"
    >
    </u-navbar>
    <u-loading-icon
      :show="pageLoading"
      color="#568ff5"
      textColor="#545556"
      mode="semicircle"
      :vertical="true"
      text="加载中..."
    ></u-loading-icon>
    <div v-if="!pageLoading">
      <div class="top-conetnt">
        <u-sticky bgColor="transparent" customNavHeight="44" offset-top="64">
          <div class="flex flex-v-center box">
            <!-- 头像 -->
            <div class="avatar">
              <image
                mode="aspectFill"
                src="https://tg-prod.oss-cn-beijing.aliyuncs.com/db5c1013-4e99-41c7-bfa4-f93500fe0622.png"
              />
            </div>
            <div class="info">
              <div class="flex desc">
                <span class="ft-normal name">{{ top_info.teacher_name }}</span>
                <span class="text-primary ft-minor course-name"
                  >{{ top_info.classroom_name }}
                </span>
              </div>
              <div class="text-primary ft-minor time">
                上课日期：{{ top_info.time }}
              </div>
            </div>
          </div>
        </u-sticky>
      </div>
      <div class="content">
        <div class="content-desc">
          <div class="row1 flex flex-v-center flex-between">
            <div class="flex flex-v-center">
              <div class="border-around checkbox1"></div>
              未操作
            </div>
            <div class="flex flex-v-center">
              <image
                class="checkbox2"
                width="13px"
                height="13px"
                mode="aspectFill"
                src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABoAAAAaCAYAAACpSkzOAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAIZSURBVHgBtZY9TxtBEIbf2UNJKCDOdwTNpYuUD65J7XP+QFKmC11EldBE6bz5BTgVVaSkpeMX+OiQQOIkoKBziRACiwYKe5eZxbZO5s6+s49XWuluZ3eenf2aJYxRoM9DQ6haa0MCfAvr90xtgGLAxh7U/1g/ikf5oVGALmydHYXIJYo80GoWkFIAFQPULcwPTKbGvn6yOhIkkA5MkysDTCHiKVWgGkfX7tepZIMyICJex8DANmXgt0Dv9flaGZAkrAtT7/+7qWOyVO7hDsRrXTvQzyIXEe+uNdyRCJ6LisqKZv4B4eLKptokKmVgljGlFisKG98eYqU6m2pXUKGyoCVMIYH8/TqPhYrKbMOMKltt5k6T6cgLWY8usb51mYXyZRiVNNNKOOumYzFjpPkhTr4aZV3oORuGFYQ4iYdWmqHvYBg2CYTV9l6Evz5LaGnW3VbHHemPr++5cnTcQePLXFEIi7a9l+HPV/wVZjVJwj4F9zHHG6QYxHXflHMUjWuYdFwUIuJz9M/t33f6rJknwX3wZ7AjERYSRfv6cc2tsEX3d54uxSGAQcclQQeS25Wv9T8oWeLzUD+PByDRDDxN7rFRjnhN4gP9dPAcGIAk7Ur6LQMmEN4AtaG623qrTxsM/I4JJNMls5N8L2SCRDd5ShJi/ueWbCpZ71TruO5v9ElAUMtclohvetu7hDniFn9z6W7xb5QF6OsaOcPkOaZd0RMAAAAASUVORK5CYII="
              />
              选中状态
            </div>
            <div class="flex flex-v-center">
              <div class="checkbox3"></div>
              剩余课时不足
            </div>
          </div>
          <div
            v-if="top_info.dynamic_lesson"
            class="row2 flex flex-v-center flex-between"
          >
            <span>本次课程消耗课时</span>
            <!-- <u-number-box
              min="0"
              button-size="20"
              color="#333"
              bgColor="#fff"
              v-model="top_info.dynamic_discount"
              iconStyle="color: #568ff5;font-size:10px"
              @change="dynamic_discount_change"
            ></u-number-box> -->
            <u--input
              placeholder="请输入"
              border="bottom"
              maxlength="10"
              type="digit"
              v-model="top_info.dynamic_discount"
              @blur="dynamic_discount_change"
              fontSize="13px"
              :customStyle="{
                width: '130rpx',
                padding: '0 20rpx !important',
                flex: 'none',
                borderBottom: '1px solid #D3DCE6 !important'
              }"
            ></u--input>
          </div>
        </div>
        <scroll-view
          class="scroll-view_H"
          scroll-x="true"
          @scroll="onTableScroll"
          scroll-left="0"
        >
          <div class="custom-table">
            <div class="flex flex-between table-header">
              <div style="width: 132rpx" class="head-cell flex flex-v-center">
                <span>学员姓名</span>
              </div>
              <div
                style="width: 110rpx; margin-left: 10rpx"
                class="head-cell flex flex-v-center"
              >
                <u-checkbox-group
                  v-model="all_attendance_checked"
                  @change="all_attendance_change"
                >
                  <u-checkbox
                    activeColor="#568ff5"
                    shape="circle"
                    size="40"
                    labelSize="28rpx"
                    labelColor="#475669"
                    label="出勤"
                    name="YES"
                  ></u-checkbox>
                </u-checkbox-group>
              </div>
              <div style="width: 150rpx" class="head-cell flex flex-v-center">
                <u-checkbox-group
                  v-model="all_billable_checked"
                  @change="all_billable_change"
                >
                  <u-checkbox
                    activeColor="#568ff5"
                    shape="circle"
                    size="40"
                    labelSize="28rpx"
                    labelColor="#475669"
                    label="计费"
                    name="YES"
                    :disabled="top_info.mandatory_lesson"
                  ></u-checkbox>
                </u-checkbox-group>
              </div>
              <div
                style="width: 350rpx; padding-left: 16rpx"
                class="head-cell flex flex-v-center"
              >
                课时
              </div>
              <div
                style="width: 120rpx; margin-left: 20rpx"
                class="head-cell flex flex-v-center"
              >
                <u-checkbox-group
                  v-model="all_try_checked"
                  @change="all_try_change"
                >
                  <u-checkbox
                    activeColor="#568ff5"
                    shape="circle"
                    size="40"
                    labelSize="28rpx"
                    labelColor="#475669"
                    label="试听"
                    name="YES"
                  ></u-checkbox>
                </u-checkbox-group>
              </div>
              <div style="width: 130rpx" class="head-cell flex flex-v-center">
                <span>缺勤原因</span>
              </div>
              <div
                style="width: 200rpx; padding-left: 32rpx"
                class="head-cell flex flex-v-center"
              >
                <span>备注</span>
              </div>
            </div>
            <div class="table-body">
              <scroll-view
                enable-passive="true"
                scroll-y="true"
                :style="
                  'padding-bottom:' +
                  scrollPaddingBootm +
                  'px;height:' +
                  scrollHeight +
                  'px'
                "
              >
                <div
                  v-for="(item, index) in table_data"
                  :key="index"
                  class="table-row border-bottom-1px flex flex-between"
                >
                  <div
                    style="width: 132rpx"
                    class="table-cell flex flex-v-center"
                  >
                    <span
                      style="word-break: break-word"
                      class="text-primary"
                      @tap="openStuInfo(item)"
                      >{{ item.student_name }}</span
                    >
                  </div>
                  <div
                    style="width: 110rpx; margin-left: 10rpx"
                    class="table-cell flex flex-v-center"
                  >
                    <u-checkbox-group
                      v-model="item.is_attendance_checked"
                      @change="(e) => attendance_change(index, e)"
                    >
                      <u-checkbox
                        activeColor="#568ff5"
                        shape="circle"
                        size="40"
                        name="YES"
                        :checked="item.is_attendance === 'YES'"
                      ></u-checkbox>
                    </u-checkbox-group>
                  </div>
                  <div
                    style="width: 150rpx"
                    class="table-cell flex flex-v-center"
                  >
                    <div class="flex flex-v-center">
                      <u-checkbox-group
                        v-model="item.is_billable_checked"
                        @change="(e) => billable_change(index, e)"
                      >
                        <!-- is_in_arrears是否剩余课时不足 -->
                        <u-checkbox
                          v-if="!item.is_in_arrears"
                          activeColor="#568ff5"
                          shape="circle"
                          size="40"
                          :checked="item.is_billable === 'YES'"
                          :disabled="
                            (item.is_billable !== 'YES' &&
                              item.is_in_arrears) ||
                            top_info.mandatory_lesson
                          "
                        ></u-checkbox>
                        <div v-if="item.is_in_arrears" class="checkbox3"></div>
                      </u-checkbox-group>
                      <!-- <u-number-box
                      v-if="top_info.dynamic_lesson"
                      v-model="item.number_deduct"
                      min="0"
                      button-size="20"
                      color="#333"
                      bgColor="#fff"
                      iconStyle="color: #568ff5;font-size:10px"
                    ></u-number-box> -->
                      <u--input
                        v-if="top_info.dynamic_lesson && !item.is_in_arrears"
                        :disabled="dynamic_discount_disable(item)"
                        placeholder="请输入"
                        border="bottom"
                        maxlength="10"
                        type="digit"
                        fontSize="13px"
                        v-model="item.number_deduct"
                        :customStyle="{
                          width: '100rpx',
                          padding: '0 10rpx !important',
                          flex: 'none',
                          borderBottom: '1px solid #D3DCE6 !important'
                        }"
                        @blur="number_deduct_blur(item)"
                      ></u--input>
                    </div>
                  </div>
                  <div
                    style="width: 350rpx; padding-left: 16rpx"
                    class="table-cell flex flex-v-center cour"
                  >
                    {{ item.lesson_list[0].course_name }}剩{{
                      item.lesson_list[0].total_lesson
                    }}课时
                  </div>
                  <div
                    style="width: 120rpx; margin-left: 20rpx"
                    class="table-cell flex flex-v-center"
                  >
                    <u-checkbox-group
                      v-model="item.is_try_checked"
                      @change="(e) => try_change(index, e)"
                    >
                      <u-checkbox
                        activeColor="#568ff5"
                        shape="circle"
                        size="40"
                        :checked="item.is_try === 'YES'"
                      ></u-checkbox>
                    </u-checkbox-group>
                  </div>
                  <div
                    style="width: 130rpx"
                    class="table-cell flex flex-center"
                  >
                    <span
                      @tap="leaveReasonClick(index)"
                      class="flex flex-center border-around leave-reason"
                      >{{ leaveReasonFilter(item.leave_reason) }}
                      <u-icon size="12" name="arrow-down"></u-icon
                    ></span>
                  </div>
                  <div
                    style="width: 200rpx"
                    class="table-cell flex flex-center"
                  >
                    <u--input
                      placeholder="请输入"
                      border="bottom"
                      maxlength="200"
                      type="text"
                      fontSize="13px"
                      v-model="item.memo"
                      :customStyle="{
                        width: '180rpx',
                        padding: '0 20rpx !important',
                        flex: 'none',
                        borderBottom: '1px solid #D3DCE6 !important'
                      }"
                      @blur="memo_blur(index)"
                    ></u--input>
                  </div>
                </div>
              </scroll-view>
            </div>
          </div>
        </scroll-view>
      </div>

      <u-picker
        v-if="leave_reason_show"
        :show="true"
        :columns="[rang]"
        :immediateChange="true"
        keyName="name"
        @confirm="reasonConfirm"
        @cancel="leave_reason_show = false"
      ></u-picker>
      <u-popup
        bgColor="#fff"
        :overlay="false"
        :show="true"
        mode="bottom"
        zIndex="100"
      >
        <div class="border-top-1px content-bottom-box">
          <div class="table-row border-bottom-1px flex flex-between">
            <div class="table-cell flex flex-v-center">
              <span>共：{{ table_data.length }}人</span>
            </div>
            <div
              style="margin-left: 10rpx"
              class="table-cell flex flex-v-center"
            >
              <span>出勤：{{ attendance_people }}人</span>
            </div>
            <div class="table-cell flex flex-v-center">
              <span>计费：{{ billable_people }}人</span>
            </div>
            <div
              style="margin-left: 20rpx"
              class="table-cell flex flex-v-center"
            >
              <span>试听：{{ try_people }}人</span>
            </div>
            <div class="table-cell flex flex-center"></div>
          </div>
          <div
            @tap="toStopTrash"
            class="to-student flex flex-center flex-between"
          >
            <span>查看已移除/停课学员名单</span>
            <u-icon size="12" color="#475669" name="arrow-right"></u-icon>
          </div>
          <!-- <div class="gap-box"></div> -->
        </div>
        <div class="btn-box">
          <u-button
            v-if="top_info.scheduling_status === 'is_started'"
            @tap="really"
            size="large"
            shape="circle"
            color="linear-gradient(270deg, #3667f0 0%, #568ff5 100%)"
            type="primary"
            text="确定"
            :disabled="!can_opearte"
          ></u-button>
          <u-button
            v-if="top_info.scheduling_status === 'not_start'"
            @tap="openBeginClass"
            size="large"
            shape="circle"
            color="linear-gradient(270deg, #3667f0 0%, #568ff5 100%)"
            type="primary"
            text="开始上课"
            :disabled="!can_opearte"
          ></u-button>
        </div>
      </u-popup>
      <div class="student-info-popup">
        <u-popup
          :safeAreaInsetBottom="false"
          round="24"
          :show="studentInfoShow"
          mode="center"
        >
          <studentInfo
            @cancel="cancelStuInfo"
            @confirm="confirmStuInfo"
            :info="stu_info"
          ></studentInfo>
        </u-popup>
      </div>
    </div>
    <u-modal
      content="是否确认上课，确认后将无法对该节课进行添加或移除学员的操作，是否继续？"
      @confirm="confirmTips"
      @cancel="tips_visible = false"
      :showCancelButton="true"
      :show="tips_visible"
      :confirmText="confirmText"
      cancelText="取消"
      :confirmColor="timer < 0 ? '#568ff5' : '#999'"
      width="600rpx"
    ></u-modal>
    <div class="check-in-info">
      <u-popup
        round="8"
        :closeable="checkInClose"
        @close="cancelCheckInfo"
        :show="checkInStuInfo"
        mode="center"
        :safeAreaInsetBottom="false"
      >
        <checkInStuInfo
          @confirm="confirmCheckInfo"
          :list="table_data"
        ></checkInStuInfo>
      </u-popup>
    </div>
  </div>
</template>

<script>
import { leave_reason } from "@/utils/dict";
import { studentInfo } from "./studentInfo.vue";
import { checkInStuInfo } from "./checkInStuInfo.vue";
import {
  getSchedulingStudentList,
  sustainLock,
  unlockAccount,
  rollCall,
  revocation,
  beginRollCall
} from "@/services/schoolServiceScheduling";
export default {
  name: "rollCallClass",
  components: { studentInfo, checkInStuInfo },
  data() {
    return {
      pageLoading: true,
      scheduling_id: "",
      rang: leave_reason,
      leave_reason_show: false,
      tips_visible: false,
      checkInStuInfo: false,
      code_no: "",
      all_billable_checked: false,
      all_attendance_checked: false,
      all_try_checked: false,
      confirmText: "继续",
      checkInClose: false,
      timer: 3,
      top_info: {
        teacher_name: "",
        school_name: "",
        time: "",
        dynamic_discount: 0
      },
      stu_info: {},
      table_data: [],
      studentInfoShow: false,
      // 是否可以点名
      can_opearte: false,
      leave_reason_show_index: "",
      errNum: 0,
      scrollHeight: 300,
      scrollPaddingBootm: 160
      // 是否完成点名
      // is_roll_call: false,
    };
  },
  computed: {
    attendance_people() {
      return (
        this.table_data.filter((item) => item.is_attendance === "YES").length ??
        0
      );
    },
    billable_people() {
      return (
        this.table_data.filter((item) => item.is_billable === "YES").length ?? 0
      );
    },
    try_people() {
      return (
        this.table_data.filter((item) => item.is_try === "YES").length ?? 0
      );
    }
  },
  watch: {
    table_data: {
      handler(val) {
        const flag = val.every((item) => {
          return (
            item.is_attendance === "NO" &&
            item.is_billable === "NO" &&
            item.is_try === "NO" &&
            item.leave_reason === "empty" &&
            item.memo === ""
          );
        });
        this.can_opearte = !flag;
      },
      deep: true
    }
  },
  methods: {
    // 开始点名
    beginClass() {
      const {
        startTime,
        endTime,
        assistant_teacher_id,
        scheduling_form,
        school_room_id,
        teacher_id
      } = this.top_info;
      const start_time = new Date(startTime);
      const end_time = new Date(endTime);
      const differenceInMilliseconds =
        end_time.getTime() - start_time.getTime();
      const duration = differenceInMilliseconds / 1000;
      const time1 = this.$ljsPublic.date.formatTime(
        startTime,
        "{y}-{m}-{d} {h}:{i}:{s}"
      );
      const time2 = this.$ljsPublic.date.formatTime(
        endTime,
        "{y}-{m}-{d} {h}:{i}:{s}"
      );
      const data = {
        scheduling_id: this.scheduling_id,
        assistant_teacher_id,
        duration,
        start_time: time1,
        end_time: time2,
        scheduling_form,
        school_room_id,
        teacher_id,
        check_audit: true // 是否检查冲突需要传么
      };
      beginRollCall(data)
        .then(() => {
          this.tips_visible = false;
          this.really();
        })
        .catch((err) => {
          this.tips_visible = false;
          uni.hideToast();
          this.$refs.uToast.show({
            type: "error",
            message: err.err
          });
        });
    },
    leaveReasonFilter(val) {
      if (val === "") {
        val = "empty";
      }
      const obj = leave_reason.find((item) => item.id === val);
      return obj?.name ?? "";
    },
    all_checked() {
      const is_attendance = this.table_data.every(
        (item) => item.is_attendance === "YES"
      );
      this.all_attendance_checked = is_attendance ? ["YES"] : [];

      const is_billable = this.table_data.every(
        (item) => item.is_billable === "YES"
      );
      this.all_billable_checked = is_billable ? ["YES"] : [];

      const is_try = this.table_data.every((item) => item.is_try === "YES");
      this.all_try_checked = is_try ? ["YES"] : [];
    },
    reasonConfirm({ value }) {
      this.leave_reason_show = false;
      const { leave_reason_show_index } = this;
      this.table_data[leave_reason_show_index].leave_reason = value[0].id;
      console.log(value);
      if (value[0].id !== "empty") {
        this.table_data[leave_reason_show_index].is_attendance = "NO";
        this.table_data[leave_reason_show_index].is_try = "NO";
      }

      // this.table_data[leave_reason_show_index].is_attendance =
      //   value[0].id === "empty" ? "YES" : "NO";
      // this.table_data[leave_reason_show_index].is_try =
      //   value[0].id === "empty" ? "YES" : "NO";
      this.all_checked();
    },
    confirmTips() {
      if (this.timer >= 0) {
        return;
      }
      uni.$u.throttle(this.beginClass, 500);
    },
    openBeginClass() {
      if (!this.can_opearte) {
        return;
      }
      this.timer = 3;
      this.tips_visible = true;
      const st = setInterval(() => {
        this.confirmText = `继续(${this.timer}s)`;
        --this.timer;
        if (this.timer < 0) {
          this.confirmText = "继续";
          clearInterval(st);
        }
      }, 1000);
    },
    // 撤销上课
    async revocationClass() {
      await revocation({
        Scheduling_id: this.scheduling_id
      });
    },
    async really() {
      if (!this.can_opearte) {
        return;
      }
      // 循环点名
      this.checkInStuInfo = true;
      this.checkInClose = false;
      this.errNum = 0;
      try {
        for (let index = 0; index < this.table_data.length; index++) {
          const row = this.table_data[index];
          this.$set(row, "loading", "loading");
          const query = {
            scheduling_id: this.scheduling_id,
            records: {
              absent_reason: row.leave_reason,

              dynamic_discount: Number(row.number_deduct),
              note: row.memo,
              on_billing: row.is_billable,
              present: row.is_attendance,
              student_id: row.student_id,
              student_name: row.student_name,
              try: row.is_try,
              handler_type: row.handler_type
            }
          };

          const res = await rollCall(query);
          const { code, message } = res;
          if (code === 0) {
            this.$set(row, "loading", "success");
          } else {
            uni.hideToast();
            this.$set(row, "loading", "error");
            this.$set(row, "msg", message);
            this.errNum++;
          }
        }
        this.checkInClose = true;
      } catch (error) {
        this.checkInClose = true;
      }
    },
    leaveReasonClick(index) {
      this.leave_reason_show_index = index;
      this.leave_reason_show = true;
    },
    openStuInfo(row) {
      this.stu_info = row;
      this.studentInfoShow = true;
    },
    cancelStuInfo() {
      this.studentInfoShow = false;
    },
    confirmStuInfo(memo) {
      this.studentInfoShow = false;
      const { student_id } = this.stu_info;
      const obj = this.table_data.find(
        (item) => item.student_id === student_id
      );
      obj.memo = memo;
    },
    // 关闭点名信息
    cancelCheckInfo() {
      console.log(this.errNum, this.table_data.length);
      if (this.errNum > 0) {
        // 如果所有学员都未点名成功，那么点击关闭按钮时，自动回退已上课状态
        if (this.errNum === this.table_data.length) {
          this.revocationClass();
        } else {
          this.checkInStuInfo = false;
        }
        // 刷新列表数据
        this.getList();
      } else {
        this.checkInStuInfo = false;

        const len = this.table_data.filter(
          (item) => item.is_attendance === "YES"
        ).length;
        uni.$emit("updateList", {
          id: this.scheduling_id,
          roll_called_student_numb: len
        });

        uni.navigateBack();
      }
    },
    // 顶部课消数量变化
    dynamic_discount_change(val) {
      if (val) {
        this.top_info.dynamic_discount = Number(val).toFixed(2);
        this.table_data.map((item) => {
          item.number_deduct = this.top_info.dynamic_discount;
        });
      }
    },
    // 动态课消点名后不允许修改,请撤销上课后重新点名
    dynamic_discount_disable(item) {
      const { scheduling_status, dynamic_discount } = this.top_info;
      if (
        scheduling_status === "is_started" &&
        dynamic_discount &&
        item.is_billable === "YES"
      ) {
        return true;
      }
      return false;
    },
    // 列表课消数量变化
    number_deduct_blur(item) {
      const { number_deduct } = item;
      if (number_deduct) {
        item.number_deduct = Number(number_deduct).toFixed(2);
      } else {
        item.number_deduct = 0;
      }
    },
    // 计费全选change
    all_billable_change(e) {
      this.table_data.map((item) => {
        if (!item.is_in_arrears) {
          // 剩余课时不为0
          item.is_billable = e.length > 0 ? "YES" : "NO";
          this.$set(item, "is_billable_checked", e.length > 0 ? ["YES"] : []);
        }
      });
      this.all_checked();
    },
    billable_change(index, e) {
      this.table_data[index].is_billable = e.length ? "YES" : "NO";
      this.all_checked();
    },
    // 出勤全选change
    all_attendance_change(e) {
      this.table_data.map((item) => {
        item.is_attendance = e.length > 0 ? "YES" : "NO";
        this.$set(item, "is_attendance_checked", e.length > 0 ? ["YES"] : []);
        if (e.length > 0) {
          item.leave_reason = "empty";
        }
      });
      this.all_checked();
    },
    attendance_change(index, e) {
      this.table_data[index].is_attendance = e.length ? "YES" : "NO";
      if (e.length) {
        this.table_data[index].leave_reason = "empty";
      }

      this.all_checked();
    },
    // 试听全选change
    all_try_change(e) {
      this.table_data.map((item) => {
        item.is_try = e.length > 0 ? "YES" : "NO";
        this.$set(item, "is_try_checked", e.length > 0 ? ["YES"] : []);
        if (e.length > 0) {
          item.leave_reason = "empty";
          item.is_attendance = "YES";
          item.is_attendance_checked = ["YES"];
        }
      });
      this.all_checked();
    },
    try_change(index, e) {
      this.table_data[index].is_try = e.length ? "YES" : "NO";
      if (e.length) {
        this.table_data[index].leave_reason = "empty";
        this.table_data[index].is_attendance = "YES";
        this.table_data[index].is_attendance_checked = ["YES"];
      }
      this.all_checked();
    },
    getList() {
      this.pageLoading = true;
      getSchedulingStudentList({
        scheduling_id: this.scheduling_id
      })
        .then((res) => {
          this.pageLoading = false;
          this.checkInStuInfo = false;

          const {
            teacher_id,
            teacher_name,
            classroom_name,
            startTime,
            endTime,
            week_day_chn,
            available,
            dynamic_discount,
            dynamic_lesson,
            mandatory_lesson, // 强制划课消
            scheduling_status,
            assistant_teacher_id,
            scheduling_form,
            school_room_id
          } = res;
          //  2023年9月25日 周一 18:30-19:30
          const date = this.$ljsPublic.date.formatTime(
            startTime,
            "{y}年{m}月{d}日"
          );
          const time1 = this.$ljsPublic.date.formatTime(
            new Date(startTime).getTime(),
            "{h}:{i}"
          );
          const time2 = this.$ljsPublic.date.formatTime(
            new Date(endTime).getTime(),
            "{h}:{i}"
          );
          const time = `${date} ${week_day_chn} ${time1}-${time2}`;
          console.log(res, time1, time2);
          this.top_info = {
            teacher_name,
            classroom_name,
            time,
            dynamic_discount,
            dynamic_lesson,
            mandatory_lesson,
            scheduling_status,
            startTime,
            endTime,
            assistant_teacher_id,
            teacher_id,
            scheduling_form,
            school_room_id
          };
          available.map((item) => {
            if (item.is_attendance === "YES") {
              this.$set(item, "is_attendance_checked", ["YES"]);
            } else {
              this.$set(item, "is_attendance_checked", []);
              item.is_attendance = "NO";
            }
            if (item.is_billable === "YES") {
              this.$set(item, "is_billable_checked", ["YES"]);
            } else {
              this.$set(item, "is_billable_checked", []);
              item.is_billable = "NO";
            }
            if (item.is_try === "YES") {
              this.$set(item, "is_try_checked", ["YES"]);
            } else {
              this.$set(item, "is_try_checked", []);
              item.is_try = "NO";
            }
            if (!item.leave_reason) {
              item.leave_reason = "empty";
            }
            if (!item.memo) {
              item.memo = "";
            }
            if (item.number_deduct === 0 && dynamic_discount) {
              item.number_deduct = dynamic_discount;
            }
          });
          this.table_data = available;
        })
        .catch((err) => {
          console.log(err);
          this.pageLoading = false;
        });
    },
    // 续锁
    async setSustainLock() {
      const res = await sustainLock({
        scheduling_id: this.scheduling_id,
        code_no: this.code_no
      });
      const { code } = res;
      if (code !== 0) {
        uni.hideToast();
      }
    },
    setUnlockAccount() {
      console.log("释放锁");
      clearInterval(this.st);
      unlockAccount({
        scheduling_id: this.scheduling_id,
        code_no: this.code_no
      })
        .then(() => {
          uni.hideToast();
        })
        .catch(() => {
          uni.hideToast();
        });
      // const { code, message } = res;
      // if (code !== 0) {
      //   this.$refs.uToast.show({
      //     type: "error",
      //     message
      //   });
      // }
    },
    toStopTrash() {
      uni.navigateTo({
        url: `/pages/teacher/subpages/schoolServiceScheduling/stopTrashStudent?id=${this.scheduling_id}&code_no=${this.code_no}`
      });
    },
    onTableScroll(event) {
      // console.log("event :>> ", event);
    },
    memo_blur(index) {
      console.log("index :>> ", index);
    }
  },

  // 页面周期函数--监听页面加载
  onLoad(options) {
    const winInfo = uni.getWindowInfo();
    console.log("winInfo :>> ", winInfo);
    const { windowHeight, statusBarHeight, safeAreaInsets } = winInfo;
    this.scrollHeight = windowHeight - statusBarHeight - 44 - 44 - 100 - 80;
    this.scrollPaddingBootm = 157 + safeAreaInsets.bottom;

    const { id, code_no } = options;
    this.scheduling_id = id;
    this.code_no = code_no;
    this.getList();
    this.st = setInterval(() => {
      this.setSustainLock();
    }, 10 * 1000);
  },
  // 页面周期函数--监听页面初次渲染完成
  onReady() {},
  // 页面周期函数--监听页面显示(not-nvue)
  onShow() {},
  // 页面周期函数--监听页面隐藏
  onHide() {
    // 释放上课点名锁
    this.setUnlockAccount();
  },
  // 页面周期函数--监听页面卸载
  onUnload() {
    console.log("页面卸载 ");
    // 释放上课点名锁
    this.setUnlockAccount();
  }
  // 页面处理函数--监听用户下拉动作
  // onPullDownRefresh() { uni.stopPullDownRefresh(); },
  // 页面处理函数--监听用户上拉触底
  // onReachBottom() {},
  // 页面处理函数--监听页面滚动(not-nvue)
  // onPageScroll(event) {},
  // 页面处理函数--用户点击右上角分享
  // onShareAppMessage(options) {},
};
</script>

<style lang="scss" scoped>
.rollCallClass {
  height: 100vh;
  overflow: hidden;
  .top-conetnt {
    background: linear-gradient(180deg, #204fef 0%, #69a2f8 100%);
    padding: 10px;
    .box {
      padding: 20px 40rpx;
      height: 80px;
      background-color: #fff;
      border-radius: 8rpx;
      box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.05);
      .avatar {
        margin-right: 16rpx;
        image {
          width: 90rpx;
          height: 90rpx;
        }
      }
      .info {
        font-weight: 500;
        .desc {
          margin-bottom: 10rpx;
        }
        .course-name {
          border-radius: 20px;
          background: rgba(45, 128, 237, 0.1);
          margin-left: 8rpx;
          padding: 6rpx 12rpx;
        }
      }
    }
  }
  .content-desc {
    padding: 14px 64rpx;
    color: #475669;
    font-size: 28rpx;
    font-weight: 500;
    // line-height: 48px;
    height: 80px;
    .row2 {
      margin-top: 20rpx;
    }
  }
  .checkbox1 {
    width: 26rpx;
    height: 26rpx;
    margin-right: 8rpx;
    border-color: #568ff5;
    border-radius: 100%;
  }
  .checkbox2 {
    margin-right: 8rpx;
    width: 26rpx;
    height: 26rpx;
  }
  .checkbox3 {
    margin-right: 8rpx;
    width: 26rpx;
    height: 26rpx;
    background-color: #fd6865;
    border-radius: 100%;
  }
  ::v-deep .u-number-box__minus {
    border: 1px solid #568ff5;
    border-radius: 3px !important;
    background-color: #fff !important;
    width: 36rpx !important;
    height: 36rpx !important;
  }
  ::v-deep .u-number-box__plus {
    border: 1px solid #568ff5;
    border-radius: 3px !important;
    background-color: #fff !important;
    width: 36rpx !important;
    height: 36rpx !important;
  }
  ::v-deep .u-number-box__input {
    width: 20rpx !important;
    margin: 0 6px !important;
  }

  .custom-table {
    min-width: 1400rpx;
    ::v-deep .u-checkbox__icon-wrap {
      border-color: #568ff5 !important;
    }
    ::v-deep .u-checkbox__icon-wrap--circle {
      height: 40rpx !important;
      width: 40rpx !important;
    }
    .table-header {
      font-size: 28rpx;
      color: #475669;
      font-weight: 500;
      background-color: #eaf2fd;
      padding: 0 30rpx;
      height: 88rpx;

      .head-cell {
        padding: 0 6rpx;
      }
    }
    .table-body {
      .table-row {
        padding: 0 30rpx;
        height: 44px;
      }

      .table-cell {
        font-size: 28rpx;
        padding: 0 6rpx;
        color: #475669;
        font-weight: 500;
        .leave-reason {
          padding: 0 16rpx;
          display: flex;
          justify-content: space-between;
          width: 128rpx;
          height: 44rpx;
          font-size: 22rpx;
          border-radius: 8rpx;
        }
      }
    }
    .checkbox3 {
      width: 40rpx;
      height: 40rpx;
    }
  }
  .btn-box {
    box-shadow: 0 10px 10px 10px #d4cece;
    padding: 10px 80rpx;
    margin-top: 10px;
    background: #fff;
  }
  .content-bottom-box {
    // position: fixed;
    // bottom: 140rpx;
    // left: 0;
    // width: 100%;
    background: #fff;
    .table-row {
      padding: 0 30rpx;
      height: 44px;
      background: #fff;
    }
    .table-cell {
      font-size: 28rpx;
      padding: 0 6rpx;
      color: #8492a6;
      font-weight: 500;
    }
    .to-student {
      background-color: #eaf2fd;
      height: 32px;
      padding: 0 30rpx;
      span {
        color: #475669;
        font-size: 28rpx;
        font-weight: 500;
      }
    }
    .gap-box {
      height: 32rpx;
      background-color: #fbfbfb;
    }
  }
  ::v-deep .u-safe-bottom {
    background-color: #fff;
  }
  .student-info-popup {
    ::v-deep .u-popup {
      background: linear-gradient(
        180deg,
        rgba(45, 128, 237, 0.2) 0%,
        rgba(217, 217, 217, 0) 100%
      );
    }
  }
  ::v-deep .u-loading-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 5;
  }
  .text-primary {
    color: #568ff5;
  }
}
.cour {
  // 超出两行打点
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
</style>
