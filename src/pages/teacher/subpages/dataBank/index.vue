<template>
  <div class="data-bank">
    <u-navbar
      :placeholder="true"
      bgColor="#3061f2"
      title="资料库"
      titleStyle="color: #fff;font-size:36rpx;font-weight:500"
      leftIconSize="20px"
      leftIconColor="#fff"
      :autoBack="false"
      @leftClick="back"
    ></u-navbar>
    <u-sticky
      index="attendance-list"
      :customNavHeight="topHeight"
      bgColor="#fff"
    >
      <!-- <u-tabs
        :current="current"
        lineWidth="60"
        :activeStyle="{
          color: '#FFBF0D',
          fontWeight: 'bold',
          transform: 'scale(1.05)'
        }"
        :inactiveStyle="{
          color: '#606266',
          transform: 'scale(1)'
        }"
        itemStyle="width:33.3%;padding-left: 30rpx; background-color: #fff; padding-right: 30rpx; height: 80rpx;"
        :list="tab_list"
        @click="tabclick"
      ></u-tabs> -->
    </u-sticky>
    <div class="data-list-wrap">
      <div class="data-list">
        <u-list v-if="dataList.length" @scrolltolower="scrolltolower">
          <u-list-item v-for="(item, index) in dataList" :key="index">
            <div class="data-item" @click="handleClickItem(item, index)">
              <div class="data-name">{{ item.name }}</div>
              <div class="content">
                <u-album v-if="current === 0" :urls="[item.content]"> </u-album>
                <!-- <video
                  v-else-if="current === 1"
                  id="myVideo"
                  style="width: 216rpx; height: 100%; border-radius: 16rpx;"
                  :src="item.content"
                  @error="videoErrorCallback"
                  :controls="false"
                >
                </video> -->
                <div v-else class="text">
                  {{ item.content }}
                  {{ index }}
                </div>
              </div>
              <div class="preview">预览</div>
              <div
                :class="['checkbox', curIndex === index ? 'is-checked' : '']"
              >
                <image
                  v-if="curIndex === index"
                  style="width: 16rpx; height: 16rpx"
                  src="https://tg-prod.oss-cn-beijing.aliyuncs.com/748e7d15-0582-44a1-894d-9c4509c6a926.png"
                  mode="scaleToFill"
                />
              </div>
            </div>
          </u-list-item>
          <u-loadmore :status="status" />
        </u-list>
        <u-empty text="暂无资料~" v-else> </u-empty>
      </div>
    </div>
    <div class="footer">
      <u-button type="primary" @click="save" text="确定"></u-button>
    </div>
  </div>
</template>

<script>
import { getList } from "@/services/dataBank";
export default {
  name: "dataBank",
  components: {},
  data() {
    return {
      topHeight: 0,
      current: 0,
      types: {
        1: "图片",
        2: "视频",
        3: "文案"
      },
      searchInputSty: {
        border: "1px solid #FFBF0D"
      },
      /*
        {
          name: "视频",
          key: "2",
        },
      */
      tab_list: [
        {
          name: "图片",
          key: "1"
        },
        {
          name: "文案",
          key: "3"
        }
      ],
      status: "nomore",
      dataList: [],
      count: 0,
      parmas: {
        category_id: 1,
        page: 1,
        page_size: 10,
        name: ""
      },
      options: {},
      curItem: {},
      curIndex: ""
    };
  },
  computed: {},
  methods: {
    tabclick(item) {
      this.parmas.category_id = item.key;
      this.current = item.index;
      this.curIndex = "";
      this.dataList = [];
      this.getDataBankList();
    },
    async getDataBankList() {
      this.status = "loading";
      const { code, data } = await getList(this.parmas);
      if (code === 0) {
        for (let i = 0; i < data.results.length; i++) {
          this.dataList.push(data.results[i]);
        }
        console.log(this.dataList);
        this.count = data.count;
      }
      this.status = "nomore";
    },
    handleClickItem(item, index) {
      if (this.curItem.file_name === item.file_name) {
        this.curItem = {};
        this.curIndex = "";
      } else {
        this.curIndex = index;
        this.curItem = item;
      }
    },
    back() {
      uni.navigateBack();
    },
    scrolltolower() {
      if (this.dataList.length >= this.count) {
        this.status = "nomore";
        return;
      }
      this.listParams.page++;
      this.getList();
    },
    save() {
      const options = {
        ...this.options,
        dataType: this.curItem.category_id,
        source: "dataBank"
      };
      const params = uni.$u.queryParams(options);
      const rich = uni.getStorageSync("richtextData");
      let appendRich = "";
      const img = (content) => `<img src="${content}" alt="" />`;
      const text = (content) => `<p>${content}</p>`;
      if (this.curItem.category_id === "1") {
        appendRich = rich + img(this.curItem.content);
      } else if (this.curItem.category_id === "2") {
        /* empty */
      } else {
        appendRich = rich + text(this.curItem.content);
      }
      uni.setStorageSync("richtextData", appendRich);
      console.log(params);
      uni.redirectTo({
        url: `/pages/teacher/subpages/richtext/index${params}`
      });
    }
  },
  watch: {},

  // 页面周期函数--监听页面加载
  onLoad(options) {
    this.options = options;
    console.log(options);
    setTimeout(() => {
      uni.$on("richtextData", function (data) {
        console.log(data);
      });
    }, 1000);
  },
  // 页面周期函数--监听页面初次渲染完成
  onReady() {},
  // 页面周期函数--监听页面显示(not-nvue)
  onShow() {
    this.getDataBankList();
  },
  // 页面周期函数--监听页面隐藏
  onHide() {},
  // 页面周期函数--监听页面卸载
  onUnload() {}
  // 页面处理函数--监听用户下拉动作
  // onPullDownRefresh() { uni.stopPullDownRefresh(); },
  // 页面处理函数--监听用户上拉触底
  // onReachBottom() {},
  // 页面处理函数--监听页面滚动(not-nvue)
  // onPageScroll(event) {},
  // 页面处理函数--用户点击右上角分享
  // onShareAppMessage(options) {},
};
</script>

<style lang="scss" scoped>
.data-bank {
  background: #f5f6fa;
  display: flex;
  flex-direction: column;
  .data-list-wrap {
    margin: 0 25rpx;
    // height: 100%;
    overflow: auto;
    flex: 1;
    .data-list {
      .data-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        background: #fff;
        border-radius: 20rpx;
        box-shadow: 0 2px 12rpx 0 rgba(0, 0, 0, 0.1);
        padding: 19rpx 42rpx;
        margin-bottom: 28rpx;
        .data-name {
          color: #475669;
          font-family: "PingFang SC";
          font-size: 32rpx;
          font-style: normal;
          font-weight: 500;
          width: 170rpx;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        .content {
          margin: 0 48rpx;
          height: 180rpx;
          width: 216rpx;
          flex-shrink: 0;
          .text {
            display: flex;
            align-items: center;
            width: 100%;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 3;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
        .preview {
          color: #ffbf0d;
          font-family: "PingFang SC";
          font-size: 24rpx;
          font-style: normal;
          margin-right: 56rpx;
          font-weight: 500;
          flex-shrink: 0;
        }
        .checkbox {
          width: 32rpx;
          height: 32rpx;
          border-radius: 4px;
          border: 2px solid #bdbdbd;
          flex-shrink: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          &.is-checked {
            border-color: #ffbf0d;
          }
        }
      }
    }
  }
  .footer {
    height: 196rpx;
    width: 100%;
    background: #fff;
  }
}
</style>

<style lang="scss">
::v-deep .u-album {
  height: 100%;
  width: 216rpx;
  .u-album__row {
    height: 100%;
    width: 100%;
    .u-album__row__wrapper {
      height: 100%;
      width: 100%;
      image {
        width: 100% !important;
        height: 100% !important;
        border-radius: 16rpx;
      }
    }
  }
}
</style>
