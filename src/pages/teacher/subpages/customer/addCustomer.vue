<template>
  <div class="add-custer">
    <u-toast ref="uToast"></u-toast>
    <div class="tg-top-bg"></div>
    <div v-if="pageType === 'add'">
      <u-navbar
        :placeholder="true"
        bgColor="transparent"
        title="新增意向客户"
        titleStyle="color: #fff;font-size:36rpx;font-weight:500"
        leftIconSize="20px"
        leftIconColor="#fff"
        :autoBack="true"
      >
      </u-navbar>
    </div>
    <div class="tg_card">
      <div class="tg_card_title">基本信息</div>
      <div class="tg_card_content">
        <u--form
          labelPosition="left"
          :model="formData"
          :rules="rules"
          labelWidth="120"
          ref="uForm"
          errorType="toast"
          :labelStyle="{ color: '#333', fontWeight: 500, fontSize: '14px' }"
        >
          <u-form-item required label="姓名" prop="student_name" borderBottom>
            <u--input
              placeholder="请输入姓名"
              v-model="formData.student_name"
              border="none"
              :disabled="isDisabled"
              maxlength="20"
            ></u--input>
          </u-form-item>
          <u-form-item label="性别" required prop="student_gender" borderBottom>
            <!-- <u--input
              v-model="formData.sex"
              disabledColor="#ffffff"
              placeholder="请选择性别"
              border="none"
            ></u--input> -->
            <u-radio-group
              v-model="formData.student_gender"
              iconPlacement="left"
            >
              <u-radio
                activeColor="#FFBF0D"
                :disabled="isDisabled"
                name="male"
                label="男"
              ></u-radio>
              <u-radio
                activeColor="#FFBF0D"
                :disabled="isDisabled"
                name="female"
                label="女"
              ></u-radio>
            </u-radio-group>
          </u-form-item>
          <u-form-item
            required
            @tap="age_focus"
            label="出生日期"
            prop="birth_day"
            borderBottom
          >
            <u--input
              placeholder="请选择出生日期"
              v-model="formData.birth_day"
              :disabled="isDisabled"
              border="none"
              readonly
            ></u--input>
            <u-datetime-picker
              :show="birth_day_show"
              v-model="birth_day"
              mode="date"
              @cancel="birth_day_show = false"
              @confirm="birth_day_confirm"
            ></u-datetime-picker>
            <!-- <u-picker
              v-if="age_show"
              :show="true"
              :columns="age_list"
              :immediateChange="true"
              @confirm="ageConfirm"
              @cancel="age_show = false"
            ></u-picker> -->
          </u-form-item>
          <u-form-item label="是否会围棋" required prop="know_go" borderBottom>
            <u-radio-group v-model="formData.know_go" iconPlacement="left">
              <u-radio
                :disabled="isDisabled"
                activeColor="#FFBF0D"
                name="yes"
                label="是"
              ></u-radio>
              <u-radio
                :disabled="isDisabled"
                activeColor="#FFBF0D"
                name="no"
                label="否"
              ></u-radio>
            </u-radio-group>
          </u-form-item>
          <u-form-item
            required
            label="联系电话"
            prop="student_mobile"
            borderBottom
          >
            <u--input
              placeholder="请输入联系电话"
              v-model="formData.student_mobile"
              border="none"
              :disabled="isDisabled"
              type="number"
              maxlength="11"
            ></u--input>
          </u-form-item>
          <u-form-item
            @tap="channel_focus"
            required
            label="渠道"
            prop="channel_id"
            borderBottom
          >
            <u--input
              placeholder="请选择渠道"
              v-model="formData.channel_name"
              border="none"
              :disabled="!isAddPage"
              readonly
            ></u--input>
            <u-picker
              v-if="channel_show"
              :show="true"
              :columns="channel"
              :immediateChange="true"
              keyName="name"
              @confirm="channelConfirm"
              @cancel="channel_show = false"
              @change="channelChange"
            ></u-picker>
          </u-form-item>
          <u-form-item
            required
            label="二级渠道"
            prop="sub_channel_id"
            borderBottom
            @tap="sub_channel_focus"
          >
            <u--input
              placeholder="请选择二级渠道"
              v-model="formData.sub_channel_name"
              :disabled="!isAddPage"
              border="none"
              readonly
            ></u--input>
            <u-picker
              v-if="sub_channel_show"
              :show="true"
              :columns="sub_channel"
              :immediateChange="true"
              keyName="name"
              @confirm="subChannelConfirm"
              @cancel="sub_channel_show = false"
            ></u-picker>
          </u-form-item>
          <u-form-item
            required
            label="所属校区"
            prop="department_id"
            borderBottom
            @tap="department_focus"
          >
            <u--input
              placeholder="请选择校区"
              v-model="formData.department_name"
              :disabled="isDisabled"
              border="none"
              readonly
            ></u--input>
          </u-form-item>
          <u-form-item required label="意向级别" prop="startNum" borderBottom>
            <div style="display: flex">
              <u-rate
                active-color="#FFBF0D"
                inactive-color="#b2b2b2"
                gutter="10"
                v-model="formData.startNum"
              ></u-rate>
              <span style="margin-left: 10px">{{
                intentionLevelText[Number(formData.startNum)]
              }}</span>
            </div>
          </u-form-item>
          <!-- // 可选择课程顾问 -->
          <u-form-item
            label="课程顾问"
            prop="advisor_id"
            required
            borderBottom
            @tap="sales_focus"
          >
            <u--input
              placeholder="请选择课程顾问"
              v-model="formData.advisor_name"
              :disabled="isDisabled"
              border="none"
              readonly
            ></u--input>
            <u-picker
              v-if="sales_show"
              :show="true"
              :columns="[advisorList]"
              :immediateChange="true"
              keyName="name"
              @confirm="salesConfirm"
              @cancel="sales_show = false"
              :defaultIndex="[
                advisorList.findIndex((item) => item.id === formData.advisor_id)
              ]"
            ></u-picker>
          </u-form-item>
          <!-- <u-form-item label="学校" prop="school" borderBottom>
            <u--input
              placeholder="请输入学校"
              v-model="formData.additional_information.school"
              :disabled="isDisabled"
              border="none"
              maxlength="100"
            ></u--input>
          </u-form-item>
          <u-form-item label="小区" prop="community" borderBottom>
            <u--input
              placeholder="请输入小区"
              v-model="formData.additional_information.community"
              :disabled="isDisabled"
              border="none"
              maxlength="100"
            ></u--input>
          </u-form-item> -->
          <u-form-item label="备注" prop="memo">
            <!-- <u--textarea
              v-model="formData.remark"
              placeholder="请输入备注(200字符以内)"
              maxlength="200"
              height="150"
            ></u--textarea> -->
          </u-form-item>
          <div class="textarea-box">
            <u--textarea
              v-model="formData.memo"
              :disabled="isDisabled"
              placeholder="请输入备注(200字符以内)"
              maxlength="200"
              height="100"
            ></u--textarea>
          </div>
          <!-- <u-form-item required label="到店状态" prop="status" borderBottom>
            <u--input
              placeholder="请选择到店状态"
              v-model="formData.status_txt"
              border="none"
              @focus="status_focus"
            ></u--input>
            <u-picker
              v-if="status_show"
              :show="true"
              :columns="status_list"
              keyName="name"
              @confirm="ageConfirm"
              @cancel="status_show = false"
            ></u-picker>
          </u-form-item> -->

          <!-- <u-form-item
            required
            label="是否有效"
            prop="valid_status"
            borderBottom
          >
            <u-radio-group v-model="formData.valid_status" iconPlacement="left">
              <u-radio
                activeColor="#FFBF0D"
                name="pending"
                label="待定"
              ></u-radio>
              <u-radio
                activeColor="#FFBF0D"
                name="valid"
                label="有效"
              ></u-radio>
            </u-radio-group>
          </u-form-item> -->
        </u--form>
      </div>
    </div>
    <div class="btn-box" v-if="!isDisabled">
      <u-button
        @tap="submit"
        size="large"
        shape="circle"
        color="linear-gradient(270deg, #3667f0 0%, #568ff5 100%)"
        type="primary"
        text="保存"
      ></u-button>
    </div>
    <u-modal
      :show="student_repeat_visible"
      @confirm="studentRepeatConfirm"
      ref="uModal"
      :asyncClose="true"
    >
      <div class="slot-content">
        <div class="flex flex-center">
          <u-icon
            v-if="errCode === 2"
            name="info-circle"
            color="#FF9800"
            size="14"
          ></u-icon>
          <u-icon
            v-if="errCode === 3"
            name="close-circle"
            color="#F44336"
            size="14"
          ></u-icon>
          <div v-if="errCode === 2" class="student-repeat-txt">
            该意向客户手机号已存在于以下校区
          </div>
          <div v-if="errCode === 3" class="student-repeat-txt">
            该意向客户已存在于以下校区
          </div>
        </div>
        <div class="data-list">
          <div
            v-for="(item, index) in sameMobileStuData"
            :key="index"
            class="flex flex-center student"
          >
            <div class="span">{{ item.student_name }}</div>
            <div class="span">{{ item.student_mobile }}</div>
            <div class="span">{{ item.department_name }}</div>
          </div>
        </div>
      </div>
    </u-modal>
    <gq-tree
      ref="gqTree"
      :range="range"
      idKey="id"
      nameKey="name"
      childKey="child"
      pidKey="pid"
      allKey="pid"
      :showSearch="true"
      :showClose="true"
      :multiple="false"
      :cascade="true"
      :selectParent="false"
      :maskClick="false"
      :lessOne="true"
      confirmColor="#007aff"
      cancelColor="#757575"
      title="校区关键字"
      titleColor="#757575"
      @cancel="treeCancel"
      @confirm="treeConfirm"
    >
    </gq-tree>
  </div>
</template>

<script>
import gqTree from "@/uni_modules/gq-tree/gq-tree.vue";
import {
  getStatusList,
  getChannelList,
  getSubChannelList,
  customerCreate,
  customerUpadte,
  getCustomerInfo,
  getIntentionLevelList,
  getAdvisorList
} from "@/services/intention";
export default {
  name: "addCuster",
  components: {
    gqTree
  },
  props: {
    pageType: {
      type: String,
      default: "add"
    },
    customerId: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      intentionLevelText: {
        1: "10%",
        2: "30%",
        3: "50%",
        4: "70%",
        5: "90%以上"
      },
      formData: {
        student_name: "",
        student_mobile: "",
        student_gender: "",
        channel_id: "",
        channel_name: "",
        status: "4",
        status_txt: "未到店",
        sub_channel_id: "",
        sub_channel_name: "",
        department_id: "",
        department_name: "",
        startNum: null,
        intention_level_id: "54f08c73-5fb7-4d52-8ac4-a6f248939c18",
        intention_level_name: 1,
        valid_status: "",
        student_age: 0,
        memo: "",
        birth_day: "",
        additional_information: {
          school: "",
          community: "" // 小区
        },
        advisor_id: "",
        advisor_name: "",
        age_txt: ""
      },
      birth_day: "",
      starList: [],
      birth_day_show: false,
      status_show: false,
      age_show: false,
      age_list: [[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]],
      status_list: [],
      department_ids: [],
      channel: [],
      sub_channel: [],
      // department_list: [],
      range: [],

      isSingSchool: false,
      channel_show: false,
      sub_channel_show: false,
      sales_show: false,
      advisorList: [],

      // department_show: false,
      errCode: null,

      sameMobileStuData: [],
      student_repeat_visible: false,

      rules: {
        student_name: {
          type: "string",
          required: true,
          message: "请填写姓名",
          trigger: ["change", "blur"]
        },

        student_mobile: [
          {
            required: true,
            message: "请输入手机号",
            trigger: ["change", "blur"]
          },
          {
            validator: (rule, value, callback) => {
              // 上面有说，返回true表示校验通过，返回false表示不通过
              // uni.$u.test.mobile()就是返回true或者false的
              // 手机号宽松校验
              const reg = /^(?:(?:\+|00)86)?1\d{10}$/;
              return reg.test(value);
            },
            message: "手机号码不正确",
            // 触发器可以同时用blur和change
            trigger: ["change", "blur"]
          }
        ],
        student_gender: {
          type: "string",
          required: true,
          message: "请选择性别"
        },
        know_go: {
          type: "string",
          required: true,
          message: "请选择是否会围棋"
        },
        birth_day: {
          type: "string",
          required: true,
          message: "请选择出生日期"
        },
        startNum: {
          type: "number",
          required: true,
          message: "请选择意向级别"
        },
        channel_id: {
          type: "string",
          required: true,
          message: "请选择渠道"
        },
        sub_channel_id: {
          type: "string",
          required: true,
          message: "请选择二级渠道"
        },
        // status: {
        //   type: "string",
        //   required: true,
        //   message: "请选择到店状态"
        // },
        department_id: {
          type: "string",
          required: true,
          message: "请选择所属校区"
        },
        advisor_id: {
          type: "string",
          required: true,
          message: "请选择课程顾问"
        }
        // intention_level_name: {
        //   type: "number",
        //   required: true,
        //   message: "请选择意向级别"
        // },
        // valid_status: {
        //   type: "string",
        //   required: true,
        //   message: "请选是否有效"
        // }
      }
    };
  },
  computed: {
    isAddPage() {
      return this.pageType === "add";
    },
    isDisabled() {
      if (this.pageType === "add") {
        return !this.$hasPermission(["customer_create"]);
      } else {
        return !this.$hasPermission(["customer_update"]);
      }
      // if (this.$hasPermission(["customer_create"])) {
      //   return false;
      // } else {
      //   return !this.$hasPermission(["customer_update"]);
      // }
    }
  },
  watch: {},
  created() {},
  methods: {
    intentionLevelList() {
      getIntentionLevelList({ UNAUTHORIZED: true }).then(({ code, data }) => {
        if (code === 0) {
          this.starList = data.map((i) => ({ ...i, name: Number(i.name) }));
        }
      });
    },
    treeCancel(e) {
      this.$refs.gqTree._hide();
    },
    treeConfirm(e) {
      if (!e.length) {
        this.$refs.uToast.show({
          type: "info",
          message: "请至少选择一个校区！"
        });
        return;
      }
      this.formData.department_id = e[0].id;
      this.formData.department_name = e[0].name;
    },
    schoolShowHandle() {
      const schoolData = uni.getStorageSync("schoolData");
      // const checkedSchool = uni.getStorageSync("checkedSchool");
      if (schoolData) {
        const idKeysResult = this.formData.department_id;
        const options = JSON.parse(JSON.stringify(schoolData));
        options.forEach((parent) => {
          if (parent.child) {
            // eslint-disable-next-line array-callback-return
            parent.child.map((item) => {
              item.pid = parent.id;
            });
          }
        });
        const newOptions = this.deepCheckValue(
          options,
          idKeysResult,
          "id",
          "child"
        );
        this.range = [...newOptions];
        this.$refs.gqTree._initTree();
        this.$refs.gqTree._show();
      } else {
        this.$refs.uToast.show({
          type: "error",
          message: "获取缓存校区失败，请退出重新登录！"
        });
      }
    },

    deepCheckValue(options, values, idKey, childKey) {
      return options.map((i) => {
        if (values.indexOf(i[idKey]) > -1) {
          i.isGqAddChecked = true;
        } else {
          i.isGqAddChecked = false;
        }
        if (i[childKey] && i[childKey].length > 0) {
          this.deepCheckValue(i[childKey], values, idKey, childKey);
        }
        return i;
      });
    },
    getChannel() {
      this.sub_channel = [];
      getChannelList({
        department_id: this.department_ids,
        is_enabled: true,
        channel_filter: "YES"
      }).then((res) => {
        if (res.results) {
          const arr = res.results.map((item) => {
            return {
              id: item.id,
              name: item.name
            };
          });
          this.channel = [arr];
        }
      });
    },
    getCustomerDetail() {
      getCustomerInfo({
        customer_id: this.customerId
      }).then((res) => {
        res.startNum = Number(res.intention_level_name);
        this.formData = res;
        // this.formData.birth_day = this.$ljsPublic.date.formatTime(
        //   res.birth_day,
        //   "{y}-{m}-{d}"
        // );

        // 默认值，前端不显示
        // this.formData.status = res.status || "4"; // 到店状态-未到店
        // this.formData.intention_level_id =
        //   res.intention_level_id || "54f08c73-5fb7-4d52-8ac4-a6f248939c18"; // 意向级别-1
        // this.formData.intention_level_name = +res.intention_level_name || 1;
        this.formData.age_txt = res.student_age + "岁";
        this.formData.birth_day = uni.$u.timeFormat(
          res.birth_day,
          "yyyy-mm-dd"
        );
        this.getAdvisorList();
        // this.formData.birth_day =
        //   new Date().getFullYear() - res.student_age + "-01-01";
      });
    },
    channelChange() {
      // this.formData.sub_channel_id = "";
      // this.formData.sub_channel_name = "";
    },
    getSubChannel(id) {
      this.sub_channel = [];
      getSubChannelList({
        parentid: id
      }).then((res) => {
        console.log(res);
        if (res.results) {
          const arr = res.results.map((item) => {
            return {
              id: item.id,
              name: item.name
            };
          });
          this.sub_channel = [arr];
        }
      });
    },
    birth_day_confirm({ value }) {
      this.birth_day_show = false;
      this.formData.birth_day = uni.$u.timeFormat(value, "yyyy-mm-dd");
      console.log(this.formData);
    },
    hideKeyboard() {
      uni.hideKeyboard();
    },
    age_focus() {
      if (this.isDisabled) {
        return;
      }
      this.birth_day_show = true;
    },
    // status_focus() {
    //   this.status_show = true;
    // },
    channel_focus() {
      const { channel_id } = this.formData;
      console.log("channel_id :>> ", channel_id);
      console.log("this.pageType :>> ", this.pageType);
      if (channel_id && this.pageType === "edit") {
        return;
      }
      this.channel_show = true;
    },
    sub_channel_focus() {
      const { sub_channel_id } = this.formData;
      if (sub_channel_id && this.pageType === "edit") {
        return;
      }
      if (!this.formData.channel_id) {
        this.$refs.uToast.show({
          type: "info",
          message: "请先选择一级渠道！"
        });
        return;
      }
      this.sub_channel_show = true;
      if (this.formData.channel_id && !this.sub_channel.length) {
        this.getSubChannel(this.formData.channel_id);
      }
    },
    // 新增/编辑意向客户不可选择校区
    department_focus() {
      return false;
      // if (this.isDisabled) {
      //   return;
      // }
      // console.log(this.isSingSchool);
      // if (!this.isSingSchool) {
      //   this.schoolShowHandle();
      // }
    },
    get_status_list() {
      getStatusList().then((res) => {
        if (res) {
          const arr = [];
          for (const key in res) {
            arr.push({
              id: res[key],
              name: key
            });
          }
          this.status_list = [arr];
        }
      });
    },
    ageConfirm(e) {
      this.formData.student_age = e.value[0];
      this.formData.age_txt = e.value[0] + "岁";
      const year = new Date().getFullYear();
      this.formData.birth_day = year - e.value[0] + "-01-01";
      this.age_show = false;
    },
    channelConfirm(e) {
      const { channel_id } = this.formData;
      this.formData.channel_id = e.value[0].id;
      this.formData.channel_name = e.value[0].name;
      this.channel_show = false;
      this.getSubChannel(e.value[0].id);

      if (channel_id !== e.value[0].id) {
        this.formData.sub_channel_id = "";
        this.formData.sub_channel_name = "";
      }
    },
    subChannelConfirm(e) {
      this.formData.sub_channel_id = e.value[0].id;
      this.formData.sub_channel_name = e.value[0].name;
      this.sub_channel_show = false;
    },
    studentRepeatConfirm() {
      this.student_repeat_visible = false;
      if (this.errCode === 2) {
        uni.navigateTo({
          url: `/pages/teacher/subpages/customer/customerList`
        });
      }
    },
    add() {
      this.formData.init_status = "intention"; // 创建意向客户
      console.log(this.starList, this.formData.startNum);
      const curLevelStart = this.starList.find(
        (i) => i.name === this.formData.startNum
      );
      this.formData.intention_level_id = curLevelStart.id;
      this.formData.intention_level_name = curLevelStart.name;
      customerCreate(this.formData)
        .then((res) => {
          this.$refs.uToast.show({
            type: "success",
            message: "添加意向客户成功！"
          });
          uni.navigateTo({
            url: `/pages/teacher/subpages/customer/customerList`
          });
        })
        .catch((err) => {
          const { code, data, message } = err;
          this.errCode = code;
          if (code === 1) {
            this.$refs.uToast.show({
              type: "error",
              message
            });
          } else if (code === 2) {
            this.sameMobileStuData = data ?? [];
            this.$refs.uToast.show({
              type: "success",
              message: "添加意向客户成功！"
            });
            this.student_repeat_visible = true;
          } else if (code === 3) {
            this.sameMobileStuData = data ?? [];
            this.$refs.uToast.show({
              type: "error",
              message: "添加意向客户失败！"
            });
            this.student_repeat_visible = true;
          }
        });
    },
    update() {
      this.formData.customer_id = this.customerId;
      const curLevelStart = this.starList.find(
        (i) => i.name === this.formData.startNum
      );
      this.formData.intention_level_id = curLevelStart.id;
      this.formData.intention_level_name = curLevelStart.name;
      customerUpadte(this.formData)
        .then((res) => {
          console.log(this.$refs.uToast);
          this.$refs.uToast.show({
            type: "success",
            message: "修改意向客户成功！"
          });
          const {
            department_name,
            student_mobile,
            student_name,
            student_gender,
            id
          } = this.formData;
          uni.$emit("updateList", {
            id,
            department_name,
            student_mobile,
            student_name,
            student_gender
          });
          setTimeout(() => {
            uni.navigateBack();
          }, 1500);
        })
        .catch((err) => {
          const { code, data, message } = err;
          this.errCode = code;
          if (code === 1) {
            this.$refs.uToast.show({
              type: "error",
              message
            });
          } else if (code === 2) {
            this.sameMobileStuData = data ?? [];
            this.$refs.uToast.show({
              type: "success",
              message: "修改意向客户成功！"
            });
            this.student_repeat_visible = true;
          } else if (code === 3) {
            this.sameMobileStuData = data ?? [];
            this.$refs.uToast.show({
              type: "error",
              message: "修改意向客户失败！"
            });
            this.student_repeat_visible = true;
          }
        });
    },
    // beforeSubmit() {
    //   console.log("1111 :>> ", 1111);
    //   uni.$u.throttle(this.submit, 500);
    // },
    submit() {
      this.$refs.uForm
        .validate()
        .then((res) => {
          if (this.pageType === "add") {
            this.add();
          } else {
            this.update();
          }
        })
        .catch((errors) => {
          // uni.$u.toast("校验失败");
        });
    },
    sales_focus() {
      if (this.isDisabled) {
        return;
      }
      this.sales_show = true;
    },
    salesConfirm(e) {
      this.formData.advisor_id = e.value[0].id;
      this.formData.advisor_name =
        e.value[0].name + "（" + e.value[0].main_post_name + "）";
      this.sales_show = false;
    },
    getAdvisorList() {
      const user = uni.getStorageSync("user");
      const { employee_id } = user;
      getAdvisorList({
        department_id: this.formData.department_id
      }).then((res) => {
        this.advisorList = res.data;
        const advisor = this.advisorList.find(
          (item) =>
            item.main_post_name === "课程顾问" && item.id === employee_id
        );
        if (advisor) {
          this.formData.advisor_id = advisor.id;
          this.formData.advisor_name =
            advisor.name + "（" + advisor.main_post_name + "）";
        }
      });
    }
  },

  // 页面周期函数--监听页面加载
  onLoad() {},
  // 页面周期函数--监听页面初次渲染完成
  onReady() {
    this.intentionLevelList();
    this.$refs.uForm.setRules(this.rules);
    const singSchoolData = uni.getStorageSync("singSchoolData");
    // 如果是单校区
    console.log(singSchoolData, "singSchoolData");
    if (singSchoolData) {
      this.isSingSchool = true;
      this.formData.department_id = singSchoolData.id;
      this.formData.department_name = singSchoolData.name;
      this.department_ids = [singSchoolData.id];
    }
    if (this.pageType === "edit") {
      this.getCustomerDetail();
    } else {
      const checkedSchool = uni.getStorageSync("checkedSchool");
      this.formData.department_id = checkedSchool[0].id;
      this.formData.department_name = checkedSchool[0].name;
      this.department_ids = [checkedSchool[0].id];
      this.getAdvisorList();
    }
    // this.get_status_list();
    this.getChannel();
  },
  // 页面周期函数--监听页面显示(not-nvue)
  onShow() {},
  // 页面周期函数--监听页面隐藏
  onHide() {},
  // 页面周期函数--监听页面卸载
  onUnload() {}
  // 页面处理函数--监听用户下拉动作
  // onPullDownRefresh() { uni.stopPullDownRefresh(); },
  // 页面处理函数--监听用户上拉触底
  // onReachBottom() {},
  // 页面处理函数--监听页面滚动(not-nvue)
  // onPageScroll(event) {},
  // 页面处理函数--用户点击右上角分享
  // onShareAppMessage(options) {},
};
</script>

<style lang="scss" scoped>
.tg_card {
  position: relative;
  width: 700rpx;
  margin: 0 auto;
  margin-top: 40rpx;
  border-radius: 8rpx 8rpx 0px 0px;
  overflow: hidden;
  background-color: #fff;
  z-index: 2;
  box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.05);
  .tg_card_title {
    position: relative;
    height: 72rpx;
    line-height: 72rpx;
    background: rgba(45, 128, 237, 0.1);
    padding-left: 70rpx;
    padding: 0 20rpx;
    padding-left: 40rpx;
    color: #333;
    font-size: 16px;
    font-weight: 500;

    &::before {
      position: absolute;
      content: "";
      border-radius: 2rpx;
      background: #ffbf0d;
      width: 4rpx;
      height: 26rpx;
      top: 22rpx;
      left: 22rpx;
    }
  }
  // .tg_card_content {
  ::v-deep .u-form-item__body {
    padding-left: 40rpx;
    padding-top: 0;
    padding-bottom: 0;
    height: 80rpx;
    .u-form-item__body__left__content__label {
      font-size: 26rpx;
    }
    .u-input__content__field-wrapper__field,
    .u-radio__text {
      font-size: 14px !important;
      font-weight: 500 !important;
    }
    .u-form-item__body__left__content__required {
      left: -11px;
      color: #f56c6c;
      font-size: 16px;
      top: 0px;
    }
    .u-radio {
      margin-right: 40rpx;
    }
    .u-rate {
      margin-left: -10px;
    }
  }
  // }
}
::v-deep .textarea-box {
  margin: 0 40rpx;
  margin-bottom: 40rpx;
  .u-textarea {
    border-radius: 8rpx;
    background: rgba(45, 128, 237, 0.1);
    border: 0;
  }
}

.add-custer {
  .btn-box {
    padding-top: 40rpx;
    padding-bottom: 40rpx;
    width: 700rpx;
    margin: 0 auto;
  }
}
.student-repeat-txt {
  color: #b1b2b3;
  font-size: 26rpx;
  padding-left: 10rpx;
}
.data-list {
  .student {
    margin-top: 16rpx;
    .span {
      padding: 6rpx 10rpx;
      color: #333;
      font-size: 28rpx;
    }
  }
}
</style>
