<template>
  <div class="customer-list">
    <!-- <u-toast ref="uToast"></u-toast> -->
    <u-loading-icon
      :show="pageLoading"
      color="#FFBF0D"
      textColor="#545556"
      mode="semicircle"
      :vertical="true"
      text="加载中..."
    ></u-loading-icon>
    <next-paging
      ref="paging"
      v-model="data"
      @query="getList"
      :autoShowBackToTop="true"
      :defaultPageSize="searchForm.page_size"
    >
      <!--top插槽-->
      <template #top
        ><view class="slotTop">
          <div class="tg-top-bg"></div>

          <u-navbar
            :placeholder="true"
            bgColor="transparent"
            title="学员列表"
            titleStyle="color: #fff;font-size:36rpx;font-weight:500"
            leftIconSize="20px"
            leftIconColor="#fff"
            :autoBack="false"
            @leftClick="back"
          >
          </u-navbar>
          <div class="customer-list-content">
            <div class="search-box">
              <u--input
                placeholder="搜索学员姓名"
                prefixIcon="search"
                v-model="searchForm.name"
                border="none"
                color="#fff"
                confirmType="search"
                @confirm="search"
                prefixIconStyle="font-size: 24px;color: #D3DCE6"
              ></u--input>

              <div @click="search" style="color: #fff">搜索</div>
            </div>
            <div class="student-status" @click="showTypeSelector = true">
              <div class="text">{{ currentStatus }}</div>
              <u-icon name="arrow-down" color="#fff" size="14"></u-icon>
            </div>
          </div> </view
      ></template>
      <!-- next-paging默认铺满全屏，此时页面所有view都应放在next-paging标签内，否则会被盖住 -->

      <!-- 多选操作区域 -->
      <div class="list-operation-sticky">
        <div class="list-operation">
          <div>
            <u-checkbox-group
              v-model="isAllChecked"
              @change="checkboxAllChange"
              iconPlacement="right"
              placement="row"
            >
              <span
                style="
                  color: #606266;
                  font-size: 28rpx;
                  font-weight: 500;
                  margin-right: 14rpx;
                "
                >全选</span
              ><u-checkbox></u-checkbox>
            </u-checkbox-group>
          </div>
          <div style="color: #2d80ed" @tap="handleOperate">操作</div>
        </div>
      </div>

      <div class="list-box">
        <u-checkbox-group
          v-model="checkboxValue1"
          placement="column"
          @change="checkboxChange"
        >
          <block :key="item.student_id" v-for="item in data">
            <div class="list-item">
              <div class="left" @click="goDetail(item)">
                <u-image
                  shape="circle"
                  width="90rpx"
                  height="90rpx"
                  :showMenuByLongpress="false"
                  :src="
                    item.student_gender === 'female'
                      ? 'https://tg-prod.oss-cn-beijing.aliyuncs.com/cf769e6f-5f97-41c6-a3c8-b796ca0df32c.png'
                      : item.student_gender === 'male'
                      ? 'https://tg-prod.oss-cn-beijing.aliyuncs.com/9e8b7d64-c249-45e8-ac42-3a67a921f84d.png'
                      : 'https://tg-prod.oss-cn-beijing.aliyuncs.com/db5c1013-4e99-41c7-bfa4-f93500fe0622.png'
                  "
                  :lazy-load="true"
                ></u-image>
                <div class="name">
                  <div class="text">{{ item.student_name }}</div>
                  <div class="school-box">
                    <div class="text school">{{ item.department_name }}</div>
                    <div class="text school">{{ item.student_number }}</div>
                    <!-- <div class="text school">{{ item.student_type_chn }}</div> -->
                  </div>
                </div>
              </div>
              <div class="right">
                <div class="phone" @click="goDetail(item)">
                  {{ item.student_mobile }}
                </div>
                <div class="checkbox-container">
                  <u-checkbox
                    :customStyle="{ height: '100%' }"
                    :key="item.student_id"
                    :name="item.student_id"
                  >
                  </u-checkbox>
                </div>
              </div>
            </div>
          </block>
        </u-checkbox-group>
      </div>
      <!--bottom插槽-->
      <!-- <template #bottom
      ><view class="slotBottom"><text>我是底部插槽内容</text></view></template
    > -->
    </next-paging>

    <!-- 学员状态选择器 -->
    <u-action-sheet
      :actions="actionsList"
      :cancelText="'取消'"
      @select="selectClick"
      @close="cancelClick"
      :show="showTypeSelector"
      round="20rpx"
    ></u-action-sheet>

    <!-- 多选操作选择器 -->
    <u-action-sheet
      :actions="operationActions"
      :show="operationShow"
      :safeAreaInsetBottom="true"
      :closeOnClickAction="true"
      round="10"
      cancelText="取消"
      @select="operationSelectClick"
      @close="closeOperationSheet"
      :description="`对已选（${checkboxValue1.length}）个学员进行操作`"
    ></u-action-sheet>
  </div>
</template>

<script>
import { getRegularStudentList } from "@/services/intention";
export default {
  name: "regularStudent",
  components: {},
  data() {
    return {
      searchForm: {
        name: "",
        page: 1,
        page_size: 20,
        student_type: "in_school"
      },
      data: [],
      count: 0, // 总条数
      pageCount: 0, // 总页数
      pageLoading: false,
      isFisrtEnter: true,
      showTypeSelector: false,
      actionsList: [
        {
          name: "全部",
          value: ""
        },
        {
          name: "试听",
          value: "audition"
        },
        {
          name: "退学",
          value: "drop_school"
        },
        {
          name: "在读",
          value: "in_school"
        },
        {
          name: "休学",
          value: "out_school"
        },
        {
          name: "临时",
          value: "temp"
        }
      ],
      isAllChecked: false,
      checkboxValue1: [],
      operationActions: [
        {
          name: "发送时光相册",
          value: "send_time_album"
        }
      ],
      operationShow: false
    };
  },
  computed: {
    currentStatus() {
      return this.actionsList.find(
        (item) => item.value === this.searchForm.student_type
      ).name;
    }
  },
  created() {},
  methods: {
    selectClick(e) {
      console.log(e);
      this.searchForm.student_type = e.value;
      this.showTypeSelector = false;
      this.$refs.paging.reload();
    },
    cancelClick() {
      this.showTypeSelector = false;
    },
    search() {
      // const { name } = this.searchForm;
      // if (name === "") {
      //   this.$refs.uToast.show({
      //     message: "请输入关键字"
      //   });
      //   return;
      // }
      this.$refs.paging.reload();
    },
    getList(pageNo) {
      // if (pageNo > this.pageCount) {
      //   this.$refs.paging.complete([]);
      // } else {
      this.searchForm.page = pageNo;
      const schId = uni.getStorageSync("checkedSchool");
      if (schId.length) {
        this.searchForm.department_id = schId.map((item) => item.id);
      }
      if (this.isFisrtEnter) {
        this.pageLoading = true;
        this.isFisrtEnter = false;
      }

      getRegularStudentList({
        ...this.searchForm
      })
        .then((res) => {
          console.log(res);
          if (res.results) {
            this.count = res.count;
            // 获取数据总页数
            this.pageCount = Math.ceil(this.count / this.searchForm.page_size);
            const arr = res.results;
            this.$refs.paging.complete(arr);
            this.pageLoading = false;
          }
        })
        .catch(() => {
          this.pageLoading = false;
        });
      // }
    },
    goDetail(item) {
      console.log(item);
      const { student_id, choose_head, student_name, department_id } = item;
      console.log(student_id);
      uni.navigateTo({
        url: `/pages/student/subpages/timeAlbum/index?student_id=${student_id}&from=1&choose_head=${choose_head}&student_name=${student_name}&department_id=${department_id}`
      });
    },
    back() {
      uni.redirectTo({
        url: `/pages/teacher/sort/index`
      });
      uni.setStorageSync("teacher_tabIndex", 1);
    },
    // 局部更新列表
    updateList(res) {
      const {
        id,
        department_name,
        student_mobile,
        student_name,
        student_gender
      } = res;
      const { data } = this;
      const index = data.findIndex((item) => item.id === id);
      console.log(data, res, index);
      if (index > -1) {
        data[index].department_name = department_name;
        data[index].student_mobile = student_mobile;
        data[index].student_name = student_name;
        data[index].student_gender = student_gender;
      }
      this.data = [...data];
    },
    // 全选/反选功能
    checkboxAllChange(value) {
      if (value.length) {
        this.checkboxValue1 = this.data.map((item) => item.student_id);
      } else {
        this.checkboxValue1 = [];
      }
    },
    // 单个复选框改变
    checkboxChange(value) {
      this.checkboxValue1 = value;
      // 根据选中状态更新全选状态
      if (value.length === this.data.length) {
        this.isAllChecked = [""];
      } else {
        this.isAllChecked = [];
      }
    },
    // 点击操作按钮
    handleOperate() {
      const selectedStudents = this.getSelectedStudents();
      if (!selectedStudents.length) {
        uni.showToast({
          title: "请选择学员",
          icon: "none"
        });
        return false;
      }
      this.operationShow = true;
    },
    // 操作选择
    operationSelectClick(item) {
      // console.log("选中操作:", item);
      const selectedStudents = this.getSelectedStudents();
      console.log("选中的学员数据:", selectedStudents);

      if (item.value === "send_time_album") {
        if (!this.$hasPermission(["time_album_batch_add"])) {
          uni.showToast({
            title: "无权限操作,请联系管理员!",
            icon: "none"
          });
          return;
        }
        this.handleSendTimeAlbum(selectedStudents);
      }
      this.operationShow = false;
    },
    // 关闭操作面板
    closeOperationSheet() {
      this.operationShow = false;
    },
    // 获取选中的学员数据
    getSelectedStudents() {
      return this.data.filter((item) =>
        this.checkboxValue1.includes(item.student_id)
      );
    },

    // 发送时光相册处理方法
    handleSendTimeAlbum(selectedStudents) {
      console.log("发送时光相册:", selectedStudents);
      const studentData = selectedStudents.map((item) => {
        return {
          student_id: item.student_id,
          student_number: item.student_number,
          student_name: item.student_name
        };
      });
      uni.navigateTo({
        url: `/pages/student/subpages/timeAlbum/edit?student_data=${JSON.stringify(
          studentData
        )}`
      });
    }
  },
  watch: {},

  // 页面周期函数--监听页面加载
  onLoad() {
    uni.$on("updateList", this.updateList);
  },
  // 页面周期函数--监听页面初次渲染完成
  onReady() {},
  // 页面周期函数--监听页面显示(not-nvue)
  onShow() {},
  // 页面周期函数--监听页面隐藏
  onHide() {},
  // 页面周期函数--监听页面卸载
  onUnload() {
    uni.$off("updateList", this.updateList);
  }
  // 页面处理函数--监听用户下拉动作
  // onPullDownRefresh() { uni.stopPullDownRefresh(); },
  // 页面处理函数--监听用户上拉触底
  // onReachBottom() {},
  // 页面处理函数--监听页面滚动(not-nvue)
  // onPageScroll(event) {},
  // 页面处理函数--用户点击右上角分享
  // onShareAppMessage(options) {},
};
</script>

<style lang="scss" scoped>
.customer-list {
  background: #f5f6fa;
  height: 100vh;
  width: 100vw;
  position: relative;
  display: block;
  .customer-list-content {
    width: 700rpx;
    margin: 40rpx auto;
    position: relative;
    z-index: 2;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .search-box {
      position: relative;

      height: 56rpx;
      width: 678rpx;
      margin: 0 auto;
      border-radius: 20px;
      display: flex;
      align-items: center;
      padding: 0 20rpx;
      border: 2rpx solid #d3dce6;
      .text {
        color: #d3dce6;
        font-size: 28rpx;
        font-style: normal;
        line-height: 32rpx;
      }
    }
    .student-status {
      width: 120rpx;
      margin-left: 20rpx;
      display: flex;
      align-items: center;
      color: #fff;
      font-size: 28rpx;
      font-style: normal;
      line-height: 32rpx;
      .text {
        margin-right: 10rpx;
      }
    }
  }

  .list-operation-sticky {
    position: sticky;
    top: 0;
    z-index: 100;
    background: #ffffff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .list-operation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 32rpx 18rpx 30rpx 24rpx;
    width: 700rpx;
    margin: 0 auto;
  }

  .list-box {
    width: 700rpx;
    margin: 0 auto;
    background: #f5f6fa;
  }
  .list-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #fff;
    box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.05);
    height: 160rpx;
    border-radius: 8rpx;
    padding: 36rpx;
    margin-top: 20rpx;
    position: relative;
    .left {
      display: flex;
      align-items: center;
      .name {
        margin-left: 14rpx;
      }
      .text {
        display: block;
        color: #333;
        padding-left: 14rpx;
        color: #333;
        font-size: 32rpx;
        font-weight: 500;
        margin-bottom: 12rpx;
        &.school {
          color: #2d80ed;
          text-align: center;
          font-size: 24rpx;
          font-style: normal;
          font-weight: 500;
          line-height: 24rpx;
          border-radius: 18px;
          background: rgba(45, 128, 237, 0.1);
          padding: 10rpx 16rpx;
          margin-bottom: 0;
        }
      }
      .school-box {
        display: flex;
        align-items: center;
        .text {
          margin-right: 10rpx;
        }
      }
    }
    .right {
      display: flex;
      align-items: flex-end;
      flex-direction: column;
      justify-content: center;
      .phone {
        color: #2d80ed;
        font-size: 28rpx;
        font-style: normal;
        font-weight: 500;
        line-height: 32rpx;
        margin-bottom: 18rpx;
        text-align: right;
      }
      .checkbox-container {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        width: 100%;
      }
      ::v-deep .u-icon--right {
        justify-content: flex-end;
      }
    }
  }
  ::v-deep .u-loading-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 5;
  }
}
</style>
