<template>
  <div class="customer-info">
    <u-navbar
      :placeholder="true"
      bgColor="transparent"
      title="意向客户信息"
      titleStyle="color: #fff;font-size:36rpx;font-weight:500"
      leftIconSize="20px"
      leftIconColor="#fff"
      :autoBack="true"
    >
    </u-navbar>
    <!-- <div class="border-top-1px tab-conetnt">
      <u-tabs
        :current="current"
        lineWidth="60"
        :activeStyle="{
          color: '#FFBF0D',
          fontWeight: 'bold',
          transform: 'scale(1.05)'
        }"
        :inactiveStyle="{
          color: '#606266',
          transform: 'scale(1)'
        }"
        itemStyle="width:50%;padding-left: 30rpx; padding-right: 30rpx; height: 80rpx;"
        :list="tab_list"
        @click="tabclick"
      ></u-tabs>
    </div> -->
    <div v-show="current === 0">
      <baseInfo :customerId="id" pageType="edit"></baseInfo>
    </div>
  </div>
</template>

<script>
// import { getStatusList } from "@/services/intention";
// 引入baseInfo
import baseInfo from "./addCustomer";
export default {
  name: "customerInfo",
  components: {
    baseInfo
  },
  data() {
    return {
      current: 0,
      tab_list: [
        {
          name: "基本信息"
        },
        {
          name: "沟通记录"
        }
      ],
      id: ""
    };
  },
  computed: {},
  methods: {
    tabclick({ index }) {
      console.log("index :>> ", index);
      this.current = index;
    }
  },
  watch: {},
  created() {},
  // 页面周期函数--监听页面加载
  onLoad(options) {
    console.log("options :>> ", options);
    this.id = options.id;
  },
  // 页面周期函数--监听页面初次渲染完成
  onReady() {
    // this.$refs.uForm.setRules(this.rules);
  },
  // 页面周期函数--监听页面显示(not-nvue)
  onShow() {},
  // 页面周期函数--监听页面隐藏
  onHide() {},
  // 页面周期函数--监听页面卸载
  onUnload() {}
  // 页面处理函数--监听用户下拉动作
  // onPullDownRefresh() { uni.stopPullDownRefresh(); },
  // 页面处理函数--监听用户上拉触底
  // onReachBottom() {},
  // 页面处理函数--监听页面滚动(not-nvue)
  // onPageScroll(event) {},
  // 页面处理函数--用户点击右上角分享
  // onShareAppMessage(options) {},
};
</script>

<style lang="scss" scoped>
.customer-info {
  .tab-conetnt {
    box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.05);
  }
}
</style>
