<template>
  <div class="customer-list">
    <!-- <u-toast ref="uToast"></u-toast> -->
    <u-loading-icon
      :show="pageLoading"
      color="#FFBF0D"
      textColor="#545556"
      mode="semicircle"
      :vertical="true"
      text="加载中..."
    ></u-loading-icon>
    <next-paging
      ref="paging"
      v-model="data"
      @query="getList"
      :autoShowBackToTop="true"
      :defaultPageSize="searchForm.page_size"
    >
      <!--top插槽-->
      <template #top
        ><view class="slotTop">
          <div class="tg-top-bg"></div>

          <u-navbar
            :placeholder="true"
            bgColor="transparent"
            title="意向客户管理"
            titleStyle="color: #fff;font-size:36rpx;font-weight:500"
            leftIconSize="20px"
            leftIconColor="#fff"
            :autoBack="false"
            @leftClick="back"
          >
          </u-navbar>
          <div class="customer-list-content">
            <div class="search-box">
              <u--input
                placeholder="搜索学员姓名"
                prefixIcon="search"
                v-model="searchForm.name"
                border="none"
                color="#fff"
                confirmType="search"
                @confirm="search"
                prefixIconStyle="font-size: 24px;color: #D3DCE6"
              ></u--input>
              <div @click="search" style="color: #fff">搜索</div>
            </div>
          </div>
        </view></template
      >
      <!-- next-paging默认铺满全屏，此时页面所有view都应放在next-paging标签内，否则会被盖住 -->
      <!-- 需要固定在页面顶部的view请通过slot="top"插入，包括自定义的导航栏 -->
      <div class="list-box">
        <block :key="item.id" v-for="item in data">
          <div @click="goDetail(item)" class="list-item">
            <div class="left">
              <u-image
                shape="circle"
                width="90rpx"
                height="90rpx"
                :showMenuByLongpress="false"
                :src="
                  item.student_gender === 'female'
                    ? 'https://tg-prod.oss-cn-beijing.aliyuncs.com/cf769e6f-5f97-41c6-a3c8-b796ca0df32c.png'
                    : item.student_gender === 'male'
                    ? 'https://tg-prod.oss-cn-beijing.aliyuncs.com/9e8b7d64-c249-45e8-ac42-3a67a921f84d.png'
                    : 'https://tg-prod.oss-cn-beijing.aliyuncs.com/db5c1013-4e99-41c7-bfa4-f93500fe0622.png'
                "
                :lazy-load="true"
              ></u-image>
              <div class="name">
                <div class="text">{{ item.student_name }}</div>
                <div class="text school">{{ item.department_name }}</div>
              </div>
            </div>
            <div class="right">
              <div class="phone">{{ item.student_mobile }}</div>
              <u-icon name="arrow-right" color="#000" size="14"></u-icon>
            </div>
          </div>
        </block>
      </div>
      <!--bottom插槽-->
      <!-- <template #bottom
      ><view class="slotBottom"><text>我是底部插槽内容</text></view></template
    > -->
    </next-paging>
  </div>
</template>

<script>
import { getStudentsCustomerList } from "@/services/intention";
export default {
  name: "customerList",
  components: {},
  data() {
    return {
      searchForm: {
        name: "",
        page: 1,
        page_size: 20
      },
      data: [],
      count: 0, // 总条数
      pageCount: 0, // 总页数
      pageLoading: false,
      isFisrtEnter: true
    };
  },
  computed: {},
  created() {},
  methods: {
    search() {
      // const { name } = this.searchForm;
      // if (name === "") {
      //   this.$refs.uToast.show({
      //     message: "请输入关键字"
      //   });
      //   return;
      // }
      this.$refs.paging.reload();
    },
    getList(pageNo) {
      // if (pageNo > this.pageCount) {
      //   this.$refs.paging.complete([]);
      // } else {
      this.searchForm.page = pageNo;
      const schId = uni.getStorageSync("checkedSchool");
      if (schId.length) {
        this.searchForm.department_id = schId.map((item) => item.id);
      }
      if (this.isFisrtEnter) {
        this.pageLoading = true;
        this.isFisrtEnter = false;
      }

      getStudentsCustomerList({
        ...this.searchForm
      })
        .then((res) => {
          console.log(res);
          if (res.results) {
            this.count = res.count;
            // 获取数据总页数
            this.pageCount = Math.ceil(this.count / this.searchForm.page_size);
            const arr = res.results.map((item) => {
              const {
                department_name,
                student_name,
                student_mobile,
                student_gender,
                id,
                student_id
              } = item;
              return {
                department_name,
                student_name,
                student_mobile,
                student_gender,
                id,
                student_id
              };
            });
            this.$refs.paging.complete(arr);
            this.pageLoading = false;
          }
        })
        .catch(() => {
          this.pageLoading = false;
        });
      // }
    },
    goDetail(item) {
      uni.navigateTo({
        url: `/pages/teacher/subpages/customer/customerInfo?id=${item.id}&student_id=${item.student_id}`
      });
    },
    back() {
      uni.redirectTo({
        url: `/pages/teacher/sort/index`
      });
      uni.setStorageSync("teacher_tabIndex", 1);
    },
    // 局部更新列表
    updateList(res) {
      const {
        id,
        department_name,
        student_mobile,
        student_name,
        student_gender
      } = res;
      const { data } = this;
      const index = data.findIndex((item) => item.id === id);
      console.log(data, res, index);
      if (index > -1) {
        data[index].department_name = department_name;
        data[index].student_mobile = student_mobile;
        data[index].student_name = student_name;
        data[index].student_gender = student_gender;
      }
      this.data = [...data];
    }
  },
  watch: {},

  // 页面周期函数--监听页面加载
  onLoad() {
    // this.getList();
    uni.$on("updateList", this.updateList);
  },
  // 页面周期函数--监听页面初次渲染完成
  onReady() {},
  // 页面周期函数--监听页面显示(not-nvue)
  onShow() {},
  // 页面周期函数--监听页面隐藏
  onHide() {},
  // 页面周期函数--监听页面卸载
  onUnload() {
    uni.$off("updateList", this.updateList);
  }
  // 页面处理函数--监听用户下拉动作
  // onPullDownRefresh() { uni.stopPullDownRefresh(); },
  // 页面处理函数--监听用户上拉触底
  // onReachBottom() {},
  // 页面处理函数--监听页面滚动(not-nvue)
  // onPageScroll(event) {},
  // 页面处理函数--用户点击右上角分享
  // onShareAppMessage(options) {},
};
</script>

<style lang="scss" scoped>
.customer-list {
  background: #f5f6fa;
  height: 100vh;
  width: 100vw;
  position: relative;
  display: block;
  .customer-list-content {
    margin: 40rpx 0;
    position: relative;
    z-index: 2;
  }
  .search-box {
    position: relative;

    height: 56rpx;
    width: 678rpx;
    margin: 0 auto;
    border-radius: 20px;
    display: flex;
    align-items: center;
    padding: 0 20rpx;
    border: 2rpx solid #d3dce6;
    .text {
      color: #d3dce6;
      font-size: 28rpx;
      font-style: normal;
      line-height: 32rpx;
    }
  }
  .list-box {
    width: 700rpx;
    margin: 0 auto;
    background: #f5f6fa;
  }
  .list-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #fff;
    box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.05);
    height: 160rpx;
    border-radius: 8rpx;
    padding: 36rpx;
    margin-bottom: 20rpx;
    .left {
      display: flex;
      align-items: center;
      .name {
        margin-left: 14rpx;
      }
      .text {
        display: block;
        color: #333;
        padding-left: 14rpx;
        color: #333;
        font-size: 32rpx;
        font-weight: 500;
        margin-bottom: 12rpx;
        &.school {
          color: #ffbf0d;
          text-align: center;
          font-size: 24rpx;
          font-style: normal;
          font-weight: 500;
          line-height: 24rpx;
          border-radius: 18px;
          background: rgba(45, 128, 237, 0.1);
          padding: 10rpx 16rpx;
          margin-bottom: 0;
        }
      }
    }
    .right {
      text-align: right;
      display: flex;
      justify-content: flex-end;
      flex-direction: column;
      .phone {
        color: #ffbf0d;
        font-size: 28rpx;
        font-style: normal;
        font-weight: 500;
        line-height: 32rpx;
        margin-bottom: 18rpx;
      }
      ::v-deep .u-icon--right {
        justify-content: flex-end;
      }
    }
  }
  ::v-deep .u-loading-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 5;
  }
}
</style>
