<template>
  <div class="add-custer">
    <div class="tg-top-bg"></div>
    <u-navbar
      :placeholder="true"
      bgColor="transparent"
      title="添加沟通记录"
      titleStyle="color: #fff;font-size:36rpx;font-weight:500"
      leftIconSize="20px"
      leftIconColor="#fff"
      :autoBack="true"
    >
    </u-navbar>
    <div class="tg_card">
      <div class="tg_card_title">沟通内容</div>
      <div class="tg_card_content">
        <u--form
          labelPosition="left"
          :model="formData"
          :rules="rules"
          labelWidth="100"
          ref="uForm"
          errorType="toast"
        >
          <u-form-item
            required
            label="姓名"
            prop="formData.student_name"
            borderBottom
          >
            <u--input
              placeholder="请输入姓名"
              v-model="formData.student_name"
              border="none"
              maxlength="20"
            ></u--input>
          </u-form-item>
          <u-form-item
            label="性别"
            required
            prop="formData.student_gender"
            borderBottom
          >
            <!-- <u--input
              v-model="formData.sex"
              disabledColor="#ffffff"
              placeholder="请选择性别"
              border="none"
            ></u--input> -->
            <u-radio-group
              v-model="formData.student_gender"
              iconPlacement="left"
            >
              <u-radio activeColor="#FFBF0D" name="male" label="男"></u-radio>
              <u-radio activeColor="#FFBF0D" name="female" label="女"></u-radio>
            </u-radio-group>
          </u-form-item>
          <u-form-item
            required
            label="出生日期"
            prop="formData.birth_day"
            borderBottom
          >
            <u--input
              placeholder="请选择出生日期"
              v-model="formData.birth_day"
              border="none"
              @focus="birth_day_focus"
            ></u--input>
            <u-datetime-picker
              :show="birth_day_show"
              v-model="birth_day"
              mode="date"
              @cancel="birth_day_show = false"
              @confirm="birth_day_confirm"
            ></u-datetime-picker>
          </u-form-item>
          <u-form-item
            required
            label="联系电话"
            prop="formData.student_mobile"
            borderBottom
          >
            <u--input
              placeholder="请输入联系电话"
              v-model="formData.student_mobile"
              border="none"
              type="number"
              maxlength="11"
            ></u--input>
          </u-form-item>
          <u-form-item
            required
            label="渠道"
            prop="formData.channel_id"
            borderBottom
          >
            <u--input
              placeholder="请选择渠道"
              v-model="formData.channel_id"
              border="none"
            ></u--input>
          </u-form-item>
          <u-form-item
            required
            label="二级渠道"
            prop="formData.sub_channel_id"
            borderBottom
          >
            <u--input
              placeholder="请选择二级渠道"
              v-model="formData.sub_channel_id"
              border="none"
            ></u--input>
          </u-form-item>
          <u-form-item
            required
            label="到店状态"
            prop="formData.status"
            borderBottom
          >
            <u--input
              placeholder="请选择到店状态"
              v-model="formData.status_txt"
              border="none"
              @focus="status_focus"
            ></u--input>
            <u-picker
              :show="status_show"
              :columns="status_list"
              :immediateChange="true"
              keyName="label"
              @confirm="statusConfirm"
              @cancel="status_show = false"
            ></u-picker>
          </u-form-item>
          <u-form-item
            required
            label="所属校区"
            prop="formData.department_id"
            borderBottom
          >
            <u--input
              placeholder="请输入姓名"
              v-model="formData.name"
              border="none"
            ></u--input>
          </u-form-item>
          <u-form-item
            required
            label="意向级别"
            prop="formData.intention_level_name"
            borderBottom
          >
            <u-rate
              active-color="#FFBF0D"
              inactive-color="#b2b2b2"
              gutter="10"
              v-model="formData.intention_level_name"
            ></u-rate>
          </u-form-item>
          <u-form-item
            required
            label="是否有效"
            prop="formData.valid_status"
            borderBottom
          >
            <u-radio-group v-model="formData.valid_status" iconPlacement="left">
              <u-radio
                activeColor="#FFBF0D"
                name="pending"
                label="待定"
              ></u-radio>
              <u-radio
                activeColor="#FFBF0D"
                name="valid"
                label="有效"
              ></u-radio>
            </u-radio-group>
          </u-form-item>
        </u--form>
      </div>
    </div>
    <div class="btn-box" style="padding-top: 100rpx">
      <u-button
        @click="submit"
        size="large"
        shape="circle"
        color="linear-gradient(270deg, #3667f0 0%, #568ff5 100%)"
        type="primary"
        text="保存"
      ></u-button>
    </div>
  </div>
</template>

<script>
import { getStatusList } from "@/services/intention";
export default {
  name: "addCuster",
  components: {},
  data() {
    return {
      formData: {
        student_name: "",
        student_mobile: "",
        student_gender: "male",
        birth_day: "",
        channel_id: "",
        sub_channel_id: "",
        status: "",
        status_txt: "",
        department_id: "",
        intention_level_name: 0,
        valid_status: ""
      },
      birth_day_show: false,
      status_show: false,
      birth_day: Number(new Date()),
      intention_level_name: 0,
      status_list: [],
      rules: {
        "formData.student_name": {
          type: "string",
          required: true,
          message: "请填写姓名"
        },
        "formData.intention_level_name": {
          type: "number",
          required: true,
          message: "请选择意向级别"
        }
      }
    };
  },
  computed: {},
  methods: {
    birth_day_confirm({ value }) {
      console.log(value);
      this.formData.birth_day = this.$ljsPublic.date.formatTime(
        value,
        "{y}-{m}-{d}"
      );
      this.birth_day_show = false;
    },
    birth_day_focus() {
      uni.hideKeyboard();
      this.birth_day_show = true;
    },
    status_focus() {
      uni.hideKeyboard();
      this.status_show = true;
    },
    get_status_list() {
      getStatusList().then((res) => {
        console.log(res);
        if (res) {
          const arr = [];
          for (const key in res) {
            arr.push({
              id: res[key],
              label: key
            });
          }
          this.status_list = [arr];
        }
      });
    },
    statusConfirm(e) {
      console.log("e :>> ", e);
      this.formData.status = e.value[0].id;
      this.formData.status_txt = e.value[0].label;
      this.status_show = false;
    }
  },
  watch: {},

  // 页面周期函数--监听页面加载
  onLoad() {
    this.get_status_list();
  },
  // 页面周期函数--监听页面初次渲染完成
  onReady() {
    this.$refs.uForm.setRules(this.rules);
  },
  // 页面周期函数--监听页面显示(not-nvue)
  onShow() {},
  // 页面周期函数--监听页面隐藏
  onHide() {},
  // 页面周期函数--监听页面卸载
  onUnload() {}
  // 页面处理函数--监听用户下拉动作
  // onPullDownRefresh() { uni.stopPullDownRefresh(); },
  // 页面处理函数--监听用户上拉触底
  // onReachBottom() {},
  // 页面处理函数--监听页面滚动(not-nvue)
  // onPageScroll(event) {},
  // 页面处理函数--用户点击右上角分享
  // onShareAppMessage(options) {},
};
</script>

<style lang="scss" scoped>
.tg_card {
  position: relative;
  width: 700rpx;
  margin: 0 auto;
  margin-top: 40rpx;
  border-radius: 8rpx 8rpx 0px 0px;
  overflow: hidden;
  background-color: #fff;
  z-index: 2;
  box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.05);
  .tg_card_title {
    position: relative;
    height: 72rpx;
    line-height: 72rpx;
    background: rgba(45, 128, 237, 0.1);
    padding-left: 70rpx;
    padding: 0 20rpx;
    padding-left: 40rpx;
    color: #333;
    font-size: 28rpx;
    font-weight: 500;

    &::before {
      position: absolute;
      content: "";
      border-radius: 2rpx;
      background: #ffbf0d;
      width: 4rpx;
      height: 26rpx;
      top: 22rpx;
      left: 22rpx;
    }
  }
  .tg_card_content {
    ::v-deep .u-form-item__body {
      padding-left: 40rpx;
      padding-top: 0;
      padding-bottom: 0;
      height: 80rpx;
      .u-form-item__body__left__content__label {
        font-size: 26rpx;
      }
      .u-form-item__body__left__content__required {
        left: -11px;
        color: #f56c6c;
        font-size: 16px;
        top: 0px;
      }
      .u-radio {
        margin-right: 40rpx;
      }
      .u-rate {
        margin-left: -10px;
      }
    }
  }
}

.add-custer {
  .btn-box {
    margin-top: 80rpx;
    width: 700rpx;
    margin: 0 auto;
  }
}
</style>
