<template>
  <div class="tg-home">
    <div class="tg-top-bg"></div>
    <div>
      <div class="navbar-box">
        <u-navbar
          bgColor="transparent"
          title="工作台"
          titleStyle="color: #fff;font-size:36rpx;font-weight:500"
          @leftClick="leftClick"
        >
          <div class="u-nav-slot" slot="left">
            <div @click="leftPlusClick" class="tg-plus text-primary">+</div>
            <div v-if="popupshow" class="tg-bubble-popup">
              <div class="tg-fa"></div>
              <div class="tg-shortcut-menu">
                <div
                  v-if="$hasPermission(['customer_list'])"
                  @click="toIntendedCustomer"
                  class="tg-shortcut-item"
                >
                  <div
                    class="tg_sprite_01 tg_home_shortcut tg-intention active"
                  ></div>
                  <div class="tg-shortcut-title">意向客户</div>
                </div>
                <!-- <div @tap="toRichText" class="tg-shortcut-item">
                  <div
                    class="tg_sprite_01 tg_home_shortcut tg-communication active"
                  ></div>
                  <div class="tg-shortcut-title">创建文章</div>
                </div> -->
                <!-- <div class="tg-shortcut-item">
                  <div
                    class="tg_sprite_01 tg_home_shortcut tg-charge active"
                  ></div>
                  <div class="tg-shortcut-title">收费</div>
                </div> -->
              </div>
            </div>
          </div>
        </u-navbar>
      </div>
    </div>
    <div>
      <TabBar :tabIndex="tabIndex"></TabBar>
    </div>
  </div>
</template>

<script>
import TabBar from "../components/tab-bar/index.vue";
export default {
  name: "homeIndex",
  components: { TabBar },
  data() {
    return {
      tabIndex: 0,
      popupshow: false
    };
  },
  computed: {},
  methods: {
    leftClick() {},
    leftPlusClick() {
      this.popupshow = !this.popupshow;
    },
    toIntendedCustomer() {
      uni.navigateTo({
        url: "/pages/teacher/subpages/customer/addCustomer"
      });

      this.leftPlusClick();
    },
    toRichText() {
      uni.navigateTo({
        url: `/pages/teacher/subpages/richtext/index`
      });
    }
  },
  watch: {},

  // 页面周期函数--监听页面加载
  onLoad() {
    const tabIndex = uni.getStorageSync("teacher_tabIndex");
    this.tabIndex = tabIndex || 0;
  },
  // 页面周期函数--监听页面初次渲染完成
  onReady() {},
  // 页面周期函数--监听页面显示(not-nvue)
  onShow() {
    console.log(
      "this.$hasPermission :>> ",
      this.$hasPermission(["customer_list"])
    );
  },
  // 页面周期函数--监听页面隐藏
  onHide() {},
  // 页面周期函数--监听页面卸载
  onUnload() {}
  // 页面处理函数--监听用户下拉动作
  // onPullDownRefresh() { uni.stopPullDownRefresh(); },
  // 页面处理函数--监听用户上拉触底
  // onReachBottom() {},
  // 页面处理函数--监听页面滚动(not-nvue)
  // onPageScroll(event) {},
  // 页面处理函数--用户点击右上角分享
  // onShareAppMessage(options) {},
};
</script>

<style lang="scss" scoped>
.tg-home {
  width: 100vw;
  height: 100vh;
  background: #f5f6fa;
  .navbar_wrap {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: white;
    font-size: 36px;
  }

  .navbar {
    position: absolute;
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 40rpx;
  }

  .header-left {
    width: 40rpx;
    height: 40rpx;
  }

  .center-title {
    color: #fff;
    font-size: 36rpx;
  }
  .tg-top-bg {
    border-radius: 50%;
    background: linear-gradient(180deg, #2956ef 0%, #69a2f8 100%);
    width: 1096rpx;
    height: 480rpx;
    position: absolute;
    left: 50%;
    top: 0rpx;
    transform: translate(-50%, -100rpx);
    z-index: 1;
  }
  .tg-nav {
    position: absolute;
    width: 100%;
    top: 60rpx;
    color: #fff;
    font-size: 30rpx;
    font-weight: 600;
    text-align: center;
    z-index: 2;
  }
  .tg-plus {
    // position: absolute;
    // top: 0rpx;
    // right: 20rpx;
    font-size: 40rpx;
    font-weight: 600;
    text-align: center;
    // z-index: 2;
    background-color: #fff;
    width: 50rpx;
    height: 50rpx;
    line-height: 44rpx;
    border-radius: 100%;
  }
  .tg-bubble-popup {
    position: absolute;
    top: 90rpx;
    left: 10rpx;
    font-weight: 600;
    text-align: center;
    z-index: 2;
    background-color: #fff;
    display: block;
    background-size: cover;
    background-repeat: no-repeat;
    border-radius: 10rpx;
    width: 200rpx;
    .tg-fa {
      position: absolute;
      top: -20rpx;
      left: 20rpx;
      width: 0;
      height: 0;
      border-left: 18rpx solid transparent;
      border-right: 18rpx solid transparent;
      border-bottom: 20rpx solid #fff;
    }
    .tg-shortcut-menu {
      .tg-shortcut-item {
        display: flex;
        align-items: center;
        padding: 20rpx;
        border-bottom: 1rpx solid rgba(211, 220, 230, 0.48);
        .tg-shortcut-title {
          color: #8492a6;
          font-size: 24rpx;
          margin-left: 16rpx;
          &.active {
            color: #ffbf0d;
          }
        }
        &:last-child {
          border: none;
        }
      }
    }
  }
  ::v-deep .u-navbar__content__left--hover {
    opacity: 1 !important;
  }
}
</style>
