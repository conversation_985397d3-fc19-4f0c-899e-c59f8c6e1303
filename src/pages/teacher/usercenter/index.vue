<template>
  <div class="tg_mine">
    <u-toast ref="uToast"></u-toast>

    <div class="tg-top-bg"></div>
    <!-- <u-navbar
      :placeholder="false"
      bgColor="#2956ef"
      titleStyle="color: #000;font-size:36rpx;font-weight:500"
    >
      <view class="u-nav-slot" slot="left"> </view>
    </u-navbar> -->
    <div>
      <div class="user-container">
        <div class="header">
          <image
            class="avatar"
            mode="aspectFill"
            src="https://tg-prod.oss-cn-beijing.aliyuncs.com/db5c1013-4e99-41c7-bfa4-f93500fe0622.png"
          />
          <div class="info">
            <div class="name">
              <text>{{ userInfo.name }}</text>
              <u-icon
                v-if="userInfo.gender === 'male'"
                name="man"
                color="#fff"
                size="40rpx"
              ></u-icon>
              <u-icon
                v-if="userInfo.gender === 'female'"
                name="women"
                color="#fff"
                size="40rpx"
              ></u-icon>
            </div>
            <span class="desc text-primary">{{ department_name }}</span>
          </div>
        </div>
        <div class="info-block">
          <div class="info-item">
            <div class="cell-row">
              <span class="tg_sprite_01 tg_mine_cell tg-phone"></span>
              <text class="label text-primary">电话</text>
            </div>
            <div class="summary">{{ userInfo.mobile }}</div>
          </div>
          <div class="info-item">
            <div class="cell-row">
              <span class="tg_sprite_01 tg_mine_cell tg-post"></span>
              <text class="label text-primary">员工职务</text>
            </div>
            <div v-if="userInfo.office_post_full" class="summary">
              {{ userInfo.office_post_full.department_name }}-{{
                userInfo.office_post_full.office_post_name
              }}
            </div>
          </div>
          <div v-if="userInfo.office_post_part.length" class="info-item">
            <div class="cell-row">
              <span class="tg_sprite_01 tg_mine_cell tg-casual"></span>
              <text class="label text-primary">兼职职务</text>
            </div>

            <div class="part-summary">
              <span
                :key="item.department_id"
                class="text-normal"
                v-for="item in userInfo.office_post_part"
              >
                {{ item.department_name + "(" + item.office_post_name + ")" }}
              </span>
            </div>
          </div>
        </div>
        <div @click="schoolShowHandle" class="action-block">
          <text class="text-primary">切换校区</text>
          <u-icon
            :bold="true"
            name="arrow-right"
            color="#FFBF0D"
            size="18"
          ></u-icon>
        </div>
        <div
          v-if="hasDtbPermission"
          @click="switchDtbQrcode"
          class="action-block"
        >
          <text class="text-primary">我的地推码</text>
          <u-icon
            :bold="true"
            name="arrow-right"
            color="#FFBF0D"
            size="18"
          ></u-icon>
        </div>
        <div @click="switchClient" class="action-block">
          <text class="text-primary">切换身份</text>
          <u-icon
            :bold="true"
            name="arrow-right"
            color="#FFBF0D"
            size="18"
          ></u-icon>
        </div>

        <div class="login-out-btn">
          <u-button
            @click="loginout"
            size="large"
            shape="circle"
            color="linear-gradient(270deg, #3667f0 0%, #568ff5 100%)"
            type="primary"
            text="退出登录"
          ></u-button>
        </div>
      </div>

      <TabBar :tabIndex="tabIndex"></TabBar>

      <div>
        <gq-tree
          ref="gqTree"
          :range="range"
          idKey="id"
          nameKey="name"
          childKey="child"
          pidKey="pid"
          allKey="pid"
          :maskClick="false"
          :showSearch="true"
          :multiple="false"
          :cascade="true"
          :selectParent="false"
          :lessOne="true"
          confirmColor="#007aff"
          cancelColor="#757575"
          title="校区关键字"
          titleColor="#757575"
          @cancel="treeCancel"
          @confirm="treeConfirm"
        >
        </gq-tree>
      </div>
    </div>
  </div>
</template>

<script>
import {
  getEmployeeInfo,
  miniprogramLogout,
  checkPower
} from "@/services/login";
import gqTree from "@/uni_modules/gq-tree/gq-tree.vue";
import TabBar from "../components/tab-bar/index.vue";

export default {
  name: "usercenterIndex",
  components: {
    TabBar,
    gqTree
  },
  data() {
    return {
      tabIndex: 0,
      range: [],
      userInfo: {
        office_post_full: {},
        office_post_part: [],
        office_post_list: []
      },
      department_id: "",
      department_name: "",
      hasDtbPermission: false
    };
  },
  computed: {},
  methods: {
    getUserInfo() {
      const user = uni.getStorageSync("user");
      const { employee_id } = user;
      if (employee_id) {
        getEmployeeInfo({
          employee_id
        })
          .then((res) => {
            const { name, mobile, gender } = res;
            this.office_post_list = res.office_post;
            const office_post_full = res.office_post?.find(
              (item) => item.office_post_type === "full"
            );
            const office_post_part = res.office_post?.filter(
              (item) => item.office_post_type === "part"
            );
            this.userInfo = {
              name,
              mobile,
              gender,
              office_post_full,
              office_post_part
            };
            // this.userInfo = res;

            this.hideSkeleton();
          })
          .catch(() => {
            this.hideSkeleton();
          });
      } else {
        this.hideSkeleton();
        this.$refs.uToast.show({
          type: "error",
          message: "获取缓存信息失败，请退出重新登录！"
        });
      }
    },
    hideSkeleton() {},
    loginout() {
      const open_id = uni.getStorageSync("openid") || "";
      miniprogramLogout({
        login_type: "miniprogram",
        open_id
      })
        .then(() => {
          uni.removeStorageSync("openid");
          uni.removeStorageSync("token");
          uni.removeStorageSync("tokenTime");
          uni.removeStorageSync("singSchoolData");
          uni.setStorageSync("teacher_tabIndex", 0);
          uni.navigateTo({ url: "/pages/index/index" });
        })
        .catch(() => {
          uni.hideToast();
          this.$refs.uToast.show({
            type: "error",
            message: "退出失败，请重试！"
          });
        });
    },
    deepCheckValue(options, values, idKey, childKey) {
      return options.map((i) => {
        if (values.indexOf(i[idKey]) > -1) {
          i.isGqAddChecked = true;
        } else {
          i.isGqAddChecked = false;
        }
        if (i[childKey] && i[childKey].length > 0) {
          this.deepCheckValue(i[childKey], values, idKey, childKey);
        }
        return i;
      });
    },
    schoolShowHandle() {
      const schoolData = uni.getStorageSync("schoolData");
      const checkedSchool = uni.getStorageSync("checkedSchool");
      if (schoolData && checkedSchool) {
        const idKeysResult = checkedSchool.map((x) => {
          return x.id;
        });
        const options = JSON.parse(JSON.stringify(schoolData));
        options.forEach((parent) => {
          if (parent.child) {
            // eslint-disable-next-line array-callback-return
            parent.child.map((item) => {
              item.pid = parent.id;
            });
          }
        });
        const newOptions = this.deepCheckValue(
          options,
          idKeysResult,
          "id",
          "child"
        );
        this.range = [...newOptions];
        this.$refs.gqTree._initTree();
        this.$refs.gqTree._show();
      } else {
        this.$refs.uToast.show({
          type: "error",
          message: "获取缓存校区失败，请退出重新登录！"
        });
      }
    },
    treeCancel(e) {
      this.$refs.gqTree._hide();
    },
    treeConfirm(e) {
      if (!e.length) {
        this.$refs.uToast.show({
          type: "info",
          message: "请至少选择一个校区！"
        });
        return;
      }
      const arr = [];
      e.forEach((item) => {
        // 有value值的是校区
        if (item.value) {
          arr.push({
            id: item.id,
            name: item.name
          });
        }
      });
      uni.setStorageSync("checkedSchool", arr);
      this.department_name = arr[0].name;
      this.department_id = arr[0].id;
      this.dtbMenuPermission();
    },
    switchClient() {
      uni.setStorageSync("teacher_tabIndex", 0);
      uni.redirectTo({
        url: "/pages/index/index"
      });
    },
    switchDtbQrcode() {
      const office_post_name1 = this.office_post_list.find(
        (i) => i.department_name === this.department_name
      );
      const office_post_name = office_post_name1
        ? office_post_name1.office_post_name
        : "";
      console.log(
        this.office_post_list,
        this.department_name,
        office_post_name1,
        office_post_name
      );
      uni.navigateTo({
        url: `/pages/teacher/subpages/channel/index?department_name=${this.department_name}&department_id=${this.department_id}&name=${this.userInfo.name}&mobile=${this.userInfo.mobile}&office_post_name=${office_post_name}`
      });
    },
    // 查询地推宝二维码校区权限
    dtbMenuPermission() {
      checkPower({
        api_url: "/api/school-service/miniprogram/dtb_qrcode",
        method: "GET"
      }).then((res) => {
        uni.hideToast();
        const { code } = res;
        this.hasDtbPermission = false;
        if (code === 0) {
          this.hasDtbPermission = true;
        }
      });
    }
  },
  watch: {},

  // 页面周期函数--监听页面加载
  onLoad() {
    const tabIndex = uni.getStorageSync("teacher_tabIndex");
    this.tabIndex = tabIndex || 0;
    this.getUserInfo();
    const checkedSchool = uni.getStorageSync("checkedSchool");
    if (checkedSchool) {
      this.department_name = checkedSchool[0].name;
      this.department_id = checkedSchool[0].id;
      this.dtbMenuPermission();
    } else {
      this.$refs.uToast.show({
        type: "error",
        message: "获取缓存校区失败，请退出重新登录！"
      });
    }
  },
  // 页面周期函数--监听页面初次渲染完成
  onReady() {},
  // 页面周期函数--监听页面显示(not-nvue)
  onShow() {},
  // 页面周期函数--监听页面隐藏
  onHide() {},
  // 页面周期函数--监听页面卸载
  onUnload() {}
  // 页面处理函数--监听用户下拉动作
  // onPullDownRefresh() { uni.stopPullDownRefresh(); },
  // 页面处理函数--监听用户上拉触底
  // onReachBottom() {},
  // 页面处理函数--监听页面滚动(not-nvue)
  // onPageScroll(event) {},
  // 页面处理函数--用户点击右上角分享
  // onShareAppMessage(options) {},
};
</script>

<style lang="scss" scoped>
.tg_mine {
  background: #f5f6fa;
  height: 100vh;
  width: 100vw;
  overflow-x: hidden;
  position: relative;
  .navbar {
    position: absolute;
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 40rpx;
  }

  .header-left {
    width: 40rpx;
    height: 40rpx;
  }

  .center-title {
    color: #fff;
    font-size: 36rpx;
  }
  .tg-top-bg {
    border-radius: 50%;
    background: linear-gradient(180deg, #2956ef 0%, #69a2f8 100%);
    width: 1096rpx;
    height: 550rpx;
    position: absolute;
    left: 50%;
    top: 0rpx;
    transform: translate(-50%, -100rpx);
    z-index: 1;
  }
  // ::v-deep .u-skeleton {
  //   position: absolute;
  //   left: 50%;
  //   top: 70px;
  //   z-index: 2;
  // }
  .user-container {
    position: relative;
    top: 84px;
    padding: 24rpx;
    font-size: 28rpx;
    width: 100%;
    z-index: 1;
    padding-bottom: 80px;
  }

  .header {
    text-align: left;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    margin-left: 40rpx;
    margin-bottom: 30rpx;
  }

  .avatar {
    width: 128rpx;
    height: 128rpx;
    flex-shrink: 0;
    border-radius: 128rpx;
    // border: 5rpx solid #fff;
    // background: linear-gradient(180deg, #e9eef8 0%, #bec7de 100%);
    // box-shadow: 0px 6rpx 12rpx 0rpx rgba(0, 0, 0, 0.1);
    margin-right: 24rpx;
  }
  .info {
    margin-bottom: 20rpx;
  }
  .name {
    color: #333;
    font-size: 40rpx;
    font-weight: bold;
    margin-bottom: 10rpx;
    display: flex;
    width: 100%;
    align-items: center;
  }

  .desc {
    font-size: 26rpx;
    border-radius: 18rpx;
    background: #fff;
    height: 36rpx;
    line-height: 36rpx;
    padding: 0 16rpx;
    width: initial;
  }

  .info-block {
    width: 700rpx;
    border-radius: 10rpx;
    margin-bottom: 16rpx;
    border-radius: 8rpx;
    background-color: #fff;
    box-shadow: 0px 4rpx 8rpx 0px rgba(0, 0, 0, 0.05);
  }

  .info-item {
    padding: 30rpx 40rpx;
    border-bottom: 1px solid #eaeaea;
    font-size: 32rpx;
    .cell-row {
      display: flex;
      align-items: center;
      .label {
        margin-left: 16rpx;
      }
    }
    .summary {
      padding-left: 56rpx;
      margin-top: 10rpx;
    }
    .part-summary {
      padding-top: 20rpx;
      span {
        font-size: 26rpx;
        border-radius: 18rpx;
        background: #fff;
        height: 36rpx;
        line-height: 36rpx;
        width: initial;
        margin-right: 20rpx;
      }
    }
  }

  .action-block {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 700rpx;
    height: 96rpx;
    background: #fff;
    box-shadow: 0px 4rpx 8rpx 0px rgba(0, 0, 0, 0.05);
    border-radius: 8rpx;
    font-size: 32rpx;
    padding: 0 40rpx;
    margin-bottom: 16rpx;
    &:active {
      opacity: 0.8;
    }
  }
  .login-out-btn {
    margin: 0 auto;
    width: 700rpx;
    left: 25rpx;
    margin-top: 60rpx;
  }
}
</style>
