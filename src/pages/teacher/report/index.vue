<template>
  <div class="tg-reprot">
    <view class="tg-top-bg"></view>
    <u-navbar
      bgColor="transparent"
      title="报表"
      :placeholder="true"
      titleStyle="color: #fff;font-size:36rpx;font-weight:500"
    >
      <div class="u-nav-slot" slot="left"></div>
    </u-navbar>

    <TabBar :tabIndex="tabIndex"></TabBar>
  </div>
</template>

<script>
import TabBar from "../components/tab-bar/index.vue";
export default {
  name: "reportIndex",
  components: { TabBar },
  data() {
    return {
      tabIndex: 0
    };
  },
  computed: {},
  methods: {},
  watch: {},

  // 页面周期函数--监听页面加载
  onLoad() {
    const tabIndex = uni.getStorageSync("teacher_tabIndex");
    console.log("tabIndex :>> ", tabIndex);
    this.tabIndex = tabIndex || 0;
  },
  // 页面周期函数--监听页面初次渲染完成
  onReady() {},
  // 页面周期函数--监听页面显示(not-nvue)
  onShow() {},
  // 页面周期函数--监听页面隐藏
  onHide() {},
  // 页面周期函数--监听页面卸载
  onUnload() {}
  // 页面处理函数--监听用户下拉动作
  // onPullDownRefresh() { uni.stopPullDownRefresh(); },
  // 页面处理函数--监听用户上拉触底
  // onReachBottom() {},
  // 页面处理函数--监听页面滚动(not-nvue)
  // onPageScroll(event) {},
  // 页面处理函数--用户点击右上角分享
  // onShareAppMessage(options) {},
};
</script>

<style lang="scss" scoped>
</style>
