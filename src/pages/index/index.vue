<template>
  <div class="choose-content">
    <!-- <u-loading-page
      :loading="true"
      loading-color="#DADADA"
      loadingMode="spinner"
      bgColor="#fff"
    ></u-loading-page> -->
    <u-toast ref="uToast"></u-toast>

    <div class="words">
      <!-- <u--image
        width="416rpx"
        height="109rpx"
        :showMenuByLongpress="false"
        src="https://tg-prod.oss-cn-beijing.aliyuncs.com/940a6560-6249-45d9-8850-2a5cab30c9eb.webp"
      ></u--image> -->
      <view class="words-title"> 你的身份是? </view>
      <view class="words-desc"> 我们会根据您的身份来呈现不同内容 </view>
    </div>
    <div
      @tap="choose('teacher')"
      :class="{ checked: type === 'teacher' }"
      class="switch card-box teacher"
    >
      <div class="card-box-img"></div>
      <div class="card-box-text">
        <div class="card-box-text-title">我是教师</div>
        <div
          :class="{ checked: type === 'teacher' }"
          class="card-box-icon"
        ></div>
      </div>
    </div>
    <div
      @tap="choose('student')"
      :class="{ checked: type === 'student' }"
      class="switch card-box student"
    >
      <div class="card-box-img"></div>
      <div class="card-box-text">
        <div class="card-box-text-title">我是家长</div>
        <div
          :class="{ checked: type === 'student' }"
          class="card-box-icon"
        ></div>
      </div>
    </div>
    <button
      open-type="getPhoneNumber"
      @getphonenumber="onGetPhoneNumber"
      class="go-arrow"
      v-if="is_fist_login && type === 'student'"
    >
      <!-- <u--image
        width="380rpx"
        height="100rpx"
        src="https://tg-prod.oss-cn-beijing.aliyuncs.com/a131051a-3a96-4395-8fe4-26bba362adef.webp"
      ></u--image> -->
      <div class="go-arrow-img">
        <div class="go-arrow-text">
          <div class="go-arrow-text-title">立即进入</div>
        </div>
      </div>
    </button>
    <view @tap="$u.throttle(goClient, 500)" class="go-arrow" v-else>
      <!-- <u--image
        width="380rpx"
        height="100rpx"
        src="https://tg-prod.oss-cn-beijing.aliyuncs.com/a131051a-3a96-4395-8fe4-26bba362adef.webp"
      ></u--image> -->
      <div class="go-arrow-img">
        <div class="go-arrow-text">
          <div class="go-arrow-text-title">立即进入</div>
        </div>
      </div>
    </view>
  </div>
</template>

<script>
import {
  getOpenId,
  miniprogramLogin,
  getPermission,
  getSchoolData
} from "@/services/login";
import {
  login,
  getPhoneByCode,
  loginByPhone,
  bindVisitor
} from "@/services/student/home";
import { rowIds } from "../student/my/config/index";
export default {
  name: "ChooseIndex",
  data() {
    return {
      type: "",
      is_fist_login: false,
      rowIds
    };
  },
  components: {},
  onLoad() {
    // 获取用户的openid
    // this.getUserOpenid();
  },
  onShow() {
    // eslint-disable-next-line no-undef
    getApp().globalData.tabIndex = 0;
    this.arouseLogin("on");
  },
  methods: {
    // 选择教师免登录
    openidTeacherLogin() {
      uni.login({
        provider: "weixin",
        onlyAuthorize: true,
        success: (event) => {
          const { code } = event;
          getOpenId({
            code,
            UNAUTHORIZED: true // 不需要token校验
          })
            .then((res) => {
              const { code, data } = res;
              if (code === 0 && data) {
                uni.setStorageSync("openid", data.openid);
                this.teacherLogin();
              } else {
                uni.hideToast();
                this.$refs.uToast.show({
                  type: "error",
                  message: "获取openid失败！"
                });
              }
            })
            .catch(() => {
              uni.hideToast();
              this.$refs.uToast.show({
                type: "error",
                message: "获取openid失败！"
              });
            });
        }
      });
    },
    getSchool() {
      return new Promise((resolve, reject) => {
        getSchoolData()
          .then((res) => {
            resolve(res);
          })
          .catch((err) => {
            reject(err);
          });
      });
    },
    getUserPermission() {
      return new Promise((resolve, reject) => {
        getPermission()
          .then((res) => {
            resolve(res);
          })
          .catch((err) => {
            reject(err);
          });
      });
    },
    setSingSchool(school_data) {
      if (school_data) {
        if (school_data[0].child && school_data[0].child.length === 1) {
          uni.setStorageSync("singSchoolData", school_data[0].child[0]);
        }
      }
    },
    toTeacherHome() {
      uni.redirectTo({ url: "/pages/teacher/sort/index" });
    },
    toTeacherLoginPage() {
      uni.navigateTo({ url: "/pages/teacher/login/index" });
    },
    teacherLogin() {
      const open_id = uni.getStorageSync("openid") || "";
      console.log(open_id);
      if (open_id) {
        // 教师免登录
        uni.showLoading({
          title: "加载中..."
        });
        miniprogramLogin({
          login_type: "miniprogram",
          open_id,
          UNAUTHORIZED: true // 不需要token校验
        })
          .then((res) => {
            uni.hideToast();
            if (res.code !== 0) {
              this.toTeacherLoginPage();
              return;
            }
            const { token, full_office_post } = res.data;
            uni.setStorageSync("token", token);
            uni.setStorageSync("tokenTime", new Date().getTime());
            uni.setStorageSync("user", res.data);
            const { getUserPermission, getSchool } = this;
            Promise.all([getUserPermission(), getSchool()])
              .then((result) => {
                uni.hideToast();
                uni.setStorageSync("permission", result[0]);
                uni.setStorageSync("schoolData", result[1].school_data);
                // const checkedSchool = uni.getStorageSync("checkedSchool");
                const arr = [];
                // if (!checkedSchool) {
                result[1].school_data.forEach((item1) => {
                  item1.child.forEach((item2) => {
                    const { id, name } = item2;
                    arr.push({
                      id,
                      name
                    });
                  });
                });
                // 多校区时，默认设置第一个
                const { department_id, department_name, department_type } =
                  full_office_post;
                const defaultSchool = {
                  id: department_id,
                  name: department_name
                };
                const { id, name } = result[1].school_data[0].child[0];
                const firstSchool = { id, name };
                console.log(
                  result[1].school_data[0].child[0],
                  "result[1].school_data-----index"
                );
                uni.setStorageSync(
                  "checkedSchool",
                  department_type !== "school" ? [firstSchool] : [defaultSchool]
                );
                // 设置单校区数据
                this.setSingSchool(result[1].school_data);
                // }
                setTimeout(() => {
                  this.toTeacherHome();
                }, 1000);
              })
              .catch((err) => {
                console.log("err :>> ", err);
                uni.hideToast();
                this.$ljsPublic.msg.msg_success("登录失败！");
              });
          })
          .catch((err) => {
            console.log("err :>> ", err);
            uni.hideLoading();
            uni.hideToast();
            this.toTeacherLoginPage();
          });
      }
    },
    choose(val) {
      this.type = val;
    },
    async arouseLogin(type = "default") {
      if (type !== "on") {
        uni.showLoading({
          title: "登录中..."
        });
      }
      uni.login({
        provider: "weixin",
        onlyAuthorize: true, // 微信登录仅请求授权认证
        success: async (event) => {
          const { code } = event;
          const res = await getOpenId({ code, UNAUTHORIZED: true });
          if (res.code === 0) {
            // is_fist_login 是否为第一次进入小程序
            this.is_fist_login = res.data.is_fist_login;
            if (this.is_fist_login) {
              uni.hideLoading();
              return;
            }
            const res2 = await login({
              open_id: res.data.openid,
              UNAUTHORIZED: true
            });
            if (res2.code === 0) {
              uni.hideLoading();
              const role =
                !res2.data.is_student && !res2.data.is_customer
                  ? "default"
                  : res2.data.is_student
                  ? "student"
                  : "customer";
              this.role = role;
              uni.setStorageSync("session", { ...res2.data, role });
              if (type === "on") return;
              if (res.data.is_fist_login) {
                uni.navigateTo({ url: "/pages/student/login/index" });
                return;
              }
              if (role === "default") {
                const bindRes = await bindVisitor({
                  open_id: res.data.openid,
                  UNAUTHORIZED: true
                });
                if (
                  bindRes.code === 0 &&
                  bindRes.data &&
                  bindRes.data.length > 0
                ) {
                  uni.navigateTo({
                    url: "/pages/student/studentPage/index?prospective=login"
                  });
                  return;
                }
              }
              uni.switchTab({ url: "/pages/student/home/<USER>" });
              // this.getCheckedStudentInfo(res2.data.open_id, role);
            } else {
              uni.hideLoading();
              // uni.navigateTo({ url: "/pages/student/login/index" });
              uni.showToast({
                title: res2.message,
                icon: "none"
              });
            }
          }
        },
        fail: () => {
          uni.hideLoading();
        }
      });
    },
    async goClient(e) {
      if (this.type === "teacher") {
        uni.setStorageSync("portType", "TEACHER");
        this.openidTeacherLogin();
      } else if (this.type === "student") {
        // uni./course/index" });
        uni.setStorageSync("portType", "STUDENT");
        // uni.switchTab({ url: "/pages/student/my/index" });
        // uni.navigateTo({ url: "/pages/student/login/index" });
        await this.arouseLogin();
        // uni./my/index" });
        // uni./subpages/coupon/index" });
        // uni./subpages/studyReport/index" });

        // uni./subpages/studyReport/detail" });
      } else {
        this.$refs.uToast.show({
          type: "warning",
          message: "请选择您的身份！"
        });
      }
    },
    async onGetPhoneNumber(e) {
      console.log("🚀 ~ onGetPhoneNumber ~ e:", e);

      // 用户拒绝授权
      if (e.detail.errMsg !== "getPhoneNumber:ok") {
        uni.navigateTo({
          url: "/pages/student/login/index?type=input"
        });
        // uni.showToast({
        //   title: "请先授权手机号",
        //   icon: "none"
        // });
        return;
      }
      uni.showLoading({
        title: "登录中..."
      });
      this.loading = true;
      getPhoneByCode({
        code: e.detail.code
      }).then((response) => {
        console.log("🚀 ~ getPhoneByCode ~ res:", response);
        if (response.code === 0) {
          uni.login({
            provider: "weixin",
            success: async (loginRes) => {
              if (!loginRes.code) {
                this.loading = false;
                // this.phoneType = "input";
                return;
              }
              const { code } = loginRes;
              const res = await getOpenId({
                code,
                UNAUTHORIZED: true
              });
              if (res.code === 0) {
                const res2 = await loginByPhone({
                  open_id: res.data.openid,
                  mobile: response.data.phoneNumber,
                  UNAUTHORIZED: true
                });
                if (res2.code === 0) {
                  uni.hideLoading();
                  console.log(
                    "🚀 ~ res2:",
                    !res2.data.is_student && !res2.data.is_customer
                  );
                  const role =
                    !res2.data.is_student && !res2.data.is_customer
                      ? "default"
                      : res2.data.is_student
                      ? "student"
                      : "customer";
                  console.log("🚀 ~ role:", role);
                  this.role = role;
                  const session = { ...res2.data, role };
                  uni.setStorageSync("session", session);
                  uni.setStorageSync("portType", "STUDENT");
                  this.session = session;
                  this.role = this.session.role;
                  this.rowIdField = this.rowIds[this.role];
                  console.log(this.session, "session");
                  // this.curCheckedStudent = uni.getStorageSync("curStudentInfo");
                  if (role === "default") {
                    const bindRes = await bindVisitor({
                      open_id: res.data.openid,
                      UNAUTHORIZED: true
                    });
                    if (
                      bindRes.code === 0 &&
                      bindRes.data &&
                      bindRes.data.length > 0
                    ) {
                      uni.navigateTo({
                        url: "/pages/student/studentPage/index?prospective=login"
                      });
                      return;
                    }
                  }
                  uni.switchTab({ url: "/pages/student/home/<USER>" });
                } else {
                  uni.hideLoading();
                  uni.showToast({
                    title: "登录失败，请重试",
                    icon: "none"
                  });
                  this.loading = false;
                }
              }
            },
            fail: () => {
              uni.hideLoading();
              uni.showToast({
                title: "登录失败，请重试",
                icon: "none"
              });
              this.loading = false;
            }
          });
        } else {
          uni.hideLoading();
          uni.showToast({
            title: "登录失败，请重试",
            icon: "none"
          });
          this.loading = false;
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.choose-content {
  height: 100vh;
  width: 100%;
  background-image: url("https://tg-prod.oss-cn-beijing.aliyuncs.com/17d12195-6c7c-4d03-ad5e-8c0491a9701e.webp");
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;
  padding: 0 48rpx;
  .words {
    padding-top: 270rpx;
    margin-bottom: 160rpx;
    // padding-left: 20rpx;
    padding-right: 20rpx;
    .words-title {
      color: #333;
      font-size: 44rpx;
      font-style: normal;
      font-weight: 600;
      line-height: 56rpx;
      margin-bottom: 10rpx;
    }
    .words-desc {
      color: #979797;
      font-size: 26rpx;
      font-style: normal;
      font-weight: 400;
      line-height: 39rpx;
    }
  }
  .card-box {
    position: relative;
    width: 654rpx;
    height: 198rpx;
    border-radius: 20rpx;
    background: linear-gradient(270deg, #fffbee 12.01%, #ffdc7d 113.39%);
    .card-box-img {
      position: absolute;
      bottom: 0;
      left: 40rpx;
      background: url("https://tg-prod.oss-cn-beijing.aliyuncs.com/909893b3-dc36-4b48-84e8-b361265ec5f0.webp")
        no-repeat center center;
      background-size: cover;
      width: 186rpx;
      height: 247rpx;
    }
    .card-box-text {
      position: absolute;
      top: 80rpx;
      right: 46rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      .card-box-text-title {
        font-size: 34rpx;
        font-weight: 500;
        color: #333333;
      }
      .card-box-icon {
        width: 32rpx;
        height: 32rpx;
        background: url("https://tg-prod.oss-cn-beijing.aliyuncs.com/acd1dce7-2379-496e-b4a2-e5fe9e6d59ba.webp")
          no-repeat center center;
        background-size: cover;
        margin-left: 18rpx;
        &.checked {
          background: url("https://tg-prod.oss-cn-beijing.aliyuncs.com/2c262392-ea00-43b1-b4fc-27c07d66ece1.webp")
            no-repeat center center;
          background-size: cover;
        }
      }
    }
    &.student {
      margin-top: 100rpx;
      .card-box-img {
        background: url("https://tg-prod.oss-cn-beijing.aliyuncs.com/31c38e7b-3a7a-444d-86ec-0a1eb61b7900.webp")
          no-repeat center center;
        width: 204rpx;
        height: 256rpx;
        background-size: cover;
      }
    }
  }
  @keyframes scale {
    0% {
      transform: scale(1.05);
    }
    100% {
      transform: scale(1);
    }
  }
  .switch {
    // margin: 0 auto;
    position: relative;
    margin-bottom: 30rpx;
    &.checked {
      animation: scale 0.7s linear;
      border: 1px solid #ffc525;
    }
  }
  .go-arrow {
    position: relative;
    display: flex;
    justify-content: center;
    margin-top: 201rpx;
    color: #fff;
    text-align: center;
    font-size: 36rpx;
    font-style: normal;
    font-weight: 400;
    padding-bottom: 40rpx;
    .go-arrow-img {
      width: 380rpx;
      height: 100rpx;
      border-radius: 71rpx;
      background: linear-gradient(15deg, #ffbf0d 18.1%, #ffcb3c 83.29%);
      box-shadow: 0px -10rpx 18rpx 0px #f3b300 inset,
        0px 4rpx 20rpx 0rpx rgba(254, 197, 36, 0.47);
    }
    .go-arrow-text {
      position: absolute;
      top: 30rpx;
      left: 50%;
      transform: translateX(-50%);
      .go-arrow-text-title {
        font-size: 34rpx;
        font-weight: 500;
        color: #fff;
      }
    }
    &:active {
      transform: scale(0.9);
      transition: all 0.3s;
      opacity: 0.8;
    }
  }
}
button {
  background: none;
  border: none;
  padding: 0;
  margin: 0;
  &::after {
    border: none;
  }
}
</style>
