import Vue from "vue";
import App from "./App";
import "./uni.promisify.adaptor";
import Oss from "./utils/oss";
// import "./uni_modules/lq-tool/js_sdk/index.js";

import ljsPublic from "./uni_modules/ljs-sdk-public/js_sdk/index.js";
import { hasPermission } from "./frontPermise";
import "./style/global.scss";
import "./style/sprite.scss";
// main.js，注意要在use方法之后执行
import uView from "uview-ui";
import filters from "./utils/filters";
const mpShare = require("uview-ui/libs/mixin/mpShare.js");
Vue.mixin(mpShare);
Vue.prototype.$ljsPublic = ljsPublic;
Vue.prototype.$hasPermission = hasPermission;
Vue.use(uView);

// 注册全局过滤器
Object.keys(filters).forEach((key) => {
  Vue.filter(key, filters[key]);
});
console.log(Oss);
Vue.use(Oss);
// uni.$u.config.unit = "rpx";

Vue.config.productionTip = false;
uni.setEnableDebug({
  enableDebug: process.env.NODE_ENV === "development"
});
App.mpType = "app";

const app = new Vue({
  ...App
});
app.$mount();
