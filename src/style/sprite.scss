/* 雪碧图的一些样式 */
.tg_sprite_01 {
  background-image: url('https://tg-prod.oss-cn-beijing.aliyuncs.com/2989ac7b-90a0-410a-a582-ff25b3b745a9.png');
  // width: 1920rpx;
  // height: 1624rpx;
  display: block;
  background-size: 960rpx 812rpx;
  background-repeat: no-repeat;
  &.tg_bottom_tab {
    width: 48rpx;
    height: 48rpx;
  }
  &.tg_menu_home {
    background-position: 0 0;
    &.active {
      background-position: 0 -58rpx;
    }
  }
  &.tg_menu_sort {
    background-position: -58rpx 0;
    &.active {
      background-position: -58rpx -58rpx;
    }
  }
  &.tg_menu_report {
    background-position: -116rpx 0;
    &.active {
      background-position: -116rpx -58rpx;
    }
  }
  &.tg_menu_usercenter {
    background-position: -174rpx 0;
    &.active {
      background-position: -174rpx -58rpx;
    }
  }
  &.tg_login_acc {
    width: 36rpx;
    height: 36rpx;
    background-position: -100rpx -430rpx;
  }
  &.tg_login_lock {
    width: 36rpx;
    height: 36rpx;
    background-position: -146rpx -430rpx;
  }
  &.tg_login_browse {
    width: 50rpx;
    height: 50rpx;
    background-position: -50rpx -430rpx;
    &.dis {
      background-position: 0 -430rpx;
    }
  }
  &.tg_home_shortcut {
    width: 32rpx;
    height: 32rpx;
    &.tg-intention {
      background-position: 0 -116rpx;
      &.active {
        background-position: 0rpx -158rpx;
      }
    }
    &.tg-communication {
      background-position: -40rpx -116rpx;
      &.active {
        background-position: -40rpx -158rpx;
      }
    }
    &.tg-charge {
      background-position: -82rpx -116rpx;
      &.active {
        background-position: -82rpx -158rpx;
      }
    }
  }
  &.tg_mine_cell {
    width: 42rpx;
    height: 42rpx;
    &.tg-phone {
      background-position: 0 -272rpx;
    }
    &.tg-post {
      background-position: -50rpx -272rpx;
    }
    &.tg-casual {
      background-position: -100rpx -272rpx;
    }
  }
}
