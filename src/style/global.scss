.text-primary {
  color: #FFBF0D;
}
.text-success {
  color: #5fb446;
}
.text-warn {
  color: #ec8131;
}
.text-danger {
  color: #de1c24;
}
.text-title {
  color: #282828;
}
.text-normal {
  color: #5d5d5d;
}
.text-small {
  color: #9b9b9b;
}
.text-minor {
  color: #ececec;
}
.text-border {
  color: #eeeeee;
}
.text-white {
  color: #fff;
}
.bg-primary {
  background-color: #FFBF0D;
  color: #fff;
}
.bg-primary-gradient {
  background: linear-gradient(270deg, #3667f0 0%, #568ff5 100%) !important;
}
.bg-success {
  background-color: #5fb446;
  color: #fff;
}
.bg-warn {
  background-color: #ec8131;
  color: #fff;
}
.bg-danger {
  background-color: #de1c24;
  color: #fff;
}
.bg-title {
  background-color: #282828;
  color: #fff;
}
.bg-normal {
  background-color: #5d5d5d;
  color: #282828;
}
.bg-small {
  background-color: #9b9b9b;
  color: #5d5d5d;
}
.bg-minor {
  background-color: #ececec;
  color: #5d5d5d;
}
.bg-border {
  background-color: #eeeeee;
  color: #5d5d5d;
}
.bd-primary {
  color: #fa550f;
}
.bd-success {
  color: #5fb446;
}
.bd-warn {
  color: #ec8131;
}
.bd-danger {
  color: #de1c24;
}
.bd-title {
  color: #282828;
}
.bd-normal {
  color: #5d5d5d;
}
.bd-small {
  color: #9b9b9b;
}
.bd-minor {
  color: #ececec;
}
.bd-border {
  color: #eeeeee;
}
.ft-super {
  font-size: 40rpx;
}
.ft-main {
  font-size: 36rpx;
}
.ft-normal {
  font-size: 32rpx;
}
.ft-assist {
  font-size: 28rpx;
}
.ft-minor {
  font-size: 24rpx;
}
.ft-mini {
  font-size: 20rpx;
}
.fw-super {
  font-weight: 800;
}
.fw-main {
  font-weight: 600;
}
.fw-normal {
  font-weight: 400;
}
.fw-minor {
  font-weight: 300;
}
.mo-border-1rpx {
  position: relative;
  z-index: 0;
}

view,
image,
icon,
scroll-view,
text,
button,
checkbox,
form,
input,
label,
navigator,
audio,
video,
canvas {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  border: 0;
  outline: 0;
}
scroll-view {
  height: 100%;
}
form,
image {
  display: block;
}
button {
  padding: 0;
  margin: 0;
  border-radius: 0;
  height: 100%;
  display: block;
  line-height: inherit;
  font-size: inherit;
  color: inherit;
  background: none;
  -webkit-appearance: none;
  border: none;
}
button::after {
  /* content: "";
  background-color: #FFBF0D!important; */
}
input,
textarea {
  font-family: 'Microsoft YaHei', '微软雅黑', 'MicrosoftJhengHei', '华文细黑', Helvetica, Arial, 'sans-serif';
  font-size: 26rpx;
  z-index: 0;
}
.price {
  color: #ec8131;
  font-size: 32rpx;
  font-weight: 600;
}
.price-del {
  color: #9b9b9b;
  font-size: 24rpx;
  font-weight: 400;
}
.page {
  background: #fff;
}
.color-price {
  color: #ec8131;
}
.bg-cart {
  background-color: #ffc220;
  color: #fff;
}
.market-addcart {
  color: #ec8131;
  font-size: 42rpx;
}
.ovh {
  overflow: hidden;
}
.hidden {
  display: none;
}
.show {
  display: block;
}
.text {
  display: inline-block;
}
.inline {
  display: inline;
}
.minHeight {
  min-height: 101%;
}
.imgCover {
  width: 100%;
  padding-bottom: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  display: block;
  position: relative;
}

.circular {
  border-radius: 50%;
}
.text-line1 {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  word-break: break-all;
}
.text-line-1 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  word-break: break-all;
}
.text-line2 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  word-break: break-all;
}
.text-line3 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  word-break: break-all;
}
.flex,
.box {
  display: flex;
  display: -webkit-flex;
}
.flex-v-center {
  align-items: center;
  -webkit-align-items: center;
}
.flex-center {
  justify-content: center;
  -webkit-justify-content: center;
  align-items: center;
  -webkit-align-items: center;
}
.flex-between {
  justify-content: space-between;
  -webkit-justify-content: space-between;
}
.flex-around {
  justify-content: space-around;
  -webkit-justify-content: space-around;
}
.flex-v-between {
  align-content: space-between;
  -webkit-align-content: space-between;
}
.flex-end {
  justify-content: flex-end;
  -webkit-justify-content: flex-end;
}
.flex-col {
  flex-direction: column;
  -webkit-flex-direction: column;
}
.flex1 {
  flex: 1;
  -webkit-flex: 1;
}
.flex0 {
  flex: none;
  -webkit-flex: none;
}
.flex-start {
  justify-content: flex-start;
  -webkit-justify-content: flex-start;
}
.border-around,
.border-bottom-1px,
.border-left-1px,
.border-right-1px,
.border-top-1px {
  position: relative;
  border: 1rpx solid #D3DCE6;
}
.border-top-1px {
  border-width: 1rpx 0 0 0;
}
.border-right-1px {
  border-width: 0 1rpx 0 0;
}
.border-bottom-1px {
  border-width: 0 0 1rpx 0;
}
.border-left-1px {
  border-width: 0 0 0 1rpx;
}
.border-t-1px,
.border-r-1px,
.border-b-1px,
.border-l-1px {
  position: relative;
}
.border-t-1px::after,
.border-r-1px::after,
.border-b-1px::after,
.border-l-1px::after {
  content: '';
  position: absolute;
  border-width: 2rpx;
  border-color: #efefef;
  border-style: solid;
}
.border-t-1px::after {
  left: 0;
  top: 0;
  width: 100%;
  transform: scaleY(0.5);
}
.border-b-1px::after {
  left: 0;
  bottom: 0;
  width: 100%;
  transform: scaleY(0.5);
}
.border-l-1px::after {
  left: 0;
  top: 0;
  height: 100%;
  transform: scaleX(0.5);
}
.border-r-1px::after {
  right: 0;
  top: 0;
  height: 100%;
  transform: scaleX(0.5);
}
.arrows {
  position: relative;
}
.arrows::after {
  content: '';
  display: inline-block;
  width: 6px;
  height: 6px;
  border: 2px solid #c3c3c3;
  border-width: 2px 2px 0 0;
  position: absolute;
  top: 50%;
  right: 26rpx;
  margin-top: -3px;
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
}
.arrows-inline::after {
  position: relative;
  left: auto;
  top: auto;
  right: auto;
  bottom: auto;
  margin-top: -4px;
  margin-left: 5px;
}
.pt-16,
.pt-8 {
  padding-top: 16rpx;
}
.pb-16,
.pb-8 {
  padding-bottom: 16rpx;
}
.pl-16,
.pl-8 {
  padding-left: 16rpx;
}
.pr-16,
.pr-8 {
  padding-right: 16rpx;
}
.pl-20,
.pl-10 {
  padding-left: 20rpx;
}
.pr-20,
.pr-10 {
  padding-right: 20rpx;
}
.pl-30 {
  padding-left: 30rpx;
}
.pr-30 {
  padding-right: 30rpx;
}
.pl-32,
.pl-15 {
  padding-left: 32rpx;
}
.pr-32,
.pr-15 {
  padding-right: 32rpx;
}
.pb360 {
  padding-bottom: 360rpx;
}
.PriceSwitch {
  line-height: 21px;
  font-size: 24rpx;
  padding: 0 8rpx;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: normal;
  color: #1a1a1a;
  margin: 0;
  padding: 0;
}
.color-3 {
  color: #333;
}
.color-6 {
  color: #666;
}
.color-9 {
  color: #999;
}
.color-e9 {
  color: #e9e9e9;
}
.color-64 {
  color: #646464;
}
.color-b4 {
  color: #b4b4b4;
}
.color-97 {
  color: #979797;
}
.color-9b {
  color: #9b9b9b;
}
.color-white {
  color: #fff;
}
.color-black {
  color: #000;
}
.color-c {
  color: #ccc;
}
.bkg-white {
  background-color: #fff;
}
.font10 {
  font-size: 20rpx;
}
.font11 {
  font-size: 22rpx;
}
.font12 {
  font-size: 24rpx;
}
.font13 {
  font-size: 26rpx;
}
.font14 {
  font-size: 28rpx;
}
.font15 {
  font-size: 30rpx;
}
.font16 {
  font-size: 32rpx;
}
.font17 {
  font-size: 34rpx;
}
.font18 {
  font-size: 36rpx;
}
.font19 {
  font-size: 38rpx;
}
.font20 {
  font-size: 20rpx;
}
.font22 {
  font-size: 22rpx;
}
.font24 {
  font-size: 24rpx;
}
.font26 {
  font-size: 26rpx;
}
.font28 {
  font-size: 28rpx;
}
.font30 {
  font-size: 30rpx;
}
.font32 {
  font-size: 32rpx;
}
.font34 {
  font-size: 34rpx;
}
.font36 {
  font-size: 36rpx;
}
.font38 {
  font-size: 38rpx;
}
.font40 {
  font-size: 40rpx;
}
.font46 {
  font-size: 46rpx;
}
.font50 {
  font-size: 50rpx;
}
.font56 {
  font-size: 56rpx;
}
.font82 {
  font-size: 82rpx;
}
.bkg-white {
  background-color: #fff;
}
.fontWeight-l {
  font-weight: 500;
}
.fontWeight-n {
  font-weight: 300;
}
.fontWeight-nor {
  font-weight: normal;
}
.line-height1 {
  line-height: 1;
}

.btn-dis {
  background-color: #999;
  color: #bbb;
}
.btn-dashed-dis {
  border: 1px solid #c8c8c8;
  color: #bbbbbb;
  border-radius: 8rpx;
}
/* 顶部状态栏安全高度 */
.status_bar {
  height: var(--status-bar-height);
  width: 100%;
}
/* 如果存在安全区域则添加内边距 */
.bottom-safe-area {
  height: 0;
  /* 根据实际安全区域高度调整 */
  padding-bottom: env(safe-area-inset-bottom);
}
.tg-top-bg {
  border-radius: 50%;
  background: linear-gradient(180deg, #2956ef 0%, #69a2f8 100%);
  width: 1096rpx;
  height: 480rpx;
  position: fixed;
  left: 50%;
  top: 0rpx;
  transform: translate(-50%, -100rpx);
}