<script>
import updateManager from "@/utils/updateManager";

export default {
  methods: {
    // 腾讯电子签页面回调
    handleSignCallback() {
      uni.onAppShow((res) => {
        if (
          res.referrerInfo &&
          res.referrerInfo.appId === "wxa023b292fd19d41d"
        ) {
          console.log("handleSignCallback2", res);
          const pages = uni.$u.pages();
          // 获取最后一个currentPage元素
          const lastPage = pages[pages.length - 1];
          lastPage.$vm.loadContractList();
        }
      });
    }
  },
  onLaunch: function () {
    uni.setStorageSync("teacher_tabIndex", 0);
    // uni.hideTabBar();
  },
  onShow: function () {
    updateManager();
    this.handleSignCallback();
    console.log("App Show");
  },
  onHide: function () {
    console.log("App Hide");
  },
  globalData: {
    tabIndex: 0
  }
};
</script>

<style lang="scss">
/*每个页面公共css */
/* 注意要写在第一行，同时给style标签加入lang="scss"属性 */
@import "uview-ui/index.scss";

.uicon-checkbox-mark {
  font-size: 24rpx !important;
}

.u-checkbox__icon-wrap--circle {
  height: 40rpx !important;
  width: 40rpx !important;
}

button::after {
  border: none;
}

// ::v-deep .u-tabbar--fixed {
//   height: 140rpx;
// }

// ::v-deep .u-tabbar-item {
//   margin-top: 18rpx;
// }
::v-deep .u-line {
  border-bottom-width: 1rpx !important;
}
::v-deep .u-border-top {
  // 投影
  border: top width 0, im !important;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}
.u-tabbar__content__item-wrapper {
  height: 100rpx !important;
}
// .u-tabbar__content__item-wrapper {
//   height: auto !important;
//   padding-bottom: 10rpx;
// }
.u-badge--error {
  background-color: #fe4f37 !important;
}
</style>
