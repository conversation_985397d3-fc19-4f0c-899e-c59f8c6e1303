.tree-mask {
  position: fixed;
  top: 0rpx;
  right: 0rpx;
  bottom: 0rpx;
  left: 0rpx;
  z-index: 9998;
  background-color: rgba(0, 0, 0, 0.6);
  opacity: 0;
  transition: all 0.3s ease;
  visibility: hidden;
}
.tree-mask.show {
  visibility: visible;
  opacity: 1;
}
.tree-cnt {
  position: fixed;
  top: 0rpx;
  right: 0rpx;
  bottom: 0rpx;
  left: 0rpx;
  z-index: 9999;
  top: 360rpx;
  transition: all 0.3s ease;
  transform: translateY(100%);
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  overflow: hidden;
}
.tree-cnt.show {
  transform: translateY(0);
}
.tree-bar {
  background-color: #fff;
  height: 100rpx;
  padding-left: 20rpx;
  padding-right: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-sizing: border-box;
  border-bottom-width: 1rpx !important;
  border-bottom-style: solid;
  border-bottom-color: #f5f5f5;
  font-size: 32rpx;
  color: #757575;
  line-height: 1;
}
.midInput {
  height: 30px;
  position: relative;
  line-height: 30px;
  display: flex;
  align-items: center;
}
.searchArea {
  text-align: left;
  background-color: #efefef;
  border-radius: 20px;
  padding: 4px 10px;
  height: 30px;
  box-sizing: border-box;
}

.searchIcon {
  position: absolute;
  right: 10px;
    top: 6px;
  height: 16px;
}


.tree-bar-confirm {
 
  color: #007aff;
}
.tree-view {
  position: absolute;
  top: 0rpx;
  right: 0rpx;
  bottom: 0rpx;
  left: 0rpx;
  top: 100rpx;
  background-color: #fff;
  padding-top: 20rpx;
  padding-right: 20rpx;
  padding-bottom: 20rpx;
  padding-left: 20rpx;
}
.tree-view-sc {
  height: 100%;
  overflow: hidden;
}
.tree-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 26rpx;
  color: #757575;
  line-height: 1;
  height: 0;
  opacity: 0;
  transition: 0.2s;
  position: relative;
  overflow: hidden;
}
.tree-item.show {
  height: 80rpx;
  opacity: 1;
}
.tree-item.showchild:before {
  transform: rotate(90deg);
}
.tree-item.last:before {
  opacity: 0;
}
.tree-icon {
  width: 26rpx;
  height: 26rpx;
  margin-right: 8rpx;
}
.tree-label {
  flex: 1;
  display: flex;
  align-items: center;
  height: 100%;
  line-height: 1.2;
}
.tree-check {
  width: 40px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.tree-check-yes,
.tree-check-no {
  width: 20px;
  height: 20px;
  border-top-left-radius: 20%;
  border-top-right-radius: 20%;
  border-bottom-right-radius: 20%;
  border-bottom-left-radius: 20%;
  border-top-width: 1rpx;
  border-left-width: 1rpx;
  border-bottom-width: 1rpx;
  border-right-width: 1rpx;
  border-style: solid;
  border-color: #007aff;
  display: flex;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
}
.tree-check-yes-b {
  width: 12px;
  height: 12px;
  border-top-left-radius: 20%;
  border-top-right-radius: 20%;
  border-bottom-right-radius: 20%;
  border-bottom-left-radius: 20%;
  background-color: #007aff;
}
.tree-check .radio {
  border-top-left-radius: 50%;
  border-top-right-radius: 50%;
  border-bottom-right-radius: 50%;
  border-bottom-left-radius: 50%;
}
.tree-check .radio .tree-check-yes-b {
  border-top-left-radius: 50%;
  border-top-right-radius: 50%;
  border-bottom-right-radius: 50%;
  border-bottom-left-radius: 50%;
}
.hover-c {
  opacity: 0.6;
}
.parent-label {
  color: #007aff;
}