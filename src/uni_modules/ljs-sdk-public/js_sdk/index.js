import base from './utils/base';
import date from './utils/date';
import number from './utils/number';
import formCheck from './utils/formCheck';
import msg from './utils/msg';
import run from './utils/run';
import animation from './utils/animation';
import cryptoJs from './utils/cryptoJs';

export default {
  base, // 公共封装
  date, // 日期处理封装
  number, // 数字处理封装
  formCheck, // 表单检查
  msg, // 消息封装
  run, // 跳转封装
  animation, // 动画封装
  cryptoJs // 加密解密
};
