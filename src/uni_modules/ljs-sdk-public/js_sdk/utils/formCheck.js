/** 
 * @module form
 * @description 表单常用处理方法
 *  */
import D from './date';

/**
 *  只能输入固定位数的正负数字，可小数。
 *  @param {object} opt 配置对象
 *  @param {object} opt.form 对象
 *  @param {object} opt.key 键名，不叫money时需要传入新的键名。默认值：'money'
 *  @param {object} opt.ws 小数位数，传1代表1位小数。默认值：''，表示不限位数
 *  @param {object} opt.type 默认数字类型的字符串，不支持首位多个0；传入String，支持以下格式。00.1；0001221。一般不会有这种使用需求。。默认值：'Number'
 *  @param {object} opt.fu 是否支持-负数符号。默认值：false
 *  @example 
<input class="input" v-model.sync="form.companyName" @input="$ljsPublic.formCheck.numDxsCheck({
	form: form,
	key: 'companyName',
	ws: 4
})"/>
 * */
export function numDxsCheck(opt) {
	setTimeout(() => {
		const allOpt = {
			form: null, // 对象
			key: 'money', // 键名，不叫money时需要传入新的键名
			ws: '', // 几位小数，默认不限位数，传1代表1位小数
			type: 'Number', // 默认数字类型的字符串，不支持首位多个0；传入String，支持以下格式。00.1；0001221。一般不会有这种使用需求。
			fu: false // 是否支持-负数符号
		};
		for (let key in opt) {
			allOpt[key] = opt[key];
		}
		const obj = allOpt.form;
		const key = allOpt.key;
		obj[key] = obj[key] + '';
		if (allOpt.type === 'Number') {
			// 首位是0，第二位必须是.
			if (obj[key].slice(0, 1) === '0') {
				obj[key] = obj[key].slice(1, 2) !== '.' ? '0' : obj[key];
			}
			// 首位是-，第二位不能是.
			if (obj[key].slice(0, 1) === '-') {
				obj[key] = obj[key].slice(1, 2) === '.' ? '-' : obj[key];
			}
			// 首位是-0，第三位必须是.
			if (obj[key].slice(0, 2) === '-0') {
				obj[key] = obj[key].slice(2, 3) !== '.' ? '-0' : obj[key];
			}
		}
		// 先把非数字的都替换掉，除了数字和.
		if (allOpt.fu) {
			obj[key] = obj[key].replace(/[^\d.-]/g, '');
		} else {
			obj[key] = obj[key].replace(/[^\d.]/g, '');
		}
		// 必须保证第一个为数字而不是.
		obj[key] = obj[key].replace(/^\./g, '');
		// 保证只有出现一个.而没有多个.
		obj[key] = obj[key].replace(/\.{2,}/g, '.');
		// 保证只有出现一个-号，且在首位
		obj[key] = obj[key].slice(0, 1) + obj[key].slice(1, obj[key].length).replace(/\-/g, '');
	
		// 动态正则，小数位动态处理
		const c = new RegExp('^(\\-)*(\\d+)\\.(\\d{0,' + allOpt.ws + '})\.*$');
		// 只能输入两个小数
		obj[key] = obj[key].replace(c, '$1$2.$3');
	}, 0)
}

/**
  *  只能输入数字。
  *  @param {object} form 表单对象
  *  @param {string} key 键名不叫num时需要传入新的键名
  *  @param {string} type 默认数字类型的字符串，不支持首位多个0；传入String，支持以下格式。00.1；0001221。一般用于编码类需求。
  *  @example
<input class="input" v-model.sync="form.companyName" @input="$ljsPublic.formCheck.numCheck(obj, 'goodsPrice')"/>
*/
export function numCheck(form, key = 'num', type = 'Number') {
	setTimeout(() => {
		if (type === 'Number') {
			// 首位不能是0
			form[key] = form[key].slice(0, 1) === '0' ? '' : form[key];
		}
		form[key] = form[key].replace(/[^\d]/g, '');
		
		// 处理为数字
		if (type === 'Number' && form[key].length > 0) {
			form[key] = Number(form[key]);
		}
	}, 0)
}

/**
  *  长度检测。
  *  @param {object} form 表单对象
  *  @param {string} key 键名
  *  @param {string} len 长度
  *  @example 
<input class="input" v-model.sync="form.companyName" @input="$ljsPublic.formCheck.numberLengthCheck(form, 'companyName', 2)"/>
*/
export function numberLengthCheck(form, key, len) {
	setTimeout(() => {
		form[key] = form[key].slice(0, len);
	}, 0)
}

/**
  *  只能输入：数字、字母
  *  @param {object} form 表单对象
  *  @param {string} key 键名不叫username时需要传入新的键名
  *  @example 
<input class="input" v-model.sync="form.companyName" @input="$ljsPublic.formCheck.numEnCheck(form, 'companyName')"/>
*/
export function numEnCheck(form, key = 'username') {
	setTimeout(() => {
		form[key] = form[key] + '';

		// 数字、字母及下划线
		form[key] = form[key].replace(/[^\w]/g, '');
		// 去掉下划线
		form[key] = form[key].replace(/\_/g, '');
	}, 0)
}

/**
  *  手机号 长度
  *  type="number"，处理手机号长度，默认对象内手机号的字段名称为phone
  *  @param {object} form 表单对象
  *  @param {string} key 键名不叫phone时需要传入新的键名
  *  @example 
<input class="input" v-model.sync="form.companyName" @input="$ljsPublic.formCheck.phoneMaxLength(form, 'companyName')"/>
*/
export function phoneMaxLength(form, key = 'phone') {
	setTimeout(() => {
		if (form[key].length > 11) {
			form[key] = form[key].substring(0, 11);
		}
	}, 0)
}

/**
  *  默认图片
  *  @param {object} form 表单对象
  *  @param {string} key 键名不叫phone时需要传入新的键名
  *  @param {string} url 默认图片地址
  *  @example 
html:
<image :src="form.companyName" @error="$ljsPublic.formCheck.picError(form, 'companyName', require('../../skm.jpg'))"></image>
js:
export default {
	data() {
		return {
			form: {
				companyName: 'http://192.168.2.250:8090/profile/avatar/2023/02/23/blob_202302231514419A002.png'
			}
		}
	},
}
*/
export function picError(form, key = 'src', url) {
	setTimeout(() => {
		form[key] = url;
	}, 0)
}

/**
 * Blob数据流下载，来源于后端接口返回（请求需要添加 responseType: "blob"）
 * @param {Blob} data 原型数据（excel文件）
 * @param {number} title 小数位，默认2位
 * @example this.$ljsPublic.formCheck.downloadBlobData('测试流内容', "房屋价格");，请求需要添加 responseType: "blob"
 * */
export function downloadBlobData(data, title) {
	const link = document.createElement('a');
	const blob = new Blob([data], {
		type: 'application/vnd.ms-excel'
	});
	link.style.display = 'none';
	link.href = URL.createObjectURL(blob);
	link.setAttribute('download', title);
	document.body.appendChild(link);
	link.click();
	document.body.removeChild(link);
}

export default {
	numCheck,
	numEnCheck,
	phoneMaxLength,
	numDxsCheck,
	picError,
	numberLengthCheck,
	downloadBlobData
};
