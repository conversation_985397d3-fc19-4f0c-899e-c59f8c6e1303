/** 
 * @module base
 * @description 基础常用处理方法
 *  */

/**
 * 地址参数查询：查询地址中是否有某个参数，有的情况下返回值，没有则返回空
 * @param {String} key 键名
 * @returns {String} 返回数组
 * @example this.$ljsPublic.base.urlCheck('userId')
 * */
export function urlCheck(key) {
  const url = location.href;
  const prams = url.split('?');
  let value = '';
  if (prams.length <= 1) {
    return value;
  }
  const pramss = prams[1].split('&');
  pramss.forEach((item) => {
    if (item.split('=')[0] === key) {
      value = item.split('=')[1];
    }
  });
  return value;
}

export function urlToJson(url = window.location.href) {  // 箭头函数默认传值为当前页面url

  let obj = {},
    index = url.indexOf('?'), // 看url有没有参数
    params = url.substr(index + 1); // 截取url参数部分 name = aaa & age = 20

  if (index != -1) { // 有参数时
    let parr = params.split('&');  // 将参数分割成数组 ["name = aaa", "age = 20"]
    for (let i of parr) {           // 遍历数组
      let arr = i.split('=');  // 1） i name = aaa   arr = [name, aaa]  2）i age = 20  arr = [age, 20]
      obj[arr[0]] = arr[1];  // obj[arr[0]] = name, obj.name = aaa   obj[arr[0]] = age, obj.age = 20
    }
  }

  return obj;
}

/**
 * console.log - 输出带颜色的log。
 * @param {String} content log内容
 * @param {String} color 颜色
 * @example const technicalSupport = ['技术支持：', '龙九山'];
 * colorLog(technicalSupport);
 */
export function colorLog(content = ['暂无内容'], color = [
  'background: #666666; color: #fff; border-radius: 4px 0 0 4px; padding: 3px 6px;',
  'background: #1475B2; color: #fff; border-radius: 0 4px 4px 0; padding: 3px 6px;'
]) {
  let logStr = '';
  content.forEach((item) => {
    logStr += `%c${item}`;
  });
  console.info(logStr, color[0], color[1]);
}

export function throttle(func, wait, options) {
  let time, context, args, result;
  let previous = 0;
  if (!options) options = {};

  let later = function () {
    previous = options.leading === false ? 0 : new Date().getTime();
    time = null;
    func.apply(context, args);
    if (!time) context = args = null;
  };

  let throttled = function () {
    let now = new Date().getTime();
    if (!previous && options.leading === false) previous = now;
    let remaining = wait - (now - previous);
    context = this;
    args = arguments;
    if (remaining <= 0 || remaining > wait) {
      if (time) {
        clearTimeout(time);
        time = null;
      }
      previous = now;
      func.apply(context, args);
      if (!time) context = args = null;
    } else if (!time && options.trailing !== false) {
      time = setTimeout(later, remaining);
    }
  };
  return throttled;
}

export default {
  throttle,
  urlCheck,
  urlToJson,
  colorLog
};


