/** 
 * @module date
 * @description 日期常用处理方法
 *  */
import N from './number.js'
/**
 *  个位数字补零，常用于日期
 * @param {string} n 数字
 * @returns {string} 补零后的数字
 * @example this.$ljsPublic.date.numberB0(1)，返回01
 *  */
export function numberB0(value) {
	if (N.checkNum(value)) {
		return value < 10 ? '0' + value : value;
	}
	return value;
}

/**
 * 通过传入的时间戳获取固定格式日期
 * @param {string} time 时间戳
 * @param {string} pattern 输出格式，{y}-{m}-{d} {h}:{i}:{s}
 * @return {string} 返回：1999-01-10
 * @example this.$ljsPublic.date.formatTime(new Date().getTime(), '{y}-{m}-{d} {h}:{i}:{s}')，返回2021-12-22 09:56:58
 */
export function formatTime(time, pattern) {
	const date = new Date(time);
	const format = pattern || '{y}-{m}-{d} {h}:{i}:{s}';
	const formatObj = {
		y: date.getFullYear(),
		m: numberB0(date.getMonth() + 1),
		d: numberB0(date.getDate()),
		h: numberB0(date.getHours()),
		i: numberB0(date.getMinutes()),
		s: numberB0(date.getSeconds())
	};
	return format.replace(/{(y|m|d|h|i|s)+}/g, (result, key) => {
		const value = formatObj[key];
		return value || '';
	});
}

/**
 * 时间处理函数：传入[开始日期, 结束日期]，期望返回数据为[2020-10-01 00:00:00, 2020-10-01 23:59:59]
 * @param {string} dates [开始日期, 结束日期]
 * @param {string} tag 1，补时分秒；2，补分秒；3，补秒
 * @returns {Array[number]} 返回一个所需规格的[开始日期, 结束日期]
 * @example this.$ljsPublic.date.dateQjChange(["2020-10-01", "2020-10-07"], 1)，返回["2020-10-01 00:00:00", "2020-10-07 23:59:59"]
 * */
export function dateQjChange(dates, tag = 1) {
	if (dates[0] === '' || dates[0] === null) {
		return dates;
	}
	const d1 = new Date(dates[0]);
	const d2 = new Date(dates[1]);
	if (tag === undefined) {
		tag = 1;
	}
	switch (tag) {
		case 1:
			return [
				d1.getFullYear() +
				'-' +
				numberB0(d1.getMonth() + 1) +
				'-' +
				numberB0(d1.getDate()) +
				' 00:00:00',
				d2.getFullYear() +
				'-' +
				numberB0(d2.getMonth() + 1) +
				'-' +
				numberB0(d2.getDate()) +
				' 23:59:59'
			];
		case 2:
			return [
				d1.getFullYear() +
				'-' +
				numberB0(d1.getMonth() + 1) +
				'-' +
				numberB0(d1.getDate()) +
				' ' +
				numberB0(d1.getHours()) +
				':00:00',
				d2.getFullYear() +
				'-' +
				numberB0(d2.getMonth() + 1) +
				'-' +
				numberB0(d2.getDate()) +
				' ' +
				numberB0(d2.getHours()) +
				':59:59'
			];
		case 3:
			return [
				d1.getFullYear() +
				'-' +
				numberB0(d1.getMonth() + 1) +
				'-' +
				numberB0(d1.getDate()) +
				' ' +
				numberB0(d1.getHours()) +
				':' +
				numberB0(d1.getMinutes()) +
				':00',
				d2.getFullYear() +
				'-' +
				numberB0(d2.getMonth() + 1) +
				'-' +
				numberB0(d2.getDate()) +
				' ' +
				numberB0(d2.getHours()) +
				':' +
				numberB0(d2.getMinutes()) +
				':59'
			];
	}
	return dates;
}


/**
 * @function getWeekday
 * 封装一个传入日期获取星期几的函数，返回值为中文表示的周几
 * @param {Date} date - 任意日期对象
 * @returns {string} - 返回星期几的英文名称
 * @example this.$ljsPublic.date.getWeekday('2024-03-20')，返回 ”周三 “
 */
export function getWeekday(date) {
	const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
	const weekdayIndex = new Date(date).getDay();
	return weekdays[weekdayIndex];
}

/**
 * 判断是否是日期格式
 * @param { Date } date 传入的日期： new Date() 或者 "2002-03-22" 或者 "2002/03/22" 等
 */
const isTypeDateFormat = (date) => !isNaN(Date.parse(date)) || date instanceof Date;


/**
 * 传入一个日期获取当前日期所在周的所有日期,不传默认当天
 * @param { string | date } val 日期
 * @param {string} pattern 输出格式，{y}-{m}-{d} {h}:{i}:{s}
 * @returns 返回所在天的本周所有日期数据
 * @example this.$ljsPublic.date.circumferenceDay('2024-03-20')，返回 ”{ month: "02", weekdays: [{ week: "周一", date: "2024-02-12" }] }“
 */
export const circumferenceDay = (pattern, val = new Date()) => {
	// 判断当前传入时间是否是 string 日期或者 是不是 Date 日期
	const weekdays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
	if (isTypeDateFormat(val)) {
		let date = new Date(val);
		let day = date.getDay() || 7;
		let start = new Date(date.getTime() - ((day - 1) * 86400000));
		let arr = new Array();
		for (let i = 0; i < 7; i++) {
			arr.push({
				week: weekdays[i],
				date: formatTime(new Date(start.getTime() + (i * 86400000)), pattern),
				fullDate: formatTime(new Date(start.getTime() + (i * 86400000)))
			});
		};
		return {
			month: formatTime(date, "{m}"),
			weekdays: arr
		};
	} else {
		throw Error("获取当前日期所在的周的函数传入的日期格式有误");
	};
};
export default {
	formatTime,
	numberB0,
	dateQjChange,
	getWeekday,
	circumferenceDay
};
