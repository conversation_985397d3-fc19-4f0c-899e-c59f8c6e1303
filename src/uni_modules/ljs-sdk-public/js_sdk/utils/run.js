/** 
 * @module run
 * @description 封装所有页面跳转的方法
 *  */

/**
 * 保留当前页面，跳转到应用内的某个页面
 * @param {String} url 地址
 * @param {Object} parms 参数
 * @example 
this.$ljsPublic.run.gp_navigateTo('/pages/ljs-sdk-public/test');
 */
export function gp_navigateTo(url, parms) {
	uni.navigateTo({
		url: getUrl(url, parms)
	});
}

/**
 * 关闭当前页面，跳转到应用内的某个页面
 * @param {String} url 地址
 * @param {Object} parms 参数
 * @example 
this.$ljsPublic.run.gp_redirectTo('/pages/ljs-sdk-public/test');
 */
export function gp_redirectTo(url, parms) {
	uni.redirectTo({
		url: getUrl(url, parms)
	});
}

/**
 * 关闭所有页面，打开到应用内的某个页面
 * @param {String} url 地址
 * @param {Object} parms 参数
 * @example 
this.$ljsPublic.run.gp_reLaunch('/pages/ljs-sdk-public/test');
 */
export function gp_reLaunch(url, parms) {
	uni.reLaunch({
		url: getUrl(url, parms)
	});
}

/**
 * 关闭当前页面，返回上一页面或多级页面
 * @param {String} url 地址
 * @param {Object} parms 参数
 * @example 
this.$ljsPublic.run.gp_navigateBack();
 */
export function gp_navigateBack(num = 1) {
	uni.navigateBack({
		url: num
	});
}

/**
 * 解析url参数，加入传入参数
 * @param {String} url 地址
 * @example analysisUrl(url)
 */
export function analysisUrl(url) {
	if (url.indexOf("?") > -1) {
		let urls = url.split("?");
		let parms = urls[1].split("&");
		let obj = {};
		parms.forEach((item) => {
			let p = item.split("=");
			obj[p[0]] = p[1];
		});
		return [urls[0], obj];
	}
	return [url, {}];
}

/**
 * 参数处理 - 解决场景$TOOLS.run.gp_navigateTo('/pages/mine/my-addres-edit?type=edit',item)
 * @param {String} url /pages/mine/my-addres-edit?type=edit
 * @param {Object} parms 一个对象
 * @returns {String} 返回一个真实的地址
 * @example getUrl(url, parms)
 */
export function getUrl(url, parms) {
	// 合并链接的参数到参数体里
	let list = analysisUrl(url);

	if (parms === undefined) {
		parms = {}
	}
	for (let key in list[1]) {
		parms[key] = list[1][key];
	}

	let parmsStr = "";
	parmsStr += "?"
	for (let key in parms) {
		parmsStr += key + "=" + parms[key] + "&"
	}
	parmsStr = parmsStr.substring(0, parmsStr.length - 1);
	return list[0] + parmsStr;
}

export default {
	gp_navigateTo,
	gp_redirectTo,
	gp_reLaunch,
	gp_navigateBack,
};
