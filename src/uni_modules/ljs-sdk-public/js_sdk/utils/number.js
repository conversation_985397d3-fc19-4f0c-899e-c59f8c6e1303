/** 
 * @module num
 * @description 数字处理类
 *  */

const limit_min_num = 1e-14; // 精度极小值

/**
 * 金额分割符：按照4位分割（仅数字）
 * @param {string} value 需要进行分割的数字
 * @returns {number} 返回分割完成的数字
 * @example this.$ljsPublic.number.moneySplit(3889999) 返回结果为388,9999
 * */
export function moneySplit(value) {
	if (checkNum(value)) {
		value += '';
		var x = value.split('.');
		var x1 = x[0];
		var x2 = x.length > 1 ? '.' + x[1] : '';
		var rgx = /(\d+)(\d{4})/;
		while (rgx.test(x1)) {
			x1 = x1.replace(rgx, '$1' + ',' + '$2');
		}
		return x1 + x2;
	}
	return value;
}

/**
 * 整数上取舍：最大位数字升，其他位归0；对一个数字上取舍，适配万亿级别的数字，无上限。
 * @param {string} value 数字
 * @returns {number} 返回分割完成的数字
 * @example this.$ljsPublic.number.numberCeil(1234) 返回结果为2000
 * */
export function numberCeil(value) {
	if (checkNum(value)) {
		value = Math.ceil(value);
		const indexNum = (value + '').length;
		const w = Math.pow(10, indexNum - 1);
		return Math.ceil(value / w) * w;
	}
	return value;
}

/**
 * 数字单位升级：数字进行万、亿、万亿处理
 * @param {string} value 原型数据
 * @param {String} ws 保留几位小数
 * @returns {Array} 返回数组
 * @example this.$ljsPublic.number.longNumText(12345, 1)，返回1.2万
 * */
export function longNumText(value, ws = 2) {
	if (checkNum(value)) {
		const num = Number(value);
		if (num < 10000) {
			return num;
		} else if (num >= 10000 && num < 10000 * 10000) {
			return (num / 10000 + limit_min_num).toFixed(ws) + '万';
		} else if (num >= 10000 * 10000 && num < 10000 * 10000 * 10000) {
			return (num / (10000 * 10000) + limit_min_num).toFixed(ws) + '亿';
		} else if (num >= 10000 * 10000 * 10000) {
			return (num / (10000 * 10000 * 10000) + limit_min_num).toFixed(ws) + '万亿';
		}
	}
	return value;
}

/**
 * 数字保留小数位：数字、字符串，处理小数位。
 * 注：整数不保留小数位，非数字原样返回。
 * @param {String} value 字典数据
 * @param {String} ws 保留几位小数
 * @returns {String} 返回处理后的数据。
 * @example this.$ljsPublic.number.numberChangeDecimal(12.3456)
 * */
export function numberChangeDecimal(value, ws = 2) {
	if (checkNum(value)) {
		value = value + '';
		if (value.indexOf('.') > -1) {
			// 优化：原数字小于位数的再做处理
			if (value.split('.')[1].length > ws) {
				value = Number(value);
				return (value + limit_min_num).toFixed(ws);
			}
		}
	}
	return value;
}

/**
 * 是否为数字：做数字处理前需要知道传入的数据是否为数字。
 * 此处已经判断了number，string类型的数字。
 * 暂不开放，仅num.js内部使用。
 * @param {String} value 字典数据
 * @returns {Boolean} 是否为数字
 * @example this.$ljsPublic.number.checkNum(12.3456)
 * */
export function checkNum(value) {
	const type = typeof value;
	if (type === 'number' || (type === 'string' && !isNaN(Number(value, 10)))) {
		return true;
	}
	return false;
}

export default {
	checkNum,
	longNumText,
	numberCeil,
	moneySplit,
	numberChangeDecimal
};
