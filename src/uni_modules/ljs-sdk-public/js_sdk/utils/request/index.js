/** 
 * @module request
 * @description 请求封装
 *  */
/* 网络请求 */
import config from '@/request/config.js'
import MSG from '../msg.js'
import RUN from '../run.js'
// import BASE from '../base.js'

const token = uni.getStorageSync('ljs_sdk_public_TOKEN'); // token验证串

/**
 * 基础请求方法
 * @param {string} url 接口地址
 * @param {Object} params 参数的对象
 * @param {string} methodTag 标记，用来区分不同需要的请求处理；LOGIN：登录请求，需要增加header头内容；
 * @param {Boolean} submitDD 防抖动，是否需要开启
 * @param {Number} timeout 超时时间，默认6秒
 * */
export function ax(url, params = {}, methodTag, submitDD = false, timeout = 6000) {
	
	// 防抖动：开启
	if(submitDD){
		const dd = uni.getStorageSync('ljs_sdk_public_DD');
		// 防抖动判断
		if(dd){
			// 阻止发起重复的请求。
			return new Promise(function(resolve, reject) {
				resolve({
					code: 9001,
					msg: '来自于系统的阻止：您的操作过于频繁，请稍后再试。'
				});
			});
		}
		uni.setStorageSync('ljs_sdk_public_DD', true);
	}
	
	let header = {
		// 'Content-Type': 'application/x-www-form-urlencoded'
		// 'Content-Type': 'multipart/form-data'
		'Content-Type': 'application/json;charset=UTF-8'
	};
	// methodTag:LOGIN
	if(methodTag !== "LOGIN"){
		header["Authorization"] = token;
	}
	
	return new Promise(function(resolve, reject) {
		uni.request({
			url: config.base_url + url,
			data: params,
			method: methodTag === 'LOGIN' ? 'POST' : methodTag,
			header: header,
			timeout: timeout,
			success: (response) => {
				resolve(response.data);
				// if (response.statusCode === 200) {
				// 	if (response.data.code === 401) {
				// 		MSG.msg("登录超时，请重新登录！", 3000);
				// 		// BASE.logout(); 
				// 		// return;
				// 	}
				// 	if (response.data.code !== 200) {
				// 		MSG.msg(response.data.msg);
				// 	}
				// } else {
				// 	MSG.msg(response.errMsg);
				// }
			},
			fail: (error) => {
				MSG.msg(error.errMsg);
				MSG.loading_close();
			},
			complete: ()=> {
				uni.setStorageSync('ljs_sdk_public_DD', false); // 防抖动 - 关
			}
		});
	});
}

/**
 * 基础上传请求方法
 * @param {string} url 接口地址
 * @param {Array[File]} files 参数的对象
 * @param {Object} formData formData数据
 * @param {Number} timeout 超时时间，默认6秒
 * */
export function uploadFile(url, files = [], formData, timeout = 6000) {
	// console.log(files, formData);
		
	let header = {
		// 'Content-Type': 'application/x-www-form-urlencoded'
		'Content-Type': 'multipart/form-data'
		// 'Content-Type': 'application/json;charset=UTF-8'
	};
	// methodTag:LOGIN
	if (true) {
		header["Authorization"] = token;
	}
	return new Promise(function(resolve, reject) {
		uni.uploadFile({
			header: header,
			url: config.base_url + url,
			files: files,
			// formData: formData, // HTTP 请求中其他额外的 form data
			timeout: timeout,
			success: (response) => {
				resolve(response.data);
			},
			fail: (error) => {
				MSG.msg(error.errMsg);
				MSG.loading_close();
			},
			complete: ()=> {
				uni.setStorageSync('ljs_sdk_public_DD', false); // 防抖动 - 关
			}
		});
	});
}

module.exports = {
	/**
	 * GET
	 * @param {string} url 接口地址
	 * @param {string} params 参数的对象
	 * @param {string} timeout 超时时间，默认10秒
	 * */
	get(url, params = {}) {
		return ax(url, params, "GET", false, timeout);
	},
	/**
	 * POST
	 * @param {string} url 接口地址
	 * @param {string} params 参数的对象
	 * @param {string} submitDD 防抖动，是否需要开启
	 * @param {string} timeout 超时时间，默认10秒
	 * */
	post(url, params = {}, submitDD, timeout) {
		return ax(url, params, "POST", submitDD, timeout);
	},
	/**
	 * PUT
	 * @param {string} url 接口地址
	 * @param {string} params 参数的对象
	 * @param {string} submitDD 防抖动，是否需要开启
	 * @param {string} timeout 超时时间，默认10秒
	 * */
	put(url, params = {}, submitDD, timeout) {
		return ax(url, params, "PUT", submitDD, timeout);
	},
	/**
	 * DEL
	 * @param {string} url 接口地址
	 * @param {string} params 参数的对象
	 * @param {string} submitDD 防抖动，是否需要开启
	 * */
	del(url, params = {}, submitDD) {
		return ax(url, params, "DELETE", submitDD);
	},
	
	/**
	 * 独立，只在登录使用，绕过header["Authorization"]
	 * @param {string} url 接口地址
	 * @param {string} params 参数的对象
	 * */
	upload(url, files, params = {}, timeout) {
		let formData = new FormData();
		for (const key in params) {
		    formData.append(key, params[key]);
		}
		return uploadFile(url, files, formData, timeout);
	},
	
	/**
	 * 独立，只在登录使用，绕过header["Authorization"]
	 * @param {string} url 接口地址
	 * @param {string} params 参数的对象
	 * */
	login(url, params = {}) {
		return ax(url, params, "LOGIN", true);
	},
}