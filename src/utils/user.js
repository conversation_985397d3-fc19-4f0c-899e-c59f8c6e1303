import { getOpenId, login } from "@/services/student/home";
import {
  checkedStudent,
  checkedCustomer,
  getStudentList,
  // customerList,
  getCustomerDetail,
  // checkedStudentInfo,
  // checkedCustomerInfo,
  getStudentDetail,
  getIntentDetail
} from "@/services/student/my";

/**
 * 更新选中的用户信息
 * @param {string} role - 用户角色 ('student'/'customer'/'default')
 * @param {string|number} id - 用户ID
 * @param {string} open_id - 开放ID
 */
export const updateCheckedUserInfo = async (role, id, open_id) => {
  if (["student", "default"].includes(role)) {
    await checkedStudent({
      student_id: id,
      open_id
    });
  } else {
    await checkedCustomer({
      customer_id: id,
      open_id
    });
  }
};

/**
 * 微信登录并获取用户信息
 * @param {Function} callback - 登录成功后的回调函数
 */
export const arouseLogin = () => {
  return new Promise((resolve) => {
    uni.login({
      provider: "weixin",
      onlyAuthorize: true,
      success: async (event) => {
        const { code } = event;
        console.log("code :>> ", code);
        const res = await getOpenId({ code, NAUTHORIZED: true });
        if (res.code === 0) {
          const res2 = await login({
            open_id: res.data.openid,
            UNAUTHORIZED: true
          });
          if (res2.code === 0) {
            const role =
              !res2.data.is_student && !res2.data.is_customer
                ? "default"
                : res2.data.is_student
                ? "student"
                : "customer";

            const session = { ...res2.data, role };
            uni.setStorageSync("session", session);
            resolve({
              session,
              role,
              openId: res.data.openid,
              is_fist_login: res2.data.is_fist_login
            });
          } else {
            uni.hideLoading();
            uni.showToast({
              title: res2.message,
              icon: "none"
            });
            resolve({
              openId: res.data.openid
            });
          }
        }
      }
    });
  });
};

/**
 * 判断是否为学生角色
 * @param {string} role - 用户角色
 * @returns {boolean} - 是否为学生角色
 */
export const isStudentRole = (role) => {
  //   return ["student", "default"].includes(role);
  return ["student"].includes(role);
};

/**
 * 根据角色获取相应的用户数据
 * @param {Object} component - Vue组件实例
 * @param {string} role - 用户角色
 * @param {string} openId - 用户openId
 * @param {Function} callback - 数据获取后的回调函数
 */
export const getDataByRole = async (component, role, openId, callback) => {
  try {
    // let result;
    console.log("getDataByRole", role);
    // if (isStudentRole(role)) {
    if (role === "default") return;
    const result = await getStudentList({ open_id: openId });
    // } else {
    //   result = await customerList({ open_id: openId });
    // }

    if (callback && typeof callback === "function") {
      callback(result.code, result.data, result.message);
    }

    return result;
  } catch (error) {
    console.error("获取数据失败:", error);
    return { code: -1, data: null, message: "获取数据失败" };
  }
};

/**
 * 处理用户数据列表
 * @param {number} code - 接口返回状态码
 * @param {Array} data - 接口返回的数据
 * @param {string} message - 接口返回的消息
 * @param {Object} context - Vue组件实例
 * @param {string} studentId - 学生ID
 */
export const processUserList = async (
  code,
  data,
  message,
  context,
  studentId
) => {
  if (code === 0) {
    context.studentList = data || [];
    if (data === null || data?.length === 0) {
      context.haveStudent = false;
      if (studentId) {
        const res = await getCustomerDetail({ open_id: studentId });
        uni.setStorageSync("visitorInfo", res.data);
        context.curCheckedStudent = res.data;
      }
    } else {
      const curStudentInfo = uni.getStorageSync("curStudentInfo");
      updateCheckedUserInfo(
        context.role,
        curStudentInfo === ""
          ? data[0][context.rowIdField]
          : curStudentInfo[context.rowIdField],
        context.session.open_id
      );

      context.haveStudent = true;

      // if (context.studentList.length) {
      if (uni.getStorageSync("curStudentInfo") === "") {
        uni.setStorageSync("curStudentInfo", data[0]);
        context.curCheckedStudent = data[0];
      } else {
        context.curCheckedStudent = uni.getStorageSync("curStudentInfo");
      }
      // }
    }

    if (context.$refs.tabbar) {
      // context.$refs.tabbar.setMenuList();
    }
  } else if (context.$refs.uToast) {
    context.$refs.uToast.show({ type: "error", message });
  }
};

/**
 * 获取选中学生/客户信息
 * @param {string} open_id - 开放ID
 * @param {string} role - 用户角色
 * @returns {Promise} 返回处理结果
 */
export const getCheckedStudentInfo = async (open_id, role, student_id) => {
  if (!student_id) return;
  const res =
    role === "student"
      ? await getStudentDetail({
          student_id
        })
      : await getIntentDetail({
          customer_id: student_id
        });
  if (res.code === 0) {
    uni.setStorageSync("curStudentInfo", res.data);
    return {
      success: true,
      curStudentInfo: res.data,
      allStudents: res.data
    };
  }
  // const { code, data } =
  //   role === "student"
  //     ? await checkedStudentInfo({ open_id })
  //     : await checkedCustomerInfo({ open_id });

  // if (code === 0) {
  // const res =
  //   role === "student"
  //     ? await getStudentList({ open_id })
  //     : await customerList({ open_id });
  // const res = await getStudentList({ open_id });
  // if (res.code === 0) {
  // const rowIdField = role === "student" ? "student_id" : "customer_id";
  // const curStudentInfo =
  //   res.data && res.data.find((i) => i[rowIdField] === student_id);
  //   const curStudentInfo =
  //   if (curStudentInfo) {
  //     uni.setStorageSync("curStudentInfo", curStudentInfo);
  //     // 更新选中的用户信息
  //     await updateCheckedUserInfo(role, curStudentInfo[rowIdField], open_id);
  //   } else {
  //     uni.setStorageSync("curStudentInfo", "");
  //   }

  return {
    success: false
  };
  // }
  // }

  // return { success: false };
};

/**
 * 调起微信支付
 * @param {Object} payData 支付参数
 * @returns {Promise} 支付结果
 */
export const wxPay = (payData) => {
  const payInfo = JSON.parse(payData.prepayTn);
  return new Promise((resolve, reject) => {
    uni.requestPayment({
      provider: "wxpay",
      ...payInfo,
      success: (res) => {
        console.log("支付成功", res);
        resolve(res);
      },
      fail: (err) => {
        console.log("支付失败", err);
        reject(err);
      }
    });
  });
};
