import { hasPermission } from "../frontPermise";

/**
 * 手机号脱敏过滤器
 * @param {String} phone - 手机号
 * @returns {String} - 处理后的手机号
 */
export const maskPhone = (phone) => {
  // 如果没有手机号，返回空字符串
  if (!phone) return "";

  // 如果有phone_mask权限，不需要脱敏
  if (hasPermission(["phone_mask"])) {
    return phone;
  }

  // 将手机号转为字符串
  const phoneStr = phone.toString();

  // 如果不是有效的11位手机号，直接返回原值
  if (phoneStr.length !== 11) {
    return phone;
  }

  // 中间4位用星号替换
  return phoneStr.replace(/(\d{3})\d{4}(\d{4})/, "$1****$2");
};

// 导出所有过滤器
export default {
  maskPhone
};
