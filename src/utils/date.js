/**
 * 日期处理工具函数
 */

/**
 * 获取当前日期的年、月、日
 * @returns {Object} 包含年、月、日的对象
 */
export function getCurrentDate() {
  const date = new Date();
  return {
    year: date.getFullYear(),
    month: date.getMonth() + 1,
    day: date.getDate(),
    weekDay: date.getDay() === 0 ? 7 : date.getDay() // 将周日的0转为7
  };
}

/**
 * 格式化日期为指定格式
 * @param {Date|string|number} date - 日期对象、日期字符串或时间戳
 * @param {string} format - 格式化模式，如 'YYYY-MM-DD'
 * @returns {string} 格式化后的日期字符串
 */
export function formatDate(date, format = "YYYY-MM-DD") {
  if (!date) return "";

  let dateObj;
  if (typeof date === "string" || typeof date === "number") {
    // 处理ISO格式的日期字符串
    if (typeof date === "string" && date.includes("T")) {
      dateObj = new Date(date);
    } else if (typeof date === "string" && date.includes("-")) {
      // 处理普通日期字符串，兼容Safari
      dateObj = new Date(date.replace(/-/g, "/"));
    } else {
      dateObj = new Date(date);
    }
  } else {
    dateObj = date;
  }

  const year = dateObj.getFullYear();
  const month = String(dateObj.getMonth() + 1).padStart(2, "0");
  const day = String(dateObj.getDate()).padStart(2, "0");
  const hours = String(dateObj.getHours()).padStart(2, "0");
  const minutes = String(dateObj.getMinutes()).padStart(2, "0");
  const seconds = String(dateObj.getSeconds()).padStart(2, "0");

  return format
    .replace("YYYY", year)
    .replace("MM", month)
    .replace("DD", day)
    .replace("HH", hours)
    .replace("mm", minutes)
    .replace("ss", seconds);
}

/**
 * 获取一周的日期数据
 * @param {Date} date - 日期对象，默认为当前日期
 * @returns {Array} 包含一周日期信息的数组
 */
export function getWeekDays(date = new Date()) {
  const currentDate = new Date(date);
  const day = currentDate.getDay() || 7; // 获取星期几，将周日的0转为7

  // 计算本周一的日期
  const mondayDate = new Date(currentDate);
  mondayDate.setDate(currentDate.getDate() - (day - 1));

  const weekDays = [];
  const weekDayNames = ["一", "二", "三", "四", "五", "六", "日"];

  // 生成一周的日期数据
  for (let i = 0; i < 7; i++) {
    const currentDay = new Date(mondayDate);
    currentDay.setDate(mondayDate.getDate() + i);

    weekDays.push({
      date: String(currentDay.getDate()),
      month: currentDay.getMonth() + 1,
      year: currentDay.getFullYear(),
      weekDay: weekDayNames[i],
      fullDate: formatDate(currentDay),
      isToday: isSameDay(currentDay, new Date())
    });
  }

  return weekDays;
}

/**
 * 判断两个日期是否是同一天
 * @param {Date} date1 - 第一个日期
 * @param {Date} date2 - 第二个日期
 * @returns {boolean} 是否是同一天
 */
export function isSameDay(date1, date2) {
  return (
    date1.getFullYear() === date2.getFullYear() &&
    date1.getMonth() === date2.getMonth() &&
    date1.getDate() === date2.getDate()
  );
}

/**
 * 获取上一周或下一周的日期
 * @param {Date} date - 当前日期
 * @param {number} offset - 偏移量，-1表示上一周，1表示下一周
 * @returns {Date} 偏移后的日期
 */
export function getOffsetWeek(date, offset) {
  const newDate = new Date(date);
  newDate.setDate(date.getDate() + offset * 7);
  return newDate;
}

/**
 * 获取周日期范围字符串，如 "2023/3/27-3/2"
 * @param {Array} weekDays - 周日期数组
 * @returns {string} 周日期范围字符串
 */
export function getWeekRangeText(weekDays) {
  if (!weekDays || weekDays.length < 7) return "";

  const firstDay = weekDays[0];
  const lastDay = weekDays[6];

  // 如果第一天和最后一天是同一个月
  if (firstDay.month === lastDay.month) {
    return `${firstDay.year}/${firstDay.month}/${firstDay.date}-${firstDay.month}/${lastDay.date}`;
  }
  // 如果跨月
  return `${firstDay.year}/${firstDay.month}/${firstDay.date}-${lastDay.month}/${lastDay.date}`;
}

/**
 * 解析ISO格式的时间字符串，返回时间部分
 * @param {string} isoString - ISO格式的时间字符串
 * @returns {string} 格式化后的时间字符串，如 "14:30"
 */
export function parseTimeFromISO(isoString) {
  if (!isoString) return "";

  const date = new Date(isoString);
  const hours = String(date.getHours()).padStart(2, "0");
  const minutes = String(date.getMinutes()).padStart(2, "0");

  return `${hours}:${minutes}`;
}

/**
 * 计算两个ISO时间字符串之间的时间范围
 * @param {string} startTimeISO - 开始时间的ISO字符串
 * @param {string} endTimeISO - 结束时间的ISO字符串
 * @returns {string} 时间范围字符串，如 "14:00-15:30"
 */
export function getTimeRange(startTimeISO, endTimeISO) {
  if (!startTimeISO || !endTimeISO) return "";

  const startTime = parseTimeFromISO(startTimeISO);
  const endTime = parseTimeFromISO(endTimeISO);

  return `${startTime}-${endTime}`;
}
/**
 * 获取年份列表
 * @param {number} currentYear - 当前年份
 * @param {number} range - 年份范围，默认为10
 * @returns {Array} 年份列表
 */
export function getYearList(currentYear, range = 10) {
  if (!currentYear) {
    currentYear = new Date().getFullYear();
  }
  const startYear = currentYear - Math.floor(range / 2);
  const endYear = currentYear + Math.floor(range / 2);
  const yearList = [];
  for (let year = startYear; year <= endYear; year++) {
    yearList.push(year);
  }
  return yearList;
}
