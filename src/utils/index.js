/**
 * 格式化秒数为时分秒的字符串
 * @param {number} value - 需要格式化的秒数
 * @returns {string} 格式化后的时分秒字符串，例如“1时30分15秒”
 */
export function formatSeconds(value) {
  const result = parseInt(value);
  const hour = Math.floor(result / 3600); // 计算小时
  const minute = Math.floor((result - hour * 3600) / 60); // 计算分钟
  const second = result - hour * 3600 - minute * 60; // 计算秒数
  // 根据小时、分钟、秒数的值，构建并返回对应的时分秒字符串
  if (hour === 0) {
    if (minute === 0) {
      return second + "秒";
    } else {
      return minute + "分" + second + "秒";
    }
  }
  return hour + "时" + minute + "分" + second + "秒";
}
