import {
  <PERSON>yunWareCategory,
  <PERSON>yunWareCategoryOfStudent
} from "@/services/oss";
import crypto from "crypto-js";
import { Base64 } from "js-base64";
let bucket = "";
let region = "";
// let SecurityToken = "";
const accessKeyId = "LTAI5tChhcre4FJsTAaJzita";
const accessSecret = "******************************";
const getAliyun = function () {
  AliyunWareCategory()
    .then((res) => {
      // SecurityToken = res.SecurityToken;
      // accessKeyId = res.accessKeyId;
      // accessSecret = res.accessSecret;
      bucket = res.bucket;
      region = res.region;
    })
    .catch((err) => {
      console.error(err);
    });
};
// 学生端上传图片用
const getAliyunOfStudent = function () {
  AliyunWareCategoryOfStudent()
    .then((res) => {
      // SecurityToken = res.SecurityToken;
      // accessKeyId = res.accessKeyId;
      // accessSecret = res.accessSecret;
      bucket = res.bucket || "tg-dev";
      region = res.region;
    })
    .catch((err) => {
      console.error(err);
    });
};
function computeSignature(accessKeySecret, canonicalString) {
  console.log("accessKeySecret :>> ", accessKeySecret);
  return crypto.enc.Base64.stringify(
    crypto.HmacSHA1(canonicalString, accessKeySecret)
  );
}

const date = new Date();
date.setHours(date.getHours() + 1);
const policyText = {
  expiration: date.toISOString(), // 设置policy过期时间。
  conditions: [
    // 限制上传大小。
    ["content-length-range", 0, 1024 * 1024 * 1024]
  ]
};

function getFormDataParams() {
  const policy = Base64.encode(JSON.stringify(policyText)); // policy必须为base64的string。
  const signature = computeSignature(accessSecret, policy);
  const formData = {
    OSSAccessKeyId: accessKeyId,
    signature,
    policy
  };
  return formData;
}
// 上传文件
// 上传文件

/**
 *
 * @param {文件file} file
 * @param cb 回调函数
 * @param urlField 文件路径
 */

//  上传
const CooOss = function (file, cb, urlField = "tempFilePath", loading = true) {
  console.log("file :>> ", file);
  // 获取后缀
  const key = file[urlField].split("/").at(-1);

  const formData = getFormDataParams();
  console.log("formData :>> ", formData);
  // 获取文件名
  console.log("key :>> ", key);
  console.log("bucket :>> ", bucket);
  const objectkey = bucket + "/" + key;
  console.log("objectkey :>> ", objectkey);
  uni.showLoading({
    title: "上传中..."
  });
  uni.uploadFile({
    url: `https://${bucket}.oss-cn-beijing.aliyuncs.com`,
    filePath: file[urlField],
    name: "file", // 必须填file。
    formData: {
      ...formData,
      key: bucket + "/" + key
      // "x-oss-security-token": getFormDataParams()["x-oss-security-token"]
    },
    success: (res) => {
      console.log(res);
      if (loading) {
        uni.hideLoading();
      }
      if (res.statusCode === 204) {
        const url = `https://${bucket}.${region}.aliyuncs.com/${objectkey}`;
        // "https://" + bucket + "." + region + ".aliyuncs.com/" + objectkey;
        console.log("url :>> ", url);
        cb(url);
      } else {
        uni.showToast({
          title: "上传失败",
          icon: "error",
          mask: true
        });
      }
    },
    fail: (err) => {
      uni.hideLoading();
      uni.showToast({
        title: "上传失败",
        icon: "error",
        mask: true
      });
      console.log(err);
    }
  });
};

export default {
  install(Vue) {
    Vue.prototype.Oss = {
      uploadFile: CooOss,
      getAliyun,
      getAliyunOfStudent
    };
  }
};
