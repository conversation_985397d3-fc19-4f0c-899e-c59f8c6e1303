// 权限接口列表
const permissions = {
  customer_create: "/api/market-service/miniprogram/customer/create",
  customer_update: "/api/market-service/miniprogram/customer/update",
  customer_status_list: "/api/market-service/miniprogram/customer/status-list",
  customer_list: "/api/market-service/miniprogram/customer/list",
  channel_list: "/api/market-service/miniprogram/channel/list",
  subchannel_list: "/api/market-service/miniprogram/subchannel/list",
  customer_info: "/api/market-service/miniprogram/customer/info",
  scheduling_list: "/api/school-service/miniprogram/scheduling/list",
  scheduling_student_list:
    "/api/school-service/miniprogram/scheduling/student/list",
  schoolroom_list_with_scheduling:
    "/api/school-service/miniprogram/schoolroom/list-with-scheduling",
  scheduling_roll_call_list:
    "/api/school-service/miniprogram/scheduling/roll-call/list",
  lock_account: "/api/school-service/miniprogram/check-in/lock-account",
  scheduling_begin: "/api/school-service/miniprogram/scheduling/begin",
  roll_call_lock: "/api/school-service/miniprogram/check-in/roll-call-lock",
  roll_call_sustain_lock:
    "/api/school-service/miniprogram/check-in/roll-call-sustain-lock",
  roll_call_unlock: "/api/school-service/miniprogram/check-in/roll-call-unlock",
  roll_call: "/api/school-service/miniprogram/check-in/roll-call",
  scheduling_revocation:
    "/api/school-service/miniprogram/scheduling/revocation",
  flower_name_list: "/api/school-service/miniprogram/classroom/list",
  niedao_circle_list: "/api/questionnaire-service/admin/moments/teacher/list",
  niedao_circle_delete:
    "/api/questionnaire-service/admin/moments/teacher/delete",
  niedao_circle_create:
    "/api/questionnaire-service/admin/moments/teacher/create",
  classroom_student_list:
    "/api/school-service/miniprogram/classroom-student/list",
  feedback_student_list: "/api/school-service/miniprogram/feedback/studentList",
  feedback_send: "/api/school-service/miniprogram/feedback/send",
  feedback_detail: "/api/school-service/miniprogram/feedback/detail",
  feedback_cancel: "/api/school-service/miniprogram/feedback/cancel",
  dtb_qrcode: "/api/school-service/miniprogram/dtb_qrcode",
  // parent_class: "/api/feedback/send/miniprogram/parent_class", // 发送家长课堂
  class_notice: "/api/feedback/send/miniprogram/class_notice", // 发送班级通知
  // course_summary: "/api/feedback/send/miniprogram/course_summary", // 发送课程总结
  parent_class_list:
    "/api/school-service/miniprogram/feedback/parent_class_list", // 获取家长课堂列表
  course_summary_list:
    "/api/school-service/miniprogram/feedback/course_summary_list", // 获取课程总结列表
  parent_class_send:
    "/api/school-service/miniprogram/feedback/parent_class_send", // 发送家长课堂
  course_summary_send:
    "/api/school-service/miniprogram/feedback/course_summary_send", // 发送课程总结
  parent_class_cancel:
    "/api/school-service/miniprogram/feedback/parent_class_cancel", // 取消家长课堂
  course_summary_cancel:
    "/api/school-service/miniprogram/feedback/course_summary_cancel", // 取消课程总结
  course_summary: "/api/feedback/send/miniprogram/course_summary", // 发送课程总结
  get_banner_list: "/web/questionnaire-web-service/banner/list", // 获取banner列表
  time_album: "/api/questionnaire-service/admin/timeAlbum/menu", // 获取时光相册
  phone_mask: "/api/show-mobile/front", // 不需要手机号脱敏

  // 时光相册
  time_album_meau: "/api/student-service/miniprogram/time-album/menu", // 获取时光相册菜单
  time_album_info: "/api/student-service/public/time-album/info", // 获取时光相册详情
  time_album_create: "/api/student-service/miniprogram/time-album/create", // 创建时光相册
  time_album_delete: "/api/student-service/miniprogram/time-album/delete", // 删除时光相册
  time_album_update: "/api/student-service/miniprogram/time-album/save", // 修改时光相册
  time_album_info_list: "/api/student-service/miniprogram/time-album/infoList", // 获取时光相册列表
  time_album_front_share:
    "/api/student-service/miniprogram/time-album/front-share", // 分享时光相册
  time_album_batch_add: "/api/student-service/miniprogram/time-album/batch-add" // 批量添加时光相册
};

/**
 * 判断是否有权限
 * @param {Array} permission
 * @returns {Boolean}
 */
const hasPermission = (permission) => {
  if (!permission || permission.length === 0) {
    return true;
  }
  const all_permissions = uni.getStorageSync("permission");
  if (all_permissions.includes("is_admin")) {
    return true;
  } else {
    return permission.some((per) => {
      return all_permissions.includes(permissions[per]);
    });
  }
};
export { permissions, hasPermission };
