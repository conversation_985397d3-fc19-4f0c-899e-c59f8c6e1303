<template>
  <view :style="{ left: visible ? '0' : '-200%  ' }" class="confirm-container">
    <view
      v-show="visible"
      class="confirm-mask"
      @click.stop="handleMaskClick"
    ></view>
    <view class="confirm-content" :class="{ 'confirm-show': visible }">
      <view
        :style="{ color: color, fontSize: fontSize }"
        class="confirm-message"
        >{{ content }}</view
      >
      <view class="confirm-buttons">
        <view class="confirm-button cancel-button" @click="handleCancel">{{
          cancelText
        }}</view>
        <view
          class="confirm-button confirm-button-primary"
          @click="handleConfirm"
          >{{ confirmText }}</view
        >
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: "MyConfirm",
  props: {
    content: {
      type: String,
      default: ""
    },
    confirmText: {
      type: String,
      default: "确定"
    },
    cancelText: {
      type: String,
      default: "取消"
    },
    visible: {
      type: <PERSON>olean,
      default: false
    },
    maskClosable: {
      type: <PERSON>olean,
      default: false
    },
    color: {
      type: String,
      default: "#333"
    },
    fontSize: {
      type: String,
      default: "36rpx"
    }
  },
  data() {
    return {
      // 使用props控制可见性
    };
  },
  watch: {
    // 监听可见性变化
    visible(val) {
      console.log("Confirm组件可见性变化:", val);
    }
  },
  methods: {
    handleConfirm() {
      console.log("Confirm组件确认按钮被点击");
      this.$emit("confirm");
    },
    handleCancel() {
      console.log("Confirm组件取消按钮被点击");
      this.$emit("cancel");
      this.$emit("close");
    },
    handleMaskClick() {
      if (this.maskClosable) {
        this.handleCancel();
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.confirm-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.confirm-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}

.confirm-content {
  position: relative;
  width: 582rpx;
  padding: 60rpx 40rpx;
  height: 334rpx;
  background-color: #fff;
  border-radius: 35rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  transform: scale(0.8);
  opacity: 0;
  // 动画 延迟1s
  transition: all 0.3s;
  z-index: 10000;

  &.confirm-show {
    transform: scale(1);
    opacity: 1;
  }
}

.confirm-message {
  color: #333;
  text-align: center;
  font-size: 36rpx;
  font-style: normal;
  font-weight: 500;
  line-height: 1.5;
  margin-bottom: 60rpx;
  width: 100%;
}

.confirm-buttons {
  display: flex;
  width: 100%;
  justify-content: space-between;
}

.confirm-button {
  width: 238rpx;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 34rpx;
  font-weight: bold;
  text-align: center;
  line-height: 88rpx;

  &:active {
    opacity: 0.8;
  }
}

.cancel-button {
  border: 2rpx solid #ffc525;
  color: #ffc525;
}

.confirm-button-primary {
  color: #fff;
  background: linear-gradient(15deg, #ffbf0d 18.1%, #ffcb3c 83.29%);
  box-shadow: 0px -5px 12px 0px #fc0 inset, 0px 9px 20px 0px #fff7e1 inset;
  filter: drop-shadow(0px 4px 4px rgba(255, 192, 18, 0.11));
}
</style>
