# ImageUploader 图片上传组件

一个用于选择、预览和上传图片的通用组件，支持多图上传、图片预览和删除功能。

## 使用示例

```vue
<template>
  <view>
    <image-uploader
      v-model="imageList"
      :max-count="6"
      :row-count="4"
      :image-size="148"
      :image-spacing="16"
      @error="handleError"
      @select="handleSelect"
      @success="handleSuccess"
      @delete="handleDelete"
    />
  </view>
</template>

<script>
import ImageUploader from "@/components/image-uploader/index.vue";

export default {
  components: {
    ImageUploader
  },
  data() {
    return {
      imageList: []
    };
  },
  created() {
    // OSS上传对象会自动在组件内初始化
  },
  methods: {
    // 处理错误
    handleError(message) {
      uni.showToast({
        title: message,
        icon: "none"
      });
    },
    // 图片选择事件
    handleSelect(res) {
      console.log("图片选择：", res);
    },
    // 图片上传成功事件
    handleSuccess(url) {
      console.log("图片上传成功：", url);
    },
    // 图片删除事件
    handleDelete(image, index) {
      console.log("删除图片：", image, index);
    }
  }
};
</script>
```

## 属性说明

| 属性名       | 类型   | 默认值 | 说明                                         |
| ------------ | ------ | ------ | -------------------------------------------- |
| value        | Array  | []     | 已上传图片的 URL 数组，使用 v-model 双向绑定 |
| maxCount     | Number | 6      | 最大上传图片数量                             |
| rowCount     | Number | 4      | 一行显示的图片数量                           |
| imageSpacing | Number | 16     | 图片间距，单位 rpx                           |
| uploadText   | String | "图片" | 上传按钮显示的文字                           |
| imageSize    | Number | 148    | 图片项的尺寸，单位 rpx                       |

## 事件说明

| 事件名  | 说明                                                 | 回调参数                                          |
| ------- | ---------------------------------------------------- | ------------------------------------------------- |
| input   | 当图片列表变化时触发，用于 v-model 双向绑定          | (imageList: Array) 更新后的图片数组               |
| error   | 当出现错误时触发，如超出最大上传数量或选择图片失败时 | (message: String) 错误信息                        |
| select  | 当用户选择图片后触发，在开始上传之前                 | (res: Object) 选择图片的结果对象                  |
| success | 当图片上传成功时触发                                 | (url: String) 上传成功的图片 URL                  |
| delete  | 当删除图片时触发                                     | (image: String, index: Number) 被删除的图片和索引 |

## 注意事项

1. 组件内部会自动初始化 OSS 上传功能，使用`this.Oss.getAliyunOfStudent()`
2. 组件内部使用了`u-image`组件来展示图片，确保项目中已安装此依赖
3. 错误处理已优化，能区分用户取消上传和其他失败情况
4. 图片布局已优化，支持设置每行显示数量和图片间距
5. 默认图片尺寸为 148rpx，可根据实际需求调整
