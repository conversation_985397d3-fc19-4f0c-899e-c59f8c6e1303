<template>
  <view class="image-uploader">
    <view class="image-list">
      <view
        v-for="(image, index) in imageList"
        :key="index"
        class="image-item"
        :style="{
          width: imageSize + 'rpx',
          height: imageSize + 'rpx',
          marginRight:
            index % rowCount === rowCount - 1 ? '0' : imageSpacing + 'rpx'
        }"
      >
        <u-image
          class="image"
          :src="image"
          @click="handlePreviewImage(image)"
          :width="imageSize + 'rpx'"
          :height="imageSize + 'rpx'"
          :radius="'16rpx'"
          mode="aspectFill"
          :showMenuByLongpress="false"
        ></u-image>
        <view class="image-delete" @click="handleDeleteImage(index)"> </view>
      </view>
      <view
        v-if="imageList.length < maxCount"
        class="upload-button"
        :style="{ width: imageSize + 'rpx', height: imageSize + 'rpx' }"
        @click="handleChooseImage"
      >
        <view class="upload-icon"></view>
        <text class="upload-text">{{ uploadText }}</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: "ImageUploader",
  props: {
    // 使用v-model绑定的值
    value: {
      type: Array,
      default: () => []
    },
    // 最大上传数量
    maxCount: {
      type: Number,
      default: 6
    },
    // 一行几张图
    rowCount: {
      type: Number,
      default: 4
    },
    // 图片间距
    imageSpacing: {
      type: Number,
      default: 16
    },
    // 上传按钮文字
    uploadText: {
      type: String,
      default: "图片"
    },
    // 图片尺寸，单位rpx
    imageSize: {
      type: Number,
      default: 148
    }
  },
  data() {
    return {
      imageList: this.value || []
    };
  },
  watch: {
    value(val) {
      this.imageList = val;
    },
    imageList(val) {
      this.$emit("input", val);
    }
  },
  created() {
    this.Oss.getAliyunOfStudent();
  },
  methods: {
    // 选择图片
    handleChooseImage() {
      if (this.imageList.length >= this.maxCount) {
        this.$emit("error", `最多只能上传${this.maxCount}张图片`);
        return;
      }

      uni.chooseImage({
        count: this.maxCount - this.imageList.length,
        sizeType: ["original", "compressed"],
        sourceType: ["album", "camera"],
        success: (res) => {
          this.$emit("select", res);

          // 遍历临时文件进行上传
          res.tempFiles.forEach((item) => {
            // 检查是否存在OSS上传方法

            this.Oss.uploadFile(
              item,
              (url) => {
                this.imageList.push(url);
                this.$emit("success", url);
              },
              "path"
            );
          });
        },
        fail: (err) => {
          console.log("err :>> ", err);
          if (err.errMsg.includes("fail cancel")) {
            this.$emit("error", "用户取消上传！");
          } else {
            this.$emit("error", "上传失败，请稍后再试！");
          }
        }
      });
    },

    // 预览图片
    handlePreviewImage(image) {
      uni.previewImage({
        urls: this.imageList,
        current: image
      });
    },

    // 删除图片
    handleDeleteImage(index) {
      const removedImage = this.imageList[index];
      this.imageList.splice(index, 1);
      this.$emit("delete", removedImage, index);
    }
  }
};
</script>

<style lang="scss" scoped>
.image-uploader {
  width: 100%;

  .image-list {
    display: flex;
    flex-wrap: wrap;
  }

  .image-item {
    // width: 160rpx;
    // height: 160rpx;
    border-radius: 16rpx;
    margin-right: 16rpx;
    margin-bottom: 16rpx;
    position: relative;
    background-color: #fafafa;
    // &:nth-child(4n) {
    //   margin-right: 0;
    // }
  }

  .image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 16rpx;
    overflow: hidden;
  }

  .image-delete {
    position: absolute;
    top: -10rpx;
    right: -10rpx;
    width: 40rpx;
    height: 40rpx;
    background-color: rgba(51, 51, 51, 0.5);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-image: url(https://tg-prod.oss-cn-beijing.aliyuncs.com/7d2b660a-78e4-45ee-9bd7-1450f284e706.png);
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
  }

  .upload-button {
    width: 160rpx;
    height: 160rpx;
    background-color: #fafafa;
    border-radius: 16rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-bottom: 20rpx;
  }

  .upload-icon {
    width: 50rpx;
    height: 50rpx;
    background-image: url("https://tg-prod.oss-cn-beijing.aliyuncs.com/0b05b29f-763a-4326-8493-f9223c99e6b0.png");
    background-size: cover;
    margin-bottom: 4rpx;
  }

  .upload-text {
    font-size: 24rpx;
    color: #999;
  }
}
</style>
