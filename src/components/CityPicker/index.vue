<template>
  <u-popup
    :show="show"
    mode="top"
    height="600rpx"
    @close="closeCityPicker"
    :safeAreaInsetBottom="false"
    round="20rpx"
  >
    <view class="city-picker">
      <!-- 弹窗头部 -->
      <view class="picker-header">
        <view class="header-title">切换地区</view>
      </view>

      <!-- 城市网格 -->
      <view class="city-grid">
        <view
          class="city-item"
          v-for="(city, index) in cityList"
          :key="index"
          :class="{
            'city-item-active': city[keys[0]] === (selectedCity || '全国')
          }"
          @click="selectCity(city)"
        >
          <text>{{ city[keys[0]] }}</text>
        </view>
      </view>
    </view>
  </u-popup>
</template>
<script>
export default {
  name: "CityPicker",
  props: {
    show: {
      type: Boolean,
      default: false
    },
    cityList: {
      type: Array,
      default: () => []
    },
    selectedCity: {
      type: String,
      default: ""
    },
    keys: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    closeCityPicker() {
      this.$emit("close");
    },
    selectCity(city) {
      this.$emit("select", city[this.keys[0]]);
    }
  }
};
</script>
<style lang="scss" scoped>
.city-picker {
  padding: 32rpx;

  .picker-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 40rpx;
    margin-top: 90rpx;
    .header-title {
      font-size: 34rpx;
      font-weight: 700;
      color: #333;
    }

    .header-action {
      display: flex;
      align-items: center;
      gap: 24rpx;

      .close-btn {
        width: 40rpx;
        height: 40rpx;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }

  .city-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 24rpx;

    .city-item {
      width: calc(25% - 18rpx);
      height: 80rpx;
      background: #f6f6f6;
      border-radius: 12rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: center;
      color: #333;
      text-align: center;
      font-size: 30rpx;
      font-weight: 400;
      line-height: 46rpx;

      &.city-item-active {
        background: rgba(255, 197, 37, 0.05);
        color: #ffc525;
        font-weight: 500;
      }

      &:active {
        // opacity: 0.8;
      }
    }
  }
}
</style>
