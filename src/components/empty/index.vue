<template>
  <div class="empty-icon">
    <u-empty
      :width="width"
      :textSize="textSize"
      :textColor="textColor"
      :text="text"
      :icon="icon"
    >
    </u-empty>
  </div>
</template>

<script>
export default {
  name: "EmptyIcon",
  props: {
    width: {
      type: Number,
      default: 82
    },
    text: {
      type: String,
      default: "暂无数据~"
    },
    textSize: {
      type: String,
      default: "28rpx"
    },
    textColor: {
      type: String,
      default: "#999"
    },
    icon: {
      type: String,
      default:
        "https://tg-prod.oss-cn-beijing.aliyuncs.com/d02814d0-bfd3-48bf-8d9d-80cda362b89a.webp"
    }
  }
};
</script>

<style lang="scss" scoped>
.empty-icon {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
</style>
