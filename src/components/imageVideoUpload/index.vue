<template>
  <view class="image-uploader">
    <view class="image-list">
      <view
        v-for="(file, index) in fileList"
        :key="index"
        class="image-item"
        :style="{
          width: imageSize + 'rpx',
          height: imageSize + 'rpx',
          marginRight:
            index % rowCount === rowCount - 1 ? '0' : imageSpacing + 'rpx'
        }"
      >
        <u-image
          v-if="file.type === 'image'"
          class="image"
          :src="file.url"
          @click="handlePreviewImage(file)"
          :width="imageSize + 'rpx'"
          :height="imageSize + 'rpx'"
          :radius="'16rpx'"
          mode="aspectFill"
          :showMenuByLongpress="false"
        ></u-image>
        <div
          v-if="file.type === 'video'"
          @click="handlePlayVideo(file)"
          class="poster-wrapper"
        >
          <u-image
            class="image"
            :src="file.poster || `${file.url}?${imageProcess}`"
            :width="imageSize + 'rpx'"
            :height="imageSize + 'rpx'"
            :radius="'16rpx'"
            mode="aspectFill"
            :showMenuByLongpress="false"
          ></u-image>
          <view class="play-btn"></view>
        </div>
        <!-- <video
          v-if="file.type === 'video'"
          class="video"
          direction="0"
          object-fit="cover"
          :width="imageSize + 'rpx'"
          :height="imageSize + 'rpx'"
          :radius="'16rpx'"
          :src="file.url"
          :show-center-play-btn="true"
          :show-fullscreen-btn="false"
          :picture-in-picture-mode="[]"
          :poster="`${file.url}?${imageProcess}`"
          play-btn-position="center"
        ></video> -->
        <view class="image-delete" @click="handleDeleteImage(index)"> </view>
      </view>
      <view
        v-if="fileList.length < maxCount && !resourceHub"
        class="upload-button"
        :style="{ width: imageSize + 'rpx', height: imageSize + 'rpx' }"
        @click="handleChooseImage"
      >
        <view class="upload-icon"></view>
        <!-- <text class="upload-text">{{ uploadText }}</text> -->
      </view>
      <view
        v-if="fileList.length < maxCount && resourceHub"
        class="upload-button-hub"
        :style="{ width: imageSize + 'rpx', height: imageSize + 'rpx' }"
      >
        <u-image
          src="https://tg-prod.oss-cn-beijing.aliyuncs.com/3ad413e5-f17e-4a89-8a75-724ebd564c87.png"
          :width="imageSize + 'rpx'"
          :height="imageSize + 'rpx'"
          @click="handleChooseImage"
        ></u-image>
        <!-- <view class="upload-button-hub-icon" @click="handleResourceHub">
          <u-image
            src="https://tg-prod.oss-cn-beijing.aliyuncs.com/a8fa7d56-5a15-4a7e-9e98-bdf8da1f3b70.png"
            width="130rpx"
            height="47rpx"
          ></u-image>
        </view> -->
        <!-- <text class="upload-text">{{ uploadText }}</text> -->
      </view>
      <view
        v-if="fileList.length < maxCount && resourceHub"
        class="upload-button-hub image-hub"
        :style="{ width: imageSize + 'rpx', height: imageSize + 'rpx' }"
      >
        <u-image
          src="https://tg-prod.oss-cn-beijing.aliyuncs.com/29e63cdf-6933-4e3f-8808-c78d7fcb44f7.png"
          :width="imageSize + 'rpx'"
          :height="imageSize + 'rpx'"
          @click="handleResourceHub"
        ></u-image>
        <!-- <view class="upload-button-hub-icon" @click="handleResourceHub">
          <u-image
            src="https://tg-prod.oss-cn-beijing.aliyuncs.com/a8fa7d56-5a15-4a7e-9e98-bdf8da1f3b70.png"
            width="130rpx"
            height="47rpx"
          ></u-image>
        </view> -->
        <!-- <text class="upload-text">{{ uploadText }}</text> -->
      </view>
    </view>
    <div v-if="showVideo" class="video-box">
      <video
        id="myVideo"
        :src="videoUrl"
        class="video"
        direction="0"
        :picture-in-picture-mode="[]"
        :show-fullscreen-btn="false"
        controls
        @fullscreenchange="bindfullscreenchange"
      ></video>
    </div>
    <!-- 弹窗组件 -->
    <alert
      :visible="alertVisible"
      :content="alertContent"
      :button-text="alertButtonText"
      @confirm="handleAlertConfirm"
      @close="handleAlertClose"
    />
    <ResourceHub
      :visible="resourceHubVisible"
      :department_id="department_id"
      :max-count="maxCount"
      :current-count="fileList.length"
      @close="resourceHubVisible = false"
      @confirm="handleResourceHubConfirm"
    />
  </view>
</template>

<script>
import Alert from "@/components/alert/index.vue";
import ResourceHub from "@/components/resourceHub/index.vue";
export default {
  name: "ImageUploader",
  components: {
    Alert,
    ResourceHub
  },
  props: {
    // 使用v-model绑定的值
    value: {
      type: Array,
      default: () => []
    },
    // 最大上传数量
    maxCount: {
      type: Number,
      default: 6
    },
    // 一行几张图
    rowCount: {
      type: Number,
      default: 4
    },
    // 图片间距
    imageSpacing: {
      type: Number,
      default: 16
    },
    // 上传按钮文字
    uploadText: {
      type: String,
      default: "图片"
    },
    // 图片尺寸，单位rpx
    imageSize: {
      type: Number,
      default: 148
    },
    // 图片处理参数
    imageProcess: {
      type: String,
      default: "x-oss-process=video/snapshot,t_2000,m_fast,w_320,ar_auto"
    },
    // 资料库上传
    resourceHub: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      fileList: this.value || [],
      showVideo: false,
      videoUrl: "",
      alertVisible: false,
      alertContent: "视频文件大于200M，请压缩后再上传！",
      alertButtonText: "我知道了",
      resourceHubVisible: false
    };
  },
  watch: {
    value(val) {
      this.fileList = val;
    },
    fileList(val) {
      this.$emit("input", val);
    }
  },
  created() {
    this.Oss.getAliyun();
  },
  methods: {
    handleAlertConfirm() {
      this.alertVisible = false;
    },
    handleAlertClose() {
      this.alertVisible = false;
    },
    // 选择图片
    handleChooseImage() {
      if (this.fileList.length >= this.maxCount) {
        this.$emit("error", `最多只能上传${this.maxCount}张图片`);
        return;
      }
      const count = this.maxCount - this.fileList.length;
      uni.chooseMedia({
        count,
        mediaType: ["image", "video"],
        sourceType: ["album", "camera"],
        maxDuration: 60,
        camera: "back",
        success: (res) => {
          this.$emit("select", res);

          // 遍历临时文件进行上传
          res.tempFiles.forEach((item) => {
            // const item = res.tempFiles[0];
            // 检查是否存在OSS上传方法
            if (item.fileType === "image") {
              const imageUrl = item.tempFilePath;
              uni.getImageInfo({
                src: imageUrl,
                success: (image) => {
                  this.Oss.uploadFile(item, (url) => {
                    this.fileList.push({
                      url,
                      size: item.size,
                      width: image.width,
                      height: image.height,
                      type: item.fileType
                    });
                    this.$emit("success", url);
                  });
                },
                fail: (err) => {
                  uni.showToast({
                    title: "上传文件失败",
                    icon: "none"
                  });
                  console.log("err :>> ", err);
                }
              });
            } else {
              if (item.size > 200 * 1024 * 1024) {
                this.alertContent = "视频文件大于200M，请压缩后再上传！";
                this.alertVisible = true;
                return false;
              }
              this.Oss.uploadFile(item, (url) => {
                this.fileList.push({
                  url,
                  size: item.size,
                  width: item.width,
                  height: item.height,
                  type: item.fileType
                });
                this.$emit("success", url);
              });
            }
          });
        },
        fail: (err) => {
          console.log("err :>> ", err);
          if (err.errMsg.includes("fail cancel")) {
            this.$emit("error", "用户取消上传！");
          } else {
            this.$emit("error", "上传失败，请稍后再试！");
          }
        }
      });
    },

    // 预览图片
    handlePreviewImage(file) {
      uni.previewImage({
        urls: [file.url],
        current: file.url
      });
    },

    // 删除图片
    handleDeleteImage(index) {
      const removedImage = this.fileList[index];
      this.fileList.splice(index, 1);
      this.$emit("delete", removedImage, index);
    },

    // 播放视频
    handlePlayVideo(file) {
      console.log("file :>> ", file);
      this.showVideo = true;
      this.videoContext = uni.createVideoContext("myVideo", this);
      this.videoUrl = file.url;

      this.videoContext.requestFullScreen();
      this.videoContext.play();
    },
    bindfullscreenchange(e) {
      const { fullScreen } = e.detail;
      if (fullScreen) {
        console.log("fullScreen :>> ", fullScreen);
      } else {
        this.showVideo = false;
      }
    },
    handleResourceHub() {
      this.resourceHubVisible = true;
    },
    handleResourceHubConfirm(content, index, poster) {
      console.log("content :>> ", content);
      console.log("index :>> ", index);
      console.log("poster :>> ", poster);
      if (index === 1) {
        content.forEach((item) => {
          uni.getImageInfo({
            src: item.content,
            success: (image) => {
              this.fileList.push({
                url: item.content,
                width: image.width,
                height: image.height,
                type: "image"
              });
            }
          });
        });
      } else if (index === 2) {
        content.forEach((item) => {
          this.fileList.push({
            url: item.content,
            poster: item.poster,
            type: "video"
          });
        });
      } else {
        this.$emit("textHub", content);
      }
      // this.fileList.push({
      //   url: content,
      //   poster,
      //   type: "image"
      // });
    }
  }
};
</script>

<style lang="scss" scoped>
.image-uploader {
  width: 100%;

  .image-list {
    display: flex;
    flex-wrap: wrap;
  }

  .image-item {
    // width: 160rpx;
    // height: 160rpx;
    border-radius: 16rpx;
    margin-right: 16rpx;
    margin-bottom: 16rpx;
    position: relative;
    background-color: #fafafa;
    // &:nth-child(4n) {
    //   margin-right: 0;
    // }
  }

  .image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 16rpx;
    overflow: hidden;
  }

  .video {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 16rpx;
    overflow: hidden;
  }

  .image-delete {
    position: absolute;
    top: -10rpx;
    right: -10rpx;
    width: 40rpx;
    height: 40rpx;
    background-color: rgba(51, 51, 51, 0.5);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-image: url(https://tg-prod.oss-cn-beijing.aliyuncs.com/7d2b660a-78e4-45ee-9bd7-1450f284e706.png);
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
  }

  .upload-button {
    width: 160rpx;
    height: 160rpx;
    background-color: #fafafa;
    border-radius: 16rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-bottom: 16rpx;
  }
  .image-hub {
    margin-left: 11rpx;
  }
  .upload-button-hub {
    width: 160rpx;
    height: 160rpx;
    // background-color: #fafafa;
    border-radius: 16rpx;
    display: flex;
    flex-direction: column;
    // align-items: center;
    // justify-content: center;
    margin-bottom: 16rpx;
    position: relative;
    .upload-button-hub-icon {
      width: 170rpx;
      height: 70rpx;
      position: absolute;
      top: 0;
      right: -55rpx;
      // 右靠边
      display: flex;
      justify-content: flex-end;
    }
  }
  .upload-icon {
    width: 50rpx;
    height: 50rpx;
    background-image: url("https://tg-prod.oss-cn-beijing.aliyuncs.com/029aee0b-8d3f-48dd-8028-e56e401fcdee.png");
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
  }

  .upload-text {
    font-size: 24rpx;
    color: #999;
  }

  .poster-wrapper {
    position: relative;
  }

  .play-btn {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 50rpx;
    height: 50rpx;
    background-image: url("https://tg-prod.oss-cn-beijing.aliyuncs.com/52c7d9d2-ac99-4743-8fb7-f21f833c000a.png");
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
  }
  .video-box {
    width: 100%;
    height: 100%;
    position: fixed;
    top: 0;
    left: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 999;
  }
}
</style>
