<template>
  <view class="custom-tabs">
    <view class="tabs-header">
      <view
        v-for="(item, index) in list"
        :key="index"
        class="tab-item"
        :class="{ active: currentIndex === index }"
        :style="{ color: currentIndex === index ? activeColor : '#999' }"
        @click="handleTabClick(index)"
      >
        {{ item.name }}
      </view>
    </view>
    <view class="tabs-line-container">
      <view
        class="tabs-line"
        :style="{
          transform: `translateX(${lineLeft}px)`,
          transition: 'transform 0.3s',
          background: activeColor
        }"
      ></view>
    </view>
  </view>
</template>

<script>
export default {
  name: "CustomTabs",
  props: {
    list: {
      type: Array,
      default: () => []
    },
    current: {
      type: Number,
      default: 0
    },
    activeColor: {
      type: String,
      default: "#fb0"
    }
  },
  data() {
    return {
      currentIndex: this.current,
      lineLeft: 0,
      tabItemWidth: 0
    };
  },
  watch: {
    current: {
      handler(newVal) {
        console.log(newVal, "newVal");
        this.currentIndex = newVal;
        this.updateLinePosition();
      },
      immediate: true
    }
  },
  mounted() {
    this.$nextTick(() => {
      // 获取每个tab的宽度
      const query = uni.createSelectorQuery().in(this);
      query
        .select(".tab-item")
        .boundingClientRect((rect) => {
          if (rect) {
            this.tabItemWidth = rect.width;
            this.updateLinePosition();
          }
        })
        .exec();
    });
  },
  methods: {
    handleTabClick(index) {
      if (this.currentIndex === index) return;
      this.currentIndex = index;
      this.$emit("change", index);
      this.updateLinePosition();
    },
    updateLinePosition() {
      // 计算指示器位置
      const centerOffset = (this.tabItemWidth - uni.upx2px(28)) / 2; // 28rpx是指示器宽度
      this.lineLeft = this.currentIndex * this.tabItemWidth + centerOffset;
    }
  }
};
</script>

<style lang="scss" scoped>
.custom-tabs {
  width: 100%;
  background: #fff;
  position: relative;
  height: 92rpx;
  .tabs-header {
    display: flex;
    position: relative;

    .tab-item {
      flex: 1;
      height: 92rpx;
      line-height: 92rpx;
      text-align: center;
      font-size: 30rpx;
      color: #999;
      transition: color 0.3s;

      &.active {
        color: #fb0;
        font-weight: bold;
      }
    }
  }

  .tabs-line-container {
    position: relative;
    height: 6rpx;

    .tabs-line {
      position: absolute;
      left: 0;
      bottom: 20rpx;
      width: 28rpx;
      height: 6rpx;
      background: #ffc525;
      border-radius: 11rpx;
    }
  }
}
</style>
