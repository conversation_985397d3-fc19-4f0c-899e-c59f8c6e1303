<template>
  <view class="expand-text">
    <!-- 文本容器 -->
    <view
      class="text-wrapper"
      :class="{ collapsed: collapsed, expanded: !collapsed }"
      ref="textContainer"
    >
      <text class="text-content" selectable>{{ text }}</text>
    </view>

    <!-- 展开/收起按钮 -->
    <view v-if="showToggle" class="toggle-btn" @tap="toggleExpand">
      {{ collapsed ? "全文" : "收起" }}
      <image
        :class="{ rotate: !collapsed }"
        class="toggle-icon"
        src="https://tg-prod.oss-cn-beijing.aliyuncs.com/c343b7c0-aa1d-4c07-9e2a-95b8fa1988ff.webp"
        mode="widthFix"
      />
    </view>
  </view>
</template>

<script>
export default {
  props: {
    text: {
      type: String,
      default: ""
    },
    lines: {
      type: Number,
      default: 2
    }
  },
  data() {
    return {
      collapsed: true,
      showToggle: false
    };
  },
  mounted() {
    this.checkOverflow();
  },
  watch: {
    text() {
      this.$nextTick(() => {
        this.checkOverflow();
      });
    }
  },
  methods: {
    toggleExpand() {
      this.collapsed = !this.collapsed;
    },
    checkOverflow() {
      // 确保DOM已更新
      this.$nextTick(() => {
        const query = uni.createSelectorQuery().in(this);
        query
          .select(".text-content")
          .boundingClientRect((textRect) => {
            // 计算行高（近似值）
            console.log(uni.getSystemInfoSync().fontSizeSetting);
            const lineHeight =
              parseInt(uni.getSystemInfoSync().fontSizeSetting) * 1.5;
            const maxHeight = lineHeight * this.lines;
            // 判断是否需要显示切换按钮
            this.showToggle = textRect.height > maxHeight;
            // 如果不需要显示按钮，确保文本完全展开
            if (!this.showToggle) {
              this.collapsed = false;
            }
          })
          .exec();
      });
    }
  }
};
</script>

<style scoped>
.expand-text {
  width: 100%;
}

.text-wrapper {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  word-break: break-all;
  line-height: 1.5em;
}

.text-wrapper.collapsed {
  -webkit-line-clamp: 5; /* 默认两行 */
}

.text-wrapper.expanded {
  -webkit-line-clamp: initial;
}

.text-content {
  font-size: 28rpx;
  color: #333;
}

.toggle-btn {
  color: #fb0;
  font-size: 28rpx;
  margin-top: 10rpx;
  display: inline-flex;
  align-items: center;
  font-weight: 500;
}
.toggle-icon {
  width: 30rpx;
  height: 50rpx;
}
.rotate {
  transform: rotate(180deg);
}
.toggle-btn .icon {
  margin-left: 6rpx;
  font-size: 24rpx;
}
</style>
