<template>
  <u-popup
    :show="show"
    @close="handleClose"
    mode="center"
    :round="10"
    :customStyle="{
      borderRadius: '32rpx',
      background: '#FFFFFF',
      fontSize: '32rpx',
      width: '582rpx',
      ...customStyle
    }"
    :safeAreaInsetBottom="false"
    :closeOnClickOverlay="closeOnClickOverlay"
  >
    <view class="confirm-popup">
      <view class="content">
        <text class="dectitle" v-if="dectitle">{{ dectitle }}</text>
        <text class="title">{{ title }}</text>
        <view class="btn-group">
          <button class="cancel-btn" @tap="handleClose" :style="cancelBtnStyle">
            {{ cancelText }}
          </button>
          <button
            class="confirm-btn"
            @tap="handleConfirm"
            :style="confirmBtnStyle"
          >
            {{ confirmText }}
          </button>
        </view>
      </view>
    </view>
  </u-popup>
</template>

<script>
export default {
  name: "ConfirmPopup",
  props: {
    value: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: "确认操作"
    },
    cancelText: {
      type: String,
      default: "取消"
    },
    confirmText: {
      type: String,
      default: "确定"
    },
    customStyle: {
      type: Object,
      default: () => ({})
    },
    cancelBtnStyle: {
      type: Object,
      default: () => ({})
    },
    confirmBtnStyle: {
      type: Object,
      default: () => ({})
    },
    dectitle: {
      type: String,
      default: ""
    },
    closeOnClickOverlay: {
      type: Boolean,
      default: true
    }
  },
  computed: {
    show: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit("input", val);
      }
    }
  },
  methods: {
    handleClose() {
      this.show = false;
      this.$emit("cancel");
    },
    handleConfirm() {
      this.$emit("confirm");
      this.show = false;
    }
  }
};
</script>

<style lang="scss" scoped>
.confirm-popup {
  padding: 60rpx 34rpx 0;

  .content {
    display: flex;
    flex-direction: column;
    align-items: center;
    .dectitle {
      color: #333;

      text-align: center;
      font-size: 34rpx;
      font-style: normal;
      font-weight: 500;
      line-height: 48rpx;
      margin-bottom: 40rpx;
    }
    .title {
      color: #333;
      text-align: center;
      font-family: PingFang SC;
      font-size: 36rpx;
      font-weight: 500;
      line-height: 56rpx;
      margin-bottom: 70rpx;
    }

    .btn-group {
      display: flex;
      gap: 20rpx;
      width: 100%;
      margin-bottom: 60rpx;
      .cancel-btn,
      .confirm-btn {
        flex: 1;
        height: 88rpx;
        border-radius: 44rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 32rpx;
        font-weight: 500;
        transition: transform 0.2s ease;

        &:active {
          transform: scale(0.98);
        }
      }

      .cancel-btn {
        background: #fff;
        color: #ffbb00;
        border: 3rpx solid #ffbb00;
      }

      .confirm-btn {
        background: linear-gradient(15deg, #ffbf0d 18.1%, #ffcb3c 83.29%);
        box-shadow: 0px -5rpx 12rpx 0px #fc0 inset,
          0px 9rpx 20rpx 0px #fff7e1 inset;
        filter: drop-shadow(0px 4rpx 4rpx rgba(255, 192, 18, 0.11));
        color: #fff;
      }
    }
  }
}
</style>
