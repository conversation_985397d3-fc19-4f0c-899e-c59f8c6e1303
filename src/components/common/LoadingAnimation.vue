<template>
  <view
    class="loading-container"
    v-if="visible"
    :style="{
      height: height ? height + 'rpx' : 'auto',
      backgroundColor: bgColor
    }"
  >
    <view class="loading-content">
      <view class="loading-animation" :style="{ width: width + 'rpx' }">
        <!-- 底部渐变底座 -->
        <view class="animation-base"></view>

        <view
          class="animation-character character-left"
          :style="{
            backgroundImage: `url(${
              characterImage ||
              'https://tg-prod.oss-cn-beijing.aliyuncs.com/93e7c558-2696-48a0-84b9-95916bbea418.webp'
            })`,
            width: characterSize + 'rpx',
            height: characterSize + 'rpx',
            marginTop: '-' + characterSize + 'rpx'
          }"
        ></view>
        <!-- <view
          class="animation-character character-right"
          :style="{
            backgroundImage: `url(${
              characterImage || 'https://placeholder.com/80x80'
            })`,
            width: characterSize + 'rpx',
            height: characterSize + 'rpx'
          }"
        ></view> -->
      </view>
      <!-- <text
        class="loading-text"
        v-if="showText"
        :style="{
          color: textColor,
          fontSize: textSize + 'rpx'
        }"
        >{{ text }}</text
      > -->
    </view>
  </view>
</template>

<script>
export default {
  name: "LoadingAnimation",
  props: {
    visible: {
      type: Boolean,
      default: true
    },
    text: {
      type: String,
      default: "加载中..."
    },
    showText: {
      type: Boolean,
      default: true
    },
    characterImage: {
      type: String,
      default: ""
    },
    width: {
      type: Number,
      default: 200
    },
    height: {
      type: Number,
      default: 0 // 0 表示自适应高度
    },
    characterSize: {
      type: Number,
      default: 260
    },
    textColor: {
      type: String,
      default: "#999"
    },
    textSize: {
      type: Number,
      default: 28
    },
    bgColor: {
      type: String,
      default: "transparent"
    }
  }
};
</script>

<style lang="scss" scoped>
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  padding: 40rpx 0;
  margin-top: 100rpx;
  .loading-content {
    display: flex;
    flex-direction: column;
    align-items: center;

    .loading-animation {
      display: flex;
      justify-content: center;
      width: 260rpx;
      //   height: 120rpx;
      position: relative;

      // 添加底座样式
      .animation-base {
        position: absolute;
        bottom: 0;
        width: 112rpx;
        height: 22rpx;
        background: url("https://tg-prod.oss-cn-beijing.aliyuncs.com/09c51cd5-46d6-4788-9638-d28f77137e5f.webp")
          no-repeat center center;
        border-radius: 50%;
        // transform: scaleX(1);
        z-index: 0;
        // 添加呼吸效果动画
        animation: breathe 1.2s infinite ease-in-out;
      }

      .animation-character {
        width: 80rpx;
        height: 80rpx;
        margin: 0 20rpx;
        background-size: contain;
        background-repeat: no-repeat;
        background-position: center;
        position: relative;
        z-index: 1; // 确保角色在底座上方

        // 使用占位图片，实际项目中替换为实际的小狐狸图片
        background-image: url("https://placeholder.com/80x80");

        // 添加跳跃动画
        animation-name: jump;
        animation-duration: 1.2s;
        animation-iteration-count: infinite;
        animation-timing-function: ease-in-out;

        &.character-left {
          animation-delay: 0s;
        }

        &.character-right {
          animation-delay: 0.6s;
        }
      }
    }

    .loading-text {
      margin-top: 20rpx;
      font-size: 28rpx;
      color: #999;
    }
  }
}

@keyframes jump {
  0% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-30rpx);
  }
  100% {
    transform: translateY(0);
  }
}

// 底座呼吸效果动画
@keyframes breathe {
  0% {
    transform: scaleX(0.5);
    opacity: 0.7;
  }
  50% {
    transform: scaleX(1);
    opacity: 0.9;
  }
  100% {
    transform: scaleX(0.5);
    opacity: 0.7;
  }
}
</style>
