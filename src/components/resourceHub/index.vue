<template>
  <u-popup
    :show="visible"
    mode="bottom"
    :closeable="false"
    :round="20"
    height="80%"
    @close="handleClose"
    :customStyle="{
      height: '1000rpx'
    }"
  >
    <view class="resource-hub-tab">
      <u-tabs
        :list="tabList"
        :current="tabIndex"
        @click="handleTabClick"
        :activeStyle="{
          color: '#3667f0',
          fontWeight: 'bold'
        }"
        :inactiveStyle="{
          color: '#606266'
        }"
        lineColor="#3667f0"
        lineHeight="3px"
      >
      </u-tabs>
      <view class="tab-content">
        <view class="serach-input">
          <u-input
            placeholder="请输入内容"
            prefixIcon="search"
            v-model="currentTabData.searchValue"
            :customStyle="{
              height: '40px',
              borderRadius: '20px'
            }"
          >
          </u-input>
          <view @click="search" class="search-txt">搜索</view>
        </view>
        <view v-if="tabIndex !== 2" class="count-tip">
          <text class="tip-text"
            >还可选择 {{ remainingCount - selectedCount }} 个文件</text
          >
        </view>
        <scroll-view
          class="infinite-list-wrapper"
          scroll-y
          @scrolltolower="load"
          :lower-threshold="50"
        >
          <view
            class="list"
            v-for="(item2, index2) in currentTabData.data"
            :key="index2"
          >
            <view class="list-item">
              <view class="name">{{ item2.name }}</view>
              <view v-if="tabIndex === 0" class="box img">
                <u-image
                  :src="item2.content"
                  width="38.8vw"
                  height="24vw"
                  mode="aspectFill"
                  @click="previewImage(item2.content)"
                  :showLoading="true"
                  :showError="true"
                  radius="2.6667vw"
                >
                </u-image>
              </view>
              <view
                @click="previewVideoHandle(item2)"
                v-else-if="tabIndex === 1"
                class="box video"
              >
                <view class="play-btn"></view>
                <view
                  :style="'background-image: url(' + item2.poster + ')'"
                  class="poster-img"
                />
              </view>
              <view
                v-else-if="tabIndex === 2"
                @click="prewViewWordsHandle(item2.content)"
                style="width: 38.8vw; height: 24vw"
                class="box text"
              >
                <view class="words">{{ item2.content }}</view>
              </view>
              <view class="btn-box" @click.stop="handleItemSelect(index2)">
                <view
                  class="custom-checkbox"
                  :class="{
                    checked: item2.checked,
                    'single-select': tabIndex === 2,
                    'multi-select': tabIndex !== 2
                  }"
                >
                  <view v-if="item2.checked" class="checkbox-icon">✓</view>
                </view>
              </view>
            </view>
          </view>
          <view v-if="currentTabData.loading" class="loading">
            <u-loading-icon mode="flower"></u-loading-icon>
            <text style="margin-left: 6px">加载中...</text>
          </view>
          <view v-if="currentTabData.noMore" class="no-more"
            >没有更多了哦~</view
          >
        </scroll-view>
      </view>
      <view class="resource-hub-footer">
        <u-button
          type="primary"
          @click="confirm"
          :customStyle="{
            width: '100%',
            background: 'linear-gradient(270deg, #3667f0 0%, #568ff5 100%)',
            borderRadius: '0',
            fontWeight: 'bold',
            padding: '15px 20px'
          }"
          >确定{{ selectedCount > 0 ? `(${selectedCount})` : "" }}</u-button
        >
      </view>
    </view>

    <!-- 文字预览弹窗 -->
    <u-popup
      :show="prewViewWordsVisible"
      mode="bottom"
      height="50%"
      :closeable="true"
      :round="20"
      @close="prewViewWordsVisible = false"
    >
      <view class="prew-view-words">
        <rich-text :nodes="prewViewWords"></rich-text>
      </view>
    </u-popup>

    <!-- 视频预览弹窗 -->
    <u-popup
      :show="previewVideoVisible"
      mode="bottom"
      height="50%"
      :round="20"
      @close="closeVideoPreview"
    >
      <view class="video-box" v-if="previewVideoVisible">
        <video
          id="drawer-video"
          :src="videoSrc"
          class="video-js vjs-default-skin vjs-big-play-centered"
          preload="auto"
        ></video>
      </view>
    </u-popup>
  </u-popup>
</template>

  <script>
import { getFileList } from "@/services/student/timeAlbum";

/* global videojs */

export default {
  name: "ResourceHub",
  data() {
    return {
      tabIndex: 0,
      tabData: [
        {
          name: "图片库",
          value: 0,
          data: [],
          page: 1,
          searchValue: "",
          loading: false,
          noMore: false
        },
        {
          name: "视频库",
          value: 1,
          page: 1,
          data: [],
          searchValue: "",
          loading: false,
          noMore: false
        },
        {
          name: "话术库",
          value: 2,
          page: 1,
          data: [],
          searchValue: "",
          loading: false,
          noMore: false
        }
      ],
      prewViewWordsVisible: false,
      previewVideoVisible: false,
      prewViewWords: "",
      videoSrc: "",
      poster: "",
      drawerPlayer: null
    };
  },
  props: {
    department_id: {
      type: String,
      default: "",
      required: true
    },
    token: {
      type: String,
      default: "",
      required: true
    },
    visible: {
      type: Boolean,
      default: false
    },
    maxCount: {
      type: Number,
      default: 6
    },
    currentCount: {
      type: Number,
      default: 0
    }
  },
  computed: {
    // 生成u-tabs需要的数据格式
    tabList() {
      return this.tabData.map((item) => ({
        name: item.name,
        value: item.value
      }));
    },
    // 当前选中的tab数据
    currentTabData() {
      return this.tabData[this.tabIndex] || {};
    },
    // 计算已选择的数量
    selectedCount() {
      const currData = this.tabData[this.tabIndex]?.data || [];
      return currData.filter((item) => item.checked).length;
    },
    // 计算剩余可选择的数量
    remainingCount() {
      return this.maxCount - this.currentCount;
    },
    // 是否可以继续选择
    canSelectMore() {
      // 话术库单选模式，或者多选模式下还有剩余数量
      return this.tabIndex === 2 || this.selectedCount < this.remainingCount;
    }
  },
  watch: {
    previewVideoVisible(val) {
      if (!val) {
        if (this.drawerPlayer) {
          console.log("this.myPlayer :>>pause ");
          this.drawerPlayer.pause();
        }
      }
    }
  },
  mounted() {
    this.getList();
  },
  methods: {
    handleClose() {
      const currData = this.tabData[this.tabIndex].data;
      currData.map((item) => {
        item.checked = false;
      });
      this.$emit("close");
    },
    confirm() {
      const currData = this.tabData[this.tabIndex].data;
      const checkedItems = currData.filter((item) => item.checked);

      if (checkedItems.length > 0) {
        if (this.tabIndex === 2) {
          // 话术库单选 - 返回单个项目
          const item = checkedItems[0];
          const content = item.content;
          const poster = item.poster || "";
          this.$emit("confirm", content, this.tabIndex + 1, poster);
        } else {
          // 图片库和视频库多选 - 返回选中项目数组
          const selectedData = checkedItems.map((item) => ({
            content: item.content,
            poster: item.poster || "",
            name: item.name
          }));
          this.$emit("confirm", selectedData, this.tabIndex + 1);
        }

        // 清除选中状态
        currData.forEach((item) => {
          item.checked = false;
        });

        this.$emit("close");
      } else {
        uni.showToast({
          title: "请选择要确认的内容",
          icon: "none"
        });
      }
    },
    handleTabClick(item) {
      console.log("handleTabClick :>> ", item);
      this.tabIndex = item.value;
      const data = this.tabData[this.tabIndex].data;
      if (data.length <= 0) {
        this.load();
      } else {
        data.map((item) => {
          item.checked = false;
        });
      }
    },
    handleItemSelect(index) {
      console.log("handleItemSelect index :>> ", index);
      const currData = this.tabData[this.tabIndex].data;
      const currentItem = currData[index];

      // 话术库(tabIndex === 2)使用单选，图片库和视频库使用多选
      if (this.tabIndex === 2) {
        // 话术库单选逻辑
        if (currentItem.checked) {
          currentItem.checked = false;
        } else {
          // 先清除所有选中状态
          currData.forEach((item) => {
            item.checked = false;
          });
          // 然后选中当前项
          currentItem.checked = true;
        }
      } else {
        // 图片库和视频库多选逻辑
        if (currentItem.checked) {
          // 取消选中
          currentItem.checked = false;
        } else {
          // 检查是否超出数量限制
          const totalWillHave = this.currentCount + this.selectedCount + 1;
          if (totalWillHave > this.maxCount) {
            uni.showToast({
              title: `最多只能选择${this.remainingCount}个文件`,
              icon: "none"
            });
            return;
          }
          // 选中当前项
          currentItem.checked = true;
        }
      }

      console.log("updated currData :>> ", currData);
      this.$forceUpdate();
    },
    // 新增图片预览方法
    previewImage(imageUrl) {
      uni.previewImage({
        urls: [imageUrl],
        current: imageUrl
      });
    },
    // 新增视频预览关闭方法
    closeVideoPreview() {
      this.previewVideoVisible = false;
      if (this.drawerPlayer) {
        this.drawerPlayer.pause();
      }
    },
    prewViewWordsHandle(content) {
      this.prewViewWords = content;
      this.prewViewWordsVisible = true;
    },
    previewVideoHandle(item) {
      this.videoSrc = item.content;
      this.poster = item.poster;
      this.previewVideoVisible = true;
      this.$nextTick(() => {
        // 注意：需要确保已经引入了videojs库
        if (typeof videojs !== "undefined") {
          this.drawerPlayer = videojs(
            "drawer-video",
            {
              controls: true,
              poster: this.poster
            },
            () => {
              this.drawerPlayer.src({
                src: this.videoSrc
              });
              this.drawerPlayer.play();
            }
          );
        } else {
          console.warn(
            "videojs is not defined, please include video.js library"
          );
        }
      });
      // videojs("drawer-video", {}, function () {
      //   window.drawerPlayer = this;
      //   drawerPlayer.src(this.videoSrc);
      //   drawerPlayer.load(this.videoSrc);
      //   drawerPlayer.play();
      // });
    },
    getList() {
      const index = this.tabIndex;
      const { page, searchValue, data } = this.tabData[index];
      this.tabData[index].loading = true;
      //   const schId = uni.getStorageSync("checkedSchool");
      //   const schId = [];
      getFileList({
        page,
        category_id: index + 1,
        // department_id: schId.length ? schId.map((item) => item.id) : "",
        name: searchValue,
        page_size: 10,
        token: uni.getStorageSync("user").token,
        requestType: "MINIGRAMER"
      })
        .then((res) => {
          this.tabData[index].loading = false;
          if (res.code === 0) {
            const results = res.data?.results || [];
            results.map((item) => {
              item.checked = false;
            });
            if (results.length) {
              this.tabData[index].data = data.concat(results);
              this.tabData[index].page = page + 1;
            } else {
              this.tabData[index].noMore = true;
            }
            this.clearAllChecked();
          } else {
            uni.showToast({
              title: res.data.message,
              icon: "none"
            });
          }
        })
        .catch((err) => {
          this.tabData[index].loading = false;
          console.error(err);
          uni.showToast({
            title: "获取数据失败!",
            icon: "none"
          });
        });
    },
    search() {
      const index = this.tabIndex;
      this.tabData[index].page = 1;
      this.tabData[index].data = [];
      this.tabData[index].noMore = false;
      this.getList();
    },
    load() {
      // 防止重复加载
      if (this.currentTabData.loading || this.currentTabData.noMore) {
        return;
      }
      this.getList();
    },
    clearAllChecked() {
      const currData = this.tabData[this.tabIndex].data;
      currData.forEach((item) => {
        item.checked = false;
      });
    }
  },
  beforeUnmount() {
    if (this.drawerPlayer) {
      this.drawerPlayer.dispose();
    }
  }
};
</script>

  <style lang="scss" scoped>
.resource-hub-tab {
  height: 100%;
  // background-color: #fafafa;
  display: flex;
  flex-direction: column;

  // u-tabs样式重写
  :deep(.u-tabs) {
    padding: 0px 20px;
    background-color: #fff;

    .u-tabs__wrapper__nav__item {
      height: 50px;
      line-height: 50px;
      font-weight: bold;
    }
  }

  .tab-content {
    flex: 1;
    overflow: hidden;
  }
  .serach-input {
    height: 50px;
    display: flex;
    align-items: center;
    padding: 0 20px;
    margin-bottom: 10px;
    position: relative;

    :deep(.u-input) {
      flex: 1;
    }

    .search-txt {
      width: 50px;
      height: 30px;
      line-height: 30px;
      position: absolute;
      top: 10px;
      right: 30px;
      color: #0280e4;
      font-weight: bold;
      z-index: 10;
    }
  }

  .count-tip {
    padding: 0 20px 10px 20px;

    .tip-text {
      font-size: 12px;
      color: #999;
      background: #f5f5f5;
      padding: 6px 12px;
      border-radius: 12px;
      display: inline-block;
    }
  }
  .infinite-list-wrapper {
    height: 800rpx;
    flex: 1;
    overflow-y: auto;
    padding: 0 20px;
    background-color: #fafafa;
    // padding-bottom: 50px;

    .list {
      width: 100%;
    }

    .list-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 2.6667vw 5.6vw;
      background-color: #fff;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      border-radius: 16px;
      overflow: hidden;
      color: #303133;
      transition: 0.3s;
      margin-bottom: 16px;

      .name {
        color: #475669;
        font-size: 12px;
        font-style: normal;
        font-weight: 500;
        width: 22.6667vw;
        word-break: break-all;
      }
      .box {
        margin: 0 2.6667vw;

        &.img {
          background: #fafafa;
          border-radius: 2.6667vw;
          overflow: hidden;
        }

        &.video {
          background: #b0b0b0;
          position: relative;
          border-radius: 2.6667vw;
          overflow: hidden;

          .play-btn {
            position: absolute;
            top: 50%;
            left: 50%;
            z-index: 3;
            transform: translate(-50%, -50%);
            background: url(https://tg-prod.oss-cn-beijing.aliyuncs.com/52c7d9d2-ac99-4743-8fb7-f21f833c000a.png)
              center no-repeat;
            width: 10vw;
            height: 10vw;
            background-size: cover;
          }

          .poster-img {
            width: 38.8vw;
            height: 24vw;
            background-size: cover;
            opacity: 0.8;
          }
        }

        &.text {
          display: flex;
          align-items: center;

          .words {
            width: 100%;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 3;
            line-clamp: 3;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
      }

      .btn-box {
        display: flex;
        align-items: center;
        cursor: pointer;

        .custom-checkbox {
          width: 20px;
          height: 20px;
          border: 2px solid #dcdfe6;
          border-radius: 4px;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.3s;

          // 单选样式（话术库）- 圆形
          &.single-select {
            border-radius: 50%;
          }

          // 多选样式（图片库、视频库）- 方形
          &.multi-select {
            border-radius: 4px;
          }

          &.checked {
            background-color: #3667f0;
            border-color: #3667f0;

            .checkbox-icon {
              color: white;
              font-size: 12px;
              font-weight: bold;
            }
          }

          &:hover {
            border-color: #3667f0;
          }
        }
      }
    }
    .loading,
    .no-more {
      text-align: center;
      color: #666;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      padding: 20px;
    }
  }

  .resource-hub-footer {
    // position: sticky;
    bottom: 0;
    left: 0;
    width: 100%;
    z-index: 10;
    background: #fff;
    padding: 10px 0;
  }
}

.prew-view-words {
  padding: 20px;
  font-size: 14px;
  line-height: 1.6;
}

.video-box {
  padding: 20px;

  .video-js {
    width: 100%;
    height: 260px;
    background: #000;
    border-radius: 1.3333vw;
    overflow: hidden;
  }

  video {
    background: #000;
    border-radius: 1.3333vw;
  }
}
</style>
