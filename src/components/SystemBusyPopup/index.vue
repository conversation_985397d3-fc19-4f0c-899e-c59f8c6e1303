<template>
  <u-popup
    :show="showPopup"
    mode="bottom"
    width="600rpx"
    round="20rpx"
    @close="closePopup"
  >
    <view class="busy-popup">
      <!-- 图片区域 -->
      <image
        class="busy-image"
        src="https://tg-prod.oss-cn-beijing.aliyuncs.com/2699790a-1379-4e53-9d0c-6cd5f3e7fdf8.webp"
        mode="aspectFit"
      ></image>

      <!-- 文字区域 -->
      <view class="busy-title">稍等片刻</view>
      <view class="busy-desc">{{
        message || "对不起，系统繁忙，请稍后再试~"
      }}</view>

      <!-- 按钮区域 -->
      <view class="button-wrapper">
        <button class="confirm-btn" @tap="closePopup">知道了</button>
      </view>
    </view>
  </u-popup>
</template>

<script>
export default {
  name: "SystemBusyPopup",
  props: {
    show: {
      type: <PERSON><PERSON><PERSON>,
      default: false
    },
    message: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      showPopup: false
    };
  },
  watch: {
    show(val) {
      this.showPopup = val;
    },
    showPopup(val) {
      if (val !== this.show) {
        this.$emit("update:show", val);
      }
    }
  },
  methods: {
    closePopup() {
      this.showPopup = false;
      this.$emit("close");
    }
  }
};
</script>

<style lang="scss" scoped>
.busy-popup {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx 32rpx;
  background-color: #ffffff;
  border-radius: 22rpx 22rpx 0px 0px;
  .busy-image {
    width: 220rpx;
    height: 220rpx;
    margin-top: 85rpx;
    margin-bottom: 37rpx;
  }

  .busy-title {
    color: #333;
    text-align: center;
    font-size: 36rpx;
    font-style: normal;
    font-weight: 500;
    line-height: 52px;
  }

  .busy-desc {
    color: #999;
    text-align: center;
    font-size: 28rpx;
    font-style: normal;
    font-weight: 400;
    line-height: 32rpx;
    margin-bottom: 92rpx;
  }

  .button-wrapper {
    width: 100%;

    .confirm-btn {
      width: 100%;
      height: 88rpx;
      background: linear-gradient(15deg, #ffbf0d 18.1%, #ffcb3c 83.29%);
      box-shadow: 0px -5rpx 12rpx 0px #fc0 inset,
        0px 9rpx 20rpx 0px #fff7e1 inset;
      filter: drop-shadow(0px 4rpx 4rpx rgba(255, 192, 18, 0.11));
      color: #ffffff;
      font-size: 32rpx;
      font-weight: 600;
      border-radius: 44rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      border: none;

      &::after {
        border: none;
      }
    }
  }
}
</style>
