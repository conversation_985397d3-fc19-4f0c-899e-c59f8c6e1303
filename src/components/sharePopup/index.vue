<template>
  <view @touchmove.stop.prevent="">
    <u-popup
      :show="showSharePopup"
      :round="22"
      mode="bottom"
      :overlayOpacity="overlayOpacity"
      @close="closeSharePopup"
      @open="openSharePopup(shareFriendInfo)"
    >
      <div class="share-popup">
        <img
          class="guidance"
          v-if="isShowGuidance"
          :style="{ bottom: guideBottom + 'rpx', left: guideLeft + 'rpx' }"
          src="https://tg-prod.oss-cn-beijing.aliyuncs.com/c8a0af17-0cc6-453a-a94a-7c1bca33552d.png"
          alt=""
        />
        <div class="share-title">
          分享至<img
            @tap="closeSharePopup"
            class="close"
            src="https://tg-prod.oss-cn-beijing.aliyuncs.com/59386f9f-a41c-463e-bb86-6f3c5ddd35b7.png"
            alt=""
          />
        </div>
        <div class="share-list">
          <div
            v-if="isShowAppMessage"
            @tap="handleShare(item, 'appMessage')"
            class="share-item"
          >
            <button
              hover-class="none"
              open-type="share"
              :data-item="shareFriendInfo"
            >
              <img
                class="share-item-icon"
                src="https://tg-prod.oss-cn-beijing.aliyuncs.com/c419e231-8d78-4dee-9fd0-fcab597802ac.png"
                alt=""
              />
              <div class="share-item-name">微信好友</div>
            </button>
          </div>
          <div
            v-if="isShowTimeLine"
            @tap="handleShare(item, 'timeLine')"
            class="share-item"
          >
            <img
              class="share-item-icon"
              src="https://tg-prod.oss-cn-beijing.aliyuncs.com/7af5cfea-1126-4a15-aa36-1dc4ae6d671b.png"
              alt=""
            />
            <div class="share-item-name">朋友圈</div>
          </div>
          <div
            v-if="isShowMiniProgram"
            class="share-item"
            @tap="handleShare(item, 'miniProgram')"
          >
            <img
              class="share-item-icon"
              src="https://tg-prod.oss-cn-beijing.aliyuncs.com/a1f14fe7-0dc8-46b8-a951-938656bbc6c3.png"
              alt=""
            />
            <div class="share-item-name">生成分享图</div>
          </div>
        </div>
      </div>
    </u-popup>
  </view>
</template>

<script>
export default {
  name: "SharePopup",
  props: {
    // 分享H5页面路径
    webviewPath: {
      type: String,
      default: ""
    },
    // 分享H5页面参数
    webviewParams: {
      type: Object,
      default: () => {}
    },
    showSharePopup: {
      type: Boolean,
      default: false
    },
    shareFriendInfo: {
      type: Object,
      default: () => {}
    },
    isShowAppMessage: {
      type: Boolean,
      default: true
    },
    isShowTimeLine: {
      type: Boolean,
      default: true
    },
    isShowMiniProgram: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      overlayOpacity: 0.5,
      isShowGuidance: false,
      guideBottom: 0,
      guideLeft: 0
    };
  },
  computed: {},
  methods: {
    calcPosition() {
      uni.getSystemInfo({
        success: (res) => {
          const menuButton = uni.getMenuButtonBoundingClientRect();
          const systemInfo = uni.getSystemInfoSync();

          const pxPerRpx = systemInfo.windowWidth / 750;
          const imgWidth = 575 * pxPerRpx;
          const popupPadding = 36 * pxPerRpx + 43 * pxPerRpx;
          const popupTitleMarginBottom = 55 * pxPerRpx;
          // 计算图片 bottom = 屏幕高度 - 菜单按钮 bottom - 安全区域底部 - 弹窗padding - 弹窗标题marginBottom
          console.log(res.safeAreaInsets, "res.safeAreaInsets");
          this.guideBottom =
            systemInfo.windowHeight -
            menuButton.bottom -
            res.safeAreaInsets.bottom -
            popupPadding -
            popupTitleMarginBottom;

          // 可调位置：比如右对齐、靠右侧留 16px
          this.guideLeft = systemInfo.windowWidth - imgWidth - 16;
          console.log(this.guideBottom, this.guideLeft, "guideBottom");
        }
      });
    },
    closeSharePopup() {
      this.$emit("closeSharePopup", false);
      this.isShowGuidance = false;
    },
    openSharePopup(shareFriendInfo) {
      this.$emit("update:shareFriendInfo", shareFriendInfo);
      this.$emit("showPopup");
    },
    handleShare(item, type) {
      if (type === "miniProgram") {
        this.isShowGuidance = false;
        this.overlayOpacity = 0.5;
        this.handleGenerateShareImage();
      } else if (type === "appMessage") {
        this.$emit("closeSharePopup", false);
        this.isShowGuidance = false;
        this.overlayOpacity = 0.5;
        this.$emit("update:showSharePopup", false);
      } else if (type === "timeLine") {
        this.overlayOpacity = 0.7;
        this.isShowGuidance = true;
      }
    },
    handleGenerateShareImage() {
      const { webviewPath, webviewParams } = this;
      const host = process.env.VUE_APP_TG_H5_HOST;
      // const host = "http://*************:9000";
      // 将webviewParams对象转换为queryParams
      const params = uni.$u.queryParams(webviewParams);
      console.log(params, webviewParams, "params");
      const webviewUrl = `${host}/${webviewPath}${params}`;
      console.log(webviewUrl, "webviewUrl");
      uni.navigateTo({
        url:
          "/pages/student/subpages/shareWebview/index?webviewUrl=" +
          encodeURIComponent(webviewUrl)
      });
      this.$emit("closeSharePopup");
    }
  },
  watch: {},

  // 组件周期函数--监听组件挂载完毕
  mounted() {
    this.calcPosition();
  },
  // 组件周期函数--监听组件数据更新之前
  beforeUpdate() {},
  // 组件周期函数--监听组件数据更新之后
  updated() {},
  // 组件周期函数--监听组件激活(显示)
  activated() {},
  // 组件周期函数--监听组件停用(隐藏)
  deactivated() {},
  // 组件周期函数--监听组件销毁之前
  beforeUnmount() {}
};
</script>

<style lang="scss" scoped>
.share-popup {
  padding: 36rpx 60rpx 43rpx 60rpx;
  position: relative;
  .guidance {
    position: absolute;
    left: 40px;
    width: 575rpx;
    height: 755rpx;
  }
  .share-title {
    width: 100%;
    font-size: 32rpx;
    font-weight: 500;
    color: #333;
    text-align: center;
    text-align: center;
    margin-bottom: 55rpx;
    .close {
      position: absolute;
      right: 32rpx;
      top: 43rpx;
      width: 30rpx;
      height: 30rpx;
      img {
        width: 100%;
        height: 100%;
      }
    }
  }
  .share-list {
    display: flex;
    flex-flow: wrap;
    justify-content: space-around;
    .share-item {
      text-align: center;
      .share-item-icon {
        width: 90rpx;
        height: 90rpx;
        margin: 0 auto;
        margin-bottom: 13rpx;
      }
      .share-item-name {
        font-size: 28rpx;
        font-weight: 400;
        color: #333;
      }
    }
  }
}
</style>
