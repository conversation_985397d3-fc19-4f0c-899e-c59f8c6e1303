{"appid": "wxaec7b5c7e1660a65", "compileType": "miniprogram", "libVersion": "3.3.5", "packOptions": {"ignore": [{"type": "folder", "value": "node_modules"}], "include": []}, "setting": {"coverView": true, "es6": true, "postcss": true, "minified": true, "enhance": true, "ignoreDevUnusedFiles": false, "ignoreUploadUnusedFiles": false, "showShadowRootInWxmlPanel": true, "packNpmRelationList": [], "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}}, "condition": {}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}}